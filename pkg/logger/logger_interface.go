package logger

import "context"

type LoggerInterface interface {
	Info(args ...interface{})
	Infof(template string, args ...interface{})
	Debug(args ...interface{})
	Debugf(template string, args ...interface{})
	Warn(args ...interface{})
	Warnf(template string, args ...interface{})
	Error(args ...interface{})
	Errorf(template string, args ...interface{})
	Fatal(args ...interface{})
	Fatalf(template string, args ...interface{})

	ErrorWithFiled(filed map[string]interface{}, args ...interface{})
	InfoWithFiled(filed map[string]interface{}, args ...interface{})

	InfoWithFiledCtx(ctx context.Context, fields map[string]interface{}, tmpl string, args ...interface{})
	InfofWithFiledCtx(ctx context.Context, fields map[string]interface{}, tmpl string, args ...interface{})

	InfofCtx(ctx context.Context, template string, args ...interface{})
	DebugfCtx(ctx context.Context, template string, args ...interface{})
	WarnfCtx(ctx context.Context, template string, args ...interface{})
	ErrorfCtx(ctx context.Context, template string, args ...interface{})
	FatalfCtx(ctx context.Context, template string, args ...interface{})
}
