package ip

import (
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/lionsoul2014/ip2region/binding/golang/xdb"
)

type Searcher struct {
	searcher *xdb.Searcher
	lock     sync.Mutex
	closed   bool
}

var _searcher Searcher

func InitIP() {
	_searcher.lock.Lock()
	defer _searcher.lock.Unlock()
	if _searcher.closed {
		// 若 searcher 已关闭，则重置 closed 标志
		_searcher.closed = false
	}

	searcher, err := xdb.NewWithFileOnly(config.GlobConfig.IpRegion.FilePath)
	if err != nil {
		logger.Logger.Errorf("failed to create searcher: %s\n", err.Error())
		return
	}
	//defer searcher.Close()
	_searcher.searcher = searcher
}

func Close() {
	_searcher.lock.Lock()
	defer _searcher.lock.Unlock()

	if _searcher.searcher != nil {
		_searcher.searcher.Close()
	}
	_searcher.closed = true
}

func GetRegionByIP(ip string) string {
	_searcher.lock.Lock()
	defer _searcher.lock.Unlock()

	if _searcher.closed || _searcher.searcher == nil {
		// 若 searcher 已关闭或尚未初始化，返回空字符串
		logger.Logger.Warnf("GetIP warn: 尚未初始化完成，地域返回空")
		return ""
	}

	region, err := _searcher.searcher.SearchByStr(ip)
	if err != nil {
		logger.Logger.Errorf("GetIP err: %s", err.Error())
		return ""
	}
	return region
}

func GetRegionCityByIP(ip string) string {
	_searcher.lock.Lock()
	defer _searcher.lock.Unlock()

	if _searcher.closed || _searcher.searcher == nil {
		logger.Logger.Warnf("GetIP warn: 尚未初始化完成，地域返回空")
		return ""
	}

	region, err := _searcher.searcher.SearchByStr(ip)
	if err != nil {
		logger.Logger.Errorf("GetIP err: %s", err.Error())
		return ""
	}

	// 安全地处理 region 字符串
	parts := strings.Split(region, "|")
	if len(parts) >= 4 {
		return parts[3]
	}

	logger.Logger.Warnf("GetIP warn: 意外的 region 格式: %s", region)
	return ""
}
