package mysql

import (
	"context"
	"errors"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"gorm.io/gorm"

	glogger "gorm.io/gorm/logger"
)

type SqlLogger struct {
	slowSQLThreshold int64
}

var _ glogger.Interface = (*SqlLogger)(nil)

func (l *SqlLogger) LogMode(level glogger.LogLevel) glogger.Interface {
	return l
}

func (l *SqlLogger) Info(ctx context.Context, fmt string, args ...interface{}) {
	logger.Logger.Infof(fmt, args)
}

func (l *SqlLogger) Warn(ctx context.Context, fmt string, args ...interface{}) {
	logger.Logger.Warnf(fmt, args)
}

func (l *SqlLogger) Error(ctx context.Context, fmt string, args ...interface{}) {
	logger.Logger.Errorf(fmt, args)
}

func (l *SqlLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	t := float64(time.Since(begin).Nanoseconds()) / 1000000.0
	sql, count := fc()
	if config.GlobConfig.Logger.Level == "debug" {
		fields := make(map[string]interface{})
		fields["sql_latency_millis"] = t
		fields["count"] = count
		fields["sql"] = sql
		logger.Logger.InfoWithFiledCtx(ctx, fields, "SQL接口耗时 %.2f ms, 影响行数 %d, SQL语句 %s", t, count, sql)
	}
	if l.slowSQLThreshold > 0 && t >= float64(l.slowSQLThreshold) {
		logger.Logger.Warnf("SLOW SQL >= %s", sql)
	}
	// 打印慢查询日志
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// 检查是否为 context canceled 错误
		if errors.Is(err, context.Canceled) {
			// context canceled 错误降级为 WarnCtx
			logger.Logger.WarnfCtx(ctx, "gorm db context canceled, sql: %s", sql)
		} else {
			// 其他错误使用 ErrorfCtx
			logger.Logger.ErrorfCtx(ctx, "gorm db err: %v, sql: %s", err, sql)
		}
	}
}

func getDBLogger(slowSQLThreshold int) *SqlLogger {
	return &SqlLogger{slowSQLThreshold: int64(slowSQLThreshold)}
}
