package mysql

import (
	"context"
	"fmt"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

var (
	lock sync.Mutex
	_db  = make(map[string]*gorm.DB)
)

func InitMysql(conf *config.MysqlConf) {
	if conf == nil {
		panic("cof is nil")
	}
	if len(conf.Host) == 0 {
		conf.Host = "localhost"
	}
	if len(conf.Port) == 0 {
		conf.Port = "3306"
	}
	if conf.OpenConns == 0 {
		conf.OpenConns = 5
	}
	if conf.IdleConns == 0 {
		conf.IdleConns = 1
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		conf.UserName,
		conf.Password,
		conf.Host,
		conf.Port,
		conf.DBName)
	dbLogger := getDBLogger(conf.SlowSQLThreshold)
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: dbLogger,
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   "",
			SingularTable: true,
		},
	})
	if err != nil {
		panic(err)
	}

	// 获取底层的 *sql.DB 并设置连接池参数
	sqlDB, err := db.DB()
	if err != nil {
		panic(err)
	}
	sqlDB.SetMaxOpenConns(conf.OpenConns)
	sqlDB.SetMaxIdleConns(conf.IdleConns)

	// 设置连接生命周期参数
	// if conf.ConnMaxLifetime > 0 {
	// 	sqlDB.SetConnMaxLifetime(conf.ConnMaxLifetime)
	// }
	// if conf.ConnMaxIdleTime > 0 {
	// 	sqlDB.SetConnMaxIdleTime(conf.ConnMaxIdleTime)
	// }

	lock.Lock()
	defer lock.Unlock()
	if _, ok := _db[conf.DBName]; ok {
		logger.Logger.Errorf("%s[mysql] exist.", conf.DBName)
		return
	}
	_db[conf.DBName] = db
}

func DB(ctx context.Context, dbName string) *gorm.DB {
	ins, ok := _db[dbName]
	if !ok {
		logger.Logger.Error("get db in	stance bizerrors")
		return nil
	}
	return ins.WithContext(ctx)
}
