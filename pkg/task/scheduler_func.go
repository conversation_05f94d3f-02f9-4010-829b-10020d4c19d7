package task

import (
	"github.com/hibiken/asynq"
)

func GetSchedulerTasks() map[SchedulerCronType]*asynq.Task {
	return map[SchedulerCronType]*asynq.Task{
		//"@every 30s": asynq.NewTask(TypePayCallbackToProduct, nil),
	}
}

//func GetEnqueueTasks() map[asynq.Option]*asynq.Task {
//	return map[asynq.Option]*asynq.Task{
//		asynq.ProcessIn(20 * time.Second): asynq.NewTask(TypePayCallbackToProduct, nil),
//	}
//}
