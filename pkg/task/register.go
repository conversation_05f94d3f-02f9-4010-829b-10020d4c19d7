package task

import "github.com/hibiken/asynq"

type (
	HandlerFuncMap map[string]asynq.HandlerFunc
)

type DynamicServeMux struct {
	mux *asynq.ServeMux
}

func NewDynamicServeMux() *DynamicServeMux {
	return &DynamicServeMux{
		mux: asynq.NewServeMux(),
	}
}

func (d *DynamicServeMux) RegisterHandlers(handlerFuncs HandlerFuncMap) {
	for typ, h := range handlerFuncs {
		d.mux.HandleFunc(typ, h)
	}
}

func (d *DynamicServeMux) GetServeMux() *asynq.ServeMux {
	return d.mux
}
