package task

import (
	"encoding/json"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"github.com/hibiken/asynq"
	"github.com/stretchr/testify/assert"
)

// TestContentMonitorCallbackTaskCreation 测试内容监控回调任务创建
func TestContentMonitorCallbackTaskCreation(t *testing.T) {
	// 创建测试数据
	contentCallbackData := &bean.ContentCallbackData{
		ContentID:        "test_content_456",
		UserID:           "test_user_789",
		SourceType:       "public_chat",
		ServerID:         "server_001",
		ServerName:       "测试服务器",
		RoleID:           "role_123",
		RoleName:         "测试角色",
		RoleLevel:        30,
		AllianceID:       "alliance_456",
		AllianceName:     "测试公会",
		IsAllianceLeader: true,
		Content:          "测试内容文本",
		Operations:       []string{`{"action": "warn"}`},
		OperatorID:       "admin_test",
		OperatorName:     "测试管理员",
		CreatedAt:        time.Now().UnixMilli(),
	}

	contentCallbackReq := &bean.ContentCallbackReq{
		Attempt:             0,
		GameID:              "test_game_123",
		CallbackURL:         "https://test.example.com/callback",
		ContentCallbackData: contentCallbackData,
	}

	// 序列化数据
	payload, err := json.Marshal(contentCallbackReq)
	assert.NoError(t, err)
	assert.NotEmpty(t, payload)

	// 创建任务
	task := asynq.NewTask(TypeMonitorCallback, payload)
	assert.NotNil(t, task)
	assert.Equal(t, TypeMonitorCallback, task.Type())
	assert.NotEmpty(t, task.Payload())

	// 验证任务类型常量
	assert.Equal(t, "monitor_callback", TypeMonitorCallback)
}

// TestContentCallbackDataValidation 测试内容回调数据验证
func TestContentCallbackDataValidation(t *testing.T) {
	tests := []struct {
		name    string
		req     *bean.ContentCallbackReq
		wantErr bool
	}{
		{
			name: "valid request",
			req: &bean.ContentCallbackReq{
				GameID: "test_game",
				ContentCallbackData: &bean.ContentCallbackData{
					ContentID: "test_content",
				},
			},
			wantErr: false,
		},
		{
			name: "nil callback data",
			req: &bean.ContentCallbackReq{
				GameID:              "test_game",
				ContentCallbackData: nil,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateContentRequest(tt.req)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestTaskRegistration 测试任务注册
func TestTaskRegistration(t *testing.T) {
	handlers := GetHandleTasks()

	// 验证内容监控回调任务已注册
	handler, exists := handlers[TypeMonitorCallback]
	assert.True(t, exists, "内容监控回调任务应该已注册")
	assert.NotNil(t, handler, "处理函数不应为空")

	// 验证其他任务也正常注册
	expectedTasks := []string{
		TypeSubscribeMessage,
		TypeQiyuSubscribeMessage,
		TypeCustomerMessage,
		TypeDouyinPayCallback,
		TypeProductShipmentOrder,
		TypeReportCallback,
		TypeMonitorCallback,
		TypeWorkOrderReply,
	}

	for _, taskType := range expectedTasks {
		_, exists := handlers[taskType]
		assert.True(t, exists, "任务类型 %s 应该已注册", taskType)
	}
}

// TestContentCallbackDataSerialization 测试内容回调数据序列化
func TestContentCallbackDataSerialization(t *testing.T) {
	originalData := &bean.ContentCallbackData{
		ContentID:        "content_789",
		UserID:           "user_012",
		SourceType:       "alliance_chat",
		ServerID:         "srv_345",
		ServerName:       "服务器名称",
		RoleID:           "role_678",
		RoleName:         "角色名称",
		RoleLevel:        99,
		AllianceID:       "alliance_901",
		AllianceName:     "公会名称",
		IsAllianceLeader: false,
		Content:          "内容文本",
		Operations:       []string{`{"action": "ban", "duration": 3600}`},
		OperatorID:       "admin_234",
		OperatorName:     "管理员234",
		CreatedAt:        1234567890123,
	}

	// 序列化
	jsonData, err := json.Marshal(originalData)
	assert.NoError(t, err)
	assert.NotEmpty(t, jsonData)

	// 反序列化
	var deserializedData bean.ContentCallbackData
	err = json.Unmarshal(jsonData, &deserializedData)
	assert.NoError(t, err)

	// 验证数据一致性
	assert.Equal(t, originalData.ContentID, deserializedData.ContentID)
	assert.Equal(t, originalData.UserID, deserializedData.UserID)
	assert.Equal(t, originalData.SourceType, deserializedData.SourceType)
	assert.Equal(t, originalData.ServerID, deserializedData.ServerID)
	assert.Equal(t, originalData.ServerName, deserializedData.ServerName)
	assert.Equal(t, originalData.RoleID, deserializedData.RoleID)
	assert.Equal(t, originalData.RoleName, deserializedData.RoleName)
	assert.Equal(t, originalData.RoleLevel, deserializedData.RoleLevel)
	assert.Equal(t, originalData.AllianceID, deserializedData.AllianceID)
	assert.Equal(t, originalData.AllianceName, deserializedData.AllianceName)
	assert.Equal(t, originalData.IsAllianceLeader, deserializedData.IsAllianceLeader)
	assert.Equal(t, originalData.Content, deserializedData.Content)
	assert.Equal(t, originalData.Operations, deserializedData.Operations)
	assert.Equal(t, originalData.OperatorID, deserializedData.OperatorID)
	assert.Equal(t, originalData.OperatorName, deserializedData.OperatorName)
	assert.Equal(t, originalData.CreatedAt, deserializedData.CreatedAt)
}
