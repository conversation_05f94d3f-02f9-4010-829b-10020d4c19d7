package task

import (
	"log"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/hibiken/asynq"
)

var _scheduler *asynq.Scheduler

func initScheduler(conf *config.RedisTaskConf, schedulerTasks map[SchedulerCronType]*asynq.Task) {
	opt := asynq.RedisClientOpt{
		Addr:     conf.Hosts[0],
		DB:       conf.DB,
		Username: conf.UserName,
		Password: conf.Password,
		PoolSize: conf.PoolSize,
	}

	scheduler := asynq.NewScheduler(opt, &asynq.SchedulerOpts{
		Logger:   NewAsyncLogger(),
		Location: time.UTC,
	})
	for k, t := range schedulerTasks {
		_, err := scheduler.Register(string(k), t)
		if err != nil {
			log.Panicf("could not register email delivery task: %v", err)
		}
	}

	_scheduler = scheduler
	if err := scheduler.Run(); err != nil {
		log.Panicf("scheduler stopped: %v", err)
	}
}

func Scheduler() *asynq.Scheduler {
	return _scheduler
}
