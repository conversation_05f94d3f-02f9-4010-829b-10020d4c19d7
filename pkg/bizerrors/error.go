package bizerrors

import (
	"fmt"
	"github.com/pkg/errors"
)

type BizError struct {
	Code int
	Msg  string
}

func (e BizError) Error() string {
	return fmt.Sprintf("code: %d, msg: %s", e.Code, e.Msg)
}

func Wrap(cause error, message string) error {
	return errors.Wrap(cause, message)
}

func Cause(err error) error {
	for errors.Unwrap(err) != nil {
		err = errors.Unwrap(err)
	}
	return err
}

// NewBizError 创建业务错误
func NewBizError(code int, msg string) error {
	return BizError{
		Code: code,
		Msg:  msg,
	}
}
