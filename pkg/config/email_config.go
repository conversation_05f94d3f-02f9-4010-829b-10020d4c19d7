package config

import "time"

// SmtpConfig SMTP邮箱配置
type SmtpConfig struct {
	Host     string `mapstructure:"host" json:"host" yaml:"host"`
	Port     int    `mapstructure:"port" json:"port" yaml:"port"`
	Username string `mapstructure:"username" json:"username" yaml:"username"`
	Password string `mapstructure:"password" json:"password" yaml:"password"`
	From     string `mapstructure:"from" json:"from" yaml:"from"`
}

// EmailTaskConfig 邮件任务配置
type EmailTaskConfig struct {
	WorkorderStats WorkorderStatsConfig `mapstructure:"workorder_stats" json:"workorder_stats" yaml:"workorder_stats"`
}

// WorkorderStatsConfig 工单统计配置
type WorkorderStatsConfig struct {
	CronSpec string        `mapstructure:"cron_spec" json:"cron_spec" yaml:"cron_spec"`
	TimeZone string        `mapstructure:"time_zone" json:"time_zone" yaml:"time_zone"`
	Duration time.Duration `mapstructure:"duration" json:"duration" yaml:"duration"` // 统计时长，默认24小时
}
