package redis

import (
	"context"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/go-redis/redis/v8"
)

const Nil = redis.Nil

type Client struct {
	client redis.UniversalClient
}

var _client redis.UniversalClient

func InitRedis(conf *config.RedisConf) {
	switch conf.Type {
	case "single":
		_client = redis.NewClient(&redis.Options{
			Addr:         conf.Hosts[0],
			DB:           conf.DB,
			Username:     conf.UserName,
			Password:     conf.Password,
			PoolSize:     conf.PoolSize,
			MinIdleConns: conf.MinIdleCons,
		})
	case "cluster":
		_client = redis.NewClusterClient(&redis.ClusterOptions{
			Addrs:        conf.Hosts,
			Username:     conf.UserName,
			Password:     conf.Password,
			PoolSize:     conf.PoolSize,
			MinIdleConns: conf.MinIdleCons,
		})
	case "sentinel":

	}

	if _client == nil {
		panic("cof is nil")
	}

	if _, err := _client.Ping(context.Background()).Result(); err != nil {
		logger.Logger.Errorf("%s[redis] connect fail.")
	}

	_ = &Client{
		client: _client,
	}
}

func Redis() redis.UniversalClient {
	return _client
}

// Get 获取缓存
func Get(ctx context.Context, key string) (string, error) {
	return _client.Get(ctx, key).Result()
}

// Exists 检查键是否存在
func Exists(ctx context.Context, key string) (bool, error) {
	n, err := _client.Exists(ctx, key).Result()
	return n > 0, err
}

// Set 设置缓存
func Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return _client.Set(ctx, key, value, expiration).Err()
}

// HSet 设置
func HSet(ctx context.Context, key string, field string, value interface{}) error {
	return _client.HSet(ctx, key, field, value).Err()
}

// HGet 获取
func HGet(ctx context.Context, key string, field string) (string, error) {
	return _client.HGet(ctx, key, field).Result()
}

// HGetAll 获取
func HGetAll(ctx context.Context, key string) (map[string]string, error) {
	return _client.HGetAll(ctx, key).Result()
}

// ZRem 删除
func ZRem(ctx context.Context, key string, value interface{}) error {
	return _client.ZRem(ctx, key, value).Err()
}

// Lock 加锁
func Lock(ctx context.Context, key string, expiration time.Duration) bool {
	lock, err := _client.SetNX(ctx, key, "", expiration).Result()
	if err != nil {
		return false
	}
	return lock
}

// UnLock 解锁
func UnLock(ctx context.Context, key string) {
	_ = _client.Del(ctx, key)
}

// Publish 发布
func Publish(ctx context.Context, key string, value interface{}) (int64, error) {
	return _client.Publish(ctx, key, value).Result()
}

// Subscribe 订阅
func Subscribe(ctx context.Context, chanNames ...string) (bool, <-chan *redis.Message) {
	pubSub := _client.Subscribe(ctx, chanNames...)
	if _, err := pubSub.Receive(ctx); err != nil {
		return false, nil
	}
	////redis 默认是100，此处要和Redis默认值保持一致
	//c := make(chan *Message, 100)
	//for m := range pubSub.Channel() {
	//	c <- &Message{
	//		Channel:      m.Channel,
	//		Pattern:      m.Pattern,
	//		Payload:      m.Payload,
	//		PayloadSlice: m.PayloadSlice,
	//	}
	//}
	return true, pubSub.Channel()
}

// func Keys(ctx context.Context, key string) (keys []string) {
// 	keys, _ = KeysE(ctx, key)
// 	return
// }

// func KeysE(ctx context.Context, key string) (keys []string, err error) {
// 	var cursor uint64
// 	var tempKeys []string
// 	for {
// 		tempKeys, cursor, err = _client.Scan(ctx, cursor, key, 5000).Result()
// 		if err != nil {
// 			break
// 		}
// 		keys = append(keys, tempKeys...)
// 		if cursor == 0 {
// 			break
// 		}
// 	}
// 	return
// }

// SADD
func SAdd(ctx context.Context, key string, value interface{}) error {
	return _client.SAdd(ctx, key, value).Err()
}

// SMembers
func SMembers(ctx context.Context, key string) ([]string, error) {
	return _client.SMembers(ctx, key).Result()
}
