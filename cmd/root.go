package cmd

import (
	"git.panlonggame.com/bkxplatform/admin-console/internal"
	"git.panlonggame.com/bkxplatform/admin-console/internal/cron"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/ip"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/mysql"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/task"
)

func InitDependency() {
	mustInit()
	initLogger()
	initMysql()
	initRedis()
	initCron() // 涉及到mysql,redis的加载配置，必须放在mysql,redis初始化之后
	initTask()
	initIP()

	internal.Run()
}

func mustInit() {
	config.MustInit()
}

func initLogger() {
	logger.InitLogger(&config.GlobConfig.Logger)
}

func initMysql() {
	mysql.InitMysql(&config.GlobConfig.Mysql)
	store.InitQueryDB()
}

func initRedis() {
	redis.InitRedis(&config.GlobConfig.Redis)
}

func initCron() {
	cron.InitCron()
	cron.LoadRedisSecret()
	cron.LoadMemorySensitiveWord()
	cron.LoadMemorySwitch()
	cron.LoadMemoryCity()
}

func initIP() {
	ip.InitIP()
}

func initTask() {
	task.InitTask()
}
