// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMMinigamePageConfigCode = "m_minigame_page_config_code"

// MMinigamePageConfigCode mapped from table <m_minigame_page_config_code>
type MMinigamePageConfigCode struct {
	ID             int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UUID           string `gorm:"column:uuid" json:"uuid"`
	RestrainItemTp string `gorm:"column:restrain_item_tp" json:"restrain_item_tp"`
	Key            string `gorm:"column:key" json:"key"`
	Value          string `gorm:"column:value" json:"value"`
	Sort           int32  `gorm:"column:sort" json:"sort"`
}

// TableName MMinigamePageConfigCode's table name
func (*MMinigamePageConfigCode) TableName() string {
	return TableNameMMinigamePageConfigCode
}
