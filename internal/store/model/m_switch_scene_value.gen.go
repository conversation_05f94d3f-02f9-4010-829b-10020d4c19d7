// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMSwitchSceneValue = "m_switch_scene_value"

// MSwitchSceneValue mapped from table <m_switch_scene_value>
type MSwitchSceneValue struct {
	ID             int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UUID           string `gorm:"column:uuid" json:"uuid"`
	RestrainItemTp string `gorm:"column:restrain_item_tp" json:"restrain_item_tp"`
	Key            string `gorm:"column:key" json:"key"`
	Value          string `gorm:"column:value" json:"value"`
	Sort           int32  `gorm:"column:sort" json:"sort"`
}

// TableName MSwitchSceneValue's table name
func (*MSwitchSceneValue) TableName() string {
	return TableNameMSwitchSceneValue
}
