// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameUser = "user"

// User mapped from table <user>
type User struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID    string `gorm:"column:game_id;not null;comment:游戏id" json:"game_id"` // 游戏id
	UserID    string `gorm:"column:user_id;not null;comment:uuid" json:"user_id"` // uuid
	Channel   string `gorm:"column:channel;not null" json:"channel"`
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName User's table name
func (*User) TableName() string {
	return TableNameUser
}
