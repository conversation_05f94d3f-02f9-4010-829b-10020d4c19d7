// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMContentConfig = "m_content_config"

// MContentConfig 内容监控配置表
type MContentConfig struct {
	ID          int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                  // 主键ID
	ConfigKey   string `gorm:"column:config_key;not null;comment:配置键" json:"config_key"`                        // 配置键
	ConfigValue string `gorm:"column:config_value;not null;comment:配置值" json:"config_value"`                    // 配置值
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间戳" json:"updated_at"` // 更新时间戳
}

// TableName MContentConfig's table name
func (*MContentConfig) TableName() string {
	return TableNameMContentConfig
}
