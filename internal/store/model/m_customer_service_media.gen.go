// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMCustomerServiceMedium = "m_customer_service_media"

// MCustomerServiceMedium mapped from table <m_customer_service_media>
type MCustomerServiceMedium struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID    string `gorm:"column:game_id;not null" json:"game_id"`
	URL       string `gorm:"column:url;not null;default:0" json:"url"`
	FileType  string `gorm:"column:file_type;not null" json:"file_type"`
	MediaID   string `gorm:"column:media_id;not null" json:"media_id"`
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	ExpiredAt int64  `gorm:"column:expired_at;not null" json:"expired_at"`
}

// TableName MCustomerServiceMedium's table name
func (*MCustomerServiceMedium) TableName() string {
	return TableNameMCustomerServiceMedium
}
