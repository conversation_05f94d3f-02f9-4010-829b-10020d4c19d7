// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMMonitorGameServer = "m_monitor_game_servers"

// MMonitorGameServer 游戏区服信息表
type MMonitorGameServer struct {
	ID         int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"` // 主键ID
	GameID     string `gorm:"column:game_id;not null" json:"game_id"`
	ServerID   string `gorm:"column:server_id;not null;comment:区服ID" json:"server_id"`                         // 区服ID
	ServerName string `gorm:"column:server_name;not null;comment:区服名称" json:"server_name"`                     // 区服名称
	PlatformID string `gorm:"column:platform_id;not null;comment:平台ID" json:"platform_id"`                     // 平台ID
	CreatedAt  int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳" json:"created_at"` // 创建时间戳
	UpdatedAt  int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间戳" json:"updated_at"` // 更新时间戳
}

// TableName MMonitorGameServer's table name
func (*MMonitorGameServer) TableName() string {
	return TableNameMMonitorGameServer
}
