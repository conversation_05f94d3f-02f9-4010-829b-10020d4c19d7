// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMQuestionWelcomeMessage = "m_question_welcome_message"

// MQuestionWelcomeMessage 欢迎语表
type MQuestionWelcomeMessage struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID    string `gorm:"column:game_id;not null;comment:游戏ID" json:"game_id"`  // 游戏ID
	Content   string `gorm:"column:content;not null;comment:欢迎语内容" json:"content"` // 欢迎语内容
	Weight    int32  `gorm:"column:weight;not null" json:"weight"`
	CreatorID string `gorm:"column:creator_id;not null;comment:创建人ID" json:"creator_id"` // 创建人ID
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MQuestionWelcomeMessage's table name
func (*MQuestionWelcomeMessage) TableName() string {
	return TableNameMQuestionWelcomeMessage
}
