// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameGood = "goods"

// Good mapped from table <goods>
type Good struct {
	ID          int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID      string `gorm:"column:game_id" json:"game_id"`
	GoodsID     string `gorm:"column:goods_id" json:"goods_id"`
	GoodsName   string `gorm:"column:goods_name;not null;comment:商品名称" json:"goods_name"`                            // 商品名称
	Money       int32  `gorm:"column:money;not null;comment:金额 (分)" json:"money"`                                    // 金额 (分)
	Description string `gorm:"column:description;not null;comment:描述" json:"description"`                            // 描述
	PayType     int32  `gorm:"column:pay_type;not null;default:1;comment:支付类型 1:米大师 2:iOS 3:Google" json:"pay_type"` // 支付类型 1:米大师 2:iOS 3:Google
	Remark      string `gorm:"column:remark;not null;comment:备注" json:"remark"`                                      // 备注
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted   bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName Good's table name
func (*Good) TableName() string {
	return TableNameGood
}
