// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMCityCode = "m_city_code"

// MCityCode mapped from table <m_city_code>
type MCityCode struct {
	ID         int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Code       string `gorm:"column:code;not null" json:"code"`
	Name       string `gorm:"column:name;not null" json:"name"`
	ParentCode string `gorm:"column:parent_code;not null;default:0" json:"parent_code"`
	Level      int32  `gorm:"column:level;not null" json:"level"`
	IsLeaf     bool   `gorm:"column:is_leaf;not null" json:"is_leaf"`
	Tier       string `gorm:"column:tier" json:"tier"`
}

// TableName MCityCode's table name
func (*MCityCode) TableName() string {
	return TableNameMCityCode
}
