// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMCompany = "m_company"

// MCompany mapped from table <m_company>
type MCompany struct {
	ID                  int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID              string `gorm:"column:game_id;not null;comment:游戏 id" json:"game_id"`                              // 游戏 id
	WechatPayPrivateKey string `gorm:"column:wechat_pay_private_key;not null;comment:微信私钥" json:"wechat_pay_private_key"` // 微信私钥
	WechatPayMchID      string `gorm:"column:wechat_pay_mch_id;not null;comment:商户 id" json:"wechat_pay_mch_id"`          // 商户 id
	WechatPayMchNum     string `gorm:"column:wechat_pay_mch_num;not null;comment:商户证书序列号" json:"wechat_pay_mch_num"`      // 商户证书序列号
	WechatPayAPIKey     string `gorm:"column:wechat_pay_api_key;not null;comment:api key" json:"wechat_pay_api_key"`      // api key
}

// TableName MCompany's table name
func (*MCompany) TableName() string {
	return TableNameMCompany
}
