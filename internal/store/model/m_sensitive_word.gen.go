// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMSensitiveWord = "m_sensitive_word"

// MSensitiveWord mapped from table <m_sensitive_word>
type MSensitiveWord struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID    string `gorm:"column:game_id;not null" json:"game_id"`
	Level     int32  `gorm:"column:level;not null;comment:等级" json:"level"`      // 等级
	Content   string `gorm:"column:content;not null;comment:敏感词" json:"content"` // 敏感词
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MSensitiveWord's table name
func (*MSensitiveWord) TableName() string {
	return TableNameMSensitiveWord
}
