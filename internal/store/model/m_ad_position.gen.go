// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMAdPosition = "m_ad_position"

// MAdPosition 广告位配置表
type MAdPosition struct {
	ID         int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID     string `gorm:"column:game_id;not null" json:"game_id"`
	PositionID string `gorm:"column:position_id;not null;comment:中台广告位ID" json:"position_id"`                     // 中台广告位ID
	Name       string `gorm:"column:name;not null;comment:广告位名称" json:"name"`                                     // 广告位名称
	AdType     int32  `gorm:"column:ad_type;not null;comment:广告类型(1激励视频/2banner广告/3原生模板广告/4插屏广告)" json:"ad_type"` // 广告类型(1激励视频/2banner广告/3原生模板广告/4插屏广告)
	Status     int32  `gorm:"column:status;not null;default:1;comment:状态 1启用 2禁用" json:"status"`                  // 状态 1启用 2禁用
	CreatedAt  int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间" json:"created_at"`     // 创建时间
	UpdatedAt  int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间" json:"updated_at"`     // 更新时间
	IsDeleted  bool   `gorm:"column:is_deleted;not null;comment:是否删除" json:"is_deleted"`                          // 是否删除
}

// TableName MAdPosition's table name
func (*MAdPosition) TableName() string {
	return TableNameMAdPosition
}
