// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMCustomSwitchParam = "m_custom_switch_param"

// MCustomSwitchParam mapped from table <m_custom_switch_param>
type MCustomSwitchParam struct {
	ID             int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CustomSwitchID int32  `gorm:"column:custom_switch_id;not null" json:"custom_switch_id"`
	ParentID       int32  `gorm:"column:parent_id" json:"parent_id"`
	Description    string `gorm:"column:description;not null;comment:描述" json:"description"`                                                                                           // 描述
	ParamType      int32  `gorm:"column:param_type;not null;default:1;comment:参数类型:  1. 平台 2. 版本 3. 用户昵称 4. IP 5. 区域 6. UniqueId 平台ID 7. 渠道 8. 场景值 9. 自定义参数 10. 其他" json:"param_type"` // 参数类型:  1. 平台 2. 版本 3. 用户昵称 4. IP 5. 区域 6. UniqueId 平台ID 7. 渠道 8. 场景值 9. 自定义参数 10. 其他
	ParamData      string `gorm:"column:param_data;not null" json:"param_data"`
	OtherParamData string `gorm:"column:other_param_data;not null" json:"other_param_data"`
	DefaultReturn  int32  `gorm:"column:default_return;not null" json:"default_return"`
	SortOrder      int32  `gorm:"column:sort_order;not null" json:"sort_order"`
	CreatedAt      int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt      int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted      bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MCustomSwitchParam's table name
func (*MCustomSwitchParam) TableName() string {
	return TableNameMCustomSwitchParam
}
