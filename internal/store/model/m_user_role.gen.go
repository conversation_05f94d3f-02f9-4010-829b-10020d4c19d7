// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMUserRole = "m_user_role"

// MUserRole 用户角色关联表（废弃）
type MUserRole struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID    string `gorm:"column:user_id;not null" json:"user_id"`
	RoleID    int32  `gorm:"column:role_id;not null" json:"role_id"`
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MUserRole's table name
func (*MUserRole) TableName() string {
	return TableNameMUserRole
}
