// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAUserDouyin = "a_user_douyin"

// AUserDouyin mapped from table <a_user_douyin>
type AUserDouyin struct {
	ID                 int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID             string `gorm:"column:user_id;not null" json:"user_id"`
	OpenID             string `gorm:"column:open_id;not null" json:"open_id"`
	UnionID            string `gorm:"column:union_id;not null" json:"union_id"`
	AnonymousOpenID    string `gorm:"column:anonymous_open_id;not null" json:"anonymous_open_id"`
	NickName           string `gorm:"column:nick_name;not null" json:"nick_name"`
	AvatarURL          string `gorm:"column:avatar_url;not null" json:"avatar_url"`
	Gender             int32  `gorm:"column:gender" json:"gender"`
	City               string `gorm:"column:city;not null" json:"city"`
	Province           string `gorm:"column:province;not null" json:"province"`
	Country            string `gorm:"column:country;not null" json:"country"`
	WatermarkAppID     string `gorm:"column:watermark_app_id;not null" json:"watermark_app_id"`
	WatermarkTimestamp int64  `gorm:"column:watermark_timestamp;not null" json:"watermark_timestamp"`
	RealNameAuth       string `gorm:"column:real_name_auth;not null;comment:实名认证情况" json:"real_name_auth"` // 实名认证情况
	SessionKey         string `gorm:"column:session_key;not null" json:"session_key"`
	CreatedAt          int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt          int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted          bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName AUserDouyin's table name
func (*AUserDouyin) TableName() string {
	return TableNameAUserDouyin
}
