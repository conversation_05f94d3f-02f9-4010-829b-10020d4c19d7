// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMWorkorderBusySwitch = "m_workorder_busy_switch"

// MWorkorderBusySwitch 工单繁忙提示开关表
type MWorkorderBusySwitch struct {
	ID         int32 `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	BusySwitch bool  `gorm:"column:busy_switch;not null;comment:繁忙提示开关，0=关闭，1=开启" json:"busy_switch"` // 繁忙提示开关，0=关闭，1=开启
	CreatedAt  int64 `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt  int64 `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted  bool  `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MWorkorderBusySwitch's table name
func (*MWorkorderBusySwitch) TableName() string {
	return TableNameMWorkorderBusySwitch
}
