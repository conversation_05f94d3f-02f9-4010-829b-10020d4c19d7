// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMRolePermission = "m_role_permission"

// MRolePermission 角色权限关联表
type MRolePermission struct {
	ID           int32 `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	RoleID       int32 `gorm:"column:role_id;not null" json:"role_id"`
	PermissionID int32 `gorm:"column:permission_id;not null" json:"permission_id"`
	CreatedAt    int64 `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt    int64 `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MRolePermission's table name
func (*MRolePermission) TableName() string {
	return TableNameMRolePermission
}
