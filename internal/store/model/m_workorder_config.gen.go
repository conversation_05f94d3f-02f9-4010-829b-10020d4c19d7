// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMWorkorderConfig = "m_workorder_config"

// MWorkorderConfig 工单系统游戏展示配置表
type MWorkorderConfig struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                  // 主键ID
	GameID    string `gorm:"column:game_id;not null;comment:关联 m_game.game_id" json:"game_id"`                // 关联 m_game.game_id
	Weight    int32  `gorm:"column:weight;not null;comment:展示权重，值越大越靠前" json:"weight"`                        // 展示权重，值越大越靠前
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳" json:"created_at"` // 创建时间戳
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间戳" json:"updated_at"` // 更新时间戳
	IsDeleted bool   `gorm:"column:is_deleted;not null;comment:逻辑删除，0=未删，1=已删" json:"is_deleted"`             // 逻辑删除，0=未删，1=已删
}

// TableName MWorkorderConfig's table name
func (*MWorkorderConfig) TableName() string {
	return TableNameMWorkorderConfig
}
