// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMMonitorContentConfig = "m_monitor_content_config"

// MMonitorContentConfig 内容监控配置表
type MMonitorContentConfig struct {
	ID          int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"` // 主键ID
	SourceType  string `gorm:"column:source_type;not null;comment:来源类型" json:"source_type"`    // 来源类型
	DisplayName string `gorm:"column:display_name;not null;comment:配置值" json:"display_name"`   // 配置值
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间戳" json:"updated_at"` // 更新时间戳
}

// TableName MMonitorContentConfig's table name
func (*MMonitorContentConfig) TableName() string {
	return TableNameMMonitorContentConfig
}
