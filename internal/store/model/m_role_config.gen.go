// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMRoleConfig = "m_role_config"

// MRoleConfig 角色配置表
type MRoleConfig struct {
	ID          int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Code        string `gorm:"column:code;not null;comment:角色编码" json:"code"`                               // 角色编码
	Name        string `gorm:"column:name;not null;comment:角色名称" json:"name"`                               // 角色名称
	Type        int32  `gorm:"column:type;not null;default:1;comment:角色类型 1:系统角色 2:游戏角色" json:"type"`       // 角色类型 1:系统角色 2:游戏角色
	Description string `gorm:"column:description;not null;comment:角色描述" json:"description"`                 // 角色描述
	IsPreset    bool   `gorm:"column:is_preset;not null;default:1;comment:是否预设角色 1:是 0:否" json:"is_preset"` // 是否预设角色 1:是 0:否
	SortOrder   int32  `gorm:"column:sort_order;not null;comment:排序号" json:"sort_order"`                    // 排序号
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MRoleConfig's table name
func (*MRoleConfig) TableName() string {
	return TableNameMRoleConfig
}
