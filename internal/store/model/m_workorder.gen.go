// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMWorkorder = "m_workorder"

// MWorkorder 工单表
type MWorkorder struct {
	ID                  int32   `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	OrderID             string  `gorm:"column:order_id;not null;comment:工单ID" json:"order_id"`                                              // 工单ID
	GameID              string  `gorm:"column:game_id;not null;comment:游戏ID" json:"game_id"`                                                // 游戏ID
	Source              string  `gorm:"column:source;not null;default:wechat;comment:工单来源: wechat-微信, douyin-抖音" json:"source"`             // 工单来源: wechat-微信, douyin-抖音
	UserID              string  `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`                                                // 用户ID
	OpenID              string  `gorm:"column:open_id;not null;comment:OpenID" json:"open_id"`                                              // OpenID
	MiniprogramOpenID   string  `gorm:"column:miniprogram_open_id;not null;comment:小程序open_id" json:"miniprogram_open_id"`                  // 小程序open_id
	Content             string  `gorm:"column:content;not null;comment:工单内容/问题描述" json:"content"`                                           // 工单内容/问题描述
	Priority            int32   `gorm:"column:priority;not null;default:1;comment:优先级: 1-一般, 2-高, 3-紧急" json:"priority"`                    // 优先级: 1-一般, 2-高, 3-紧急
	Status              int32   `gorm:"column:status;not null;default:1;comment:状态: 1-待接单, 2-受理中, 3-已完结" json:"status"`                     // 状态: 1-待接单, 2-受理中, 3-已完结
	Category            string  `gorm:"column:category;not null;comment:工单分类" json:"category"`                                              // 工单分类
	AcceptUserID        string  `gorm:"column:accept_user_id;not null;comment:受理人ID" json:"accept_user_id"`                                 // 受理人ID
	AcceptUsername      string  `gorm:"column:accept_username;not null;comment:受理人用户名" json:"accept_username"`                              // 受理人用户名
	AcceptTime          int64   `gorm:"column:accept_time;not null;comment:受理时间" json:"accept_time"`                                        // 受理时间
	CompleteUserID      string  `gorm:"column:complete_user_id;not null;comment:完结人ID" json:"complete_user_id"`                             // 完结人ID
	CompleteUsername    string  `gorm:"column:complete_username;not null;comment:完结人用户名" json:"complete_username"`                          // 完结人用户名
	CompleteTime        int64   `gorm:"column:complete_time;not null;comment:完结时间" json:"complete_time"`                                    // 完结时间
	Remark              string  `gorm:"column:remark;comment:备注" json:"remark"`                                                             // 备注
	HasNewReply         bool    `gorm:"column:has_new_reply;not null;comment:是否有新回复: 0-否, 1-是" json:"has_new_reply"`                        // 是否有新回复: 0-否, 1-是
	HasRead             bool    `gorm:"column:has_read;not null;comment:是否已读: 0-否, 1-是" json:"has_read"`                                    // 是否已读: 0-否, 1-是
	LastReplyUserType   int32   `gorm:"column:last_reply_user_type;not null;comment:最后回复用户类型: 0-无, 1-用户, 2-客服" json:"last_reply_user_type"` // 最后回复用户类型: 0-无, 1-用户, 2-客服
	LastReplyTime       int64   `gorm:"column:last_reply_time;not null;comment:最后回复时间" json:"last_reply_time"`                              // 最后回复时间
	RoleID              string  `gorm:"column:role_id;not null;comment:角色ID" json:"role_id"`                                                // 角色ID
	PlayerID            string  `gorm:"column:player_id;not null;comment:玩家ID" json:"player_id"`                                            // 玩家ID
	PlayerName          string  `gorm:"column:player_name;not null;comment:玩家名称" json:"player_name"`                                        // 玩家名称
	PlayerLevel         int32   `gorm:"column:player_level;not null;comment:玩家等级" json:"player_level"`                                      // 玩家等级
	RechargeTotalAmount int32   `gorm:"column:recharge_total_amount;not null;comment:充值总额" json:"recharge_total_amount"`                    // 充值总额
	Zone                string  `gorm:"column:zone;not null;comment:区服" json:"zone"`                                                        // 区服
	CustomData          string  `gorm:"column:custom_data;not null;comment:自定义数据" json:"custom_data"`                                       // 自定义数据
	SceneValue          string  `gorm:"column:scene_value;not null;comment:场景值（抖音）" json:"scene_value"`                                     // 场景值（抖音）
	DeviceBrand         string  `gorm:"column:device_brand;not null;comment:设备品牌" json:"device_brand"`                                      // 设备品牌
	DeviceModel         string  `gorm:"column:device_model;not null;comment:设备型号" json:"device_model"`                                      // 设备型号
	SystemVersion       string  `gorm:"column:system_version;not null;comment:系统版本" json:"system_version"`                                  // 系统版本
	WxVersion           string  `gorm:"column:wx_version;not null;comment:微信版本" json:"wx_version"`                                          // 微信版本
	RechargeAmount      float64 `gorm:"column:recharge_amount;not null;default:0.00;comment:充值金额" json:"recharge_amount"`                   // 充值金额
	Region              string  `gorm:"column:region;not null;comment:地区" json:"region"`                                                    // 地区
	IssueAt             int64   `gorm:"column:issue_at;not null;comment:问题发生时间" json:"issue_at"`                                            // 问题发生时间
	CreatedAt           int64   `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt           int64   `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted           bool    `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MWorkorder's table name
func (*MWorkorder) TableName() string {
	return TableNameMWorkorder
}
