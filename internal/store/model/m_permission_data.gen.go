// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMPermissionDatum = "m_permission_data"

// MPermissionDatum 数据权限表
type MPermissionDatum struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID    string `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`                     // 用户ID
	EntityID  string `gorm:"column:entity_id;not null;comment:实体ID(game_id)" json:"entity_id"`        // 实体ID(game_id)
	RoleID    string `gorm:"column:role_id;comment:角色id数组" json:"role_id"`                            // 角色id数组
	Type      int32  `gorm:"column:type;not null;default:1;comment:权限类型: 1=读取 2=编辑 3=管理" json:"type"` // 权限类型: 1=读取 2=编辑 3=管理
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MPermissionDatum's table name
func (*MPermissionDatum) TableName() string {
	return TableNameMPermissionDatum
}
