// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMWorkorderOperation = "m_workorder_operation"

// MWorkorderOperation 工单操作记录表
type MWorkorderOperation struct {
	ID                int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	OrderID           string `gorm:"column:order_id;not null;comment:工单ID" json:"order_id"`                                                                                    // 工单ID
	OperationType     int32  `gorm:"column:operation_type;not null;default:1;comment:操作类型: 1-创建, 2-接单, 3-完结, 4-重新开单, 5-修改优先级, 6-修改标签, 7-系统自动完结, 8-回复工单" json:"operation_type"` // 操作类型: 1-创建, 2-接单, 3-完结, 4-重新开单, 5-修改优先级, 6-修改标签, 7-系统自动完结, 8-回复工单
	OperationUserID   string `gorm:"column:operation_user_id;not null;comment:操作人ID" json:"operation_user_id"`                                                                 // 操作人ID
	OperationUsername string `gorm:"column:operation_username;not null;comment:操作人用户名" json:"operation_username"`                                                              // 操作人用户名
	OperationDetail   string `gorm:"column:operation_detail;not null;comment:操作详情" json:"operation_detail"`                                                                    // 操作详情
	CreatedAt         int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt         int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted         bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MWorkorderOperation's table name
func (*MWorkorderOperation) TableName() string {
	return TableNameMWorkorderOperation
}
