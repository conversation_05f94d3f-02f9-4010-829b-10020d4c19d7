// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAGamePlayerDouyin = "a_game_player_douyin"

// AGamePlayerDouyin 游戏玩家
type AGamePlayerDouyin struct {
	ID                  int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID              string `gorm:"column:user_id;not null" json:"user_id"`
	OpenID              string `gorm:"column:open_id;not null" json:"open_id"`
	RoleID              string `gorm:"column:role_id;not null" json:"role_id"`
	PlayerID            string `gorm:"column:player_id;not null" json:"player_id"`
	PlayerName          string `gorm:"column:player_name;not null" json:"player_name"`
	PlayerLevel         int32  `gorm:"column:player_level;not null" json:"player_level"`
	RechargeTotalAmount int32  `gorm:"column:recharge_total_amount;not null" json:"recharge_total_amount"`
	CustomData          string `gorm:"column:custom_data;not null" json:"custom_data"`
	Zone                string `gorm:"column:zone;not null" json:"zone"`
	CreatedAt           int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt           int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName AGamePlayerDouyin's table name
func (*AGamePlayerDouyin) TableName() string {
	return TableNameAGamePlayerDouyin
}
