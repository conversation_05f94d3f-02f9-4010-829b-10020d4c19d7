// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAUserShareCode = "a_user_share_codes"

// AUserShareCode 用户分享码表
type AUserShareCode struct {
	ID        int64  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID    string `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`                            // 用户ID
	ShareCode string `gorm:"column:share_code;not null;comment:分享码" json:"share_code"`                       // 分享码
	Status    int32  `gorm:"column:status;not null;default:1;comment:状态：1-有效，0-无效" json:"status"`            // 状态：1-有效，0-无效
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间" json:"updated_at"` // 更新时间
	IsDeleted bool   `gorm:"column:is_deleted" json:"is_deleted"`
}

// TableName AUserShareCode's table name
func (*AUserShareCode) TableName() string {
	return TableNameAUserShareCode
}
