// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMAdPositionPlatform = "m_ad_position_platform"

// MAdPositionPlatform 广告位平台配置表
type MAdPositionPlatform struct {
	ID           int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	PositionID   string `gorm:"column:position_id;not null;comment:关联的中台广告位ID" json:"position_id"`              // 关联的中台广告位ID
	PlatformType string `gorm:"column:platform_type;not null;comment:广告平台(微信小游戏/抖音小游戏等)" json:"platform_type"`  // 广告平台(微信小游戏/抖音小游戏等)
	PlatformCode string `gorm:"column:platform_code;not null;comment:平台广告位ID" json:"platform_code"`             // 平台广告位ID
	Status       int32  `gorm:"column:status;not null;default:1;comment:状态 1启用 2禁用" json:"status"`              // 状态 1启用 2禁用
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt    int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间" json:"updated_at"` // 更新时间
	IsDeleted    bool   `gorm:"column:is_deleted;not null;comment:是否删除" json:"is_deleted"`                      // 是否删除
}

// TableName MAdPositionPlatform's table name
func (*MAdPositionPlatform) TableName() string {
	return TableNameMAdPositionPlatform
}
