// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMContentProcessing = "m_content_processing"

// MContentProcessing 内容处理记录表
type MContentProcessing struct {
	ID              int64  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                  // 主键ID
	ProcessingID    string `gorm:"column:processing_id;not null;comment:处理记录唯一标识" json:"processing_id"`             // 处理记录唯一标识
	ContentID       string `gorm:"column:content_id;not null;comment:关联的内容ID" json:"content_id"`                    // 关联的内容ID
	PlatformID      string `gorm:"column:platform_id;not null;comment:平台ID" json:"platform_id"`                     // 平台ID
	Operations      string `gorm:"column:operations;not null;comment:处理操作列表" json:"operations"`                     // 处理操作列表
	Operator        string `gorm:"column:operator;not null;comment:操作人" json:"operator"`                            // 操作人
	CreatedAt       int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳" json:"created_at"` // 创建时间戳
	ContentSnapshot string `gorm:"column:content_snapshot;not null;comment:内容快照" json:"content_snapshot"`           // 内容快照
}

// TableName MContentProcessing's table name
func (*MContentProcessing) TableName() string {
	return TableNameMContentProcessing
}
