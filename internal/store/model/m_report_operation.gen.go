// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMReportOperation = "m_report_operation"

// MReportOperation 举报处理记录表
type MReportOperation struct {
	ID                 int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                      // 主键ID
	ReportsID          int32  `gorm:"column:reports_id;not null;comment:关联的举报ID" json:"reports_id"`                        // 关联的举报ID
	Action             int32  `gorm:"column:action;not null;comment:处理动作 1:禁言 2:警告 3:封号 4:封账号" json:"action"`              // 处理动作 1:禁言 2:警告 3:封号 4:封账号
	CreatorID          string `gorm:"column:creator_id;not null;comment:处理人ID" json:"creator_id"`                          // 处理人ID
	ReportedPlatformID string `gorm:"column:reported_platform_id;not null;comment:被处理人平台ID" json:"reported_platform_id"`   // 被处理人平台ID
	ReportedPlayerID   string `gorm:"column:reported_player_id;not null;comment:被处理人玩家ID" json:"reported_player_id"`       // 被处理人玩家ID
	ActionParam        string `gorm:"column:action_param;comment:处理参数(JSON格式)" json:"action_param"`                        // 处理参数(JSON格式)
	HandleTimeAt       int64  `gorm:"column:handle_time_at;not null;comment:处理时间" json:"handle_time_at"`                   // 处理时间
	CallbackStatus     int32  `gorm:"column:callback_status;not null;comment:0 初始状态 1 回调成功 2 回调失败" json:"callback_status"` // 0 初始状态 1 回调成功 2 回调失败
	RecordDescription  string `gorm:"column:record_description;not null;comment:处理描述" json:"record_description"`           // 处理描述
	CreatedAt          int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间" json:"created_at"`      // 创建时间
	UpdatedAt          int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间" json:"updated_at"`      // 更新时间
}

// TableName MReportOperation's table name
func (*MReportOperation) TableName() string {
	return TableNameMReportOperation
}
