// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMReportActionConfig = "m_report_action_config"

// MReportActionConfig 举报处理动作配置表
type MReportActionConfig struct {
	ID               int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                                                                                            // 主键ID
	GameID           string `gorm:"column:game_id;comment:游戏ID, 为空表示全局配置" json:"game_id"`                                                                                                      // 游戏ID, 为空表示全局配置
	ActionValue      int32  `gorm:"column:action_value;not null;comment:处理动作的数值" json:"action_value"`                                                                                          // 处理动作的数值
	Description      string `gorm:"column:description;comment:描述" json:"description"`                                                                                                          // 描述
	IsPreset         bool   `gorm:"column:is_preset;not null;comment:是否预置, 0:否, 1:是, 预置不可修改" json:"is_preset"`                                                                                 // 是否预置, 0:否, 1:是, 预置不可修改
	ParamsDefinition string `gorm:"column:params_definition;comment:参数定义列表 [{"param_key":"duration", "param_desc":"封禁时长", "param_type":"text", "is_required":true}]" json:"params_definition"` // 参数定义列表 [{"param_key":"duration", "param_desc":"封禁时长", "param_type":"text", "is_required":true}]
	CreatedAt        int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳" json:"created_at"`                                                                           // 创建时间戳
	UpdatedAt        int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间戳" json:"updated_at"`                                                                           // 更新时间戳
	IsDeleted        bool   `gorm:"column:is_deleted;not null;comment:软删除标记 0:未删除 1:已删除" json:"is_deleted"`                                                                                    // 软删除标记 0:未删除 1:已删除
}

// TableName MReportActionConfig's table name
func (*MReportActionConfig) TableName() string {
	return TableNameMReportActionConfig
}
