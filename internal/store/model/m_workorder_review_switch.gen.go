// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMWorkorderReviewSwitch = "m_workorder_review_switch"

// MWorkorderReviewSwitch 工单审核开关表
type MWorkorderReviewSwitch struct {
	ID           int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                  // 主键ID
	Version      string `gorm:"column:version;not null;comment:版本号" json:"version"`                              // 版本号
	ReviewSwitch bool   `gorm:"column:review_switch;not null;comment:审核开关：0-关闭，1-开启" json:"review_switch"`       // 审核开关：0-关闭，1-开启
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳" json:"created_at"` // 创建时间戳
	UpdatedAt    int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间戳" json:"updated_at"` // 更新时间戳
	IsDeleted    bool   `gorm:"column:is_deleted;not null;comment:逻辑删除，0=未删，1=已删" json:"is_deleted"`             // 逻辑删除，0=未删，1=已删
}

// TableName MWorkorderReviewSwitch's table name
func (*MWorkorderReviewSwitch) TableName() string {
	return TableNameMWorkorderReviewSwitch
}
