// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAConfigQq = "a_config_qq"

// AConfigQq mapped from table <a_config_qq>
type AConfigQq struct {
	ID          int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID      string `gorm:"column:game_id;not null;comment:游戏id" json:"game_id"`                       // 游戏id
	AppID       string `gorm:"column:app_id;not null;comment:QQ应用ID" json:"app_id"`                       // QQ应用ID
	AppSecret   string `gorm:"column:app_secret;not null;comment:QQ应用密钥" json:"app_secret"`               // QQ应用密钥
	AccessToken string `gorm:"column:access_token;not null;comment:QQ平台access token" json:"access_token"` // QQ平台access token
	ExpiresIn   int32  `gorm:"column:expires_in;not null;comment:access token过期时间" json:"expires_in"`     // access token过期时间
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted   bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName AConfigQq's table name
func (*AConfigQq) TableName() string {
	return TableNameAConfigQq
}
