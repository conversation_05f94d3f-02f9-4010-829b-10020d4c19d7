// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMUserPermission = "m_user_permission"

// MUserPermission 用户角色表
type MUserPermission struct {
	ID           int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID       string `gorm:"column:user_id;not null" json:"user_id"`
	PermissionID int32  `gorm:"column:permission_id;not null" json:"permission_id"`
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt    int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MUserPermission's table name
func (*MUserPermission) TableName() string {
	return TableNameMUserPermission
}
