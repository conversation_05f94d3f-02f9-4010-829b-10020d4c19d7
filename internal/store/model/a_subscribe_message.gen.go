// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameASubscribeMessage = "a_subscribe_message"

// ASubscribeMessage mapped from table <a_subscribe_message>
type ASubscribeMessage struct {
	ID           int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	TaskID       string `gorm:"column:task_id;not null;comment:来自asynq的Member" json:"task_id"` // 来自asynq的Member
	TaskName     string `gorm:"column:task_name;not null;comment:名称" json:"task_name"`         // 名称
	GameID       string `gorm:"column:game_id;not null" json:"game_id"`
	UserID       string `gorm:"column:user_id;not null" json:"user_id"`
	Status       int32  `gorm:"column:status;not null;comment:订阅状态 0 等待中 1 成功 2 失败 3 延迟发送 4 延迟删除" json:"status"`                 // 订阅状态 0 等待中 1 成功 2 失败 3 延迟发送 4 延迟删除
	PlatformType string `gorm:"column:platform_type;not null;comment:游戏类型 目前仅小游戏 minigame、douyin_minigame" json:"platform_type"` // 游戏类型 目前仅小游戏 minigame、douyin_minigame
	PushType     string `gorm:"column:push_type;not null;comment:推送类型：push(直接推)， delayed_push(延迟推送)" json:"push_type"`           // 推送类型：push(直接推)， delayed_push(延迟推送)
	Delay        int32  `gorm:"column:delay;not null;comment:延迟多少秒, *type=delayed_push 时，必传, -1 表示删除延迟任务" json:"delay"`          // 延迟多少秒, *type=delayed_push 时，必传, -1 表示删除延迟任务
	TemplateID   string `gorm:"column:template_id;not null;comment:订阅模板 id" json:"template_id"`                                  // 订阅模板 id
	ErrorMsg     string `gorm:"column:error_msg;not null" json:"error_msg"`
	Page         string `gorm:"column:page;not null;comment:进入游戏页面地址" json:"page"` // 进入游戏页面地址
	Data         string `gorm:"column:data;not null" json:"data"`
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt    int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted    bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName ASubscribeMessage's table name
func (*ASubscribeMessage) TableName() string {
	return TableNameASubscribeMessage
}
