// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMWorkorderAttachment = "m_workorder_attachment"

// MWorkorderAttachment 工单附件表
type MWorkorderAttachment struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	OrderID   string `gorm:"column:order_id;not null;comment:工单ID" json:"order_id"`                         // 工单ID
	FileURL   string `gorm:"column:file_url;not null;comment:文件URL" json:"file_url"`                        // 文件URL
	FileType  int32  `gorm:"column:file_type;not null;default:1;comment:文件类型: 1-图片, 2-视频" json:"file_type"` // 文件类型: 1-图片, 2-视频
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MWorkorderAttachment's table name
func (*MWorkorderAttachment) TableName() string {
	return TableNameMWorkorderAttachment
}
