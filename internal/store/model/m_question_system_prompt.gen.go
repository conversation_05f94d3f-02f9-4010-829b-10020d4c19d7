// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMQuestionSystemPrompt = "m_question_system_prompt"

// MQuestionSystemPrompt mapped from table <m_question_system_prompt>
type MQuestionSystemPrompt struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Content   string `gorm:"column:content;not null;comment:提示词内容" json:"content"`       // 提示词内容
	CreatorID string `gorm:"column:creator_id;not null;comment:创建人id" json:"creator_id"` // 创建人id
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MQuestionSystemPrompt's table name
func (*MQuestionSystemPrompt) TableName() string {
	return TableNameMQuestionSystemPrompt
}
