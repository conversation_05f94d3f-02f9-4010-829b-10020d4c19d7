// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMWorkorderReply = "m_workorder_reply"

// MWorkorderReply 工单回复表
type MWorkorderReply struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	OrderID   string `gorm:"column:order_id;not null;comment:工单ID" json:"order_id"`                         // 工单ID
	UserID    string `gorm:"column:user_id;not null;comment:回复人ID" json:"user_id"`                          // 回复人ID
	Username  string `gorm:"column:username;not null;comment:回复人用户名" json:"username"`                       // 回复人用户名
	Content   string `gorm:"column:content;not null;comment:回复内容" json:"content"`                           // 回复内容
	UserType  int32  `gorm:"column:user_type;not null;default:1;comment:用户类型: 1-用户, 2-客服" json:"user_type"` // 用户类型: 1-用户, 2-客服
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MWorkorderReply's table name
func (*MWorkorderReply) TableName() string {
	return TableNameMWorkorderReply
}
