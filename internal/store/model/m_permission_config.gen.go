// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMPermissionConfig = "m_permission_config"

// MPermissionConfig mapped from table <m_permission_config>
type MPermissionConfig struct {
	ID       int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Base     string `gorm:"column:base;not null" json:"base"`
	Code     string `gorm:"column:code;not null;comment:权限标识" json:"code"`                           // 权限标识
	Name     string `gorm:"column:name;not null;comment:权限说明" json:"name"`                           // 权限说明
	Type     int32  `gorm:"column:type;not null;default:1;comment:权限类型 1 页面权限 2 操作权限" json:"type"`   // 权限类型 1 页面权限 2 操作权限
	IsGlobal bool   `gorm:"column:is_global;not null;comment:是否是全局字段，此权限，可不包含实体ID" json:"is_global"` // 是否是全局字段，此权限，可不包含实体ID
}

// TableName MPermissionConfig's table name
func (*MPermissionConfig) TableName() string {
	return TableNameMPermissionConfig
}
