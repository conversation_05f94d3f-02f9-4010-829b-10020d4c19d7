// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameH5AdminConfig = "h5_admin_config"

// H5AdminConfig H5打包管理后台配置
type H5AdminConfig struct {
	ID            int32 `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`      // 主键ID
	IsSpecifyTime bool  `gorm:"column:is_specify_time;not null;comment:指定时间" json:"is_specify_time"` // 指定时间
	TimestampDiff int64 `gorm:"column:timestamp_diff;not null" json:"timestamp_diff"`
	CreatedAt     int64 `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt     int64 `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName H5AdminConfig's table name
func (*H5AdminConfig) TableName() string {
	return TableNameH5AdminConfig
}
