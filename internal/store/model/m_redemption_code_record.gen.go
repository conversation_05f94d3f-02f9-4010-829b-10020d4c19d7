// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMRedemptionCodeRecord = "m_redemption_code_record"

// MRedemptionCodeRecord mapped from table <m_redemption_code_record>
type MRedemptionCodeRecord struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID    string `gorm:"column:game_id;not null" json:"game_id"`
	UserID    string `gorm:"column:user_id;not null" json:"user_id"`
	CodeType  int32  `gorm:"column:code_type;default:2;comment:1 普通 2 通兑  目前只记录通兑码" json:"code_type"`              // 1 普通 2 通兑  目前只记录通兑码
	Code      string `gorm:"column:code;comment:兑换码" json:"code"`                                                  // 兑换码
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间（兑换时间）" json:"created_at"` // 创建时间（兑换时间）
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MRedemptionCodeRecord's table name
func (*MRedemptionCodeRecord) TableName() string {
	return TableNameMRedemptionCodeRecord
}
