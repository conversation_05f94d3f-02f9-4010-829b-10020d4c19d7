// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMWorkorderFeedback = "m_workorder_feedback"

// MWorkorderFeedback 工单反馈表
type MWorkorderFeedback struct {
	ID           int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                  // 主键ID
	GameID       string `gorm:"column:game_id;not null;comment:游戏ID" json:"game_id"`                             // 游戏ID
	UserID       string `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`                             // 用户ID
	OpenID       string `gorm:"column:open_id;not null;comment:小程序OpenID" json:"open_id"`                        // 小程序OpenID
	OrderID      string `gorm:"column:order_id;not null;comment:工单ID" json:"order_id"`                           // 工单ID
	ReplyID      int32  `gorm:"column:reply_id;not null;comment:回复ID" json:"reply_id"`                           // 回复ID
	Question     string `gorm:"column:question;not null;comment:用户提问" json:"question"`                           // 用户提问
	Answer       string `gorm:"column:answer;not null;comment:机器人回答" json:"answer"`                              // 机器人回答
	FeedbackType int32  `gorm:"column:feedback_type;not null;comment:反馈类型：1-有用，2-无用" json:"feedback_type"`       // 反馈类型：1-有用，2-无用
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳" json:"created_at"` // 创建时间戳
	UpdatedAt    int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间戳" json:"updated_at"` // 更新时间戳
	IsDeleted    bool   `gorm:"column:is_deleted;not null;comment:逻辑删除，0=未删，1=已删" json:"is_deleted"`             // 逻辑删除，0=未删，1=已删
}

// TableName MWorkorderFeedback's table name
func (*MWorkorderFeedback) TableName() string {
	return TableNameMWorkorderFeedback
}
