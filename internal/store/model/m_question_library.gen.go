// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMQuestionLibrary = "m_question_library"

// MQuestionLibrary 问题库表
type MQuestionLibrary struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                  // 主键ID
	GameID    string `gorm:"column:game_id;not null;comment:游戏ID" json:"game_id"`                             // 游戏ID
	Question  string `gorm:"column:question;not null;comment:问题内容" json:"question"`                           // 问题内容
	Answer    string `gorm:"column:answer;not null;comment:回答内容" json:"answer"`                               // 回答内容
	CreatorID string `gorm:"column:creator_id;not null;comment:创建人ID" json:"creator_id"`                      // 创建人ID
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳" json:"created_at"` // 创建时间戳
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间戳" json:"updated_at"` // 更新时间戳
	IsDeleted bool   `gorm:"column:is_deleted;not null;comment:是否删除：0-否，1-是" json:"is_deleted"`               // 是否删除：0-否，1-是
}

// TableName MQuestionLibrary's table name
func (*MQuestionLibrary) TableName() string {
	return TableNameMQuestionLibrary
}
