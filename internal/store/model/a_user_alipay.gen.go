// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAUserAlipay = "a_user_alipay"

// AUserAlipay 支付宝用户信息表
type AUserAlipay struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID    string `gorm:"column:user_id;not null" json:"user_id"`
	OpenID    string `gorm:"column:open_id;not null" json:"open_id"`
	NickName  string `gorm:"column:nick_name;not null" json:"nick_name"`
	AvatarURL string `gorm:"column:avatar_url;not null" json:"avatar_url"`
	Gender    int32  `gorm:"column:gender" json:"gender"`
	City      string `gorm:"column:city;not null" json:"city"`
	Province  string `gorm:"column:province;not null" json:"province"`
	Country   string `gorm:"column:country;not null" json:"country"`
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName AUserAlipay's table name
func (*AUserAlipay) TableName() string {
	return TableNameAUserAlipay
}
