// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMMonitorGameContent = "m_monitor_game_content"

// MMonitorGameContent 游戏内容监控表
type MMonitorGameContent struct {
	ID               int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                  // 主键ID
	GameID           string `gorm:"column:game_id;not null;comment:游戏ID" json:"game_id"`                             // 游戏ID
	ContentID        string `gorm:"column:content_id;not null;comment:内容唯一标识" json:"content_id"`                     // 内容唯一标识
	UserID           string `gorm:"column:user_id;not null;comment:平台用户ID" json:"user_id"`                           // 平台用户ID
	SessionFrom      string `gorm:"column:session_from;comment:透传参数" json:"session_from"`                            // 透传参数
	SourceType       string `gorm:"column:source_type;not null;comment:文本来源类型" json:"source_type"`                   // 文本来源类型
	ServerID         string `gorm:"column:server_id;not null;comment:区服ID" json:"server_id"`                         // 区服ID
	ServerName       string `gorm:"column:server_name;not null;comment:区服名称" json:"server_name"`                     // 区服名称
	RoleID           string `gorm:"column:role_id;not null;comment:角色ID" json:"role_id"`                             // 角色ID
	RoleName         string `gorm:"column:role_name;not null;comment:角色名称" json:"role_name"`                         // 角色名称
	RoleLevel        int32  `gorm:"column:role_level;not null;comment:角色等级" json:"role_level"`                       // 角色等级
	AllianceID       string `gorm:"column:alliance_id;not null;comment:公会ID" json:"alliance_id"`                     // 公会ID
	AllianceName     string `gorm:"column:alliance_name;not null;comment:公会名称" json:"alliance_name"`                 // 公会名称
	IsAllianceLeader int32  `gorm:"column:is_alliance_leader;not null;comment:是否公会长" json:"is_alliance_leader"`      // 是否公会长
	Status           int32  `gorm:"column:status;not null;comment:0: 待处理,1: 已处理" json:"status"`                      // 0: 待处理,1: 已处理
	Content          string `gorm:"column:content;not null;comment:内容文本" json:"content"`                             // 内容文本
	ExpireAt         int64  `gorm:"column:expire_at;not null;comment:过期时间戳" json:"expire_at"`                        // 过期时间戳
	CreatorID        string `gorm:"column:creator_id;not null;comment:创建人id" json:"creator_id"`                      // 创建人id
	CreatedAt        int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳" json:"created_at"` // 创建时间戳
	UpdatedAt        int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间戳" json:"updated_at"` // 更新时间戳
	IsDeleted        bool   `gorm:"column:is_deleted;not null;comment:是否删除" json:"is_deleted"`                       // 是否删除
}

// TableName MMonitorGameContent's table name
func (*MMonitorGameContent) TableName() string {
	return TableNameMMonitorGameContent
}
