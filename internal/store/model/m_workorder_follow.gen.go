// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMWorkorderFollow = "m_workorder_follow"

// MWorkorderFollow 工单关注表
type MWorkorderFollow struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	OrderID   string `gorm:"column:order_id;not null;comment:工单ID" json:"order_id"` // 工单ID
	UserID    string `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`   // 用户ID
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MWorkorderFollow's table name
func (*MWorkorderFollow) TableName() string {
	return TableNameMWorkorderFollow
}
