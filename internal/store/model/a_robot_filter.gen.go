// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameARobotFilter = "a_robot_filter"

// ARobotFilter 七鱼机器人过滤表，填写内容即为某项目的过滤文案
type ARobotFilter struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID    string `gorm:"column:game_id;not null" json:"game_id"`
	Content   string `gorm:"column:content;not null" json:"content"`
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
}

// TableName ARobotFilter's table name
func (*ARobotFilter) TableName() string {
	return TableNameARobotFilter
}
