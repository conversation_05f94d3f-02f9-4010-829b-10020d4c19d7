// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMRedemptionCodeEntity = "m_redemption_code_entity"

// MRedemptionCodeEntity mapped from table <m_redemption_code_entity>
type MRedemptionCodeEntity struct {
	ID          int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CodeID      int32  `gorm:"column:code_id;not null" json:"code_id"`
	Code        string `gorm:"column:code;not null" json:"code"`
	Status      int32  `gorm:"column:status;comment:使用状态 1 未使用 2 已使用 3 已下发，但是未使用" json:"status"`      // 使用状态 1 未使用 2 已使用 3 已下发，但是未使用
	SpendUserID string `gorm:"column:spend_user_id;not null;comment:使用的user id" json:"spend_user_id"` // 使用的user id
	Frequency   int32  `gorm:"column:frequency;default:1;comment:兑换次数" json:"frequency"`              // 兑换次数
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MRedemptionCodeEntity's table name
func (*MRedemptionCodeEntity) TableName() string {
	return TableNameMRedemptionCodeEntity
}
