// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAConfigAlipay = "a_config_alipay"

// AConfigAlipay 支付宝支付配置表，参考 m_game 规范
type AConfigAlipay struct {
	ID          int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"` // 主键ID
	GameID      string `gorm:"column:game_id;not null" json:"game_id"`
	AppID       string `gorm:"column:app_id;not null;comment:支付宝应用ID" json:"app_id"`                                   // 支付宝应用ID
	PrivateKey  string `gorm:"column:private_key;not null;comment:支付宝私钥" json:"private_key"`                           // 支付宝私钥
	PublicKey   string `gorm:"column:public_key;not null;comment:支付宝公钥" json:"public_key"`                             // 支付宝公钥
	Description string `gorm:"column:description;not null;comment:配置描述" json:"description"`                            // 配置描述
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间（Unix毫秒）" json:"created_at"` // 创建时间（Unix毫秒）
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间（Unix毫秒）" json:"updated_at"` // 更新时间（Unix毫秒）
	IsDeleted   bool   `gorm:"column:is_deleted;not null;comment:删除标记：0-未删除，1-已删除" json:"is_deleted"`                  // 删除标记：0-未删除，1-已删除
}

// TableName AConfigAlipay's table name
func (*AConfigAlipay) TableName() string {
	return TableNameAConfigAlipay
}
