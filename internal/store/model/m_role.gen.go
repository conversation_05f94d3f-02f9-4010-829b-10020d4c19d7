// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMRole = "m_role"

// MRole 角色表
type MRole struct {
	ID          int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Code        string `gorm:"column:code;not null" json:"code"`
	Name        string `gorm:"column:name;not null;comment:角色名称" json:"name"`               // 角色名称
	Description string `gorm:"column:description;not null;comment:角色描述" json:"description"` // 角色描述
	CreatorID   string `gorm:"column:creator_id;not null;comment:创建人ID" json:"creator_id"`  // 创建人ID
	SystemID    int32  `gorm:"column:system_id" json:"system_id"`
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MRole's table name
func (*MRole) TableName() string {
	return TableNameMRole
}
