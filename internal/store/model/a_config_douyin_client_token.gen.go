// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAConfigDouyinClientToken = "a_config_douyin_client_token"

// AConfigDouyinClientToken 抖音稳定客户端令牌配置表
type AConfigDouyinClientToken struct {
	ID                int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:唯一标识符" json:"id"`   // 唯一标识符
	AppID             string `gorm:"column:app_id;not null;comment:抖音开放平台应用ID" json:"app_id"`           // 抖音开放平台应用ID
	AppSecret         string `gorm:"column:app_secret;not null;comment:抖音开放平台应用密钥" json:"app_secret"`   // 抖音开放平台应用密钥
	AccessToken       string `gorm:"column:access_token;not null;comment:稳定客户端令牌" json:"access_token"`  // 稳定客户端令牌
	ExpiresIn         int32  `gorm:"column:expires_in;not null;comment:Token过期时间(秒)" json:"expires_in"` // Token过期时间(秒)
	Ticket            string `gorm:"column:ticket;not null;comment:票据" json:"ticket"`                   // 票据
	TicketExpiresIn   int32  `gorm:"column:ticket_expires_in;not null" json:"ticket_expires_in"`
	TokenRefreshedAt  int64  `gorm:"column:token_refreshed_at;not null;comment:Token刷新时间戳(Unix时间戳毫秒)" json:"token_refreshed_at"`      // Token刷新时间戳(Unix时间戳毫秒)
	TicketRefreshedAt int64  `gorm:"column:ticket_refreshed_at;not null;comment:JSTicket刷新时间戳(Unix时间戳毫秒)" json:"ticket_refreshed_at"` // JSTicket刷新时间戳(Unix时间戳毫秒)
	CreatedAt         int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳(Unix时间戳毫秒)" json:"created_at"`      // 创建时间戳(Unix时间戳毫秒)
	UpdatedAt         int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:最后更新时间戳(Unix时间戳毫秒)" json:"updated_at"`    // 最后更新时间戳(Unix时间戳毫秒)
	IsDeleted         bool   `gorm:"column:is_deleted;not null;comment:删除标记: 0-未删除, 1-已删除" json:"is_deleted"`                         // 删除标记: 0-未删除, 1-已删除
}

// TableName AConfigDouyinClientToken's table name
func (*AConfigDouyinClientToken) TableName() string {
	return TableNameAConfigDouyinClientToken
}
