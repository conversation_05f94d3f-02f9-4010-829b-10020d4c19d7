// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMCustomSwitch = "m_custom_switch"

// MCustomSwitch mapped from table <m_custom_switch>
type MCustomSwitch struct {
	ID                  int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID              string `gorm:"column:game_id" json:"game_id"`
	Title               string `gorm:"column:title;not null" json:"title"`
	SwitchID            string `gorm:"column:switch_id;not null" json:"switch_id"`
	EffectiveTimeStart  int64  `gorm:"column:effective_time_start;not null" json:"effective_time_start"`
	EffectiveTimeEnd    int64  `gorm:"column:effective_time_end;not null" json:"effective_time_end"`
	ApplicablePlatforms string `gorm:"column:applicable_platforms;comment:适用平台 1 微信小游戏 2 抖音小游戏" json:"applicable_platforms"` // 适用平台 1 微信小游戏 2 抖音小游戏
	Versions            string `gorm:"column:versions;not null" json:"versions"`
	DefaultReturn       int32  `gorm:"column:default_return;not null;comment:默认返回类型 0 false 1 true" json:"default_return"` // 默认返回类型 0 false 1 true
	Status              int32  `gorm:"column:status;not null;default:2;comment:开关状态 1开启 2关闭" json:"status"`                // 开关状态 1开启 2关闭
	CreatorID           string `gorm:"column:creator_id;not null" json:"creator_id"`
	CreatedAt           int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt           int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted           bool   `gorm:"column:is_deleted" json:"is_deleted"`
}

// TableName MCustomSwitch's table name
func (*MCustomSwitch) TableName() string {
	return TableNameMCustomSwitch
}
