// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAConfigMiniprogram = "a_config_miniprogram"

// AConfigMiniprogram mapped from table <a_config_miniprogram>
type AConfigMiniprogram struct {
	ID               int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	AppID            string `gorm:"column:app_id;not null" json:"app_id"`
	AppSecret        string `gorm:"column:app_secret;not null" json:"app_secret"`
	AccessToken      string `gorm:"column:access_token;not null" json:"access_token"`
	ExpiresIn        int32  `gorm:"column:expires_in;not null;comment:有效期（秒）" json:"expires_in"` // 有效期（秒）
	IsUpdatedSys     int32  `gorm:"column:is_updated_sys;not null" json:"is_updated_sys"`
	URLLink          string `gorm:"column:url_link;not null" json:"url_link"`
	URLLinkCreatedAt int64  `gorm:"column:url_link_created_at;not null" json:"url_link_created_at"`
	CreatedAt        int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt        int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName AConfigMiniprogram's table name
func (*AConfigMiniprogram) TableName() string {
	return TableNameAConfigMiniprogram
}
