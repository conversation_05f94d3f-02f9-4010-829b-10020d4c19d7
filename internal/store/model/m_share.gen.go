// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMShare = "m_share"

// MShare mapped from table <m_share>
type MShare struct {
	ID           int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UUID         string `gorm:"column:uuid;not null" json:"uuid"`
	RoadblockID  int32  `gorm:"column:roadblock_id;not null;comment:标题" json:"roadblock_id"`                               // 标题
	Name         string `gorm:"column:name;not null;comment:方案名称" json:"name"`                                             // 方案名称
	Title        string `gorm:"column:title;not null;comment:方案标题" json:"title"`                                           // 方案标题
	Status       int32  `gorm:"column:status;not null;comment:方案状态  1 开 2 关" json:"status"`                                // 方案状态  1 开 2 关
	ShareScenes  int32  `gorm:"column:share_scenes;not null;comment:分享场景 1 小游戏分享到会话 2 小游戏分享到朋友圈 3 抖音" json:"share_scenes"` // 分享场景 1 小游戏分享到会话 2 小游戏分享到朋友圈 3 抖音
	MsgType      int32  `gorm:"column:msg_type;not null;comment:分享到会话-消息类型 1 普通消息 2 动态消息 3 海报消息" json:"msg_type"`          // 分享到会话-消息类型 1 普通消息 2 动态消息 3 海报消息
	SharePic     string `gorm:"column:share_pic;not null;comment:分享到会话-方案图片" json:"share_pic"`                             // 分享到会话-方案图片
	ThumbnailURL string `gorm:"column:thumbnail_url;not null;comment:分享到朋友圈-缩略图url" json:"thumbnail_url"`                  // 分享到朋友圈-缩略图url
	ThumbnailID  string `gorm:"column:thumbnail_id;not null;comment:分享到朋友圈-缩略图id" json:"thumbnail_id"`                     // 分享到朋友圈-缩略图id
	PreviewURL   string `gorm:"column:preview_url;not null;comment:分享到朋友圈-预览图url" json:"preview_url"`                      // 分享到朋友圈-预览图url
	PreviewID    string `gorm:"column:preview_id;not null;comment:分享到朋友圈-预览图id" json:"preview_id"`                         // 分享到朋友圈-预览图id
	CreatorID    string `gorm:"column:creator_id;not null" json:"creator_id"`
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt    int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted    bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MShare's table name
func (*MShare) TableName() string {
	return TableNameMShare
}
