// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAConfigDouyin = "a_config_douyin"

// AConfigDouyin mapped from table <a_config_douyin>
type AConfigDouyin struct {
	ID                   int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID               string `gorm:"column:game_id;not null" json:"game_id"`
	AppID                string `gorm:"column:app_id" json:"app_id"`
	AppSecret            string `gorm:"column:app_secret;not null" json:"app_secret"`
	AccessToken          string `gorm:"column:access_token;not null" json:"access_token"`
	CustomerServiceToken string `gorm:"column:customer_service_token;not null;comment:客服回调验证Token" json:"customer_service_token"` // 客服回调验证Token
	ExpiresIn            int32  `gorm:"column:expires_in;not null" json:"expires_in"`
	PayToken             string `gorm:"column:pay_token;not null;comment:支付Token" json:"pay_token"` // 支付Token
	PaySecret            string `gorm:"column:pay_secret;not null" json:"pay_secret"`
	RtcAppID             string `gorm:"column:rtc_app_id;not null" json:"rtc_app_id"`
	RtcAppKey            string `gorm:"column:rtc_app_key;not null" json:"rtc_app_key"`
	TokenRefreshedAt     int64  `gorm:"column:token_refreshed_at;not null" json:"token_refreshed_at"`
	CreatedAt            int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt            int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted            bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName AConfigDouyin's table name
func (*AConfigDouyin) TableName() string {
	return TableNameAConfigDouyin
}
