// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMCaptchaConfig = "m_captcha_config"

// MCaptchaConfig 验证码配置表
type MCaptchaConfig struct {
	ID           int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID       string `gorm:"column:game_id;not null;comment:关联游戏ID" json:"game_id"`                        // 关联游戏ID
	Provider     int32  `gorm:"column:provider;not null;comment:验证码服务商 1=腾讯云 2=网易易盾" json:"provider"`         // 验证码服务商 1=腾讯云 2=网易易盾
	CaptchaAppID int32  `gorm:"column:captcha_app_id;not null;comment:腾讯云CaptchaAppId" json:"captcha_app_id"` // 腾讯云CaptchaAppId
	AppSecretKey string `gorm:"column:app_secret_key;not null;comment:腾讯云AppSecretKey" json:"app_secret_key"` // 腾讯云AppSecretKey
	CaptchaID    string `gorm:"column:captcha_id;not null;comment:网易易盾captchaId" json:"captcha_id"`           // 网易易盾captchaId
	SecretID     string `gorm:"column:secret_id;not null;comment:网易易盾secretId" json:"secret_id"`              // 网易易盾secretId
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt    int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted    bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MCaptchaConfig's table name
func (*MCaptchaConfig) TableName() string {
	return TableNameMCaptchaConfig
}
