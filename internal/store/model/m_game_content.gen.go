// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMGameContent = "m_game_content"

// MGameContent 游戏内容监控表
type MGameContent struct {
	ID               int64  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                  // 主键ID
	ContentID        string `gorm:"column:content_id;not null;comment:内容唯一标识" json:"content_id"`                     // 内容唯一标识
	PlatformID       string `gorm:"column:platform_id;not null;comment:平台ID" json:"platform_id"`                     // 平台ID
	SourceType       string `gorm:"column:source_type;not null;comment:文本来源类型" json:"source_type"`                   // 文本来源类型
	ServerID         string `gorm:"column:server_id;not null;comment:区服ID" json:"server_id"`                         // 区服ID
	ServerName       string `gorm:"column:server_name;not null;comment:区服名称" json:"server_name"`                     // 区服名称
	RoleID           string `gorm:"column:role_id;not null;comment:角色ID" json:"role_id"`                             // 角色ID
	RoleName         string `gorm:"column:role_name;not null;comment:角色名称" json:"role_name"`                         // 角色名称
	RoleLevel        int32  `gorm:"column:role_level;not null;comment:角色等级" json:"role_level"`                       // 角色等级
	AllianceID       string `gorm:"column:alliance_id;comment:公会ID" json:"alliance_id"`                              // 公会ID
	AllianceName     string `gorm:"column:alliance_name;comment:公会名称" json:"alliance_name"`                          // 公会名称
	IsAllianceLeader bool   `gorm:"column:is_alliance_leader;comment:是否公会长" json:"is_alliance_leader"`               // 是否公会长
	Content          string `gorm:"column:content;not null;comment:内容文本" json:"content"`                             // 内容文本
	CreatedAt        int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳" json:"created_at"` // 创建时间戳
	ExpireAt         int64  `gorm:"column:expire_at;not null;comment:过期时间戳" json:"expire_at"`                        // 过期时间戳
}

// TableName MGameContent's table name
func (*MGameContent) TableName() string {
	return TableNameMGameContent
}
