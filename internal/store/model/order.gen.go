// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameOrder = "order"

// Order mapped from table <order>
type Order struct {
	ID                 int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID             string `gorm:"column:user_id;not null" json:"user_id"`
	OrderID            string `gorm:"column:order_id;not null;comment:订单 id" json:"order_id"` // 订单 id
	GameID             string `gorm:"column:game_id;not null" json:"game_id"`
	GoodsID            string `gorm:"column:goods_id;not null;comment:商品 ID" json:"goods_id"`                                                      // 商品 ID
	Money              int32  `gorm:"column:money;not null;comment:金额 (分)" json:"money"`                                                           // 金额 (分)
	CurrencyPrice      int32  `gorm:"column:currency_price;not null;comment:实际支付金额" json:"currency_price"`                                         // 实际支付金额
	PlatformType       int32  `gorm:"column:platform_type;not null;default:1;comment:平台类型 1 iOS 2 安卓" json:"platform_type"`                        // 平台类型 1 iOS 2 安卓
	Status             int32  `gorm:"column:status;not null;default:1;comment:支付状态 1.创建订单 2.待支付 3.支付成功(微信)  4.发货成功(回调平台)  5.发货支付失败" json:"status"` // 支付状态 1.创建订单 2.待支付 3.支付成功(微信)  4.发货成功(回调平台)  5.发货支付失败
	Extra              string `gorm:"column:extra;not null;comment:额外信息" json:"extra"`                                                             // 额外信息
	PayerOpenID        string `gorm:"column:payer_open_id;not null;comment:支付的open_id" json:"payer_open_id"`                                       // 支付的open_id
	Channel            int32  `gorm:"column:channel;default:1;comment:1.JSAPI 2. 米大师" json:"channel"`                                              // 1.JSAPI 2. 米大师
	PrepayID           string `gorm:"column:prepay_id;not null;comment:微信" json:"prepay_id"`                                                       // 微信
	CallbackOriginData string `gorm:"column:callback_origin_data;not null;comment:微信回调支付原始数据信息" json:"callback_origin_data"`                       // 微信回调支付原始数据信息
	CreatedAt          int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt          int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted          bool   `gorm:"column:is_deleted" json:"is_deleted"`
}

// TableName Order's table name
func (*Order) TableName() string {
	return TableNameOrder
}
