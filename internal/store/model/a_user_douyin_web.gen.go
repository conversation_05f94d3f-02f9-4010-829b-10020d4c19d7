// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAUserDouyinWeb = "a_user_douyin_web"

// AUserDouyinWeb 抖音开放平台-获取用户公开信息（/oauth/userinfo/）
type AUserDouyinWeb struct {
	ClientKey    string `gorm:"column:client_key;primaryKey;comment:API data.client_key 应用client_key" json:"client_key"`                                        // API data.client_key 应用client_key
	OpenID       string `gorm:"column:open_id;primaryKey;comment:API data.open_id 用户在应用下的唯一标识" json:"open_id"`                                                  // API data.open_id 用户在应用下的唯一标识
	UnionID      string `gorm:"column:union_id;comment:API data.union_id 用户在开发者账号下的唯一标识（可空）" json:"union_id"`                                                   // API data.union_id 用户在开发者账号下的唯一标识（可空）
	Nickname     string `gorm:"column:nickname;not null;comment:API data.nickname 用户昵称（可空）" json:"nickname"`                                                    // API data.nickname 用户昵称（可空）
	Avatar       string `gorm:"column:avatar;not null;comment:API data.avatar 用户头像URL（可空）" json:"avatar"`                                                       // API data.avatar 用户头像URL（可空）
	EAccountRole string `gorm:"column:e_account_role;not null;comment:API data.e_account_role 企业号认证身份：EAccountM/EAccountS/EAccountK（可空）" json:"e_account_role"` // API data.e_account_role 企业号认证身份：EAccountM/EAccountS/EAccountK（可空）
}

// TableName AUserDouyinWeb's table name
func (*AUserDouyinWeb) TableName() string {
	return TableNameAUserDouyinWeb
}
