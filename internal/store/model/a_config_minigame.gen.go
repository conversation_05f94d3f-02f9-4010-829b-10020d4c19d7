// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAConfigMinigame = "a_config_minigame"

// AConfigMinigame mapped from table <a_config_minigame>
type AConfigMinigame struct {
	ID                int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID            string `gorm:"column:game_id;not null;comment:游戏id" json:"game_id"`              // 游戏id
	AppID             string `gorm:"column:app_id;not null;comment:平台应用id" json:"app_id"`              // 平台应用id
	AppSercet         string `gorm:"column:app_sercet;not null;comment:平台应用密钥" json:"app_sercet"`      // 平台应用密钥
	AccessToken       string `gorm:"column:access_token;not null;comment:平台token" json:"access_token"` // 平台token
	PayAppKey         string `gorm:"column:pay_app_key;not null;comment:支付密钥" json:"pay_app_key"`      // 支付密钥
	PayOfferID        string `gorm:"column:pay_offer_id;not null" json:"pay_offer_id"`
	PayCallback       string `gorm:"column:pay_callback;not null;comment:iOS H5的支付回调， 已废弃，使用服务器回调固定值" json:"pay_callback"` // iOS H5的支付回调， 已废弃，使用服务器回调固定值
	ExpiresIn         int32  `gorm:"column:expires_in;not null;comment:过期时间" json:"expires_in"`                            // 过期时间
	MessageToken      string `gorm:"column:message_token;not null;comment:消息服务器token" json:"message_token"`                // 消息服务器token
	EncodingAesKey    string `gorm:"column:encoding_aes_key;not null;comment:微信消息加密密钥" json:"encoding_aes_key"`            // 微信消息加密密钥
	CsPaymentBigPic   string `gorm:"column:cs_payment_big_pic;not null;comment:h5客服支付大图" json:"cs_payment_big_pic"`        // h5客服支付大图
	CsPaymentSmallPic string `gorm:"column:cs_payment_small_pic;not null;comment:h5客服支付小图" json:"cs_payment_small_pic"`    // h5客服支付小图
	IsEncrypt         int32  `gorm:"column:is_encrypt;not null;comment:0 关闭 1 开启" json:"is_encrypt"`                       // 0 关闭 1 开启
	DisableInsecure   int32  `gorm:"column:disable_insecure;not null;comment:禁止非加密接口，0 允许 1 禁止" json:"disable_insecure"`   // 禁止非加密接口，0 允许 1 禁止
	IsVerifyUnionID   bool   `gorm:"column:is_verify_union_id;comment:是否校验union_id" json:"is_verify_union_id"`             // 是否校验union_id
	TokenRefreshedAt  int64  `gorm:"column:token_refreshed_at;not null;comment:token刷新时间" json:"token_refreshed_at"`       // token刷新时间
	CreatedAt         int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt         int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted         bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName AConfigMinigame's table name
func (*AConfigMinigame) TableName() string {
	return TableNameAConfigMinigame
}
