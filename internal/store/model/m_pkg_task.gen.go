// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMPkgTask = "m_pkg_task"

// MPkgTask mapped from table <m_pkg_task>
type MPkgTask struct {
	ID                  int64  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameNameZh          string `gorm:"column:game_name_zh;not null;comment:游戏中文名" json:"game_name_zh"`                                            // 游戏中文名
	GameNameEn          string `gorm:"column:game_name_en;not null;comment:游戏英文名" json:"game_name_en"`                                            // 游戏英文名
	GameURL             string `gorm:"column:game_url;not null;comment:游戏URL" json:"game_url"`                                                    // 游戏URL
	GameVersion         string `gorm:"column:game_version;not null;comment:游戏版本号" json:"game_version"`                                            // 游戏版本号
	VersionTextColor    string `gorm:"column:version_text_color;not null;comment:版本号颜色" json:"version_text_color"`                                // 版本号颜色
	VersionText         string `gorm:"column:version_text;not null;comment:版本号文案" json:"version_text"`                                            // 版本号文案
	GameOrientation     int32  `gorm:"column:game_orientation;not null;default:1;comment:游戏方向(1:竖向,2:横向)" json:"game_orientation"`                // 游戏方向(1:竖向,2:横向)
	GameIcon            string `gorm:"column:game_icon;not null;comment:游戏图标路径" json:"game_icon"`                                                 // 游戏图标路径
	LaunchBg            string `gorm:"column:launch_bg;not null;comment:启动背景图路径" json:"launch_bg"`                                                // 启动背景图路径
	LaunchPopupText     string `gorm:"column:launch_popup_text;comment:开屏弹窗文案" json:"launch_popup_text"`                                          // 开屏弹窗文案
	AgeRating           int32  `gorm:"column:age_rating;not null;default:1;comment:适龄提示年龄(1:8+,2:12+,3:16+)" json:"age_rating"`                   // 适龄提示年龄(1:8+,2:12+,3:16+)
	AgeRatingPosition   int32  `gorm:"column:age_rating_position;not null;comment:适龄提示位置(0左上 1右上 2左下 3右下)" json:"age_rating_position"`            // 适龄提示位置(0左上 1右上 2左下 3右下)
	AgeRatingDesc       string `gorm:"column:age_rating_desc;comment:适龄说明文案" json:"age_rating_desc"`                                              // 适龄说明文案
	AppropriateAge      int32  `gorm:"column:appropriate_age;not null;comment:适龄年龄" json:"appropriate_age"`                                       // 适龄年龄
	ShowSplashDialog    int32  `gorm:"column:show_splash_dialog;not null;comment:开屏弹窗开关" json:"show_splash_dialog"`                               // 开屏弹窗开关
	LoadLocalWeb        string `gorm:"column:load_local_web;not null;comment:是否加载本地Web" json:"load_local_web"`                                    // 是否加载本地Web
	SplashTips          string `gorm:"column:splash_tips;not null;comment:开屏防沉迷提示文案" json:"splash_tips"`                                          // 开屏防沉迷提示文案
	AllowRegister       int32  `gorm:"column:allow_register;not null;comment:是否开放注册(0:不开放,1:开放)" json:"allow_register"`                           // 是否开放注册(0:不开放,1:开放)
	MinorPlayTimeType   int32  `gorm:"column:minor_play_time_type;not null;default:1;comment:未成年可游玩时间类型(1:默认,2:自定义)" json:"minor_play_time_type"` // 未成年可游玩时间类型(1:默认,2:自定义)
	MinorPlayTimeConfig string `gorm:"column:minor_play_time_config;comment:未成年可游玩时间配置(JSON格式)" json:"minor_play_time_config"`                    // 未成年可游玩时间配置(JSON格式)
	DownloadURL         string `gorm:"column:download_url;comment:下载地址" json:"download_url"`                                                      // 下载地址
	Status              int32  `gorm:"column:status;not null;comment:状态(0:待打包,1:打包中,2:打包完成)" json:"status"`                                       // 状态(0:待打包,1:打包中,2:打包完成)
	CreatorID           string `gorm:"column:creator_id;not null;comment:创建人ID" json:"creator_id"`                                                // 创建人ID
	CreatedAt           int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt           int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted           bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MPkgTask's table name
func (*MPkgTask) TableName() string {
	return TableNameMPkgTask
}
