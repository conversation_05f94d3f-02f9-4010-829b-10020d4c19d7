// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMPermission = "m_permission"

// MPermission 权限类型:  1页面 2操作
type MPermission struct {
	ID         int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ParentID   int32  `gorm:"column:parent_id;not null" json:"parent_id"`
	Base       string `gorm:"column:base;not null" json:"base"`
	Code       string `gorm:"column:code;not null;comment:权限码" json:"code"`                           // 权限码
	Name       string `gorm:"column:name;not null;comment:权限名称" json:"name"`                          // 权限名称
	Type       int32  `gorm:"column:type;not null;comment:权限类型:  1页面 2操作" json:"type"`                // 权限类型:  1页面 2操作
	SystemType int32  `gorm:"column:system_type;not null;comment:0 平台 1 游戏" json:"system_type"`       // 0 平台 1 游戏
	CreatorID  string `gorm:"column:creator_id;not null;default:0;comment:创建人用户id" json:"creator_id"` // 创建人用户id
	CreatedAt  int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt  int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MPermission's table name
func (*MPermission) TableName() string {
	return TableNameMPermission
}
