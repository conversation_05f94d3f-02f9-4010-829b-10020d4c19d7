// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameGame = "game"

// Game mapped from table <game>
type Game struct {
	ID            int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID        string `gorm:"column:game_id;not null;comment:平台自定义id" json:"game_id"`                           // 平台自定义id
	Name          string `gorm:"column:name;not null;comment:游戏名称" json:"name"`                                    // 游戏名称
	Status        int32  `gorm:"column:status;not null;comment:游戏状态" json:"status"`                                // 游戏状态
	Secret        string `gorm:"column:secret;not null;comment:密钥信息" json:"secret"`                                // 密钥信息
	PlatformAppID string `gorm:"column:platform_app_id;not null;comment:数数平台的APP ID" json:"platform_app_id"`       // 数数平台的APP ID
	PayCallback   string `gorm:"column:pay_callback;not null;comment:回调地址" json:"pay_callback"`                    // 回调地址
	PlatformType  string `gorm:"column:platform_type;not null;default:minigame;comment:平台类型" json:"platform_type"` // 平台类型
	CreatorID     string `gorm:"column:creator_id;not null;default:0;comment:创建人id" json:"creator_id"`             // 创建人id
	CreatedAt     int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt     int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted     bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName Game's table name
func (*Game) TableName() string {
	return TableNameGame
}
