// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMShareRoadblock = "m_share_roadblock"

// MShareRoadblock mapped from table <m_share_roadblock>
type MShareRoadblock struct {
	ID              int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID          string `gorm:"column:game_id;not null;comment:游戏id" json:"game_id"` // 游戏id
	RoadblockNameEn string `gorm:"column:roadblock_name_en;not null" json:"roadblock_name_en"`
	RoadblockNameCn string `gorm:"column:roadblock_name_cn;not null" json:"roadblock_name_cn"`
	ShareTimeout    int32  `gorm:"column:share_timeout;not null" json:"share_timeout"`
	CreatorID       string `gorm:"column:creator_id;not null" json:"creator_id"`
	CreatedAt       int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt       int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted       bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MShareRoadblock's table name
func (*MShareRoadblock) TableName() string {
	return TableNameMShareRoadblock
}
