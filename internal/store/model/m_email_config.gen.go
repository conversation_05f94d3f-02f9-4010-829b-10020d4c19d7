// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMEmailConfig = "m_email_config"

// MEmailConfig 邮箱配置表
type MEmailConfig struct {
	ID           int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键" json:"id"`                      // 主键
	ConfigType   string `gorm:"column:config_type;not null;comment:配置类型,如workorder_stats-工单统计" json:"config_type"` // 配置类型,如workorder_stats-工单统计
	EmailAddress string `gorm:"column:email_address;not null;comment:邮箱地址" json:"email_address"`                   // 邮箱地址
	EmailName    string `gorm:"column:email_name;not null;comment:邮箱备注名称" json:"email_name"`                       // 邮箱备注名称
	CreatorID    string `gorm:"column:creator_id;not null;comment:创建人ID" json:"creator_id"`                        // 创建人ID
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间" json:"created_at"`    // 创建时间
	UpdatedAt    int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间" json:"updated_at"`    // 更新时间
	IsDeleted    bool   `gorm:"column:is_deleted;not null;comment:是否删除" json:"is_deleted"`                         // 是否删除
}

// TableName MEmailConfig's table name
func (*MEmailConfig) TableName() string {
	return TableNameMEmailConfig
}
