// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameH5AdminUserRechargeLimit = "h5_admin_user_recharge_limit"

// H5AdminUserRechargeLimit 用户每月充值额度限制表
type H5AdminUserRechargeLimit struct {
	ID          int64   `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键，自增ID" json:"id"`                    // 主键，自增ID
	UserID      string  `gorm:"column:user_id;not null;comment:用户唯一标识" json:"user_id"`                                // 用户唯一标识
	YearMonth   string  `gorm:"column:year_month;not null;comment:年月，格式YYYYMM" json:"year_month"`                     // 年月，格式YYYYMM
	TotalAmount float64 `gorm:"column:total_amount;not null;default:0.00;comment:该月累计充值金额" json:"total_amount"`       // 该月累计充值金额
	CreatedAt   int64   `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间，时间戳" json:"created_at"`   // 创建时间，时间戳
	UpdatedAt   int64   `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:最后更新时间，时间戳" json:"updated_at"` // 最后更新时间，时间戳
}

// TableName H5AdminUserRechargeLimit's table name
func (*H5AdminUserRechargeLimit) TableName() string {
	return TableNameH5AdminUserRechargeLimit
}
