// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMStopServiceConfig = "m_stop_service_config"

// MStopServiceConfig 停服配置表
type MStopServiceConfig struct {
	ID                     int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                                       // 主键ID
	GameID                 string `gorm:"column:game_id;not null;comment:游戏ID" json:"game_id"`                                                  // 游戏ID
	PlatformType           string `gorm:"column:platform_type;not null;comment:平台类型：minigame-微信小游戏，douyin_minigame-抖音小游戏" json:"platform_type"` // 平台类型：minigame-微信小游戏，douyin_minigame-抖音小游戏
	DisableNewUserRegister bool   `gorm:"column:disable_new_user_register;not null;comment:禁止新用户注册：0-允许，1-禁止" json:"disable_new_user_register"` // 禁止新用户注册：0-允许，1-禁止
	DisableRecharge        bool   `gorm:"column:disable_recharge;not null;comment:禁止充值：0-允许，1-禁止" json:"disable_recharge"`                      // 禁止充值：0-允许，1-禁止
	CreatorID              string `gorm:"column:creator_id;not null;comment:创建者用户ID" json:"creator_id"`                                         // 创建者用户ID
	CreatedAt              int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳（Unix毫秒）" json:"created_at"`              // 创建时间戳（Unix毫秒）
	UpdatedAt              int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间戳（Unix毫秒）" json:"updated_at"`              // 更新时间戳（Unix毫秒）
	IsDeleted              bool   `gorm:"column:is_deleted;not null;comment:删除标记：0-未删除，1-已删除" json:"is_deleted"`                                // 删除标记：0-未删除，1-已删除
}

// TableName MStopServiceConfig's table name
func (*MStopServiceConfig) TableName() string {
	return TableNameMStopServiceConfig
}
