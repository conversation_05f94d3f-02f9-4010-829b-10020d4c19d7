// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAUserMinigame = "a_user_minigame"

// AUserMinigame mapped from table <a_user_minigame>
type AUserMinigame struct {
	ID                 int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID             string `gorm:"column:user_id;not null;comment:uuid" json:"user_id"` // uuid
	OpenID             string `gorm:"column:open_id;not null" json:"open_id"`
	UnionID            string `gorm:"column:union_id;not null" json:"union_id"`
	NickName           string `gorm:"column:nick_name;not null;comment:昵称" json:"nick_name"`                        // 昵称
	Gender             int32  `gorm:"column:gender;not null;comment:性别" json:"gender"`                              // 性别
	City               string `gorm:"column:city;not null;comment:城市" json:"city"`                                  // 城市
	Province           string `gorm:"column:province;not null;comment:省份" json:"province"`                          // 省份
	Country            string `gorm:"column:country;not null;comment:国家" json:"country"`                            // 国家
	AvatarURL          string `gorm:"column:avatar_url;not null;comment:头像url" json:"avatar_url"`                   // 头像url
	Language           string `gorm:"column:language;not null;comment:语言" json:"language"`                          // 语言
	WatermarkAppID     string `gorm:"column:watermark_app_id;not null;comment:水印应用id" json:"watermark_app_id"`      // 水印应用id
	WatermarkTimestamp int64  `gorm:"column:watermark_timestamp;not null;comment:水印时间戳" json:"watermark_timestamp"` // 水印时间戳
	SessionKey         string `gorm:"column:session_key;not null;comment:会话密钥" json:"session_key"`                  // 会话密钥
	CreatedAt          int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt          int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted          bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName AUserMinigame's table name
func (*AUserMinigame) TableName() string {
	return TableNameAUserMinigame
}
