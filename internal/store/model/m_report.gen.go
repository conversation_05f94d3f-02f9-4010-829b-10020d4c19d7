// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMReport = "m_report"

// MReport 举报记录表
type MReport struct {
	ID                 int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                    // 主键ID
	GameID             string `gorm:"column:game_id;not null;comment:游戏ID" json:"game_id"`                               // 游戏ID
	ReportedPlatformID string `gorm:"column:reported_platform_id;not null;comment:被举报者平台ID" json:"reported_platform_id"` // 被举报者平台ID
	ReportedServerID   string `gorm:"column:reported_server_id;not null" json:"reported_server_id"`
	ReportedRoleID     string `gorm:"column:reported_role_id;not null;comment:被举报者角色ID" json:"reported_role_id"` // 被举报者角色ID
	ReportedAvatar     string `gorm:"column:reported_avatar;not null" json:"reported_avatar"`
	ReportedNickname   string `gorm:"column:reported_nickname;not null" json:"reported_nickname"`
	ReporterRoleID     string `gorm:"column:reporter_role_id;not null;comment:举报者角色ID" json:"reporter_role_id"` // 举报者角色ID
	ReporterServerID   string `gorm:"column:reporter_server_id;not null" json:"reporter_server_id"`
	ReporterAvatar     string `gorm:"column:reporter_avatar;not null" json:"reporter_avatar"`
	ReporterNickname   string `gorm:"column:reporter_nickname;comment:举报者昵称" json:"reporter_nickname"` // 举报者昵称
	ReporterPlatformID string `gorm:"column:reporter_platform_id;not null" json:"reporter_platform_id"`
	ReportItem         string `gorm:"column:report_item;comment:举报项 [1, 2,3,4]" json:"report_item"` // 举报项 [1, 2,3,4]
	ReportReason       string `gorm:"column:report_reason;not null" json:"report_reason"`
	Status             int32  `gorm:"column:status;not null;comment:状态 0:未处理 1:已处理 2: 举报不成立" json:"status"` // 状态 0:未处理 1:已处理 2: 举报不成立
	ExtraParamA        string `gorm:"column:extra_param_a;not null" json:"extra_param_a"`
	ExtraParamB        string `gorm:"column:extra_param_b;comment:额外参数" json:"extra_param_b"`                         // 额外参数
	SessionFrom        string `gorm:"column:session_from;not null;comment:透传数据" json:"session_from"`                  // 透传数据
	ReportTimeAt       int64  `gorm:"column:report_time_at;not null;comment:举报时间" json:"report_time_at"`              // 举报时间
	CreatedAt          int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt          int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间" json:"updated_at"` // 更新时间
	IsDeleted          bool   `gorm:"column:is_deleted;not null;comment:是否删除 0:否 1:是" json:"is_deleted"`              // 是否删除 0:否 1:是
}

// TableName MReport's table name
func (*MReport) TableName() string {
	return TableNameMReport
}
