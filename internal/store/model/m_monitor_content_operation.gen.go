// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMMonitorContentOperation = "m_monitor_content_operation"

// MMonitorContentOperation 内容操作记录表
type MMonitorContentOperation struct {
	ID                int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"` // 主键ID
	GameID            string `gorm:"column:game_id;not null" json:"game_id"`
	ContentID         string `gorm:"column:content_id;not null;comment:关联的内容ID" json:"content_id"` // 关联的内容ID
	UserID            string `gorm:"column:user_id;not null;comment:平台ID" json:"user_id"`          // 平台ID
	RoleID            string `gorm:"column:role_id;not null" json:"role_id"`
	Action            int32  `gorm:"column:action;not null;comment:处理动作索引" json:"action"`               // 处理动作索引
	ActionParam       string `gorm:"column:action_param;comment:处理参数(JSON格式)" json:"action_param"`      // 处理参数(JSON格式)
	HandleTimeAt      int64  `gorm:"column:handle_time_at;not null;comment:处理时间" json:"handle_time_at"` // 处理时间
	Description       string `gorm:"column:description;not null" json:"description"`
	RecordDescription string `gorm:"column:record_description;not null;comment:处理描述" json:"record_description"`      // 处理描述
	Content           string `gorm:"column:content;not null;comment:文本内容" json:"content"`                            // 文本内容
	CreatorID         string `gorm:"column:creator_id;not null;comment:处理人ID" json:"creator_id"`                     // 处理人ID
	CreatedAt         int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt         int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间" json:"updated_at"` // 更新时间
	IsDeleted         bool   `gorm:"column:is_deleted;not null;comment:是否删除" json:"is_deleted"`                      // 是否删除
}

// TableName MMonitorContentOperation's table name
func (*MMonitorContentOperation) TableName() string {
	return TableNameMMonitorContentOperation
}
