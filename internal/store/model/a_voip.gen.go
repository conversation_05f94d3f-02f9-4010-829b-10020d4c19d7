// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAVoip = "a_voip"

// AVoip mapped from table <a_voip>
type AVoip struct {
	ID           int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID       string `gorm:"column:game_id;not null" json:"game_id"`
	GroupID      string `gorm:"column:group_id;not null" json:"group_id"`
	PlatformType string `gorm:"column:platform_type;not null" json:"platform_type"`
	UserID       string `gorm:"column:user_id;not null" json:"user_id"`
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt    int64  `gorm:"column:updated_at;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted    int32  `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName AVoip's table name
func (*AVoip) TableName() string {
	return TableNameAVoip
}
