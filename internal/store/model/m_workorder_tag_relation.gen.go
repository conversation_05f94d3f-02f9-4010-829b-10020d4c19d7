// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMWorkorderTagRelation = "m_workorder_tag_relation"

// MWorkorderTagRelation 工单-标签关联表
type MWorkorderTagRelation struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	OrderID   string `gorm:"column:order_id;not null;comment:工单ID" json:"order_id"` // 工单ID
	TagID     int32  `gorm:"column:tag_id;not null;comment:标签ID" json:"tag_id"`     // 标签ID
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MWorkorderTagRelation's table name
func (*MWorkorderTagRelation) TableName() string {
	return TableNameMWorkorderTagRelation
}
