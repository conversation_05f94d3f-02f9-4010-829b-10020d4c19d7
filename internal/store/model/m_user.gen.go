// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMUser = "m_user"

// MUser 用户表
type MUser struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID    string `gorm:"column:user_id;not null" json:"user_id"`
	Username  string `gorm:"column:username;not null;comment:用户名称" json:"username"` // 用户名称
	Password  string `gorm:"column:password;not null" json:"password"`
	Name      string `gorm:"column:name;not null;comment:用户名" json:"name"`                           // 用户名
	Phone     string `gorm:"column:phone;not null;comment:手机号" json:"phone"`                         // 手机号
	Status    int32  `gorm:"column:status;not null;default:1;comment:状态 1 启用 2 关闭" json:"status"`    // 状态 1 启用 2 关闭
	IsAdmin   bool   `gorm:"column:is_admin;not null;comment:是否是管理员（具有所有权限）默认为普通用户" json:"is_admin"` // 是否是管理员（具有所有权限）默认为普通用户
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MUser's table name
func (*MUser) TableName() string {
	return TableNameMUser
}
