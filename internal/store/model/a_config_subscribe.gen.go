// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAConfigSubscribe = "a_config_subscribe"

// AConfigSubscribe mapped from table <a_config_subscribe>
type AConfigSubscribe struct {
	ID              int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID          string `gorm:"column:game_id;not null;comment:游戏id" json:"game_id"`     // 游戏id
	AppID           string `gorm:"column:app_id;not null;comment:订阅号id" json:"app_id"`      // 订阅号id
	AppSercet       string `gorm:"column:app_sercet;not null;comment:密钥" json:"app_sercet"` // 密钥
	AccessToken     string `gorm:"column:access_token" json:"access_token"`
	Ticket          string `gorm:"column:ticket;not null;comment:jsapi ticket" json:"ticket"`                      // jsapi ticket
	ExpiresIn       int32  `gorm:"column:expires_in;not null;comment:token过期时间" json:"expires_in"`                 // token过期时间
	TicketExpiresIn int32  `gorm:"column:ticket_expires_in;not null;comment:ticket 过期时间" json:"ticket_expires_in"` // ticket 过期时间
	CreatedAt       int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt       int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName AConfigSubscribe's table name
func (*AConfigSubscribe) TableName() string {
	return TableNameAConfigSubscribe
}
