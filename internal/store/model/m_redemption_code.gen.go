// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMRedemptionCode = "m_redemption_code"

// MRedemptionCode mapped from table <m_redemption_code>
type MRedemptionCode struct {
	ID               int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UUID             string `gorm:"column:uuid;not null" json:"uuid"`
	GameID           string `gorm:"column:game_id;not null" json:"game_id"`
	Batch            string `gorm:"column:batch;not null" json:"batch"`
	Title            string `gorm:"column:title;comment:名称" json:"title"`                                 // 名称
	CodeType         int32  `gorm:"column:code_type;not null;comment:1 普通码 2 通兑码" json:"code_type"`       // 1 普通码 2 通兑码
	Status           int32  `gorm:"column:status;not null;comment:状态 1 正常开启 2 关闭状态 3 正在生成" json:"status"` // 状态 1 正常开启 2 关闭状态 3 正在生成
	Description      string `gorm:"column:description;not null;comment:描述" json:"description"`            // 描述
	Content          string `gorm:"column:content;not null" json:"content"`
	CommencementDate int64  `gorm:"column:commencement_date;not null" json:"commencement_date"`
	ExpiredDate      int64  `gorm:"column:expired_date;not null" json:"expired_date"`
	Number           int32  `gorm:"column:number;not null;comment:兑换码数量" json:"number"`       // 兑换码数量
	Frequency        int32  `gorm:"column:frequency;not null;comment:兑换码次数" json:"frequency"` // 兑换码次数
	Slogan           string `gorm:"column:slogan;not null" json:"slogan"`
	RemainingCode    int32  `gorm:"column:remaining_code;not null;comment:兑换剩余" json:"remaining_code"` // 兑换剩余
	CreatorID        string `gorm:"column:creator_id;not null" json:"creator_id"`
	CreatedAt        int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt        int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted        bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MRedemptionCode's table name
func (*MRedemptionCode) TableName() string {
	return TableNameMRedemptionCode
}
