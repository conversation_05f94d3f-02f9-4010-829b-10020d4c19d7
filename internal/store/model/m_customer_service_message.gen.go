// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMCustomerServiceMessage = "m_customer_service_message"

// MCustomerServiceMessage mapped from table <m_customer_service_message>
type MCustomerServiceMessage struct {
	ID               int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID           string `gorm:"column:game_id;not null" json:"game_id"`
	PlatformType     string `gorm:"column:platform_type;not null;comment:平台类型 minigame 、douyin_minigame" json:"platform_type"`                                                 // 平台类型 minigame 、douyin_minigame
	Scenes           int32  `gorm:"column:scenes;not null;comment:场景: 1 用户进入客服消息 2 用户发消息 3 用户发送小程序卡片 4 用户发消息，未命中文本" json:"scenes"`                                             // 场景: 1 用户进入客服消息 2 用户发消息 3 用户发送小程序卡片 4 用户发消息，未命中文本
	AcceptText       string `gorm:"column:accept_text;not null;comment:接受文本, 数组" json:"accept_text"`                                                                           // 接受文本, 数组
	ReplyType        int32  `gorm:"column:reply_type;not null;comment:回复方式 1 文本 2 图片 3 透传给服务器，由服务器处理 4 推送小秘 5 链接 6 H5订单地址 7跳转工单小程序 8 跳转未成年退款 9 重置zhanghaoo" json:"reply_type"` // 回复方式 1 文本 2 图片 3 透传给服务器，由服务器处理 4 推送小秘 5 链接 6 H5订单地址 7跳转工单小程序 8 跳转未成年退款 9 重置zhanghaoo
	MatchParam       string `gorm:"column:match_param;not null;comment:匹配参数值 （当回复方式为文本时）" json:"match_param"`                                                                  // 匹配参数值 （当回复方式为文本时）
	ReplyContent     string `gorm:"column:reply_content;not null;comment:回复内容（当回复方式为文本时）" json:"reply_content"`                                                                // 回复内容（当回复方式为文本时）
	PicURL           string `gorm:"column:pic_url;not null;comment:图片 url (当回复方式为图片)" json:"pic_url"`                                                                          // 图片 url (当回复方式为图片)
	PenetrateOperate int32  `gorm:"column:penetrate_operate;not null;comment:透传成功是否额外操作 1 是 2 否" json:"penetrate_operate"`                                                     // 透传成功是否额外操作 1 是 2 否
	Title            string `gorm:"column:title;not null;comment:标题" json:"title"`                                                                                             // 标题
	Description      string `gorm:"column:description;not null;comment:描述" json:"description"`                                                                                 // 描述
	Link             string `gorm:"column:link;not null;comment:跳转链接" json:"link"`                                                                                             // 跳转链接
	CreatorID        string `gorm:"column:creator_id;not null" json:"creator_id"`
	CreatedAt        int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt        int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted        bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MCustomerServiceMessage's table name
func (*MCustomerServiceMessage) TableName() string {
	return TableNameMCustomerServiceMessage
}
