// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMGame = "m_game"

// MGame mapped from table <m_game>
type MGame struct {
	ID                            int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID                        string `gorm:"column:game_id;not null;comment:平台自定义id" json:"game_id"`                                     // 平台自定义id
	Name                          string `gorm:"column:name;not null;comment:游戏名称" json:"name"`                                              // 游戏名称
	Status                        int32  `gorm:"column:status;not null;comment:游戏状态" json:"status"`                                          // 游戏状态
	Icon                          string `gorm:"column:icon;not null;comment:游戏图标" json:"icon"`                                              // 游戏图标
	Secret                        string `gorm:"column:secret;not null;comment:密钥信息" json:"secret"`                                          // 密钥信息
	PlatformType                  string `gorm:"column:platform_type;not null;comment:平台类型 minigame | douyin_minigame" json:"platform_type"` // 平台类型 minigame | douyin_minigame
	PlatformAppID                 string `gorm:"column:platform_app_id;not null;comment:数数平台的APP ID" json:"platform_app_id"`                 // 数数平台的APP ID
	PayCallback                   string `gorm:"column:pay_callback;not null;comment:回调地址" json:"pay_callback"`                              // 回调地址
	GravityAppID                  string `gorm:"column:gravity_app_id;not null" json:"gravity_app_id"`
	GravityAccessToken            string `gorm:"column:gravity_access_token;not null" json:"gravity_access_token"`
	GravityIsEnabled              int32  `gorm:"column:gravity_is_enabled;not null;default:1;comment:引力是否开启" json:"gravity_is_enabled"` // 引力是否开启
	QiyuWechatAppID               string `gorm:"column:qiyu_wechat_app_id;not null" json:"qiyu_wechat_app_id"`
	CustomerServiceCallback       string `gorm:"column:customer_service_callback" json:"customer_service_callback"`
	CustomerServiceDouyinCallback string `gorm:"column:customer_service_douyin_callback;not null;comment:抖音客服(礼包)回调地址" json:"customer_service_douyin_callback"` // 抖音客服(礼包)回调地址
	ReportServiceCallback         string `gorm:"column:report_service_callback;not null;comment:举报系统回调" json:"report_service_callback"`                         // 举报系统回调
	MonitorServiceCallback        string `gorm:"column:monitor_service_callback;not null;comment:聊天监控会调" json:"monitor_service_callback"`                       // 聊天监控会调
	QiyuAppKey                    string `gorm:"column:qiyu_app_key;not null" json:"qiyu_app_key"`
	QiyuAppSecret                 string `gorm:"column:qiyu_app_secret;not null" json:"qiyu_app_secret"`
	PayMethod                     int32  `gorm:"column:pay_method;not null;default:1;comment:1 发送卡片 2 手动输入cz或充值 3 弹出二维码 4 弹框复制支付链接" json:"pay_method"` // 1 发送卡片 2 手动输入cz或充值 3 弹出二维码 4 弹框复制支付链接
	TencentDataSourceID           string `gorm:"column:tencent_data_source_id;not null" json:"tencent_data_source_id"`
	TencentEncryptionKey          string `gorm:"column:tencent_encryption_key;not null" json:"tencent_encryption_key"`
	TencentAdCycle                int32  `gorm:"column:tencent_ad_cycle;not null" json:"tencent_ad_cycle"`
	GameSceneURL                  string `gorm:"column:game_scene_url;not null;comment:抖音游戏场景api" json:"game_scene_url"` // 抖音游戏场景api
	CreatorID                     string `gorm:"column:creator_id;not null;comment:创建人id" json:"creator_id"`             // 创建人id
	CreatedAt                     int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt                     int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted                     bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MGame's table name
func (*MGame) TableName() string {
	return TableNameMGame
}
