// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAOrder = "a_order"

// AOrder mapped from table <a_order>
type AOrder struct {
	ID                      int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID                  string `gorm:"column:user_id;not null" json:"user_id"`
	OrderID                 string `gorm:"column:order_id;not null;comment:订单 id" json:"order_id"` // 订单 id
	GameID                  string `gorm:"column:game_id;not null" json:"game_id"`
	GoodsID                 string `gorm:"column:goods_id;not null;comment:商品 ID" json:"goods_id"`                                                                       // 商品 ID
	PayType                 int32  `gorm:"column:pay_type;default:1;comment:支付类型 1:微信安卓米大师 2:iOS H5支付 3:Google 4: iOS APP苹果支付 5: 抖音安卓虚拟支付 6: 抖音iOS虚拟支付" json:"pay_type"` // 支付类型 1:微信安卓米大师 2:iOS H5支付 3:Google 4: iOS APP苹果支付 5: 抖音安卓虚拟支付 6: 抖音iOS虚拟支付
	Money                   int32  `gorm:"column:money;not null;comment:金额 (分)" json:"money"`                                                                            // 金额 (分)
	CurrencyPrice           int32  `gorm:"column:currency_price;not null;comment:实际支付金额" json:"currency_price"`                                                          // 实际支付金额
	PlatformType            int32  `gorm:"column:platform_type;not null;default:1;comment:平台类型 1 iOS 2 安卓" json:"platform_type"`                                         // 平台类型 1 iOS 2 安卓
	Status                  int32  `gorm:"column:status;not null;default:1;comment:支付状态 1.创建订单 2.待支付 3.支付成功(微信)  4.发货成功(回调平台)  5.发货支付失败" json:"status"`                  // 支付状态 1.创建订单 2.待支付 3.支付成功(微信)  4.发货成功(回调平台)  5.发货支付失败
	Extra                   string `gorm:"column:extra;not null;comment:额外信息" json:"extra"`                                                                              // 额外信息
	PayerOpenID             string `gorm:"column:payer_open_id;not null;comment:支付的open_id" json:"payer_open_id"`                                                        // 支付的open_id
	GameCurrency            int32  `gorm:"column:game_currency;not null;comment:抖音平台购买的游戏币数量, 微信平台为0" json:"game_currency"`                                              // 抖音平台购买的游戏币数量, 微信平台为0
	PrepayID                string `gorm:"column:prepay_id;not null;comment:微信" json:"prepay_id"`                                                                        // 微信
	ThirdPartyTransactionID string `gorm:"column:third_party_transaction_id;not null" json:"third_party_transaction_id"`
	TransactionsInfo        string `gorm:"column:transactions_info;not null" json:"transactions_info"`
	CallbackOriginData      string `gorm:"column:callback_origin_data;not null;comment:微信回调支付原始数据信息" json:"callback_origin_data"` // 微信回调支付原始数据信息
	ShipmentCallback        string `gorm:"column:shipment_callback;not null;comment:游戏服务器自定义回调url" json:"shipment_callback"`      // 游戏服务器自定义回调url
	SaveAmt                 int32  `gorm:"column:save_amt;not null;comment:抖音历史累计充值游戏币数量" json:"save_amt"`                        // 抖音历史累计充值游戏币数量
	CreatedAt               int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt               int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted               bool   `gorm:"column:is_deleted" json:"is_deleted"`
}

// TableName AOrder's table name
func (*AOrder) TableName() string {
	return TableNameAOrder
}
