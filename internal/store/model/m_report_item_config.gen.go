// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMReportItemConfig = "m_report_item_config"

// MReportItemConfig 举报事项配置表
type MReportItemConfig struct {
	ID          int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                  // 主键ID
	GameID      string `gorm:"column:game_id;comment:游戏ID, 为空表示全局配置" json:"game_id"`                            // 游戏ID, 为空表示全局配置
	ItemValue   int32  `gorm:"column:item_value;not null;comment:举报事项的数值, SDK上报" json:"item_value"`             // 举报事项的数值, SDK上报
	ItemName    string `gorm:"column:item_name;not null;comment:举报事项名称" json:"item_name"`                       // 举报事项名称
	Description string `gorm:"column:description;comment:描述" json:"description"`                                // 描述
	IsPreset    bool   `gorm:"column:is_preset;not null;comment:是否预置, 0:否, 1:是, 预置不可修改" json:"is_preset"`       // 是否预置, 0:否, 1:是, 预置不可修改
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳" json:"created_at"` // 创建时间戳
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间戳" json:"updated_at"` // 更新时间戳
	IsDeleted   bool   `gorm:"column:is_deleted;not null;comment:软删除标记 0:未删除 1:已删除" json:"is_deleted"`          // 软删除标记 0:未删除 1:已删除
}

// TableName MReportItemConfig's table name
func (*MReportItemConfig) TableName() string {
	return TableNameMReportItemConfig
}
