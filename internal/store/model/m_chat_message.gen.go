// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMChatMessage = "m_chat_message"

// MChatMessage 聊天消息表
type MChatMessage struct {
	ID                int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"` // 主键ID
	GameID            string `gorm:"column:game_id;not null;comment:游戏ID" json:"game_id"`            // 游戏ID
	UserID            string `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`            // 用户ID
	OpenID            string `gorm:"column:open_id;not null;comment:小程序OpenID" json:"open_id"`       // 小程序OpenID
	MiniprogramOpenID string `gorm:"column:miniprogram_open_id;not null" json:"miniprogram_open_id"`
	MessageType       int32  `gorm:"column:message_type;not null;comment:消息类型：1-用户消息，2-系统消息" json:"message_type"`     // 消息类型：1-用户消息，2-系统消息
	Content           string `gorm:"column:content;not null;comment:消息内容" json:"content"`                             // 消息内容
	FeedbackType      int32  `gorm:"column:feedback_type;not null;comment:反馈类型：0-无反馈，1-有用，2-无用" json:"feedback_type"` // 反馈类型：0-无反馈，1-有用，2-无用
	CreatedAt         int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳" json:"created_at"` // 创建时间戳
	UpdatedAt         int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间戳" json:"updated_at"` // 更新时间戳
	IsDeleted         bool   `gorm:"column:is_deleted;not null;comment:逻辑删除，0=未删，1=已删" json:"is_deleted"`             // 逻辑删除，0=未删，1=已删
}

// TableName MChatMessage's table name
func (*MChatMessage) TableName() string {
	return TableNameMChatMessage
}
