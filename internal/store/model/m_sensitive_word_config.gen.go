// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMSensitiveWordConfig = "m_sensitive_word_config"

// MSensitiveWordConfig mapped from table <m_sensitive_word_config>
type MSensitiveWordConfig struct {
	ID         int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID     string `gorm:"column:game_id;not null" json:"game_id"`
	IgnoreCase int32  `gorm:"column:ignore_case;not null;default:2;comment:忽略大小写 1 开启 2 关闭 （默认关闭）" json:"ignore_case"` // 忽略大小写 1 开启 2 关闭 （默认关闭）
	CreatedAt  int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt  int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MSensitiveWordConfig's table name
func (*MSensitiveWordConfig) TableName() string {
	return TableNameMSensitiveWordConfig
}
