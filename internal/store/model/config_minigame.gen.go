// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameConfigMinigame = "config_minigame"

// ConfigMinigame mapped from table <config_minigame>
type ConfigMinigame struct {
	ID           int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID       string `gorm:"column:game_id;not null;comment:游戏id" json:"game_id"`              // 游戏id
	AppID        string `gorm:"column:app_id;not null;comment:平台应用id" json:"app_id"`              // 平台应用id
	AppSercet    string `gorm:"column:app_sercet;not null;comment:平台应用密钥" json:"app_sercet"`      // 平台应用密钥
	AccessToken  string `gorm:"column:access_token;not null;comment:平台token" json:"access_token"` // 平台token
	PayAppKey    string `gorm:"column:pay_app_key;not null;comment:支付密钥" json:"pay_app_key"`      // 支付密钥
	PayOfferID   string `gorm:"column:pay_offer_id;not null" json:"pay_offer_id"`
	PayCallback  string `gorm:"column:pay_callback;not null" json:"pay_callback"`
	ExpiresIn    int32  `gorm:"column:expires_in;not null;comment:过期时间" json:"expires_in"`             // 过期时间
	MessageToken string `gorm:"column:message_token;not null;comment:消息服务器token" json:"message_token"` // 消息服务器token
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt    int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted    bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName ConfigMinigame's table name
func (*ConfigMinigame) TableName() string {
	return TableNameConfigMinigame
}
