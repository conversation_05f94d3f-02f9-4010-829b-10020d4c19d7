// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameH5AdminUser = "h5_admin_user"

// H5AdminUser H5打包管理后台用户表
type H5AdminUser struct {
	ID             int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"` // 主键ID
	UserID         string `gorm:"column:user_id;not null" json:"user_id"`
	Username       string `gorm:"column:username;not null;comment:用户名" json:"username"`      // 用户名
	Password       string `gorm:"column:password;not null;comment:密码(加密存储)" json:"password"` // 密码(加密存储)
	IsRealNameAuth bool   `gorm:"column:is_real_name_auth;not null" json:"is_real_name_auth"`
	IsMinors       bool   `gorm:"column:is_minors;not null" json:"is_minors"`
	BirthDateAt    int64  `gorm:"column:birth_date_at;not null;comment:生日时间戳" json:"birth_date_at"`               // 生日时间戳
	CreatedAt      int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt      int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName H5AdminUser's table name
func (*H5AdminUser) TableName() string {
	return TableNameH5AdminUser
}
