// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMUpload = "m_upload"

// MUpload mapped from table <m_upload>
type MUpload struct {
	ID          int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	FileName    string `gorm:"column:file_name;not null" json:"file_name"`
	FileSize    int64  `gorm:"column:file_size;not null" json:"file_size"`
	FileType    string `gorm:"column:file_type;comment:文件后缀类型" json:"file_type"` // 文件后缀类型
	Md5         string `gorm:"column:md5;not null;comment:md5值" json:"md5"`      // md5值
	URL         string `gorm:"column:url;not null;comment:完整url" json:"url"`     // 完整url
	OssURL      string `gorm:"column:oss_url;not null" json:"oss_url"`
	OssBucket   string `gorm:"column:oss_bucket;not null" json:"oss_bucket"`
	Description string `gorm:"column:description;not null;comment:文件描述" json:"description"` // 文件描述
	CreatorID   string `gorm:"column:creator_id;not null" json:"creator_id"`
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted   bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MUpload's table name
func (*MUpload) TableName() string {
	return TableNameMUpload
}
