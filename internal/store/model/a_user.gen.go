// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAUser = "a_user"

// AUser mapped from table <a_user>
type AUser struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID    string `gorm:"column:game_id;not null;comment:游戏id" json:"game_id"` // 游戏id
	UserID    string `gorm:"column:user_id;not null;comment:uuid" json:"user_id"` // uuid
	Channel   string `gorm:"column:channel;not null" json:"channel"`
	AdFrom    string `gorm:"column:ad_from;not null" json:"ad_from"`
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName AUser's table name
func (*AUser) TableName() string {
	return TableNameAUser
}
