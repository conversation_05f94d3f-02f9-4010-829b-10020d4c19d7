// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMFaceFusion = "m_face_fusion"

// MFaceFusion 人脸融合记录表
type MFaceFusion struct {
	ID                int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                                 // 主键ID
	GameID            string `gorm:"column:game_id;not null;comment:游戏ID" json:"game_id"`                                            // 游戏ID
	UserID            string `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`                                            // 用户ID
	TaskID            string `gorm:"column:task_id;not null;comment:任务ID" json:"task_id"`                                            // 任务ID
	ModelID           string `gorm:"column:model_id;not null;comment:素材模板ID" json:"model_id"`                                        // 素材模板ID
	ProjectID         string `gorm:"column:project_id;not null;comment:项目ID" json:"project_id"`                                      // 项目ID
	RequestID         string `gorm:"column:request_id;not null;comment:腾讯云请求ID" json:"request_id"`                                   // 腾讯云请求ID
	ImageURL          string `gorm:"column:image_url;not null;comment:融合后图片OSS URL" json:"image_url"`                                // 融合后图片OSS URL
	OssURL            string `gorm:"column:oss_url;not null;comment:融合后图片OSS内部URL" json:"oss_url"`                                   // 融合后图片OSS内部URL
	Status            string `gorm:"column:status;not null;default:processing;comment:处理状态：processing/success/failed" json:"status"` // 处理状态：processing/success/failed
	Message           string `gorm:"column:message;not null;comment:状态消息" json:"message"`                                            // 状态消息
	MergeInfos        string `gorm:"column:merge_infos;comment:融合参数JSON" json:"merge_infos"`                                         // 融合参数JSON
	FuseFaceDegree    int32  `gorm:"column:fuse_face_degree;comment:人脸融合度，取值范围[0,100]" json:"fuse_face_degree"`                      // 人脸融合度，取值范围[0,100]
	FuseProfileDegree int32  `gorm:"column:fuse_profile_degree;comment:五官融合度，取值范围[0,1]" json:"fuse_profile_degree"`                  // 五官融合度，取值范围[0,1]
	LogoAdd           int32  `gorm:"column:logo_add;comment:是否添加logo，0不添加，1添加" json:"logo_add"`                                      // 是否添加logo，0不添加，1添加
	LogoParam         string `gorm:"column:logo_param;not null;comment:水印参数" json:"logo_param"`                                      // 水印参数
	FuseParam         string `gorm:"column:fuse_param;not null;comment:融合参数" json:"fuse_param"`                                      // 融合参数
	RspImgType        string `gorm:"column:rsp_img_type;not null;default:url;comment:返回图片类型：base64/url" json:"rsp_img_type"`         // 返回图片类型：base64/url
	CreatedAt         int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳（毫秒）" json:"created_at"`            // 创建时间戳（毫秒）
	UpdatedAt         int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间戳（毫秒）" json:"updated_at"`            // 更新时间戳（毫秒）
	ProcessedAt       int64  `gorm:"column:processed_at;comment:处理完成时间戳（毫秒）" json:"processed_at"`                                    // 处理完成时间戳（毫秒）
}

// TableName MFaceFusion's table name
func (*MFaceFusion) TableName() string {
	return TableNameMFaceFusion
}
