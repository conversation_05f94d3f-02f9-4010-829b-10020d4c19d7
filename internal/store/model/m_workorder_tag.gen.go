// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMWorkorderTag = "m_workorder_tag"

// MWorkorderTag 工单标签表
type MWorkorderTag struct {
	ID        int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	TagName   string `gorm:"column:tag_name;not null;comment:标签名称" json:"tag_name"` // 标签名称
	CreatedAt int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MWorkorderTag's table name
func (*MWorkorderTag) TableName() string {
	return TableNameMWorkorderTag
}
