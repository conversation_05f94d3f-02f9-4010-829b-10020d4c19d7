// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMWorkorderTemplateField = "m_workorder_template_field"

// MWorkorderTemplateField 工单模板字段表
type MWorkorderTemplateField struct {
	ID          int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID      string `gorm:"column:game_id;not null" json:"game_id"`
	FieldKey    string `gorm:"column:field_key;not null;comment:字段键名" json:"field_key"`                                           // 字段键名
	DisplayName string `gorm:"column:display_name;not null;comment:显示名称" json:"display_name"`                                     // 显示名称
	FieldType   string `gorm:"column:field_type;not null;default:string;comment:字段类型: string, number, boolean" json:"field_type"` // 字段类型: string, number, boolean
	IsVisible   bool   `gorm:"column:is_visible;not null;comment:toC 是否可见" json:"is_visible"`                                     // toC 是否可见
	Required    bool   `gorm:"column:required;not null;comment:是否必填: 0-否, 1-是" json:"required"`                                   // 是否必填: 0-否, 1-是
	SortOrder   int32  `gorm:"column:sort_order;not null;comment:排序顺序" json:"sort_order"`                                         // 排序顺序
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted   bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MWorkorderTemplateField's table name
func (*MWorkorderTemplateField) TableName() string {
	return TableNameMWorkorderTemplateField
}
