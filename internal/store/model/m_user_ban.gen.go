// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMUserBan = "m_user_ban"

// MUserBan mapped from table <m_user_ban>
type MUserBan struct {
	ID           int64  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID       string `gorm:"column:game_id;not null;comment:游戏ID" json:"game_id"`                                       // 游戏ID
	UserID       string `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`                                       // 用户ID
	BanType      int32  `gorm:"column:ban_type;not null;default:4;comment:封禁类型:1-登录封禁 2-聊天封禁 3-支付封禁 4-其他" json:"ban_type"` // 封禁类型:1-登录封禁 2-聊天封禁 3-支付封禁 4-其他
	BanReason    string `gorm:"column:ban_reason;not null;comment:封禁原因" json:"ban_reason"`                                 // 封禁原因
	BanStartTime int64  `gorm:"column:ban_start_time;not null;comment:封禁开始时间" json:"ban_start_time"`                       // 封禁开始时间
	BanEndTime   int64  `gorm:"column:ban_end_time;not null;comment:封禁结束时间" json:"ban_end_time"`                           // 封禁结束时间
	Status       int32  `gorm:"column:status;not null;default:1;comment:状态:1-生效中 2-已失效" json:"status"`                     // 状态:1-生效中 2-已失效
	Operator     string `gorm:"column:operator;not null;comment:操作人" json:"operator"`                                      // 操作人
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt    int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MUserBan's table name
func (*MUserBan) TableName() string {
	return TableNameMUserBan
}
