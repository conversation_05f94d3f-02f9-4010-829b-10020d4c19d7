package store

import (
	"context"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/query"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/mysql"

	"gorm.io/gorm"
)

var (
	_queryDB *query.Query
)

func InitQueryDB() {
	_queryDB = query.Use(mysql.DB(context.Background(), config.GlobConfig.Mysql.DBName))
}

func QueryDB() *query.Query {
	return _queryDB
}

func GOrmDB(ctx context.Context) *gorm.DB {
	return mysql.DB(ctx, config.GlobConfig.Mysql.DBName)
}
