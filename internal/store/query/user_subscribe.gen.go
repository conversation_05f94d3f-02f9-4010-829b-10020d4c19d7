// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newUserSubscribe(db *gorm.DB, opts ...gen.DOOption) userSubscribe {
	_userSubscribe := userSubscribe{}

	_userSubscribe.userSubscribeDo.UseDB(db, opts...)
	_userSubscribe.userSubscribeDo.UseModel(&model.UserSubscribe{})

	tableName := _userSubscribe.userSubscribeDo.TableName()
	_userSubscribe.ALL = field.NewAsterisk(tableName)
	_userSubscribe.ID = field.NewInt32(tableName, "id")
	_userSubscribe.UserID = field.NewString(tableName, "user_id")
	_userSubscribe.OpenID = field.NewString(tableName, "open_id")
	_userSubscribe.UnionID = field.NewString(tableName, "union_id")
	_userSubscribe.NickName = field.NewString(tableName, "nick_name")
	_userSubscribe.Gender = field.NewInt32(tableName, "gender")
	_userSubscribe.City = field.NewString(tableName, "city")
	_userSubscribe.Province = field.NewString(tableName, "province")
	_userSubscribe.Country = field.NewString(tableName, "country")
	_userSubscribe.AvatarURL = field.NewString(tableName, "avatar_url")
	_userSubscribe.Language = field.NewString(tableName, "language")
	_userSubscribe.WatermarkAppID = field.NewString(tableName, "watermark_app_id")
	_userSubscribe.WatermarkTimestamp = field.NewInt64(tableName, "watermark_timestamp")
	_userSubscribe.SessionKey = field.NewString(tableName, "session_key")
	_userSubscribe.CreatedAt = field.NewInt64(tableName, "created_at")
	_userSubscribe.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_userSubscribe.IsDeleted = field.NewBool(tableName, "is_deleted")

	_userSubscribe.fillFieldMap()

	return _userSubscribe
}

type userSubscribe struct {
	userSubscribeDo

	ALL                field.Asterisk
	ID                 field.Int32
	UserID             field.String // uuid
	OpenID             field.String
	UnionID            field.String
	NickName           field.String // 昵称
	Gender             field.Int32  // 性别
	City               field.String // 城市
	Province           field.String // 省份
	Country            field.String // 国家
	AvatarURL          field.String // 头像url
	Language           field.String // 语言
	WatermarkAppID     field.String // 水印应用id
	WatermarkTimestamp field.Int64  // 水印时间戳
	SessionKey         field.String // 会话密钥
	CreatedAt          field.Int64
	UpdatedAt          field.Int64
	IsDeleted          field.Bool

	fieldMap map[string]field.Expr
}

func (u userSubscribe) Table(newTableName string) *userSubscribe {
	u.userSubscribeDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userSubscribe) As(alias string) *userSubscribe {
	u.userSubscribeDo.DO = *(u.userSubscribeDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userSubscribe) updateTableName(table string) *userSubscribe {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewInt32(table, "id")
	u.UserID = field.NewString(table, "user_id")
	u.OpenID = field.NewString(table, "open_id")
	u.UnionID = field.NewString(table, "union_id")
	u.NickName = field.NewString(table, "nick_name")
	u.Gender = field.NewInt32(table, "gender")
	u.City = field.NewString(table, "city")
	u.Province = field.NewString(table, "province")
	u.Country = field.NewString(table, "country")
	u.AvatarURL = field.NewString(table, "avatar_url")
	u.Language = field.NewString(table, "language")
	u.WatermarkAppID = field.NewString(table, "watermark_app_id")
	u.WatermarkTimestamp = field.NewInt64(table, "watermark_timestamp")
	u.SessionKey = field.NewString(table, "session_key")
	u.CreatedAt = field.NewInt64(table, "created_at")
	u.UpdatedAt = field.NewInt64(table, "updated_at")
	u.IsDeleted = field.NewBool(table, "is_deleted")

	u.fillFieldMap()

	return u
}

func (u *userSubscribe) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userSubscribe) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 17)
	u.fieldMap["id"] = u.ID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["open_id"] = u.OpenID
	u.fieldMap["union_id"] = u.UnionID
	u.fieldMap["nick_name"] = u.NickName
	u.fieldMap["gender"] = u.Gender
	u.fieldMap["city"] = u.City
	u.fieldMap["province"] = u.Province
	u.fieldMap["country"] = u.Country
	u.fieldMap["avatar_url"] = u.AvatarURL
	u.fieldMap["language"] = u.Language
	u.fieldMap["watermark_app_id"] = u.WatermarkAppID
	u.fieldMap["watermark_timestamp"] = u.WatermarkTimestamp
	u.fieldMap["session_key"] = u.SessionKey
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
	u.fieldMap["is_deleted"] = u.IsDeleted
}

func (u userSubscribe) clone(db *gorm.DB) userSubscribe {
	u.userSubscribeDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userSubscribe) replaceDB(db *gorm.DB) userSubscribe {
	u.userSubscribeDo.ReplaceDB(db)
	return u
}

type userSubscribeDo struct{ gen.DO }

type IUserSubscribeDo interface {
	gen.SubQuery
	Debug() IUserSubscribeDo
	WithContext(ctx context.Context) IUserSubscribeDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserSubscribeDo
	WriteDB() IUserSubscribeDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserSubscribeDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserSubscribeDo
	Not(conds ...gen.Condition) IUserSubscribeDo
	Or(conds ...gen.Condition) IUserSubscribeDo
	Select(conds ...field.Expr) IUserSubscribeDo
	Where(conds ...gen.Condition) IUserSubscribeDo
	Order(conds ...field.Expr) IUserSubscribeDo
	Distinct(cols ...field.Expr) IUserSubscribeDo
	Omit(cols ...field.Expr) IUserSubscribeDo
	Join(table schema.Tabler, on ...field.Expr) IUserSubscribeDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserSubscribeDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserSubscribeDo
	Group(cols ...field.Expr) IUserSubscribeDo
	Having(conds ...gen.Condition) IUserSubscribeDo
	Limit(limit int) IUserSubscribeDo
	Offset(offset int) IUserSubscribeDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserSubscribeDo
	Unscoped() IUserSubscribeDo
	Create(values ...*model.UserSubscribe) error
	CreateInBatches(values []*model.UserSubscribe, batchSize int) error
	Save(values ...*model.UserSubscribe) error
	First() (*model.UserSubscribe, error)
	Take() (*model.UserSubscribe, error)
	Last() (*model.UserSubscribe, error)
	Find() ([]*model.UserSubscribe, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserSubscribe, err error)
	FindInBatches(result *[]*model.UserSubscribe, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.UserSubscribe) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserSubscribeDo
	Assign(attrs ...field.AssignExpr) IUserSubscribeDo
	Joins(fields ...field.RelationField) IUserSubscribeDo
	Preload(fields ...field.RelationField) IUserSubscribeDo
	FirstOrInit() (*model.UserSubscribe, error)
	FirstOrCreate() (*model.UserSubscribe, error)
	FindByPage(offset int, limit int) (result []*model.UserSubscribe, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserSubscribeDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userSubscribeDo) Debug() IUserSubscribeDo {
	return u.withDO(u.DO.Debug())
}

func (u userSubscribeDo) WithContext(ctx context.Context) IUserSubscribeDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userSubscribeDo) ReadDB() IUserSubscribeDo {
	return u.Clauses(dbresolver.Read)
}

func (u userSubscribeDo) WriteDB() IUserSubscribeDo {
	return u.Clauses(dbresolver.Write)
}

func (u userSubscribeDo) Session(config *gorm.Session) IUserSubscribeDo {
	return u.withDO(u.DO.Session(config))
}

func (u userSubscribeDo) Clauses(conds ...clause.Expression) IUserSubscribeDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userSubscribeDo) Returning(value interface{}, columns ...string) IUserSubscribeDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userSubscribeDo) Not(conds ...gen.Condition) IUserSubscribeDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userSubscribeDo) Or(conds ...gen.Condition) IUserSubscribeDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userSubscribeDo) Select(conds ...field.Expr) IUserSubscribeDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userSubscribeDo) Where(conds ...gen.Condition) IUserSubscribeDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userSubscribeDo) Order(conds ...field.Expr) IUserSubscribeDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userSubscribeDo) Distinct(cols ...field.Expr) IUserSubscribeDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userSubscribeDo) Omit(cols ...field.Expr) IUserSubscribeDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userSubscribeDo) Join(table schema.Tabler, on ...field.Expr) IUserSubscribeDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userSubscribeDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserSubscribeDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userSubscribeDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserSubscribeDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userSubscribeDo) Group(cols ...field.Expr) IUserSubscribeDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userSubscribeDo) Having(conds ...gen.Condition) IUserSubscribeDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userSubscribeDo) Limit(limit int) IUserSubscribeDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userSubscribeDo) Offset(offset int) IUserSubscribeDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userSubscribeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserSubscribeDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userSubscribeDo) Unscoped() IUserSubscribeDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userSubscribeDo) Create(values ...*model.UserSubscribe) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userSubscribeDo) CreateInBatches(values []*model.UserSubscribe, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userSubscribeDo) Save(values ...*model.UserSubscribe) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userSubscribeDo) First() (*model.UserSubscribe, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSubscribe), nil
	}
}

func (u userSubscribeDo) Take() (*model.UserSubscribe, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSubscribe), nil
	}
}

func (u userSubscribeDo) Last() (*model.UserSubscribe, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSubscribe), nil
	}
}

func (u userSubscribeDo) Find() ([]*model.UserSubscribe, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserSubscribe), err
}

func (u userSubscribeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserSubscribe, err error) {
	buf := make([]*model.UserSubscribe, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userSubscribeDo) FindInBatches(result *[]*model.UserSubscribe, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userSubscribeDo) Attrs(attrs ...field.AssignExpr) IUserSubscribeDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userSubscribeDo) Assign(attrs ...field.AssignExpr) IUserSubscribeDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userSubscribeDo) Joins(fields ...field.RelationField) IUserSubscribeDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userSubscribeDo) Preload(fields ...field.RelationField) IUserSubscribeDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userSubscribeDo) FirstOrInit() (*model.UserSubscribe, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSubscribe), nil
	}
}

func (u userSubscribeDo) FirstOrCreate() (*model.UserSubscribe, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSubscribe), nil
	}
}

func (u userSubscribeDo) FindByPage(offset int, limit int) (result []*model.UserSubscribe, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userSubscribeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userSubscribeDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userSubscribeDo) Delete(models ...*model.UserSubscribe) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userSubscribeDo) withDO(do gen.Dao) *userSubscribeDo {
	u.DO = *do.(*gen.DO)
	return u
}
