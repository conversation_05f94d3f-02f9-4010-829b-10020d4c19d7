// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMRedemptionCode(db *gorm.DB, opts ...gen.DOOption) mRedemptionCode {
	_mRedemptionCode := mRedemptionCode{}

	_mRedemptionCode.mRedemptionCodeDo.UseDB(db, opts...)
	_mRedemptionCode.mRedemptionCodeDo.UseModel(&model.MRedemptionCode{})

	tableName := _mRedemptionCode.mRedemptionCodeDo.TableName()
	_mRedemptionCode.ALL = field.NewAsterisk(tableName)
	_mRedemptionCode.ID = field.NewInt32(tableName, "id")
	_mRedemptionCode.UUID = field.NewString(tableName, "uuid")
	_mRedemptionCode.GameID = field.NewString(tableName, "game_id")
	_mRedemptionCode.Batch = field.NewString(tableName, "batch")
	_mRedemptionCode.Title = field.NewString(tableName, "title")
	_mRedemptionCode.CodeType = field.NewInt32(tableName, "code_type")
	_mRedemptionCode.Status = field.NewInt32(tableName, "status")
	_mRedemptionCode.Description = field.NewString(tableName, "description")
	_mRedemptionCode.Content = field.NewString(tableName, "content")
	_mRedemptionCode.CommencementDate = field.NewInt64(tableName, "commencement_date")
	_mRedemptionCode.ExpiredDate = field.NewInt64(tableName, "expired_date")
	_mRedemptionCode.Number = field.NewInt32(tableName, "number")
	_mRedemptionCode.Frequency = field.NewInt32(tableName, "frequency")
	_mRedemptionCode.Slogan = field.NewString(tableName, "slogan")
	_mRedemptionCode.RemainingCode = field.NewInt32(tableName, "remaining_code")
	_mRedemptionCode.CreatorID = field.NewString(tableName, "creator_id")
	_mRedemptionCode.CreatedAt = field.NewInt64(tableName, "created_at")
	_mRedemptionCode.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mRedemptionCode.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mRedemptionCode.fillFieldMap()

	return _mRedemptionCode
}

type mRedemptionCode struct {
	mRedemptionCodeDo

	ALL              field.Asterisk
	ID               field.Int32
	UUID             field.String
	GameID           field.String
	Batch            field.String
	Title            field.String // 名称
	CodeType         field.Int32  // 1 普通码 2 通兑码
	Status           field.Int32  // 状态 1 正常开启 2 关闭状态 3 正在生成
	Description      field.String // 描述
	Content          field.String
	CommencementDate field.Int64
	ExpiredDate      field.Int64
	Number           field.Int32 // 兑换码数量
	Frequency        field.Int32 // 兑换码次数
	Slogan           field.String
	RemainingCode    field.Int32 // 兑换剩余
	CreatorID        field.String
	CreatedAt        field.Int64
	UpdatedAt        field.Int64
	IsDeleted        field.Bool

	fieldMap map[string]field.Expr
}

func (m mRedemptionCode) Table(newTableName string) *mRedemptionCode {
	m.mRedemptionCodeDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mRedemptionCode) As(alias string) *mRedemptionCode {
	m.mRedemptionCodeDo.DO = *(m.mRedemptionCodeDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mRedemptionCode) updateTableName(table string) *mRedemptionCode {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.UUID = field.NewString(table, "uuid")
	m.GameID = field.NewString(table, "game_id")
	m.Batch = field.NewString(table, "batch")
	m.Title = field.NewString(table, "title")
	m.CodeType = field.NewInt32(table, "code_type")
	m.Status = field.NewInt32(table, "status")
	m.Description = field.NewString(table, "description")
	m.Content = field.NewString(table, "content")
	m.CommencementDate = field.NewInt64(table, "commencement_date")
	m.ExpiredDate = field.NewInt64(table, "expired_date")
	m.Number = field.NewInt32(table, "number")
	m.Frequency = field.NewInt32(table, "frequency")
	m.Slogan = field.NewString(table, "slogan")
	m.RemainingCode = field.NewInt32(table, "remaining_code")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mRedemptionCode) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mRedemptionCode) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 19)
	m.fieldMap["id"] = m.ID
	m.fieldMap["uuid"] = m.UUID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["batch"] = m.Batch
	m.fieldMap["title"] = m.Title
	m.fieldMap["code_type"] = m.CodeType
	m.fieldMap["status"] = m.Status
	m.fieldMap["description"] = m.Description
	m.fieldMap["content"] = m.Content
	m.fieldMap["commencement_date"] = m.CommencementDate
	m.fieldMap["expired_date"] = m.ExpiredDate
	m.fieldMap["number"] = m.Number
	m.fieldMap["frequency"] = m.Frequency
	m.fieldMap["slogan"] = m.Slogan
	m.fieldMap["remaining_code"] = m.RemainingCode
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mRedemptionCode) clone(db *gorm.DB) mRedemptionCode {
	m.mRedemptionCodeDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mRedemptionCode) replaceDB(db *gorm.DB) mRedemptionCode {
	m.mRedemptionCodeDo.ReplaceDB(db)
	return m
}

type mRedemptionCodeDo struct{ gen.DO }

type IMRedemptionCodeDo interface {
	gen.SubQuery
	Debug() IMRedemptionCodeDo
	WithContext(ctx context.Context) IMRedemptionCodeDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMRedemptionCodeDo
	WriteDB() IMRedemptionCodeDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMRedemptionCodeDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMRedemptionCodeDo
	Not(conds ...gen.Condition) IMRedemptionCodeDo
	Or(conds ...gen.Condition) IMRedemptionCodeDo
	Select(conds ...field.Expr) IMRedemptionCodeDo
	Where(conds ...gen.Condition) IMRedemptionCodeDo
	Order(conds ...field.Expr) IMRedemptionCodeDo
	Distinct(cols ...field.Expr) IMRedemptionCodeDo
	Omit(cols ...field.Expr) IMRedemptionCodeDo
	Join(table schema.Tabler, on ...field.Expr) IMRedemptionCodeDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMRedemptionCodeDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMRedemptionCodeDo
	Group(cols ...field.Expr) IMRedemptionCodeDo
	Having(conds ...gen.Condition) IMRedemptionCodeDo
	Limit(limit int) IMRedemptionCodeDo
	Offset(offset int) IMRedemptionCodeDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMRedemptionCodeDo
	Unscoped() IMRedemptionCodeDo
	Create(values ...*model.MRedemptionCode) error
	CreateInBatches(values []*model.MRedemptionCode, batchSize int) error
	Save(values ...*model.MRedemptionCode) error
	First() (*model.MRedemptionCode, error)
	Take() (*model.MRedemptionCode, error)
	Last() (*model.MRedemptionCode, error)
	Find() ([]*model.MRedemptionCode, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MRedemptionCode, err error)
	FindInBatches(result *[]*model.MRedemptionCode, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MRedemptionCode) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMRedemptionCodeDo
	Assign(attrs ...field.AssignExpr) IMRedemptionCodeDo
	Joins(fields ...field.RelationField) IMRedemptionCodeDo
	Preload(fields ...field.RelationField) IMRedemptionCodeDo
	FirstOrInit() (*model.MRedemptionCode, error)
	FirstOrCreate() (*model.MRedemptionCode, error)
	FindByPage(offset int, limit int) (result []*model.MRedemptionCode, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMRedemptionCodeDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mRedemptionCodeDo) Debug() IMRedemptionCodeDo {
	return m.withDO(m.DO.Debug())
}

func (m mRedemptionCodeDo) WithContext(ctx context.Context) IMRedemptionCodeDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mRedemptionCodeDo) ReadDB() IMRedemptionCodeDo {
	return m.Clauses(dbresolver.Read)
}

func (m mRedemptionCodeDo) WriteDB() IMRedemptionCodeDo {
	return m.Clauses(dbresolver.Write)
}

func (m mRedemptionCodeDo) Session(config *gorm.Session) IMRedemptionCodeDo {
	return m.withDO(m.DO.Session(config))
}

func (m mRedemptionCodeDo) Clauses(conds ...clause.Expression) IMRedemptionCodeDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mRedemptionCodeDo) Returning(value interface{}, columns ...string) IMRedemptionCodeDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mRedemptionCodeDo) Not(conds ...gen.Condition) IMRedemptionCodeDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mRedemptionCodeDo) Or(conds ...gen.Condition) IMRedemptionCodeDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mRedemptionCodeDo) Select(conds ...field.Expr) IMRedemptionCodeDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mRedemptionCodeDo) Where(conds ...gen.Condition) IMRedemptionCodeDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mRedemptionCodeDo) Order(conds ...field.Expr) IMRedemptionCodeDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mRedemptionCodeDo) Distinct(cols ...field.Expr) IMRedemptionCodeDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mRedemptionCodeDo) Omit(cols ...field.Expr) IMRedemptionCodeDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mRedemptionCodeDo) Join(table schema.Tabler, on ...field.Expr) IMRedemptionCodeDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mRedemptionCodeDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMRedemptionCodeDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mRedemptionCodeDo) RightJoin(table schema.Tabler, on ...field.Expr) IMRedemptionCodeDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mRedemptionCodeDo) Group(cols ...field.Expr) IMRedemptionCodeDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mRedemptionCodeDo) Having(conds ...gen.Condition) IMRedemptionCodeDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mRedemptionCodeDo) Limit(limit int) IMRedemptionCodeDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mRedemptionCodeDo) Offset(offset int) IMRedemptionCodeDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mRedemptionCodeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMRedemptionCodeDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mRedemptionCodeDo) Unscoped() IMRedemptionCodeDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mRedemptionCodeDo) Create(values ...*model.MRedemptionCode) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mRedemptionCodeDo) CreateInBatches(values []*model.MRedemptionCode, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mRedemptionCodeDo) Save(values ...*model.MRedemptionCode) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mRedemptionCodeDo) First() (*model.MRedemptionCode, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCode), nil
	}
}

func (m mRedemptionCodeDo) Take() (*model.MRedemptionCode, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCode), nil
	}
}

func (m mRedemptionCodeDo) Last() (*model.MRedemptionCode, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCode), nil
	}
}

func (m mRedemptionCodeDo) Find() ([]*model.MRedemptionCode, error) {
	result, err := m.DO.Find()
	return result.([]*model.MRedemptionCode), err
}

func (m mRedemptionCodeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MRedemptionCode, err error) {
	buf := make([]*model.MRedemptionCode, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mRedemptionCodeDo) FindInBatches(result *[]*model.MRedemptionCode, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mRedemptionCodeDo) Attrs(attrs ...field.AssignExpr) IMRedemptionCodeDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mRedemptionCodeDo) Assign(attrs ...field.AssignExpr) IMRedemptionCodeDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mRedemptionCodeDo) Joins(fields ...field.RelationField) IMRedemptionCodeDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mRedemptionCodeDo) Preload(fields ...field.RelationField) IMRedemptionCodeDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mRedemptionCodeDo) FirstOrInit() (*model.MRedemptionCode, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCode), nil
	}
}

func (m mRedemptionCodeDo) FirstOrCreate() (*model.MRedemptionCode, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCode), nil
	}
}

func (m mRedemptionCodeDo) FindByPage(offset int, limit int) (result []*model.MRedemptionCode, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mRedemptionCodeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mRedemptionCodeDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mRedemptionCodeDo) Delete(models ...*model.MRedemptionCode) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mRedemptionCodeDo) withDO(do gen.Dao) *mRedemptionCodeDo {
	m.DO = *do.(*gen.DO)
	return m
}
