// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMMonitorContentProcessing(db *gorm.DB, opts ...gen.DOOption) mMonitorContentProcessing {
	_mMonitorContentProcessing := mMonitorContentProcessing{}

	_mMonitorContentProcessing.mMonitorContentProcessingDo.UseDB(db, opts...)
	_mMonitorContentProcessing.mMonitorContentProcessingDo.UseModel(&model.MMonitorContentProcessing{})

	tableName := _mMonitorContentProcessing.mMonitorContentProcessingDo.TableName()
	_mMonitorContentProcessing.ALL = field.NewAsterisk(tableName)
	_mMonitorContentProcessing.ID = field.NewInt64(tableName, "id")
	_mMonitorContentProcessing.GameID = field.NewString(tableName, "game_id")
	_mMonitorContentProcessing.ProcessingID = field.NewString(tableName, "processing_id")
	_mMonitorContentProcessing.ContentID = field.NewString(tableName, "content_id")
	_mMonitorContentProcessing.PlatformID = field.NewString(tableName, "platform_id")
	_mMonitorContentProcessing.Operations = field.NewString(tableName, "operations")
	_mMonitorContentProcessing.Operator = field.NewString(tableName, "operator")
	_mMonitorContentProcessing.CreatedAt = field.NewInt64(tableName, "created_at")
	_mMonitorContentProcessing.ContentSnapshot = field.NewString(tableName, "content_snapshot")

	_mMonitorContentProcessing.fillFieldMap()

	return _mMonitorContentProcessing
}

// mMonitorContentProcessing 内容处理记录表
type mMonitorContentProcessing struct {
	mMonitorContentProcessingDo

	ALL             field.Asterisk
	ID              field.Int64 // 主键ID
	GameID          field.String
	ProcessingID    field.String // 处理记录唯一标识
	ContentID       field.String // 关联的内容ID
	PlatformID      field.String // 平台ID
	Operations      field.String // 处理操作列表
	Operator        field.String // 操作人
	CreatedAt       field.Int64  // 创建时间戳
	ContentSnapshot field.String // 内容快照

	fieldMap map[string]field.Expr
}

func (m mMonitorContentProcessing) Table(newTableName string) *mMonitorContentProcessing {
	m.mMonitorContentProcessingDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mMonitorContentProcessing) As(alias string) *mMonitorContentProcessing {
	m.mMonitorContentProcessingDo.DO = *(m.mMonitorContentProcessingDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mMonitorContentProcessing) updateTableName(table string) *mMonitorContentProcessing {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt64(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.ProcessingID = field.NewString(table, "processing_id")
	m.ContentID = field.NewString(table, "content_id")
	m.PlatformID = field.NewString(table, "platform_id")
	m.Operations = field.NewString(table, "operations")
	m.Operator = field.NewString(table, "operator")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.ContentSnapshot = field.NewString(table, "content_snapshot")

	m.fillFieldMap()

	return m
}

func (m *mMonitorContentProcessing) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mMonitorContentProcessing) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 9)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["processing_id"] = m.ProcessingID
	m.fieldMap["content_id"] = m.ContentID
	m.fieldMap["platform_id"] = m.PlatformID
	m.fieldMap["operations"] = m.Operations
	m.fieldMap["operator"] = m.Operator
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["content_snapshot"] = m.ContentSnapshot
}

func (m mMonitorContentProcessing) clone(db *gorm.DB) mMonitorContentProcessing {
	m.mMonitorContentProcessingDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mMonitorContentProcessing) replaceDB(db *gorm.DB) mMonitorContentProcessing {
	m.mMonitorContentProcessingDo.ReplaceDB(db)
	return m
}

type mMonitorContentProcessingDo struct{ gen.DO }

type IMMonitorContentProcessingDo interface {
	gen.SubQuery
	Debug() IMMonitorContentProcessingDo
	WithContext(ctx context.Context) IMMonitorContentProcessingDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMMonitorContentProcessingDo
	WriteDB() IMMonitorContentProcessingDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMMonitorContentProcessingDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMMonitorContentProcessingDo
	Not(conds ...gen.Condition) IMMonitorContentProcessingDo
	Or(conds ...gen.Condition) IMMonitorContentProcessingDo
	Select(conds ...field.Expr) IMMonitorContentProcessingDo
	Where(conds ...gen.Condition) IMMonitorContentProcessingDo
	Order(conds ...field.Expr) IMMonitorContentProcessingDo
	Distinct(cols ...field.Expr) IMMonitorContentProcessingDo
	Omit(cols ...field.Expr) IMMonitorContentProcessingDo
	Join(table schema.Tabler, on ...field.Expr) IMMonitorContentProcessingDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMMonitorContentProcessingDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMMonitorContentProcessingDo
	Group(cols ...field.Expr) IMMonitorContentProcessingDo
	Having(conds ...gen.Condition) IMMonitorContentProcessingDo
	Limit(limit int) IMMonitorContentProcessingDo
	Offset(offset int) IMMonitorContentProcessingDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMMonitorContentProcessingDo
	Unscoped() IMMonitorContentProcessingDo
	Create(values ...*model.MMonitorContentProcessing) error
	CreateInBatches(values []*model.MMonitorContentProcessing, batchSize int) error
	Save(values ...*model.MMonitorContentProcessing) error
	First() (*model.MMonitorContentProcessing, error)
	Take() (*model.MMonitorContentProcessing, error)
	Last() (*model.MMonitorContentProcessing, error)
	Find() ([]*model.MMonitorContentProcessing, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MMonitorContentProcessing, err error)
	FindInBatches(result *[]*model.MMonitorContentProcessing, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MMonitorContentProcessing) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMMonitorContentProcessingDo
	Assign(attrs ...field.AssignExpr) IMMonitorContentProcessingDo
	Joins(fields ...field.RelationField) IMMonitorContentProcessingDo
	Preload(fields ...field.RelationField) IMMonitorContentProcessingDo
	FirstOrInit() (*model.MMonitorContentProcessing, error)
	FirstOrCreate() (*model.MMonitorContentProcessing, error)
	FindByPage(offset int, limit int) (result []*model.MMonitorContentProcessing, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMMonitorContentProcessingDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mMonitorContentProcessingDo) Debug() IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Debug())
}

func (m mMonitorContentProcessingDo) WithContext(ctx context.Context) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mMonitorContentProcessingDo) ReadDB() IMMonitorContentProcessingDo {
	return m.Clauses(dbresolver.Read)
}

func (m mMonitorContentProcessingDo) WriteDB() IMMonitorContentProcessingDo {
	return m.Clauses(dbresolver.Write)
}

func (m mMonitorContentProcessingDo) Session(config *gorm.Session) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Session(config))
}

func (m mMonitorContentProcessingDo) Clauses(conds ...clause.Expression) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mMonitorContentProcessingDo) Returning(value interface{}, columns ...string) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mMonitorContentProcessingDo) Not(conds ...gen.Condition) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mMonitorContentProcessingDo) Or(conds ...gen.Condition) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mMonitorContentProcessingDo) Select(conds ...field.Expr) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mMonitorContentProcessingDo) Where(conds ...gen.Condition) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mMonitorContentProcessingDo) Order(conds ...field.Expr) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mMonitorContentProcessingDo) Distinct(cols ...field.Expr) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mMonitorContentProcessingDo) Omit(cols ...field.Expr) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mMonitorContentProcessingDo) Join(table schema.Tabler, on ...field.Expr) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mMonitorContentProcessingDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mMonitorContentProcessingDo) RightJoin(table schema.Tabler, on ...field.Expr) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mMonitorContentProcessingDo) Group(cols ...field.Expr) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mMonitorContentProcessingDo) Having(conds ...gen.Condition) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mMonitorContentProcessingDo) Limit(limit int) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mMonitorContentProcessingDo) Offset(offset int) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mMonitorContentProcessingDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mMonitorContentProcessingDo) Unscoped() IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mMonitorContentProcessingDo) Create(values ...*model.MMonitorContentProcessing) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mMonitorContentProcessingDo) CreateInBatches(values []*model.MMonitorContentProcessing, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mMonitorContentProcessingDo) Save(values ...*model.MMonitorContentProcessing) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mMonitorContentProcessingDo) First() (*model.MMonitorContentProcessing, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentProcessing), nil
	}
}

func (m mMonitorContentProcessingDo) Take() (*model.MMonitorContentProcessing, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentProcessing), nil
	}
}

func (m mMonitorContentProcessingDo) Last() (*model.MMonitorContentProcessing, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentProcessing), nil
	}
}

func (m mMonitorContentProcessingDo) Find() ([]*model.MMonitorContentProcessing, error) {
	result, err := m.DO.Find()
	return result.([]*model.MMonitorContentProcessing), err
}

func (m mMonitorContentProcessingDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MMonitorContentProcessing, err error) {
	buf := make([]*model.MMonitorContentProcessing, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mMonitorContentProcessingDo) FindInBatches(result *[]*model.MMonitorContentProcessing, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mMonitorContentProcessingDo) Attrs(attrs ...field.AssignExpr) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mMonitorContentProcessingDo) Assign(attrs ...field.AssignExpr) IMMonitorContentProcessingDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mMonitorContentProcessingDo) Joins(fields ...field.RelationField) IMMonitorContentProcessingDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mMonitorContentProcessingDo) Preload(fields ...field.RelationField) IMMonitorContentProcessingDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mMonitorContentProcessingDo) FirstOrInit() (*model.MMonitorContentProcessing, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentProcessing), nil
	}
}

func (m mMonitorContentProcessingDo) FirstOrCreate() (*model.MMonitorContentProcessing, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentProcessing), nil
	}
}

func (m mMonitorContentProcessingDo) FindByPage(offset int, limit int) (result []*model.MMonitorContentProcessing, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mMonitorContentProcessingDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mMonitorContentProcessingDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mMonitorContentProcessingDo) Delete(models ...*model.MMonitorContentProcessing) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mMonitorContentProcessingDo) withDO(do gen.Dao) *mMonitorContentProcessingDo {
	m.DO = *do.(*gen.DO)
	return m
}
