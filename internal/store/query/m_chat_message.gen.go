// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMChatMessage(db *gorm.DB, opts ...gen.DOOption) mChatMessage {
	_mChatMessage := mChatMessage{}

	_mChatMessage.mChatMessageDo.UseDB(db, opts...)
	_mChatMessage.mChatMessageDo.UseModel(&model.MChatMessage{})

	tableName := _mChatMessage.mChatMessageDo.TableName()
	_mChatMessage.ALL = field.NewAsterisk(tableName)
	_mChatMessage.ID = field.NewInt32(tableName, "id")
	_mChatMessage.GameID = field.NewString(tableName, "game_id")
	_mChatMessage.UserID = field.NewString(tableName, "user_id")
	_mChatMessage.OpenID = field.NewString(tableName, "open_id")
	_mChatMessage.MiniprogramOpenID = field.NewString(tableName, "miniprogram_open_id")
	_mChatMessage.MessageType = field.NewInt32(tableName, "message_type")
	_mChatMessage.Content = field.NewString(tableName, "content")
	_mChatMessage.FeedbackType = field.NewInt32(tableName, "feedback_type")
	_mChatMessage.CreatedAt = field.NewInt64(tableName, "created_at")
	_mChatMessage.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mChatMessage.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mChatMessage.fillFieldMap()

	return _mChatMessage
}

// mChatMessage 聊天消息表
type mChatMessage struct {
	mChatMessageDo

	ALL               field.Asterisk
	ID                field.Int32  // 主键ID
	GameID            field.String // 游戏ID
	UserID            field.String // 用户ID
	OpenID            field.String // 小程序OpenID
	MiniprogramOpenID field.String
	MessageType       field.Int32  // 消息类型：1-用户消息，2-系统消息
	Content           field.String // 消息内容
	FeedbackType      field.Int32  // 反馈类型：0-无反馈，1-有用，2-无用
	CreatedAt         field.Int64  // 创建时间戳
	UpdatedAt         field.Int64  // 更新时间戳
	IsDeleted         field.Bool   // 逻辑删除，0=未删，1=已删

	fieldMap map[string]field.Expr
}

func (m mChatMessage) Table(newTableName string) *mChatMessage {
	m.mChatMessageDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mChatMessage) As(alias string) *mChatMessage {
	m.mChatMessageDo.DO = *(m.mChatMessageDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mChatMessage) updateTableName(table string) *mChatMessage {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.UserID = field.NewString(table, "user_id")
	m.OpenID = field.NewString(table, "open_id")
	m.MiniprogramOpenID = field.NewString(table, "miniprogram_open_id")
	m.MessageType = field.NewInt32(table, "message_type")
	m.Content = field.NewString(table, "content")
	m.FeedbackType = field.NewInt32(table, "feedback_type")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mChatMessage) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mChatMessage) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 11)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["open_id"] = m.OpenID
	m.fieldMap["miniprogram_open_id"] = m.MiniprogramOpenID
	m.fieldMap["message_type"] = m.MessageType
	m.fieldMap["content"] = m.Content
	m.fieldMap["feedback_type"] = m.FeedbackType
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mChatMessage) clone(db *gorm.DB) mChatMessage {
	m.mChatMessageDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mChatMessage) replaceDB(db *gorm.DB) mChatMessage {
	m.mChatMessageDo.ReplaceDB(db)
	return m
}

type mChatMessageDo struct{ gen.DO }

type IMChatMessageDo interface {
	gen.SubQuery
	Debug() IMChatMessageDo
	WithContext(ctx context.Context) IMChatMessageDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMChatMessageDo
	WriteDB() IMChatMessageDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMChatMessageDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMChatMessageDo
	Not(conds ...gen.Condition) IMChatMessageDo
	Or(conds ...gen.Condition) IMChatMessageDo
	Select(conds ...field.Expr) IMChatMessageDo
	Where(conds ...gen.Condition) IMChatMessageDo
	Order(conds ...field.Expr) IMChatMessageDo
	Distinct(cols ...field.Expr) IMChatMessageDo
	Omit(cols ...field.Expr) IMChatMessageDo
	Join(table schema.Tabler, on ...field.Expr) IMChatMessageDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMChatMessageDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMChatMessageDo
	Group(cols ...field.Expr) IMChatMessageDo
	Having(conds ...gen.Condition) IMChatMessageDo
	Limit(limit int) IMChatMessageDo
	Offset(offset int) IMChatMessageDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMChatMessageDo
	Unscoped() IMChatMessageDo
	Create(values ...*model.MChatMessage) error
	CreateInBatches(values []*model.MChatMessage, batchSize int) error
	Save(values ...*model.MChatMessage) error
	First() (*model.MChatMessage, error)
	Take() (*model.MChatMessage, error)
	Last() (*model.MChatMessage, error)
	Find() ([]*model.MChatMessage, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MChatMessage, err error)
	FindInBatches(result *[]*model.MChatMessage, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MChatMessage) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMChatMessageDo
	Assign(attrs ...field.AssignExpr) IMChatMessageDo
	Joins(fields ...field.RelationField) IMChatMessageDo
	Preload(fields ...field.RelationField) IMChatMessageDo
	FirstOrInit() (*model.MChatMessage, error)
	FirstOrCreate() (*model.MChatMessage, error)
	FindByPage(offset int, limit int) (result []*model.MChatMessage, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMChatMessageDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mChatMessageDo) Debug() IMChatMessageDo {
	return m.withDO(m.DO.Debug())
}

func (m mChatMessageDo) WithContext(ctx context.Context) IMChatMessageDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mChatMessageDo) ReadDB() IMChatMessageDo {
	return m.Clauses(dbresolver.Read)
}

func (m mChatMessageDo) WriteDB() IMChatMessageDo {
	return m.Clauses(dbresolver.Write)
}

func (m mChatMessageDo) Session(config *gorm.Session) IMChatMessageDo {
	return m.withDO(m.DO.Session(config))
}

func (m mChatMessageDo) Clauses(conds ...clause.Expression) IMChatMessageDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mChatMessageDo) Returning(value interface{}, columns ...string) IMChatMessageDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mChatMessageDo) Not(conds ...gen.Condition) IMChatMessageDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mChatMessageDo) Or(conds ...gen.Condition) IMChatMessageDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mChatMessageDo) Select(conds ...field.Expr) IMChatMessageDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mChatMessageDo) Where(conds ...gen.Condition) IMChatMessageDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mChatMessageDo) Order(conds ...field.Expr) IMChatMessageDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mChatMessageDo) Distinct(cols ...field.Expr) IMChatMessageDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mChatMessageDo) Omit(cols ...field.Expr) IMChatMessageDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mChatMessageDo) Join(table schema.Tabler, on ...field.Expr) IMChatMessageDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mChatMessageDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMChatMessageDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mChatMessageDo) RightJoin(table schema.Tabler, on ...field.Expr) IMChatMessageDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mChatMessageDo) Group(cols ...field.Expr) IMChatMessageDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mChatMessageDo) Having(conds ...gen.Condition) IMChatMessageDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mChatMessageDo) Limit(limit int) IMChatMessageDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mChatMessageDo) Offset(offset int) IMChatMessageDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mChatMessageDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMChatMessageDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mChatMessageDo) Unscoped() IMChatMessageDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mChatMessageDo) Create(values ...*model.MChatMessage) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mChatMessageDo) CreateInBatches(values []*model.MChatMessage, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mChatMessageDo) Save(values ...*model.MChatMessage) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mChatMessageDo) First() (*model.MChatMessage, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MChatMessage), nil
	}
}

func (m mChatMessageDo) Take() (*model.MChatMessage, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MChatMessage), nil
	}
}

func (m mChatMessageDo) Last() (*model.MChatMessage, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MChatMessage), nil
	}
}

func (m mChatMessageDo) Find() ([]*model.MChatMessage, error) {
	result, err := m.DO.Find()
	return result.([]*model.MChatMessage), err
}

func (m mChatMessageDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MChatMessage, err error) {
	buf := make([]*model.MChatMessage, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mChatMessageDo) FindInBatches(result *[]*model.MChatMessage, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mChatMessageDo) Attrs(attrs ...field.AssignExpr) IMChatMessageDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mChatMessageDo) Assign(attrs ...field.AssignExpr) IMChatMessageDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mChatMessageDo) Joins(fields ...field.RelationField) IMChatMessageDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mChatMessageDo) Preload(fields ...field.RelationField) IMChatMessageDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mChatMessageDo) FirstOrInit() (*model.MChatMessage, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MChatMessage), nil
	}
}

func (m mChatMessageDo) FirstOrCreate() (*model.MChatMessage, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MChatMessage), nil
	}
}

func (m mChatMessageDo) FindByPage(offset int, limit int) (result []*model.MChatMessage, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mChatMessageDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mChatMessageDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mChatMessageDo) Delete(models ...*model.MChatMessage) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mChatMessageDo) withDO(do gen.Dao) *mChatMessageDo {
	m.DO = *do.(*gen.DO)
	return m
}
