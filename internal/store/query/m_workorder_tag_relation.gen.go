// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMWorkorderTagRelation(db *gorm.DB, opts ...gen.DOOption) mWorkorderTagRelation {
	_mWorkorderTagRelation := mWorkorderTagRelation{}

	_mWorkorderTagRelation.mWorkorderTagRelationDo.UseDB(db, opts...)
	_mWorkorderTagRelation.mWorkorderTagRelationDo.UseModel(&model.MWorkorderTagRelation{})

	tableName := _mWorkorderTagRelation.mWorkorderTagRelationDo.TableName()
	_mWorkorderTagRelation.ALL = field.NewAsterisk(tableName)
	_mWorkorderTagRelation.ID = field.NewInt32(tableName, "id")
	_mWorkorderTagRelation.OrderID = field.NewString(tableName, "order_id")
	_mWorkorderTagRelation.TagID = field.NewInt32(tableName, "tag_id")
	_mWorkorderTagRelation.CreatedAt = field.NewInt64(tableName, "created_at")
	_mWorkorderTagRelation.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mWorkorderTagRelation.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mWorkorderTagRelation.fillFieldMap()

	return _mWorkorderTagRelation
}

// mWorkorderTagRelation 工单-标签关联表
type mWorkorderTagRelation struct {
	mWorkorderTagRelationDo

	ALL       field.Asterisk
	ID        field.Int32
	OrderID   field.String // 工单ID
	TagID     field.Int32  // 标签ID
	CreatedAt field.Int64
	UpdatedAt field.Int64
	IsDeleted field.Bool

	fieldMap map[string]field.Expr
}

func (m mWorkorderTagRelation) Table(newTableName string) *mWorkorderTagRelation {
	m.mWorkorderTagRelationDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mWorkorderTagRelation) As(alias string) *mWorkorderTagRelation {
	m.mWorkorderTagRelationDo.DO = *(m.mWorkorderTagRelationDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mWorkorderTagRelation) updateTableName(table string) *mWorkorderTagRelation {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.OrderID = field.NewString(table, "order_id")
	m.TagID = field.NewInt32(table, "tag_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mWorkorderTagRelation) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mWorkorderTagRelation) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 6)
	m.fieldMap["id"] = m.ID
	m.fieldMap["order_id"] = m.OrderID
	m.fieldMap["tag_id"] = m.TagID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mWorkorderTagRelation) clone(db *gorm.DB) mWorkorderTagRelation {
	m.mWorkorderTagRelationDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mWorkorderTagRelation) replaceDB(db *gorm.DB) mWorkorderTagRelation {
	m.mWorkorderTagRelationDo.ReplaceDB(db)
	return m
}

type mWorkorderTagRelationDo struct{ gen.DO }

type IMWorkorderTagRelationDo interface {
	gen.SubQuery
	Debug() IMWorkorderTagRelationDo
	WithContext(ctx context.Context) IMWorkorderTagRelationDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMWorkorderTagRelationDo
	WriteDB() IMWorkorderTagRelationDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMWorkorderTagRelationDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMWorkorderTagRelationDo
	Not(conds ...gen.Condition) IMWorkorderTagRelationDo
	Or(conds ...gen.Condition) IMWorkorderTagRelationDo
	Select(conds ...field.Expr) IMWorkorderTagRelationDo
	Where(conds ...gen.Condition) IMWorkorderTagRelationDo
	Order(conds ...field.Expr) IMWorkorderTagRelationDo
	Distinct(cols ...field.Expr) IMWorkorderTagRelationDo
	Omit(cols ...field.Expr) IMWorkorderTagRelationDo
	Join(table schema.Tabler, on ...field.Expr) IMWorkorderTagRelationDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderTagRelationDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderTagRelationDo
	Group(cols ...field.Expr) IMWorkorderTagRelationDo
	Having(conds ...gen.Condition) IMWorkorderTagRelationDo
	Limit(limit int) IMWorkorderTagRelationDo
	Offset(offset int) IMWorkorderTagRelationDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderTagRelationDo
	Unscoped() IMWorkorderTagRelationDo
	Create(values ...*model.MWorkorderTagRelation) error
	CreateInBatches(values []*model.MWorkorderTagRelation, batchSize int) error
	Save(values ...*model.MWorkorderTagRelation) error
	First() (*model.MWorkorderTagRelation, error)
	Take() (*model.MWorkorderTagRelation, error)
	Last() (*model.MWorkorderTagRelation, error)
	Find() ([]*model.MWorkorderTagRelation, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderTagRelation, err error)
	FindInBatches(result *[]*model.MWorkorderTagRelation, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MWorkorderTagRelation) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMWorkorderTagRelationDo
	Assign(attrs ...field.AssignExpr) IMWorkorderTagRelationDo
	Joins(fields ...field.RelationField) IMWorkorderTagRelationDo
	Preload(fields ...field.RelationField) IMWorkorderTagRelationDo
	FirstOrInit() (*model.MWorkorderTagRelation, error)
	FirstOrCreate() (*model.MWorkorderTagRelation, error)
	FindByPage(offset int, limit int) (result []*model.MWorkorderTagRelation, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMWorkorderTagRelationDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mWorkorderTagRelationDo) Debug() IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Debug())
}

func (m mWorkorderTagRelationDo) WithContext(ctx context.Context) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mWorkorderTagRelationDo) ReadDB() IMWorkorderTagRelationDo {
	return m.Clauses(dbresolver.Read)
}

func (m mWorkorderTagRelationDo) WriteDB() IMWorkorderTagRelationDo {
	return m.Clauses(dbresolver.Write)
}

func (m mWorkorderTagRelationDo) Session(config *gorm.Session) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Session(config))
}

func (m mWorkorderTagRelationDo) Clauses(conds ...clause.Expression) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mWorkorderTagRelationDo) Returning(value interface{}, columns ...string) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mWorkorderTagRelationDo) Not(conds ...gen.Condition) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mWorkorderTagRelationDo) Or(conds ...gen.Condition) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mWorkorderTagRelationDo) Select(conds ...field.Expr) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mWorkorderTagRelationDo) Where(conds ...gen.Condition) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mWorkorderTagRelationDo) Order(conds ...field.Expr) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mWorkorderTagRelationDo) Distinct(cols ...field.Expr) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mWorkorderTagRelationDo) Omit(cols ...field.Expr) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mWorkorderTagRelationDo) Join(table schema.Tabler, on ...field.Expr) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mWorkorderTagRelationDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mWorkorderTagRelationDo) RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mWorkorderTagRelationDo) Group(cols ...field.Expr) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mWorkorderTagRelationDo) Having(conds ...gen.Condition) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mWorkorderTagRelationDo) Limit(limit int) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mWorkorderTagRelationDo) Offset(offset int) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mWorkorderTagRelationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mWorkorderTagRelationDo) Unscoped() IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mWorkorderTagRelationDo) Create(values ...*model.MWorkorderTagRelation) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mWorkorderTagRelationDo) CreateInBatches(values []*model.MWorkorderTagRelation, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mWorkorderTagRelationDo) Save(values ...*model.MWorkorderTagRelation) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mWorkorderTagRelationDo) First() (*model.MWorkorderTagRelation, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTagRelation), nil
	}
}

func (m mWorkorderTagRelationDo) Take() (*model.MWorkorderTagRelation, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTagRelation), nil
	}
}

func (m mWorkorderTagRelationDo) Last() (*model.MWorkorderTagRelation, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTagRelation), nil
	}
}

func (m mWorkorderTagRelationDo) Find() ([]*model.MWorkorderTagRelation, error) {
	result, err := m.DO.Find()
	return result.([]*model.MWorkorderTagRelation), err
}

func (m mWorkorderTagRelationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderTagRelation, err error) {
	buf := make([]*model.MWorkorderTagRelation, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mWorkorderTagRelationDo) FindInBatches(result *[]*model.MWorkorderTagRelation, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mWorkorderTagRelationDo) Attrs(attrs ...field.AssignExpr) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mWorkorderTagRelationDo) Assign(attrs ...field.AssignExpr) IMWorkorderTagRelationDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mWorkorderTagRelationDo) Joins(fields ...field.RelationField) IMWorkorderTagRelationDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mWorkorderTagRelationDo) Preload(fields ...field.RelationField) IMWorkorderTagRelationDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mWorkorderTagRelationDo) FirstOrInit() (*model.MWorkorderTagRelation, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTagRelation), nil
	}
}

func (m mWorkorderTagRelationDo) FirstOrCreate() (*model.MWorkorderTagRelation, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTagRelation), nil
	}
}

func (m mWorkorderTagRelationDo) FindByPage(offset int, limit int) (result []*model.MWorkorderTagRelation, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mWorkorderTagRelationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mWorkorderTagRelationDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mWorkorderTagRelationDo) Delete(models ...*model.MWorkorderTagRelation) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mWorkorderTagRelationDo) withDO(do gen.Dao) *mWorkorderTagRelationDo {
	m.DO = *do.(*gen.DO)
	return m
}
