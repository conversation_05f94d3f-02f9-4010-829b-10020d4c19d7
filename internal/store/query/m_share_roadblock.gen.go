// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMShareRoadblock(db *gorm.DB, opts ...gen.DOOption) mShareRoadblock {
	_mShareRoadblock := mShareRoadblock{}

	_mShareRoadblock.mShareRoadblockDo.UseDB(db, opts...)
	_mShareRoadblock.mShareRoadblockDo.UseModel(&model.MShareRoadblock{})

	tableName := _mShareRoadblock.mShareRoadblockDo.TableName()
	_mShareRoadblock.ALL = field.NewAsterisk(tableName)
	_mShareRoadblock.ID = field.NewInt32(tableName, "id")
	_mShareRoadblock.GameID = field.NewString(tableName, "game_id")
	_mShareRoadblock.RoadblockNameEn = field.NewString(tableName, "roadblock_name_en")
	_mShareRoadblock.RoadblockNameCn = field.NewString(tableName, "roadblock_name_cn")
	_mShareRoadblock.ShareTimeout = field.NewInt32(tableName, "share_timeout")
	_mShareRoadblock.CreatorID = field.NewString(tableName, "creator_id")
	_mShareRoadblock.CreatedAt = field.NewInt64(tableName, "created_at")
	_mShareRoadblock.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mShareRoadblock.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mShareRoadblock.fillFieldMap()

	return _mShareRoadblock
}

type mShareRoadblock struct {
	mShareRoadblockDo

	ALL             field.Asterisk
	ID              field.Int32
	GameID          field.String // 游戏id
	RoadblockNameEn field.String
	RoadblockNameCn field.String
	ShareTimeout    field.Int32
	CreatorID       field.String
	CreatedAt       field.Int64
	UpdatedAt       field.Int64
	IsDeleted       field.Bool

	fieldMap map[string]field.Expr
}

func (m mShareRoadblock) Table(newTableName string) *mShareRoadblock {
	m.mShareRoadblockDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mShareRoadblock) As(alias string) *mShareRoadblock {
	m.mShareRoadblockDo.DO = *(m.mShareRoadblockDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mShareRoadblock) updateTableName(table string) *mShareRoadblock {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.RoadblockNameEn = field.NewString(table, "roadblock_name_en")
	m.RoadblockNameCn = field.NewString(table, "roadblock_name_cn")
	m.ShareTimeout = field.NewInt32(table, "share_timeout")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mShareRoadblock) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mShareRoadblock) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 9)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["roadblock_name_en"] = m.RoadblockNameEn
	m.fieldMap["roadblock_name_cn"] = m.RoadblockNameCn
	m.fieldMap["share_timeout"] = m.ShareTimeout
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mShareRoadblock) clone(db *gorm.DB) mShareRoadblock {
	m.mShareRoadblockDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mShareRoadblock) replaceDB(db *gorm.DB) mShareRoadblock {
	m.mShareRoadblockDo.ReplaceDB(db)
	return m
}

type mShareRoadblockDo struct{ gen.DO }

type IMShareRoadblockDo interface {
	gen.SubQuery
	Debug() IMShareRoadblockDo
	WithContext(ctx context.Context) IMShareRoadblockDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMShareRoadblockDo
	WriteDB() IMShareRoadblockDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMShareRoadblockDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMShareRoadblockDo
	Not(conds ...gen.Condition) IMShareRoadblockDo
	Or(conds ...gen.Condition) IMShareRoadblockDo
	Select(conds ...field.Expr) IMShareRoadblockDo
	Where(conds ...gen.Condition) IMShareRoadblockDo
	Order(conds ...field.Expr) IMShareRoadblockDo
	Distinct(cols ...field.Expr) IMShareRoadblockDo
	Omit(cols ...field.Expr) IMShareRoadblockDo
	Join(table schema.Tabler, on ...field.Expr) IMShareRoadblockDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMShareRoadblockDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMShareRoadblockDo
	Group(cols ...field.Expr) IMShareRoadblockDo
	Having(conds ...gen.Condition) IMShareRoadblockDo
	Limit(limit int) IMShareRoadblockDo
	Offset(offset int) IMShareRoadblockDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMShareRoadblockDo
	Unscoped() IMShareRoadblockDo
	Create(values ...*model.MShareRoadblock) error
	CreateInBatches(values []*model.MShareRoadblock, batchSize int) error
	Save(values ...*model.MShareRoadblock) error
	First() (*model.MShareRoadblock, error)
	Take() (*model.MShareRoadblock, error)
	Last() (*model.MShareRoadblock, error)
	Find() ([]*model.MShareRoadblock, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MShareRoadblock, err error)
	FindInBatches(result *[]*model.MShareRoadblock, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MShareRoadblock) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMShareRoadblockDo
	Assign(attrs ...field.AssignExpr) IMShareRoadblockDo
	Joins(fields ...field.RelationField) IMShareRoadblockDo
	Preload(fields ...field.RelationField) IMShareRoadblockDo
	FirstOrInit() (*model.MShareRoadblock, error)
	FirstOrCreate() (*model.MShareRoadblock, error)
	FindByPage(offset int, limit int) (result []*model.MShareRoadblock, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMShareRoadblockDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mShareRoadblockDo) Debug() IMShareRoadblockDo {
	return m.withDO(m.DO.Debug())
}

func (m mShareRoadblockDo) WithContext(ctx context.Context) IMShareRoadblockDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mShareRoadblockDo) ReadDB() IMShareRoadblockDo {
	return m.Clauses(dbresolver.Read)
}

func (m mShareRoadblockDo) WriteDB() IMShareRoadblockDo {
	return m.Clauses(dbresolver.Write)
}

func (m mShareRoadblockDo) Session(config *gorm.Session) IMShareRoadblockDo {
	return m.withDO(m.DO.Session(config))
}

func (m mShareRoadblockDo) Clauses(conds ...clause.Expression) IMShareRoadblockDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mShareRoadblockDo) Returning(value interface{}, columns ...string) IMShareRoadblockDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mShareRoadblockDo) Not(conds ...gen.Condition) IMShareRoadblockDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mShareRoadblockDo) Or(conds ...gen.Condition) IMShareRoadblockDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mShareRoadblockDo) Select(conds ...field.Expr) IMShareRoadblockDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mShareRoadblockDo) Where(conds ...gen.Condition) IMShareRoadblockDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mShareRoadblockDo) Order(conds ...field.Expr) IMShareRoadblockDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mShareRoadblockDo) Distinct(cols ...field.Expr) IMShareRoadblockDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mShareRoadblockDo) Omit(cols ...field.Expr) IMShareRoadblockDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mShareRoadblockDo) Join(table schema.Tabler, on ...field.Expr) IMShareRoadblockDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mShareRoadblockDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMShareRoadblockDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mShareRoadblockDo) RightJoin(table schema.Tabler, on ...field.Expr) IMShareRoadblockDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mShareRoadblockDo) Group(cols ...field.Expr) IMShareRoadblockDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mShareRoadblockDo) Having(conds ...gen.Condition) IMShareRoadblockDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mShareRoadblockDo) Limit(limit int) IMShareRoadblockDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mShareRoadblockDo) Offset(offset int) IMShareRoadblockDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mShareRoadblockDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMShareRoadblockDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mShareRoadblockDo) Unscoped() IMShareRoadblockDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mShareRoadblockDo) Create(values ...*model.MShareRoadblock) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mShareRoadblockDo) CreateInBatches(values []*model.MShareRoadblock, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mShareRoadblockDo) Save(values ...*model.MShareRoadblock) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mShareRoadblockDo) First() (*model.MShareRoadblock, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MShareRoadblock), nil
	}
}

func (m mShareRoadblockDo) Take() (*model.MShareRoadblock, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MShareRoadblock), nil
	}
}

func (m mShareRoadblockDo) Last() (*model.MShareRoadblock, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MShareRoadblock), nil
	}
}

func (m mShareRoadblockDo) Find() ([]*model.MShareRoadblock, error) {
	result, err := m.DO.Find()
	return result.([]*model.MShareRoadblock), err
}

func (m mShareRoadblockDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MShareRoadblock, err error) {
	buf := make([]*model.MShareRoadblock, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mShareRoadblockDo) FindInBatches(result *[]*model.MShareRoadblock, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mShareRoadblockDo) Attrs(attrs ...field.AssignExpr) IMShareRoadblockDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mShareRoadblockDo) Assign(attrs ...field.AssignExpr) IMShareRoadblockDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mShareRoadblockDo) Joins(fields ...field.RelationField) IMShareRoadblockDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mShareRoadblockDo) Preload(fields ...field.RelationField) IMShareRoadblockDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mShareRoadblockDo) FirstOrInit() (*model.MShareRoadblock, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MShareRoadblock), nil
	}
}

func (m mShareRoadblockDo) FirstOrCreate() (*model.MShareRoadblock, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MShareRoadblock), nil
	}
}

func (m mShareRoadblockDo) FindByPage(offset int, limit int) (result []*model.MShareRoadblock, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mShareRoadblockDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mShareRoadblockDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mShareRoadblockDo) Delete(models ...*model.MShareRoadblock) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mShareRoadblockDo) withDO(do gen.Dao) *mShareRoadblockDo {
	m.DO = *do.(*gen.DO)
	return m
}
