// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMMonitorGameServer(db *gorm.DB, opts ...gen.DOOption) mMonitorGameServer {
	_mMonitorGameServer := mMonitorGameServer{}

	_mMonitorGameServer.mMonitorGameServerDo.UseDB(db, opts...)
	_mMonitorGameServer.mMonitorGameServerDo.UseModel(&model.MMonitorGameServer{})

	tableName := _mMonitorGameServer.mMonitorGameServerDo.TableName()
	_mMonitorGameServer.ALL = field.NewAsterisk(tableName)
	_mMonitorGameServer.ID = field.NewInt32(tableName, "id")
	_mMonitorGameServer.GameID = field.NewString(tableName, "game_id")
	_mMonitorGameServer.ServerID = field.NewString(tableName, "server_id")
	_mMonitorGameServer.ServerName = field.NewString(tableName, "server_name")
	_mMonitorGameServer.PlatformID = field.NewString(tableName, "platform_id")
	_mMonitorGameServer.CreatedAt = field.NewInt64(tableName, "created_at")
	_mMonitorGameServer.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mMonitorGameServer.fillFieldMap()

	return _mMonitorGameServer
}

// mMonitorGameServer 游戏区服信息表
type mMonitorGameServer struct {
	mMonitorGameServerDo

	ALL        field.Asterisk
	ID         field.Int32 // 主键ID
	GameID     field.String
	ServerID   field.String // 区服ID
	ServerName field.String // 区服名称
	PlatformID field.String // 平台ID
	CreatedAt  field.Int64  // 创建时间戳
	UpdatedAt  field.Int64  // 更新时间戳

	fieldMap map[string]field.Expr
}

func (m mMonitorGameServer) Table(newTableName string) *mMonitorGameServer {
	m.mMonitorGameServerDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mMonitorGameServer) As(alias string) *mMonitorGameServer {
	m.mMonitorGameServerDo.DO = *(m.mMonitorGameServerDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mMonitorGameServer) updateTableName(table string) *mMonitorGameServer {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.ServerID = field.NewString(table, "server_id")
	m.ServerName = field.NewString(table, "server_name")
	m.PlatformID = field.NewString(table, "platform_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mMonitorGameServer) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mMonitorGameServer) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 7)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["server_id"] = m.ServerID
	m.fieldMap["server_name"] = m.ServerName
	m.fieldMap["platform_id"] = m.PlatformID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mMonitorGameServer) clone(db *gorm.DB) mMonitorGameServer {
	m.mMonitorGameServerDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mMonitorGameServer) replaceDB(db *gorm.DB) mMonitorGameServer {
	m.mMonitorGameServerDo.ReplaceDB(db)
	return m
}

type mMonitorGameServerDo struct{ gen.DO }

type IMMonitorGameServerDo interface {
	gen.SubQuery
	Debug() IMMonitorGameServerDo
	WithContext(ctx context.Context) IMMonitorGameServerDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMMonitorGameServerDo
	WriteDB() IMMonitorGameServerDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMMonitorGameServerDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMMonitorGameServerDo
	Not(conds ...gen.Condition) IMMonitorGameServerDo
	Or(conds ...gen.Condition) IMMonitorGameServerDo
	Select(conds ...field.Expr) IMMonitorGameServerDo
	Where(conds ...gen.Condition) IMMonitorGameServerDo
	Order(conds ...field.Expr) IMMonitorGameServerDo
	Distinct(cols ...field.Expr) IMMonitorGameServerDo
	Omit(cols ...field.Expr) IMMonitorGameServerDo
	Join(table schema.Tabler, on ...field.Expr) IMMonitorGameServerDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMMonitorGameServerDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMMonitorGameServerDo
	Group(cols ...field.Expr) IMMonitorGameServerDo
	Having(conds ...gen.Condition) IMMonitorGameServerDo
	Limit(limit int) IMMonitorGameServerDo
	Offset(offset int) IMMonitorGameServerDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMMonitorGameServerDo
	Unscoped() IMMonitorGameServerDo
	Create(values ...*model.MMonitorGameServer) error
	CreateInBatches(values []*model.MMonitorGameServer, batchSize int) error
	Save(values ...*model.MMonitorGameServer) error
	First() (*model.MMonitorGameServer, error)
	Take() (*model.MMonitorGameServer, error)
	Last() (*model.MMonitorGameServer, error)
	Find() ([]*model.MMonitorGameServer, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MMonitorGameServer, err error)
	FindInBatches(result *[]*model.MMonitorGameServer, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MMonitorGameServer) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMMonitorGameServerDo
	Assign(attrs ...field.AssignExpr) IMMonitorGameServerDo
	Joins(fields ...field.RelationField) IMMonitorGameServerDo
	Preload(fields ...field.RelationField) IMMonitorGameServerDo
	FirstOrInit() (*model.MMonitorGameServer, error)
	FirstOrCreate() (*model.MMonitorGameServer, error)
	FindByPage(offset int, limit int) (result []*model.MMonitorGameServer, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMMonitorGameServerDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mMonitorGameServerDo) Debug() IMMonitorGameServerDo {
	return m.withDO(m.DO.Debug())
}

func (m mMonitorGameServerDo) WithContext(ctx context.Context) IMMonitorGameServerDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mMonitorGameServerDo) ReadDB() IMMonitorGameServerDo {
	return m.Clauses(dbresolver.Read)
}

func (m mMonitorGameServerDo) WriteDB() IMMonitorGameServerDo {
	return m.Clauses(dbresolver.Write)
}

func (m mMonitorGameServerDo) Session(config *gorm.Session) IMMonitorGameServerDo {
	return m.withDO(m.DO.Session(config))
}

func (m mMonitorGameServerDo) Clauses(conds ...clause.Expression) IMMonitorGameServerDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mMonitorGameServerDo) Returning(value interface{}, columns ...string) IMMonitorGameServerDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mMonitorGameServerDo) Not(conds ...gen.Condition) IMMonitorGameServerDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mMonitorGameServerDo) Or(conds ...gen.Condition) IMMonitorGameServerDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mMonitorGameServerDo) Select(conds ...field.Expr) IMMonitorGameServerDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mMonitorGameServerDo) Where(conds ...gen.Condition) IMMonitorGameServerDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mMonitorGameServerDo) Order(conds ...field.Expr) IMMonitorGameServerDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mMonitorGameServerDo) Distinct(cols ...field.Expr) IMMonitorGameServerDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mMonitorGameServerDo) Omit(cols ...field.Expr) IMMonitorGameServerDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mMonitorGameServerDo) Join(table schema.Tabler, on ...field.Expr) IMMonitorGameServerDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mMonitorGameServerDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMMonitorGameServerDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mMonitorGameServerDo) RightJoin(table schema.Tabler, on ...field.Expr) IMMonitorGameServerDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mMonitorGameServerDo) Group(cols ...field.Expr) IMMonitorGameServerDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mMonitorGameServerDo) Having(conds ...gen.Condition) IMMonitorGameServerDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mMonitorGameServerDo) Limit(limit int) IMMonitorGameServerDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mMonitorGameServerDo) Offset(offset int) IMMonitorGameServerDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mMonitorGameServerDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMMonitorGameServerDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mMonitorGameServerDo) Unscoped() IMMonitorGameServerDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mMonitorGameServerDo) Create(values ...*model.MMonitorGameServer) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mMonitorGameServerDo) CreateInBatches(values []*model.MMonitorGameServer, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mMonitorGameServerDo) Save(values ...*model.MMonitorGameServer) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mMonitorGameServerDo) First() (*model.MMonitorGameServer, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorGameServer), nil
	}
}

func (m mMonitorGameServerDo) Take() (*model.MMonitorGameServer, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorGameServer), nil
	}
}

func (m mMonitorGameServerDo) Last() (*model.MMonitorGameServer, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorGameServer), nil
	}
}

func (m mMonitorGameServerDo) Find() ([]*model.MMonitorGameServer, error) {
	result, err := m.DO.Find()
	return result.([]*model.MMonitorGameServer), err
}

func (m mMonitorGameServerDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MMonitorGameServer, err error) {
	buf := make([]*model.MMonitorGameServer, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mMonitorGameServerDo) FindInBatches(result *[]*model.MMonitorGameServer, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mMonitorGameServerDo) Attrs(attrs ...field.AssignExpr) IMMonitorGameServerDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mMonitorGameServerDo) Assign(attrs ...field.AssignExpr) IMMonitorGameServerDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mMonitorGameServerDo) Joins(fields ...field.RelationField) IMMonitorGameServerDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mMonitorGameServerDo) Preload(fields ...field.RelationField) IMMonitorGameServerDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mMonitorGameServerDo) FirstOrInit() (*model.MMonitorGameServer, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorGameServer), nil
	}
}

func (m mMonitorGameServerDo) FirstOrCreate() (*model.MMonitorGameServer, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorGameServer), nil
	}
}

func (m mMonitorGameServerDo) FindByPage(offset int, limit int) (result []*model.MMonitorGameServer, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mMonitorGameServerDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mMonitorGameServerDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mMonitorGameServerDo) Delete(models ...*model.MMonitorGameServer) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mMonitorGameServerDo) withDO(do gen.Dao) *mMonitorGameServerDo {
	m.DO = *do.(*gen.DO)
	return m
}
