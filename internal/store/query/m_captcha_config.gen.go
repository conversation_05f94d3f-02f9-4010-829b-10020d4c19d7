// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMCaptchaConfig(db *gorm.DB, opts ...gen.DOOption) mCaptchaConfig {
	_mCaptchaConfig := mCaptchaConfig{}

	_mCaptchaConfig.mCaptchaConfigDo.UseDB(db, opts...)
	_mCaptchaConfig.mCaptchaConfigDo.UseModel(&model.MCaptchaConfig{})

	tableName := _mCaptchaConfig.mCaptchaConfigDo.TableName()
	_mCaptchaConfig.ALL = field.NewAsterisk(tableName)
	_mCaptchaConfig.ID = field.NewInt32(tableName, "id")
	_mCaptchaConfig.GameID = field.NewString(tableName, "game_id")
	_mCaptchaConfig.Provider = field.NewInt32(tableName, "provider")
	_mCaptchaConfig.CaptchaAppID = field.NewInt32(tableName, "captcha_app_id")
	_mCaptchaConfig.AppSecretKey = field.NewString(tableName, "app_secret_key")
	_mCaptchaConfig.CaptchaID = field.NewString(tableName, "captcha_id")
	_mCaptchaConfig.SecretID = field.NewString(tableName, "secret_id")
	_mCaptchaConfig.CreatedAt = field.NewInt64(tableName, "created_at")
	_mCaptchaConfig.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mCaptchaConfig.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mCaptchaConfig.fillFieldMap()

	return _mCaptchaConfig
}

// mCaptchaConfig 验证码配置表
type mCaptchaConfig struct {
	mCaptchaConfigDo

	ALL          field.Asterisk
	ID           field.Int32
	GameID       field.String // 关联游戏ID
	Provider     field.Int32  // 验证码服务商 1=腾讯云 2=网易易盾
	CaptchaAppID field.Int32  // 腾讯云CaptchaAppId
	AppSecretKey field.String // 腾讯云AppSecretKey
	CaptchaID    field.String // 网易易盾captchaId
	SecretID     field.String // 网易易盾secretId
	CreatedAt    field.Int64
	UpdatedAt    field.Int64
	IsDeleted    field.Bool

	fieldMap map[string]field.Expr
}

func (m mCaptchaConfig) Table(newTableName string) *mCaptchaConfig {
	m.mCaptchaConfigDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mCaptchaConfig) As(alias string) *mCaptchaConfig {
	m.mCaptchaConfigDo.DO = *(m.mCaptchaConfigDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mCaptchaConfig) updateTableName(table string) *mCaptchaConfig {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.Provider = field.NewInt32(table, "provider")
	m.CaptchaAppID = field.NewInt32(table, "captcha_app_id")
	m.AppSecretKey = field.NewString(table, "app_secret_key")
	m.CaptchaID = field.NewString(table, "captcha_id")
	m.SecretID = field.NewString(table, "secret_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mCaptchaConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mCaptchaConfig) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 10)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["provider"] = m.Provider
	m.fieldMap["captcha_app_id"] = m.CaptchaAppID
	m.fieldMap["app_secret_key"] = m.AppSecretKey
	m.fieldMap["captcha_id"] = m.CaptchaID
	m.fieldMap["secret_id"] = m.SecretID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mCaptchaConfig) clone(db *gorm.DB) mCaptchaConfig {
	m.mCaptchaConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mCaptchaConfig) replaceDB(db *gorm.DB) mCaptchaConfig {
	m.mCaptchaConfigDo.ReplaceDB(db)
	return m
}

type mCaptchaConfigDo struct{ gen.DO }

type IMCaptchaConfigDo interface {
	gen.SubQuery
	Debug() IMCaptchaConfigDo
	WithContext(ctx context.Context) IMCaptchaConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMCaptchaConfigDo
	WriteDB() IMCaptchaConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMCaptchaConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMCaptchaConfigDo
	Not(conds ...gen.Condition) IMCaptchaConfigDo
	Or(conds ...gen.Condition) IMCaptchaConfigDo
	Select(conds ...field.Expr) IMCaptchaConfigDo
	Where(conds ...gen.Condition) IMCaptchaConfigDo
	Order(conds ...field.Expr) IMCaptchaConfigDo
	Distinct(cols ...field.Expr) IMCaptchaConfigDo
	Omit(cols ...field.Expr) IMCaptchaConfigDo
	Join(table schema.Tabler, on ...field.Expr) IMCaptchaConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMCaptchaConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMCaptchaConfigDo
	Group(cols ...field.Expr) IMCaptchaConfigDo
	Having(conds ...gen.Condition) IMCaptchaConfigDo
	Limit(limit int) IMCaptchaConfigDo
	Offset(offset int) IMCaptchaConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMCaptchaConfigDo
	Unscoped() IMCaptchaConfigDo
	Create(values ...*model.MCaptchaConfig) error
	CreateInBatches(values []*model.MCaptchaConfig, batchSize int) error
	Save(values ...*model.MCaptchaConfig) error
	First() (*model.MCaptchaConfig, error)
	Take() (*model.MCaptchaConfig, error)
	Last() (*model.MCaptchaConfig, error)
	Find() ([]*model.MCaptchaConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCaptchaConfig, err error)
	FindInBatches(result *[]*model.MCaptchaConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MCaptchaConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMCaptchaConfigDo
	Assign(attrs ...field.AssignExpr) IMCaptchaConfigDo
	Joins(fields ...field.RelationField) IMCaptchaConfigDo
	Preload(fields ...field.RelationField) IMCaptchaConfigDo
	FirstOrInit() (*model.MCaptchaConfig, error)
	FirstOrCreate() (*model.MCaptchaConfig, error)
	FindByPage(offset int, limit int) (result []*model.MCaptchaConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMCaptchaConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mCaptchaConfigDo) Debug() IMCaptchaConfigDo {
	return m.withDO(m.DO.Debug())
}

func (m mCaptchaConfigDo) WithContext(ctx context.Context) IMCaptchaConfigDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mCaptchaConfigDo) ReadDB() IMCaptchaConfigDo {
	return m.Clauses(dbresolver.Read)
}

func (m mCaptchaConfigDo) WriteDB() IMCaptchaConfigDo {
	return m.Clauses(dbresolver.Write)
}

func (m mCaptchaConfigDo) Session(config *gorm.Session) IMCaptchaConfigDo {
	return m.withDO(m.DO.Session(config))
}

func (m mCaptchaConfigDo) Clauses(conds ...clause.Expression) IMCaptchaConfigDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mCaptchaConfigDo) Returning(value interface{}, columns ...string) IMCaptchaConfigDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mCaptchaConfigDo) Not(conds ...gen.Condition) IMCaptchaConfigDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mCaptchaConfigDo) Or(conds ...gen.Condition) IMCaptchaConfigDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mCaptchaConfigDo) Select(conds ...field.Expr) IMCaptchaConfigDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mCaptchaConfigDo) Where(conds ...gen.Condition) IMCaptchaConfigDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mCaptchaConfigDo) Order(conds ...field.Expr) IMCaptchaConfigDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mCaptchaConfigDo) Distinct(cols ...field.Expr) IMCaptchaConfigDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mCaptchaConfigDo) Omit(cols ...field.Expr) IMCaptchaConfigDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mCaptchaConfigDo) Join(table schema.Tabler, on ...field.Expr) IMCaptchaConfigDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mCaptchaConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMCaptchaConfigDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mCaptchaConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) IMCaptchaConfigDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mCaptchaConfigDo) Group(cols ...field.Expr) IMCaptchaConfigDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mCaptchaConfigDo) Having(conds ...gen.Condition) IMCaptchaConfigDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mCaptchaConfigDo) Limit(limit int) IMCaptchaConfigDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mCaptchaConfigDo) Offset(offset int) IMCaptchaConfigDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mCaptchaConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMCaptchaConfigDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mCaptchaConfigDo) Unscoped() IMCaptchaConfigDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mCaptchaConfigDo) Create(values ...*model.MCaptchaConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mCaptchaConfigDo) CreateInBatches(values []*model.MCaptchaConfig, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mCaptchaConfigDo) Save(values ...*model.MCaptchaConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mCaptchaConfigDo) First() (*model.MCaptchaConfig, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCaptchaConfig), nil
	}
}

func (m mCaptchaConfigDo) Take() (*model.MCaptchaConfig, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCaptchaConfig), nil
	}
}

func (m mCaptchaConfigDo) Last() (*model.MCaptchaConfig, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCaptchaConfig), nil
	}
}

func (m mCaptchaConfigDo) Find() ([]*model.MCaptchaConfig, error) {
	result, err := m.DO.Find()
	return result.([]*model.MCaptchaConfig), err
}

func (m mCaptchaConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCaptchaConfig, err error) {
	buf := make([]*model.MCaptchaConfig, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mCaptchaConfigDo) FindInBatches(result *[]*model.MCaptchaConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mCaptchaConfigDo) Attrs(attrs ...field.AssignExpr) IMCaptchaConfigDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mCaptchaConfigDo) Assign(attrs ...field.AssignExpr) IMCaptchaConfigDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mCaptchaConfigDo) Joins(fields ...field.RelationField) IMCaptchaConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mCaptchaConfigDo) Preload(fields ...field.RelationField) IMCaptchaConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mCaptchaConfigDo) FirstOrInit() (*model.MCaptchaConfig, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCaptchaConfig), nil
	}
}

func (m mCaptchaConfigDo) FirstOrCreate() (*model.MCaptchaConfig, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCaptchaConfig), nil
	}
}

func (m mCaptchaConfigDo) FindByPage(offset int, limit int) (result []*model.MCaptchaConfig, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mCaptchaConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mCaptchaConfigDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mCaptchaConfigDo) Delete(models ...*model.MCaptchaConfig) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mCaptchaConfigDo) withDO(do gen.Dao) *mCaptchaConfigDo {
	m.DO = *do.(*gen.DO)
	return m
}
