// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMQuestionWelcomeMessage(db *gorm.DB, opts ...gen.DOOption) mQuestionWelcomeMessage {
	_mQuestionWelcomeMessage := mQuestionWelcomeMessage{}

	_mQuestionWelcomeMessage.mQuestionWelcomeMessageDo.UseDB(db, opts...)
	_mQuestionWelcomeMessage.mQuestionWelcomeMessageDo.UseModel(&model.MQuestionWelcomeMessage{})

	tableName := _mQuestionWelcomeMessage.mQuestionWelcomeMessageDo.TableName()
	_mQuestionWelcomeMessage.ALL = field.NewAsterisk(tableName)
	_mQuestionWelcomeMessage.ID = field.NewInt32(tableName, "id")
	_mQuestionWelcomeMessage.GameID = field.NewString(tableName, "game_id")
	_mQuestionWelcomeMessage.Content = field.NewString(tableName, "content")
	_mQuestionWelcomeMessage.Weight = field.NewInt32(tableName, "weight")
	_mQuestionWelcomeMessage.CreatorID = field.NewString(tableName, "creator_id")
	_mQuestionWelcomeMessage.CreatedAt = field.NewInt64(tableName, "created_at")
	_mQuestionWelcomeMessage.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mQuestionWelcomeMessage.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mQuestionWelcomeMessage.fillFieldMap()

	return _mQuestionWelcomeMessage
}

// mQuestionWelcomeMessage 欢迎语表
type mQuestionWelcomeMessage struct {
	mQuestionWelcomeMessageDo

	ALL       field.Asterisk
	ID        field.Int32
	GameID    field.String // 游戏ID
	Content   field.String // 欢迎语内容
	Weight    field.Int32
	CreatorID field.String // 创建人ID
	CreatedAt field.Int64
	UpdatedAt field.Int64
	IsDeleted field.Bool

	fieldMap map[string]field.Expr
}

func (m mQuestionWelcomeMessage) Table(newTableName string) *mQuestionWelcomeMessage {
	m.mQuestionWelcomeMessageDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mQuestionWelcomeMessage) As(alias string) *mQuestionWelcomeMessage {
	m.mQuestionWelcomeMessageDo.DO = *(m.mQuestionWelcomeMessageDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mQuestionWelcomeMessage) updateTableName(table string) *mQuestionWelcomeMessage {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.Content = field.NewString(table, "content")
	m.Weight = field.NewInt32(table, "weight")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mQuestionWelcomeMessage) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mQuestionWelcomeMessage) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 8)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["content"] = m.Content
	m.fieldMap["weight"] = m.Weight
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mQuestionWelcomeMessage) clone(db *gorm.DB) mQuestionWelcomeMessage {
	m.mQuestionWelcomeMessageDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mQuestionWelcomeMessage) replaceDB(db *gorm.DB) mQuestionWelcomeMessage {
	m.mQuestionWelcomeMessageDo.ReplaceDB(db)
	return m
}

type mQuestionWelcomeMessageDo struct{ gen.DO }

type IMQuestionWelcomeMessageDo interface {
	gen.SubQuery
	Debug() IMQuestionWelcomeMessageDo
	WithContext(ctx context.Context) IMQuestionWelcomeMessageDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMQuestionWelcomeMessageDo
	WriteDB() IMQuestionWelcomeMessageDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMQuestionWelcomeMessageDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMQuestionWelcomeMessageDo
	Not(conds ...gen.Condition) IMQuestionWelcomeMessageDo
	Or(conds ...gen.Condition) IMQuestionWelcomeMessageDo
	Select(conds ...field.Expr) IMQuestionWelcomeMessageDo
	Where(conds ...gen.Condition) IMQuestionWelcomeMessageDo
	Order(conds ...field.Expr) IMQuestionWelcomeMessageDo
	Distinct(cols ...field.Expr) IMQuestionWelcomeMessageDo
	Omit(cols ...field.Expr) IMQuestionWelcomeMessageDo
	Join(table schema.Tabler, on ...field.Expr) IMQuestionWelcomeMessageDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMQuestionWelcomeMessageDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMQuestionWelcomeMessageDo
	Group(cols ...field.Expr) IMQuestionWelcomeMessageDo
	Having(conds ...gen.Condition) IMQuestionWelcomeMessageDo
	Limit(limit int) IMQuestionWelcomeMessageDo
	Offset(offset int) IMQuestionWelcomeMessageDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMQuestionWelcomeMessageDo
	Unscoped() IMQuestionWelcomeMessageDo
	Create(values ...*model.MQuestionWelcomeMessage) error
	CreateInBatches(values []*model.MQuestionWelcomeMessage, batchSize int) error
	Save(values ...*model.MQuestionWelcomeMessage) error
	First() (*model.MQuestionWelcomeMessage, error)
	Take() (*model.MQuestionWelcomeMessage, error)
	Last() (*model.MQuestionWelcomeMessage, error)
	Find() ([]*model.MQuestionWelcomeMessage, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MQuestionWelcomeMessage, err error)
	FindInBatches(result *[]*model.MQuestionWelcomeMessage, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MQuestionWelcomeMessage) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMQuestionWelcomeMessageDo
	Assign(attrs ...field.AssignExpr) IMQuestionWelcomeMessageDo
	Joins(fields ...field.RelationField) IMQuestionWelcomeMessageDo
	Preload(fields ...field.RelationField) IMQuestionWelcomeMessageDo
	FirstOrInit() (*model.MQuestionWelcomeMessage, error)
	FirstOrCreate() (*model.MQuestionWelcomeMessage, error)
	FindByPage(offset int, limit int) (result []*model.MQuestionWelcomeMessage, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMQuestionWelcomeMessageDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mQuestionWelcomeMessageDo) Debug() IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Debug())
}

func (m mQuestionWelcomeMessageDo) WithContext(ctx context.Context) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mQuestionWelcomeMessageDo) ReadDB() IMQuestionWelcomeMessageDo {
	return m.Clauses(dbresolver.Read)
}

func (m mQuestionWelcomeMessageDo) WriteDB() IMQuestionWelcomeMessageDo {
	return m.Clauses(dbresolver.Write)
}

func (m mQuestionWelcomeMessageDo) Session(config *gorm.Session) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Session(config))
}

func (m mQuestionWelcomeMessageDo) Clauses(conds ...clause.Expression) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mQuestionWelcomeMessageDo) Returning(value interface{}, columns ...string) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mQuestionWelcomeMessageDo) Not(conds ...gen.Condition) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mQuestionWelcomeMessageDo) Or(conds ...gen.Condition) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mQuestionWelcomeMessageDo) Select(conds ...field.Expr) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mQuestionWelcomeMessageDo) Where(conds ...gen.Condition) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mQuestionWelcomeMessageDo) Order(conds ...field.Expr) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mQuestionWelcomeMessageDo) Distinct(cols ...field.Expr) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mQuestionWelcomeMessageDo) Omit(cols ...field.Expr) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mQuestionWelcomeMessageDo) Join(table schema.Tabler, on ...field.Expr) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mQuestionWelcomeMessageDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mQuestionWelcomeMessageDo) RightJoin(table schema.Tabler, on ...field.Expr) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mQuestionWelcomeMessageDo) Group(cols ...field.Expr) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mQuestionWelcomeMessageDo) Having(conds ...gen.Condition) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mQuestionWelcomeMessageDo) Limit(limit int) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mQuestionWelcomeMessageDo) Offset(offset int) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mQuestionWelcomeMessageDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mQuestionWelcomeMessageDo) Unscoped() IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mQuestionWelcomeMessageDo) Create(values ...*model.MQuestionWelcomeMessage) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mQuestionWelcomeMessageDo) CreateInBatches(values []*model.MQuestionWelcomeMessage, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mQuestionWelcomeMessageDo) Save(values ...*model.MQuestionWelcomeMessage) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mQuestionWelcomeMessageDo) First() (*model.MQuestionWelcomeMessage, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionWelcomeMessage), nil
	}
}

func (m mQuestionWelcomeMessageDo) Take() (*model.MQuestionWelcomeMessage, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionWelcomeMessage), nil
	}
}

func (m mQuestionWelcomeMessageDo) Last() (*model.MQuestionWelcomeMessage, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionWelcomeMessage), nil
	}
}

func (m mQuestionWelcomeMessageDo) Find() ([]*model.MQuestionWelcomeMessage, error) {
	result, err := m.DO.Find()
	return result.([]*model.MQuestionWelcomeMessage), err
}

func (m mQuestionWelcomeMessageDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MQuestionWelcomeMessage, err error) {
	buf := make([]*model.MQuestionWelcomeMessage, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mQuestionWelcomeMessageDo) FindInBatches(result *[]*model.MQuestionWelcomeMessage, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mQuestionWelcomeMessageDo) Attrs(attrs ...field.AssignExpr) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mQuestionWelcomeMessageDo) Assign(attrs ...field.AssignExpr) IMQuestionWelcomeMessageDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mQuestionWelcomeMessageDo) Joins(fields ...field.RelationField) IMQuestionWelcomeMessageDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mQuestionWelcomeMessageDo) Preload(fields ...field.RelationField) IMQuestionWelcomeMessageDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mQuestionWelcomeMessageDo) FirstOrInit() (*model.MQuestionWelcomeMessage, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionWelcomeMessage), nil
	}
}

func (m mQuestionWelcomeMessageDo) FirstOrCreate() (*model.MQuestionWelcomeMessage, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionWelcomeMessage), nil
	}
}

func (m mQuestionWelcomeMessageDo) FindByPage(offset int, limit int) (result []*model.MQuestionWelcomeMessage, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mQuestionWelcomeMessageDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mQuestionWelcomeMessageDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mQuestionWelcomeMessageDo) Delete(models ...*model.MQuestionWelcomeMessage) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mQuestionWelcomeMessageDo) withDO(do gen.Dao) *mQuestionWelcomeMessageDo {
	m.DO = *do.(*gen.DO)
	return m
}
