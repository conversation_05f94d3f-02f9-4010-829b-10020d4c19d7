// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMGame(db *gorm.DB, opts ...gen.DOOption) mGame {
	_mGame := mGame{}

	_mGame.mGameDo.UseDB(db, opts...)
	_mGame.mGameDo.UseModel(&model.MGame{})

	tableName := _mGame.mGameDo.TableName()
	_mGame.ALL = field.NewAsterisk(tableName)
	_mGame.ID = field.NewInt32(tableName, "id")
	_mGame.GameID = field.NewString(tableName, "game_id")
	_mGame.Name = field.NewString(tableName, "name")
	_mGame.Status = field.NewInt32(tableName, "status")
	_mGame.Icon = field.NewString(tableName, "icon")
	_mGame.Secret = field.NewString(tableName, "secret")
	_mGame.PlatformType = field.NewString(tableName, "platform_type")
	_mGame.PlatformAppID = field.NewString(tableName, "platform_app_id")
	_mGame.PayCallback = field.NewString(tableName, "pay_callback")
	_mGame.GravityAppID = field.NewString(tableName, "gravity_app_id")
	_mGame.GravityAccessToken = field.NewString(tableName, "gravity_access_token")
	_mGame.GravityIsEnabled = field.NewInt32(tableName, "gravity_is_enabled")
	_mGame.QiyuWechatAppID = field.NewString(tableName, "qiyu_wechat_app_id")
	_mGame.CustomerServiceCallback = field.NewString(tableName, "customer_service_callback")
	_mGame.CustomerServiceDouyinCallback = field.NewString(tableName, "customer_service_douyin_callback")
	_mGame.ReportServiceCallback = field.NewString(tableName, "report_service_callback")
	_mGame.MonitorServiceCallback = field.NewString(tableName, "monitor_service_callback")
	_mGame.QiyuAppKey = field.NewString(tableName, "qiyu_app_key")
	_mGame.QiyuAppSecret = field.NewString(tableName, "qiyu_app_secret")
	_mGame.PayMethod = field.NewInt32(tableName, "pay_method")
	_mGame.TencentDataSourceID = field.NewString(tableName, "tencent_data_source_id")
	_mGame.TencentEncryptionKey = field.NewString(tableName, "tencent_encryption_key")
	_mGame.TencentAdCycle = field.NewInt32(tableName, "tencent_ad_cycle")
	_mGame.GameSceneURL = field.NewString(tableName, "game_scene_url")
	_mGame.CreatorID = field.NewString(tableName, "creator_id")
	_mGame.CreatedAt = field.NewInt64(tableName, "created_at")
	_mGame.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mGame.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mGame.fillFieldMap()

	return _mGame
}

type mGame struct {
	mGameDo

	ALL                           field.Asterisk
	ID                            field.Int32
	GameID                        field.String // 平台自定义id
	Name                          field.String // 游戏名称
	Status                        field.Int32  // 游戏状态
	Icon                          field.String // 游戏图标
	Secret                        field.String // 密钥信息
	PlatformType                  field.String // 平台类型 minigame | douyin_minigame
	PlatformAppID                 field.String // 数数平台的APP ID
	PayCallback                   field.String // 回调地址
	GravityAppID                  field.String
	GravityAccessToken            field.String
	GravityIsEnabled              field.Int32 // 引力是否开启
	QiyuWechatAppID               field.String
	CustomerServiceCallback       field.String
	CustomerServiceDouyinCallback field.String // 抖音客服(礼包)回调地址
	ReportServiceCallback         field.String // 举报系统回调
	MonitorServiceCallback        field.String // 聊天监控会调
	QiyuAppKey                    field.String
	QiyuAppSecret                 field.String
	PayMethod                     field.Int32 // 1 发送卡片 2 手动输入cz或充值 3 弹出二维码 4 弹框复制支付链接
	TencentDataSourceID           field.String
	TencentEncryptionKey          field.String
	TencentAdCycle                field.Int32
	GameSceneURL                  field.String // 抖音游戏场景api
	CreatorID                     field.String // 创建人id
	CreatedAt                     field.Int64
	UpdatedAt                     field.Int64
	IsDeleted                     field.Bool

	fieldMap map[string]field.Expr
}

func (m mGame) Table(newTableName string) *mGame {
	m.mGameDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mGame) As(alias string) *mGame {
	m.mGameDo.DO = *(m.mGameDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mGame) updateTableName(table string) *mGame {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.Name = field.NewString(table, "name")
	m.Status = field.NewInt32(table, "status")
	m.Icon = field.NewString(table, "icon")
	m.Secret = field.NewString(table, "secret")
	m.PlatformType = field.NewString(table, "platform_type")
	m.PlatformAppID = field.NewString(table, "platform_app_id")
	m.PayCallback = field.NewString(table, "pay_callback")
	m.GravityAppID = field.NewString(table, "gravity_app_id")
	m.GravityAccessToken = field.NewString(table, "gravity_access_token")
	m.GravityIsEnabled = field.NewInt32(table, "gravity_is_enabled")
	m.QiyuWechatAppID = field.NewString(table, "qiyu_wechat_app_id")
	m.CustomerServiceCallback = field.NewString(table, "customer_service_callback")
	m.CustomerServiceDouyinCallback = field.NewString(table, "customer_service_douyin_callback")
	m.ReportServiceCallback = field.NewString(table, "report_service_callback")
	m.MonitorServiceCallback = field.NewString(table, "monitor_service_callback")
	m.QiyuAppKey = field.NewString(table, "qiyu_app_key")
	m.QiyuAppSecret = field.NewString(table, "qiyu_app_secret")
	m.PayMethod = field.NewInt32(table, "pay_method")
	m.TencentDataSourceID = field.NewString(table, "tencent_data_source_id")
	m.TencentEncryptionKey = field.NewString(table, "tencent_encryption_key")
	m.TencentAdCycle = field.NewInt32(table, "tencent_ad_cycle")
	m.GameSceneURL = field.NewString(table, "game_scene_url")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mGame) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mGame) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 28)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["name"] = m.Name
	m.fieldMap["status"] = m.Status
	m.fieldMap["icon"] = m.Icon
	m.fieldMap["secret"] = m.Secret
	m.fieldMap["platform_type"] = m.PlatformType
	m.fieldMap["platform_app_id"] = m.PlatformAppID
	m.fieldMap["pay_callback"] = m.PayCallback
	m.fieldMap["gravity_app_id"] = m.GravityAppID
	m.fieldMap["gravity_access_token"] = m.GravityAccessToken
	m.fieldMap["gravity_is_enabled"] = m.GravityIsEnabled
	m.fieldMap["qiyu_wechat_app_id"] = m.QiyuWechatAppID
	m.fieldMap["customer_service_callback"] = m.CustomerServiceCallback
	m.fieldMap["customer_service_douyin_callback"] = m.CustomerServiceDouyinCallback
	m.fieldMap["report_service_callback"] = m.ReportServiceCallback
	m.fieldMap["monitor_service_callback"] = m.MonitorServiceCallback
	m.fieldMap["qiyu_app_key"] = m.QiyuAppKey
	m.fieldMap["qiyu_app_secret"] = m.QiyuAppSecret
	m.fieldMap["pay_method"] = m.PayMethod
	m.fieldMap["tencent_data_source_id"] = m.TencentDataSourceID
	m.fieldMap["tencent_encryption_key"] = m.TencentEncryptionKey
	m.fieldMap["tencent_ad_cycle"] = m.TencentAdCycle
	m.fieldMap["game_scene_url"] = m.GameSceneURL
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mGame) clone(db *gorm.DB) mGame {
	m.mGameDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mGame) replaceDB(db *gorm.DB) mGame {
	m.mGameDo.ReplaceDB(db)
	return m
}

type mGameDo struct{ gen.DO }

type IMGameDo interface {
	gen.SubQuery
	Debug() IMGameDo
	WithContext(ctx context.Context) IMGameDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMGameDo
	WriteDB() IMGameDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMGameDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMGameDo
	Not(conds ...gen.Condition) IMGameDo
	Or(conds ...gen.Condition) IMGameDo
	Select(conds ...field.Expr) IMGameDo
	Where(conds ...gen.Condition) IMGameDo
	Order(conds ...field.Expr) IMGameDo
	Distinct(cols ...field.Expr) IMGameDo
	Omit(cols ...field.Expr) IMGameDo
	Join(table schema.Tabler, on ...field.Expr) IMGameDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMGameDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMGameDo
	Group(cols ...field.Expr) IMGameDo
	Having(conds ...gen.Condition) IMGameDo
	Limit(limit int) IMGameDo
	Offset(offset int) IMGameDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMGameDo
	Unscoped() IMGameDo
	Create(values ...*model.MGame) error
	CreateInBatches(values []*model.MGame, batchSize int) error
	Save(values ...*model.MGame) error
	First() (*model.MGame, error)
	Take() (*model.MGame, error)
	Last() (*model.MGame, error)
	Find() ([]*model.MGame, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MGame, err error)
	FindInBatches(result *[]*model.MGame, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MGame) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMGameDo
	Assign(attrs ...field.AssignExpr) IMGameDo
	Joins(fields ...field.RelationField) IMGameDo
	Preload(fields ...field.RelationField) IMGameDo
	FirstOrInit() (*model.MGame, error)
	FirstOrCreate() (*model.MGame, error)
	FindByPage(offset int, limit int) (result []*model.MGame, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMGameDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mGameDo) Debug() IMGameDo {
	return m.withDO(m.DO.Debug())
}

func (m mGameDo) WithContext(ctx context.Context) IMGameDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mGameDo) ReadDB() IMGameDo {
	return m.Clauses(dbresolver.Read)
}

func (m mGameDo) WriteDB() IMGameDo {
	return m.Clauses(dbresolver.Write)
}

func (m mGameDo) Session(config *gorm.Session) IMGameDo {
	return m.withDO(m.DO.Session(config))
}

func (m mGameDo) Clauses(conds ...clause.Expression) IMGameDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mGameDo) Returning(value interface{}, columns ...string) IMGameDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mGameDo) Not(conds ...gen.Condition) IMGameDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mGameDo) Or(conds ...gen.Condition) IMGameDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mGameDo) Select(conds ...field.Expr) IMGameDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mGameDo) Where(conds ...gen.Condition) IMGameDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mGameDo) Order(conds ...field.Expr) IMGameDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mGameDo) Distinct(cols ...field.Expr) IMGameDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mGameDo) Omit(cols ...field.Expr) IMGameDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mGameDo) Join(table schema.Tabler, on ...field.Expr) IMGameDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mGameDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMGameDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mGameDo) RightJoin(table schema.Tabler, on ...field.Expr) IMGameDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mGameDo) Group(cols ...field.Expr) IMGameDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mGameDo) Having(conds ...gen.Condition) IMGameDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mGameDo) Limit(limit int) IMGameDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mGameDo) Offset(offset int) IMGameDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mGameDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMGameDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mGameDo) Unscoped() IMGameDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mGameDo) Create(values ...*model.MGame) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mGameDo) CreateInBatches(values []*model.MGame, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mGameDo) Save(values ...*model.MGame) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mGameDo) First() (*model.MGame, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGame), nil
	}
}

func (m mGameDo) Take() (*model.MGame, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGame), nil
	}
}

func (m mGameDo) Last() (*model.MGame, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGame), nil
	}
}

func (m mGameDo) Find() ([]*model.MGame, error) {
	result, err := m.DO.Find()
	return result.([]*model.MGame), err
}

func (m mGameDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MGame, err error) {
	buf := make([]*model.MGame, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mGameDo) FindInBatches(result *[]*model.MGame, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mGameDo) Attrs(attrs ...field.AssignExpr) IMGameDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mGameDo) Assign(attrs ...field.AssignExpr) IMGameDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mGameDo) Joins(fields ...field.RelationField) IMGameDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mGameDo) Preload(fields ...field.RelationField) IMGameDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mGameDo) FirstOrInit() (*model.MGame, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGame), nil
	}
}

func (m mGameDo) FirstOrCreate() (*model.MGame, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGame), nil
	}
}

func (m mGameDo) FindByPage(offset int, limit int) (result []*model.MGame, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mGameDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mGameDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mGameDo) Delete(models ...*model.MGame) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mGameDo) withDO(do gen.Dao) *mGameDo {
	m.DO = *do.(*gen.DO)
	return m
}
