// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMAdPosition(db *gorm.DB, opts ...gen.DOOption) mAdPosition {
	_mAdPosition := mAdPosition{}

	_mAdPosition.mAdPositionDo.UseDB(db, opts...)
	_mAdPosition.mAdPositionDo.UseModel(&model.MAdPosition{})

	tableName := _mAdPosition.mAdPositionDo.TableName()
	_mAdPosition.ALL = field.NewAsterisk(tableName)
	_mAdPosition.ID = field.NewInt32(tableName, "id")
	_mAdPosition.GameID = field.NewString(tableName, "game_id")
	_mAdPosition.PositionID = field.NewString(tableName, "position_id")
	_mAdPosition.Name = field.NewString(tableName, "name")
	_mAdPosition.AdType = field.NewInt32(tableName, "ad_type")
	_mAdPosition.Status = field.NewInt32(tableName, "status")
	_mAdPosition.CreatedAt = field.NewInt64(tableName, "created_at")
	_mAdPosition.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mAdPosition.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mAdPosition.fillFieldMap()

	return _mAdPosition
}

// mAdPosition 广告位配置表
type mAdPosition struct {
	mAdPositionDo

	ALL        field.Asterisk
	ID         field.Int32
	GameID     field.String
	PositionID field.String // 中台广告位ID
	Name       field.String // 广告位名称
	AdType     field.Int32  // 广告类型(1激励视频/2banner广告/3原生模板广告/4插屏广告)
	Status     field.Int32  // 状态 1启用 2禁用
	CreatedAt  field.Int64  // 创建时间
	UpdatedAt  field.Int64  // 更新时间
	IsDeleted  field.Bool   // 是否删除

	fieldMap map[string]field.Expr
}

func (m mAdPosition) Table(newTableName string) *mAdPosition {
	m.mAdPositionDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mAdPosition) As(alias string) *mAdPosition {
	m.mAdPositionDo.DO = *(m.mAdPositionDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mAdPosition) updateTableName(table string) *mAdPosition {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.PositionID = field.NewString(table, "position_id")
	m.Name = field.NewString(table, "name")
	m.AdType = field.NewInt32(table, "ad_type")
	m.Status = field.NewInt32(table, "status")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mAdPosition) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mAdPosition) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 9)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["position_id"] = m.PositionID
	m.fieldMap["name"] = m.Name
	m.fieldMap["ad_type"] = m.AdType
	m.fieldMap["status"] = m.Status
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mAdPosition) clone(db *gorm.DB) mAdPosition {
	m.mAdPositionDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mAdPosition) replaceDB(db *gorm.DB) mAdPosition {
	m.mAdPositionDo.ReplaceDB(db)
	return m
}

type mAdPositionDo struct{ gen.DO }

type IMAdPositionDo interface {
	gen.SubQuery
	Debug() IMAdPositionDo
	WithContext(ctx context.Context) IMAdPositionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMAdPositionDo
	WriteDB() IMAdPositionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMAdPositionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMAdPositionDo
	Not(conds ...gen.Condition) IMAdPositionDo
	Or(conds ...gen.Condition) IMAdPositionDo
	Select(conds ...field.Expr) IMAdPositionDo
	Where(conds ...gen.Condition) IMAdPositionDo
	Order(conds ...field.Expr) IMAdPositionDo
	Distinct(cols ...field.Expr) IMAdPositionDo
	Omit(cols ...field.Expr) IMAdPositionDo
	Join(table schema.Tabler, on ...field.Expr) IMAdPositionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMAdPositionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMAdPositionDo
	Group(cols ...field.Expr) IMAdPositionDo
	Having(conds ...gen.Condition) IMAdPositionDo
	Limit(limit int) IMAdPositionDo
	Offset(offset int) IMAdPositionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMAdPositionDo
	Unscoped() IMAdPositionDo
	Create(values ...*model.MAdPosition) error
	CreateInBatches(values []*model.MAdPosition, batchSize int) error
	Save(values ...*model.MAdPosition) error
	First() (*model.MAdPosition, error)
	Take() (*model.MAdPosition, error)
	Last() (*model.MAdPosition, error)
	Find() ([]*model.MAdPosition, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MAdPosition, err error)
	FindInBatches(result *[]*model.MAdPosition, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MAdPosition) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMAdPositionDo
	Assign(attrs ...field.AssignExpr) IMAdPositionDo
	Joins(fields ...field.RelationField) IMAdPositionDo
	Preload(fields ...field.RelationField) IMAdPositionDo
	FirstOrInit() (*model.MAdPosition, error)
	FirstOrCreate() (*model.MAdPosition, error)
	FindByPage(offset int, limit int) (result []*model.MAdPosition, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMAdPositionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mAdPositionDo) Debug() IMAdPositionDo {
	return m.withDO(m.DO.Debug())
}

func (m mAdPositionDo) WithContext(ctx context.Context) IMAdPositionDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mAdPositionDo) ReadDB() IMAdPositionDo {
	return m.Clauses(dbresolver.Read)
}

func (m mAdPositionDo) WriteDB() IMAdPositionDo {
	return m.Clauses(dbresolver.Write)
}

func (m mAdPositionDo) Session(config *gorm.Session) IMAdPositionDo {
	return m.withDO(m.DO.Session(config))
}

func (m mAdPositionDo) Clauses(conds ...clause.Expression) IMAdPositionDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mAdPositionDo) Returning(value interface{}, columns ...string) IMAdPositionDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mAdPositionDo) Not(conds ...gen.Condition) IMAdPositionDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mAdPositionDo) Or(conds ...gen.Condition) IMAdPositionDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mAdPositionDo) Select(conds ...field.Expr) IMAdPositionDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mAdPositionDo) Where(conds ...gen.Condition) IMAdPositionDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mAdPositionDo) Order(conds ...field.Expr) IMAdPositionDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mAdPositionDo) Distinct(cols ...field.Expr) IMAdPositionDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mAdPositionDo) Omit(cols ...field.Expr) IMAdPositionDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mAdPositionDo) Join(table schema.Tabler, on ...field.Expr) IMAdPositionDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mAdPositionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMAdPositionDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mAdPositionDo) RightJoin(table schema.Tabler, on ...field.Expr) IMAdPositionDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mAdPositionDo) Group(cols ...field.Expr) IMAdPositionDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mAdPositionDo) Having(conds ...gen.Condition) IMAdPositionDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mAdPositionDo) Limit(limit int) IMAdPositionDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mAdPositionDo) Offset(offset int) IMAdPositionDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mAdPositionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMAdPositionDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mAdPositionDo) Unscoped() IMAdPositionDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mAdPositionDo) Create(values ...*model.MAdPosition) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mAdPositionDo) CreateInBatches(values []*model.MAdPosition, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mAdPositionDo) Save(values ...*model.MAdPosition) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mAdPositionDo) First() (*model.MAdPosition, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MAdPosition), nil
	}
}

func (m mAdPositionDo) Take() (*model.MAdPosition, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MAdPosition), nil
	}
}

func (m mAdPositionDo) Last() (*model.MAdPosition, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MAdPosition), nil
	}
}

func (m mAdPositionDo) Find() ([]*model.MAdPosition, error) {
	result, err := m.DO.Find()
	return result.([]*model.MAdPosition), err
}

func (m mAdPositionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MAdPosition, err error) {
	buf := make([]*model.MAdPosition, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mAdPositionDo) FindInBatches(result *[]*model.MAdPosition, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mAdPositionDo) Attrs(attrs ...field.AssignExpr) IMAdPositionDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mAdPositionDo) Assign(attrs ...field.AssignExpr) IMAdPositionDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mAdPositionDo) Joins(fields ...field.RelationField) IMAdPositionDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mAdPositionDo) Preload(fields ...field.RelationField) IMAdPositionDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mAdPositionDo) FirstOrInit() (*model.MAdPosition, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MAdPosition), nil
	}
}

func (m mAdPositionDo) FirstOrCreate() (*model.MAdPosition, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MAdPosition), nil
	}
}

func (m mAdPositionDo) FindByPage(offset int, limit int) (result []*model.MAdPosition, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mAdPositionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mAdPositionDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mAdPositionDo) Delete(models ...*model.MAdPosition) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mAdPositionDo) withDO(do gen.Dao) *mAdPositionDo {
	m.DO = *do.(*gen.DO)
	return m
}
