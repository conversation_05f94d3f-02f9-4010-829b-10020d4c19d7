// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMSwitchSceneValue(db *gorm.DB, opts ...gen.DOOption) mSwitchSceneValue {
	_mSwitchSceneValue := mSwitchSceneValue{}

	_mSwitchSceneValue.mSwitchSceneValueDo.UseDB(db, opts...)
	_mSwitchSceneValue.mSwitchSceneValueDo.UseModel(&model.MSwitchSceneValue{})

	tableName := _mSwitchSceneValue.mSwitchSceneValueDo.TableName()
	_mSwitchSceneValue.ALL = field.NewAsterisk(tableName)
	_mSwitchSceneValue.ID = field.NewInt32(tableName, "id")
	_mSwitchSceneValue.UUID = field.NewString(tableName, "uuid")
	_mSwitchSceneValue.RestrainItemTp = field.NewString(tableName, "restrain_item_tp")
	_mSwitchSceneValue.Key = field.NewString(tableName, "key")
	_mSwitchSceneValue.Value = field.NewString(tableName, "value")
	_mSwitchSceneValue.Sort = field.NewInt32(tableName, "sort")

	_mSwitchSceneValue.fillFieldMap()

	return _mSwitchSceneValue
}

type mSwitchSceneValue struct {
	mSwitchSceneValueDo

	ALL            field.Asterisk
	ID             field.Int32
	UUID           field.String
	RestrainItemTp field.String
	Key            field.String
	Value          field.String
	Sort           field.Int32

	fieldMap map[string]field.Expr
}

func (m mSwitchSceneValue) Table(newTableName string) *mSwitchSceneValue {
	m.mSwitchSceneValueDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mSwitchSceneValue) As(alias string) *mSwitchSceneValue {
	m.mSwitchSceneValueDo.DO = *(m.mSwitchSceneValueDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mSwitchSceneValue) updateTableName(table string) *mSwitchSceneValue {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.UUID = field.NewString(table, "uuid")
	m.RestrainItemTp = field.NewString(table, "restrain_item_tp")
	m.Key = field.NewString(table, "key")
	m.Value = field.NewString(table, "value")
	m.Sort = field.NewInt32(table, "sort")

	m.fillFieldMap()

	return m
}

func (m *mSwitchSceneValue) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mSwitchSceneValue) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 6)
	m.fieldMap["id"] = m.ID
	m.fieldMap["uuid"] = m.UUID
	m.fieldMap["restrain_item_tp"] = m.RestrainItemTp
	m.fieldMap["key"] = m.Key
	m.fieldMap["value"] = m.Value
	m.fieldMap["sort"] = m.Sort
}

func (m mSwitchSceneValue) clone(db *gorm.DB) mSwitchSceneValue {
	m.mSwitchSceneValueDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mSwitchSceneValue) replaceDB(db *gorm.DB) mSwitchSceneValue {
	m.mSwitchSceneValueDo.ReplaceDB(db)
	return m
}

type mSwitchSceneValueDo struct{ gen.DO }

type IMSwitchSceneValueDo interface {
	gen.SubQuery
	Debug() IMSwitchSceneValueDo
	WithContext(ctx context.Context) IMSwitchSceneValueDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMSwitchSceneValueDo
	WriteDB() IMSwitchSceneValueDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMSwitchSceneValueDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMSwitchSceneValueDo
	Not(conds ...gen.Condition) IMSwitchSceneValueDo
	Or(conds ...gen.Condition) IMSwitchSceneValueDo
	Select(conds ...field.Expr) IMSwitchSceneValueDo
	Where(conds ...gen.Condition) IMSwitchSceneValueDo
	Order(conds ...field.Expr) IMSwitchSceneValueDo
	Distinct(cols ...field.Expr) IMSwitchSceneValueDo
	Omit(cols ...field.Expr) IMSwitchSceneValueDo
	Join(table schema.Tabler, on ...field.Expr) IMSwitchSceneValueDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMSwitchSceneValueDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMSwitchSceneValueDo
	Group(cols ...field.Expr) IMSwitchSceneValueDo
	Having(conds ...gen.Condition) IMSwitchSceneValueDo
	Limit(limit int) IMSwitchSceneValueDo
	Offset(offset int) IMSwitchSceneValueDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMSwitchSceneValueDo
	Unscoped() IMSwitchSceneValueDo
	Create(values ...*model.MSwitchSceneValue) error
	CreateInBatches(values []*model.MSwitchSceneValue, batchSize int) error
	Save(values ...*model.MSwitchSceneValue) error
	First() (*model.MSwitchSceneValue, error)
	Take() (*model.MSwitchSceneValue, error)
	Last() (*model.MSwitchSceneValue, error)
	Find() ([]*model.MSwitchSceneValue, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MSwitchSceneValue, err error)
	FindInBatches(result *[]*model.MSwitchSceneValue, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MSwitchSceneValue) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMSwitchSceneValueDo
	Assign(attrs ...field.AssignExpr) IMSwitchSceneValueDo
	Joins(fields ...field.RelationField) IMSwitchSceneValueDo
	Preload(fields ...field.RelationField) IMSwitchSceneValueDo
	FirstOrInit() (*model.MSwitchSceneValue, error)
	FirstOrCreate() (*model.MSwitchSceneValue, error)
	FindByPage(offset int, limit int) (result []*model.MSwitchSceneValue, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMSwitchSceneValueDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mSwitchSceneValueDo) Debug() IMSwitchSceneValueDo {
	return m.withDO(m.DO.Debug())
}

func (m mSwitchSceneValueDo) WithContext(ctx context.Context) IMSwitchSceneValueDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mSwitchSceneValueDo) ReadDB() IMSwitchSceneValueDo {
	return m.Clauses(dbresolver.Read)
}

func (m mSwitchSceneValueDo) WriteDB() IMSwitchSceneValueDo {
	return m.Clauses(dbresolver.Write)
}

func (m mSwitchSceneValueDo) Session(config *gorm.Session) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Session(config))
}

func (m mSwitchSceneValueDo) Clauses(conds ...clause.Expression) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mSwitchSceneValueDo) Returning(value interface{}, columns ...string) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mSwitchSceneValueDo) Not(conds ...gen.Condition) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mSwitchSceneValueDo) Or(conds ...gen.Condition) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mSwitchSceneValueDo) Select(conds ...field.Expr) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mSwitchSceneValueDo) Where(conds ...gen.Condition) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mSwitchSceneValueDo) Order(conds ...field.Expr) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mSwitchSceneValueDo) Distinct(cols ...field.Expr) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mSwitchSceneValueDo) Omit(cols ...field.Expr) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mSwitchSceneValueDo) Join(table schema.Tabler, on ...field.Expr) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mSwitchSceneValueDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMSwitchSceneValueDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mSwitchSceneValueDo) RightJoin(table schema.Tabler, on ...field.Expr) IMSwitchSceneValueDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mSwitchSceneValueDo) Group(cols ...field.Expr) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mSwitchSceneValueDo) Having(conds ...gen.Condition) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mSwitchSceneValueDo) Limit(limit int) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mSwitchSceneValueDo) Offset(offset int) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mSwitchSceneValueDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mSwitchSceneValueDo) Unscoped() IMSwitchSceneValueDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mSwitchSceneValueDo) Create(values ...*model.MSwitchSceneValue) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mSwitchSceneValueDo) CreateInBatches(values []*model.MSwitchSceneValue, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mSwitchSceneValueDo) Save(values ...*model.MSwitchSceneValue) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mSwitchSceneValueDo) First() (*model.MSwitchSceneValue, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSwitchSceneValue), nil
	}
}

func (m mSwitchSceneValueDo) Take() (*model.MSwitchSceneValue, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSwitchSceneValue), nil
	}
}

func (m mSwitchSceneValueDo) Last() (*model.MSwitchSceneValue, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSwitchSceneValue), nil
	}
}

func (m mSwitchSceneValueDo) Find() ([]*model.MSwitchSceneValue, error) {
	result, err := m.DO.Find()
	return result.([]*model.MSwitchSceneValue), err
}

func (m mSwitchSceneValueDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MSwitchSceneValue, err error) {
	buf := make([]*model.MSwitchSceneValue, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mSwitchSceneValueDo) FindInBatches(result *[]*model.MSwitchSceneValue, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mSwitchSceneValueDo) Attrs(attrs ...field.AssignExpr) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mSwitchSceneValueDo) Assign(attrs ...field.AssignExpr) IMSwitchSceneValueDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mSwitchSceneValueDo) Joins(fields ...field.RelationField) IMSwitchSceneValueDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mSwitchSceneValueDo) Preload(fields ...field.RelationField) IMSwitchSceneValueDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mSwitchSceneValueDo) FirstOrInit() (*model.MSwitchSceneValue, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSwitchSceneValue), nil
	}
}

func (m mSwitchSceneValueDo) FirstOrCreate() (*model.MSwitchSceneValue, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSwitchSceneValue), nil
	}
}

func (m mSwitchSceneValueDo) FindByPage(offset int, limit int) (result []*model.MSwitchSceneValue, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mSwitchSceneValueDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mSwitchSceneValueDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mSwitchSceneValueDo) Delete(models ...*model.MSwitchSceneValue) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mSwitchSceneValueDo) withDO(do gen.Dao) *mSwitchSceneValueDo {
	m.DO = *do.(*gen.DO)
	return m
}
