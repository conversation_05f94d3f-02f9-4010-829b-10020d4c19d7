// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMUpload(db *gorm.DB, opts ...gen.DOOption) mUpload {
	_mUpload := mUpload{}

	_mUpload.mUploadDo.UseDB(db, opts...)
	_mUpload.mUploadDo.UseModel(&model.MUpload{})

	tableName := _mUpload.mUploadDo.TableName()
	_mUpload.ALL = field.NewAsterisk(tableName)
	_mUpload.ID = field.NewInt32(tableName, "id")
	_mUpload.FileName = field.NewString(tableName, "file_name")
	_mUpload.FileSize = field.NewInt64(tableName, "file_size")
	_mUpload.FileType = field.NewString(tableName, "file_type")
	_mUpload.Md5 = field.NewString(tableName, "md5")
	_mUpload.URL = field.NewString(tableName, "url")
	_mUpload.OssURL = field.NewString(tableName, "oss_url")
	_mUpload.OssBucket = field.NewString(tableName, "oss_bucket")
	_mUpload.Description = field.NewString(tableName, "description")
	_mUpload.CreatorID = field.NewString(tableName, "creator_id")
	_mUpload.CreatedAt = field.NewInt64(tableName, "created_at")
	_mUpload.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mUpload.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mUpload.fillFieldMap()

	return _mUpload
}

type mUpload struct {
	mUploadDo

	ALL         field.Asterisk
	ID          field.Int32
	FileName    field.String
	FileSize    field.Int64
	FileType    field.String // 文件后缀类型
	Md5         field.String // md5值
	URL         field.String // 完整url
	OssURL      field.String
	OssBucket   field.String
	Description field.String // 文件描述
	CreatorID   field.String
	CreatedAt   field.Int64
	UpdatedAt   field.Int64
	IsDeleted   field.Bool

	fieldMap map[string]field.Expr
}

func (m mUpload) Table(newTableName string) *mUpload {
	m.mUploadDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mUpload) As(alias string) *mUpload {
	m.mUploadDo.DO = *(m.mUploadDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mUpload) updateTableName(table string) *mUpload {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.FileName = field.NewString(table, "file_name")
	m.FileSize = field.NewInt64(table, "file_size")
	m.FileType = field.NewString(table, "file_type")
	m.Md5 = field.NewString(table, "md5")
	m.URL = field.NewString(table, "url")
	m.OssURL = field.NewString(table, "oss_url")
	m.OssBucket = field.NewString(table, "oss_bucket")
	m.Description = field.NewString(table, "description")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mUpload) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mUpload) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 13)
	m.fieldMap["id"] = m.ID
	m.fieldMap["file_name"] = m.FileName
	m.fieldMap["file_size"] = m.FileSize
	m.fieldMap["file_type"] = m.FileType
	m.fieldMap["md5"] = m.Md5
	m.fieldMap["url"] = m.URL
	m.fieldMap["oss_url"] = m.OssURL
	m.fieldMap["oss_bucket"] = m.OssBucket
	m.fieldMap["description"] = m.Description
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mUpload) clone(db *gorm.DB) mUpload {
	m.mUploadDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mUpload) replaceDB(db *gorm.DB) mUpload {
	m.mUploadDo.ReplaceDB(db)
	return m
}

type mUploadDo struct{ gen.DO }

type IMUploadDo interface {
	gen.SubQuery
	Debug() IMUploadDo
	WithContext(ctx context.Context) IMUploadDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMUploadDo
	WriteDB() IMUploadDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMUploadDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMUploadDo
	Not(conds ...gen.Condition) IMUploadDo
	Or(conds ...gen.Condition) IMUploadDo
	Select(conds ...field.Expr) IMUploadDo
	Where(conds ...gen.Condition) IMUploadDo
	Order(conds ...field.Expr) IMUploadDo
	Distinct(cols ...field.Expr) IMUploadDo
	Omit(cols ...field.Expr) IMUploadDo
	Join(table schema.Tabler, on ...field.Expr) IMUploadDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMUploadDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMUploadDo
	Group(cols ...field.Expr) IMUploadDo
	Having(conds ...gen.Condition) IMUploadDo
	Limit(limit int) IMUploadDo
	Offset(offset int) IMUploadDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMUploadDo
	Unscoped() IMUploadDo
	Create(values ...*model.MUpload) error
	CreateInBatches(values []*model.MUpload, batchSize int) error
	Save(values ...*model.MUpload) error
	First() (*model.MUpload, error)
	Take() (*model.MUpload, error)
	Last() (*model.MUpload, error)
	Find() ([]*model.MUpload, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MUpload, err error)
	FindInBatches(result *[]*model.MUpload, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MUpload) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMUploadDo
	Assign(attrs ...field.AssignExpr) IMUploadDo
	Joins(fields ...field.RelationField) IMUploadDo
	Preload(fields ...field.RelationField) IMUploadDo
	FirstOrInit() (*model.MUpload, error)
	FirstOrCreate() (*model.MUpload, error)
	FindByPage(offset int, limit int) (result []*model.MUpload, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMUploadDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mUploadDo) Debug() IMUploadDo {
	return m.withDO(m.DO.Debug())
}

func (m mUploadDo) WithContext(ctx context.Context) IMUploadDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mUploadDo) ReadDB() IMUploadDo {
	return m.Clauses(dbresolver.Read)
}

func (m mUploadDo) WriteDB() IMUploadDo {
	return m.Clauses(dbresolver.Write)
}

func (m mUploadDo) Session(config *gorm.Session) IMUploadDo {
	return m.withDO(m.DO.Session(config))
}

func (m mUploadDo) Clauses(conds ...clause.Expression) IMUploadDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mUploadDo) Returning(value interface{}, columns ...string) IMUploadDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mUploadDo) Not(conds ...gen.Condition) IMUploadDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mUploadDo) Or(conds ...gen.Condition) IMUploadDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mUploadDo) Select(conds ...field.Expr) IMUploadDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mUploadDo) Where(conds ...gen.Condition) IMUploadDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mUploadDo) Order(conds ...field.Expr) IMUploadDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mUploadDo) Distinct(cols ...field.Expr) IMUploadDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mUploadDo) Omit(cols ...field.Expr) IMUploadDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mUploadDo) Join(table schema.Tabler, on ...field.Expr) IMUploadDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mUploadDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMUploadDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mUploadDo) RightJoin(table schema.Tabler, on ...field.Expr) IMUploadDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mUploadDo) Group(cols ...field.Expr) IMUploadDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mUploadDo) Having(conds ...gen.Condition) IMUploadDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mUploadDo) Limit(limit int) IMUploadDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mUploadDo) Offset(offset int) IMUploadDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mUploadDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMUploadDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mUploadDo) Unscoped() IMUploadDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mUploadDo) Create(values ...*model.MUpload) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mUploadDo) CreateInBatches(values []*model.MUpload, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mUploadDo) Save(values ...*model.MUpload) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mUploadDo) First() (*model.MUpload, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUpload), nil
	}
}

func (m mUploadDo) Take() (*model.MUpload, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUpload), nil
	}
}

func (m mUploadDo) Last() (*model.MUpload, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUpload), nil
	}
}

func (m mUploadDo) Find() ([]*model.MUpload, error) {
	result, err := m.DO.Find()
	return result.([]*model.MUpload), err
}

func (m mUploadDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MUpload, err error) {
	buf := make([]*model.MUpload, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mUploadDo) FindInBatches(result *[]*model.MUpload, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mUploadDo) Attrs(attrs ...field.AssignExpr) IMUploadDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mUploadDo) Assign(attrs ...field.AssignExpr) IMUploadDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mUploadDo) Joins(fields ...field.RelationField) IMUploadDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mUploadDo) Preload(fields ...field.RelationField) IMUploadDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mUploadDo) FirstOrInit() (*model.MUpload, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUpload), nil
	}
}

func (m mUploadDo) FirstOrCreate() (*model.MUpload, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUpload), nil
	}
}

func (m mUploadDo) FindByPage(offset int, limit int) (result []*model.MUpload, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mUploadDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mUploadDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mUploadDo) Delete(models ...*model.MUpload) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mUploadDo) withDO(do gen.Dao) *mUploadDo {
	m.DO = *do.(*gen.DO)
	return m
}
