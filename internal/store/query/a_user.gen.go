// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newAUser(db *gorm.DB, opts ...gen.DOOption) aUser {
	_aUser := aUser{}

	_aUser.aUserDo.UseDB(db, opts...)
	_aUser.aUserDo.UseModel(&model.AUser{})

	tableName := _aUser.aUserDo.TableName()
	_aUser.ALL = field.NewAsterisk(tableName)
	_aUser.ID = field.NewInt32(tableName, "id")
	_aUser.GameID = field.NewString(tableName, "game_id")
	_aUser.UserID = field.NewString(tableName, "user_id")
	_aUser.Channel = field.NewString(tableName, "channel")
	_aUser.AdFrom = field.NewString(tableName, "ad_from")
	_aUser.CreatedAt = field.NewInt64(tableName, "created_at")
	_aUser.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aUser.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aUser.fillFieldMap()

	return _aUser
}

type aUser struct {
	aUserDo

	ALL       field.Asterisk
	ID        field.Int32
	GameID    field.String // 游戏id
	UserID    field.String // uuid
	Channel   field.String
	AdFrom    field.String
	CreatedAt field.Int64
	UpdatedAt field.Int64
	IsDeleted field.Bool

	fieldMap map[string]field.Expr
}

func (a aUser) Table(newTableName string) *aUser {
	a.aUserDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aUser) As(alias string) *aUser {
	a.aUserDo.DO = *(a.aUserDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aUser) updateTableName(table string) *aUser {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.GameID = field.NewString(table, "game_id")
	a.UserID = field.NewString(table, "user_id")
	a.Channel = field.NewString(table, "channel")
	a.AdFrom = field.NewString(table, "ad_from")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aUser) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 8)
	a.fieldMap["id"] = a.ID
	a.fieldMap["game_id"] = a.GameID
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["channel"] = a.Channel
	a.fieldMap["ad_from"] = a.AdFrom
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aUser) clone(db *gorm.DB) aUser {
	a.aUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aUser) replaceDB(db *gorm.DB) aUser {
	a.aUserDo.ReplaceDB(db)
	return a
}

type aUserDo struct{ gen.DO }

type IAUserDo interface {
	gen.SubQuery
	Debug() IAUserDo
	WithContext(ctx context.Context) IAUserDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAUserDo
	WriteDB() IAUserDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAUserDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAUserDo
	Not(conds ...gen.Condition) IAUserDo
	Or(conds ...gen.Condition) IAUserDo
	Select(conds ...field.Expr) IAUserDo
	Where(conds ...gen.Condition) IAUserDo
	Order(conds ...field.Expr) IAUserDo
	Distinct(cols ...field.Expr) IAUserDo
	Omit(cols ...field.Expr) IAUserDo
	Join(table schema.Tabler, on ...field.Expr) IAUserDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAUserDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAUserDo
	Group(cols ...field.Expr) IAUserDo
	Having(conds ...gen.Condition) IAUserDo
	Limit(limit int) IAUserDo
	Offset(offset int) IAUserDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserDo
	Unscoped() IAUserDo
	Create(values ...*model.AUser) error
	CreateInBatches(values []*model.AUser, batchSize int) error
	Save(values ...*model.AUser) error
	First() (*model.AUser, error)
	Take() (*model.AUser, error)
	Last() (*model.AUser, error)
	Find() ([]*model.AUser, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUser, err error)
	FindInBatches(result *[]*model.AUser, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AUser) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAUserDo
	Assign(attrs ...field.AssignExpr) IAUserDo
	Joins(fields ...field.RelationField) IAUserDo
	Preload(fields ...field.RelationField) IAUserDo
	FirstOrInit() (*model.AUser, error)
	FirstOrCreate() (*model.AUser, error)
	FindByPage(offset int, limit int) (result []*model.AUser, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAUserDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aUserDo) Debug() IAUserDo {
	return a.withDO(a.DO.Debug())
}

func (a aUserDo) WithContext(ctx context.Context) IAUserDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aUserDo) ReadDB() IAUserDo {
	return a.Clauses(dbresolver.Read)
}

func (a aUserDo) WriteDB() IAUserDo {
	return a.Clauses(dbresolver.Write)
}

func (a aUserDo) Session(config *gorm.Session) IAUserDo {
	return a.withDO(a.DO.Session(config))
}

func (a aUserDo) Clauses(conds ...clause.Expression) IAUserDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aUserDo) Returning(value interface{}, columns ...string) IAUserDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aUserDo) Not(conds ...gen.Condition) IAUserDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aUserDo) Or(conds ...gen.Condition) IAUserDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aUserDo) Select(conds ...field.Expr) IAUserDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aUserDo) Where(conds ...gen.Condition) IAUserDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aUserDo) Order(conds ...field.Expr) IAUserDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aUserDo) Distinct(cols ...field.Expr) IAUserDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aUserDo) Omit(cols ...field.Expr) IAUserDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aUserDo) Join(table schema.Tabler, on ...field.Expr) IAUserDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAUserDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aUserDo) RightJoin(table schema.Tabler, on ...field.Expr) IAUserDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aUserDo) Group(cols ...field.Expr) IAUserDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aUserDo) Having(conds ...gen.Condition) IAUserDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aUserDo) Limit(limit int) IAUserDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aUserDo) Offset(offset int) IAUserDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aUserDo) Unscoped() IAUserDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aUserDo) Create(values ...*model.AUser) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aUserDo) CreateInBatches(values []*model.AUser, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aUserDo) Save(values ...*model.AUser) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aUserDo) First() (*model.AUser, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUser), nil
	}
}

func (a aUserDo) Take() (*model.AUser, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUser), nil
	}
}

func (a aUserDo) Last() (*model.AUser, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUser), nil
	}
}

func (a aUserDo) Find() ([]*model.AUser, error) {
	result, err := a.DO.Find()
	return result.([]*model.AUser), err
}

func (a aUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUser, err error) {
	buf := make([]*model.AUser, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aUserDo) FindInBatches(result *[]*model.AUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aUserDo) Attrs(attrs ...field.AssignExpr) IAUserDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aUserDo) Assign(attrs ...field.AssignExpr) IAUserDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aUserDo) Joins(fields ...field.RelationField) IAUserDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aUserDo) Preload(fields ...field.RelationField) IAUserDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aUserDo) FirstOrInit() (*model.AUser, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUser), nil
	}
}

func (a aUserDo) FirstOrCreate() (*model.AUser, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUser), nil
	}
}

func (a aUserDo) FindByPage(offset int, limit int) (result []*model.AUser, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aUserDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aUserDo) Delete(models ...*model.AUser) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aUserDo) withDO(do gen.Dao) *aUserDo {
	a.DO = *do.(*gen.DO)
	return a
}
