// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newH5PlayableDate(db *gorm.DB, opts ...gen.DOOption) h5PlayableDate {
	_h5PlayableDate := h5PlayableDate{}

	_h5PlayableDate.h5PlayableDateDo.UseDB(db, opts...)
	_h5PlayableDate.h5PlayableDateDo.UseModel(&model.H5PlayableDate{})

	tableName := _h5PlayableDate.h5PlayableDateDo.TableName()
	_h5PlayableDate.ALL = field.NewAsterisk(tableName)
	_h5PlayableDate.ID = field.NewInt32(tableName, "id")
	_h5PlayableDate.PlayableDate = field.NewTime(tableName, "playable_date")
	_h5PlayableDate.Description = field.NewString(tableName, "description")
	_h5PlayableDate.CreatorID = field.NewString(tableName, "creator_id")
	_h5PlayableDate.CreatedAt = field.NewInt64(tableName, "created_at")
	_h5PlayableDate.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_h5PlayableDate.IsDeleted = field.NewBool(tableName, "is_deleted")

	_h5PlayableDate.fillFieldMap()

	return _h5PlayableDate
}

// h5PlayableDate H5可玩日期表
type h5PlayableDate struct {
	h5PlayableDateDo

	ALL          field.Asterisk
	ID           field.Int32  // 主键ID
	PlayableDate field.Time   // 可玩日期（北京时区）
	Description  field.String // 日期描述信息
	CreatorID    field.String // 创建人ID
	CreatedAt    field.Int64  // 创建时间戳（毫秒）
	UpdatedAt    field.Int64  // 更新时间戳（毫秒）
	IsDeleted    field.Bool   // 逻辑删除，0=未删，1=已删

	fieldMap map[string]field.Expr
}

func (h h5PlayableDate) Table(newTableName string) *h5PlayableDate {
	h.h5PlayableDateDo.UseTable(newTableName)
	return h.updateTableName(newTableName)
}

func (h h5PlayableDate) As(alias string) *h5PlayableDate {
	h.h5PlayableDateDo.DO = *(h.h5PlayableDateDo.As(alias).(*gen.DO))
	return h.updateTableName(alias)
}

func (h *h5PlayableDate) updateTableName(table string) *h5PlayableDate {
	h.ALL = field.NewAsterisk(table)
	h.ID = field.NewInt32(table, "id")
	h.PlayableDate = field.NewTime(table, "playable_date")
	h.Description = field.NewString(table, "description")
	h.CreatorID = field.NewString(table, "creator_id")
	h.CreatedAt = field.NewInt64(table, "created_at")
	h.UpdatedAt = field.NewInt64(table, "updated_at")
	h.IsDeleted = field.NewBool(table, "is_deleted")

	h.fillFieldMap()

	return h
}

func (h *h5PlayableDate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := h.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (h *h5PlayableDate) fillFieldMap() {
	h.fieldMap = make(map[string]field.Expr, 7)
	h.fieldMap["id"] = h.ID
	h.fieldMap["playable_date"] = h.PlayableDate
	h.fieldMap["description"] = h.Description
	h.fieldMap["creator_id"] = h.CreatorID
	h.fieldMap["created_at"] = h.CreatedAt
	h.fieldMap["updated_at"] = h.UpdatedAt
	h.fieldMap["is_deleted"] = h.IsDeleted
}

func (h h5PlayableDate) clone(db *gorm.DB) h5PlayableDate {
	h.h5PlayableDateDo.ReplaceConnPool(db.Statement.ConnPool)
	return h
}

func (h h5PlayableDate) replaceDB(db *gorm.DB) h5PlayableDate {
	h.h5PlayableDateDo.ReplaceDB(db)
	return h
}

type h5PlayableDateDo struct{ gen.DO }

type IH5PlayableDateDo interface {
	gen.SubQuery
	Debug() IH5PlayableDateDo
	WithContext(ctx context.Context) IH5PlayableDateDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IH5PlayableDateDo
	WriteDB() IH5PlayableDateDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IH5PlayableDateDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IH5PlayableDateDo
	Not(conds ...gen.Condition) IH5PlayableDateDo
	Or(conds ...gen.Condition) IH5PlayableDateDo
	Select(conds ...field.Expr) IH5PlayableDateDo
	Where(conds ...gen.Condition) IH5PlayableDateDo
	Order(conds ...field.Expr) IH5PlayableDateDo
	Distinct(cols ...field.Expr) IH5PlayableDateDo
	Omit(cols ...field.Expr) IH5PlayableDateDo
	Join(table schema.Tabler, on ...field.Expr) IH5PlayableDateDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IH5PlayableDateDo
	RightJoin(table schema.Tabler, on ...field.Expr) IH5PlayableDateDo
	Group(cols ...field.Expr) IH5PlayableDateDo
	Having(conds ...gen.Condition) IH5PlayableDateDo
	Limit(limit int) IH5PlayableDateDo
	Offset(offset int) IH5PlayableDateDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IH5PlayableDateDo
	Unscoped() IH5PlayableDateDo
	Create(values ...*model.H5PlayableDate) error
	CreateInBatches(values []*model.H5PlayableDate, batchSize int) error
	Save(values ...*model.H5PlayableDate) error
	First() (*model.H5PlayableDate, error)
	Take() (*model.H5PlayableDate, error)
	Last() (*model.H5PlayableDate, error)
	Find() ([]*model.H5PlayableDate, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.H5PlayableDate, err error)
	FindInBatches(result *[]*model.H5PlayableDate, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.H5PlayableDate) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IH5PlayableDateDo
	Assign(attrs ...field.AssignExpr) IH5PlayableDateDo
	Joins(fields ...field.RelationField) IH5PlayableDateDo
	Preload(fields ...field.RelationField) IH5PlayableDateDo
	FirstOrInit() (*model.H5PlayableDate, error)
	FirstOrCreate() (*model.H5PlayableDate, error)
	FindByPage(offset int, limit int) (result []*model.H5PlayableDate, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IH5PlayableDateDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (h h5PlayableDateDo) Debug() IH5PlayableDateDo {
	return h.withDO(h.DO.Debug())
}

func (h h5PlayableDateDo) WithContext(ctx context.Context) IH5PlayableDateDo {
	return h.withDO(h.DO.WithContext(ctx))
}

func (h h5PlayableDateDo) ReadDB() IH5PlayableDateDo {
	return h.Clauses(dbresolver.Read)
}

func (h h5PlayableDateDo) WriteDB() IH5PlayableDateDo {
	return h.Clauses(dbresolver.Write)
}

func (h h5PlayableDateDo) Session(config *gorm.Session) IH5PlayableDateDo {
	return h.withDO(h.DO.Session(config))
}

func (h h5PlayableDateDo) Clauses(conds ...clause.Expression) IH5PlayableDateDo {
	return h.withDO(h.DO.Clauses(conds...))
}

func (h h5PlayableDateDo) Returning(value interface{}, columns ...string) IH5PlayableDateDo {
	return h.withDO(h.DO.Returning(value, columns...))
}

func (h h5PlayableDateDo) Not(conds ...gen.Condition) IH5PlayableDateDo {
	return h.withDO(h.DO.Not(conds...))
}

func (h h5PlayableDateDo) Or(conds ...gen.Condition) IH5PlayableDateDo {
	return h.withDO(h.DO.Or(conds...))
}

func (h h5PlayableDateDo) Select(conds ...field.Expr) IH5PlayableDateDo {
	return h.withDO(h.DO.Select(conds...))
}

func (h h5PlayableDateDo) Where(conds ...gen.Condition) IH5PlayableDateDo {
	return h.withDO(h.DO.Where(conds...))
}

func (h h5PlayableDateDo) Order(conds ...field.Expr) IH5PlayableDateDo {
	return h.withDO(h.DO.Order(conds...))
}

func (h h5PlayableDateDo) Distinct(cols ...field.Expr) IH5PlayableDateDo {
	return h.withDO(h.DO.Distinct(cols...))
}

func (h h5PlayableDateDo) Omit(cols ...field.Expr) IH5PlayableDateDo {
	return h.withDO(h.DO.Omit(cols...))
}

func (h h5PlayableDateDo) Join(table schema.Tabler, on ...field.Expr) IH5PlayableDateDo {
	return h.withDO(h.DO.Join(table, on...))
}

func (h h5PlayableDateDo) LeftJoin(table schema.Tabler, on ...field.Expr) IH5PlayableDateDo {
	return h.withDO(h.DO.LeftJoin(table, on...))
}

func (h h5PlayableDateDo) RightJoin(table schema.Tabler, on ...field.Expr) IH5PlayableDateDo {
	return h.withDO(h.DO.RightJoin(table, on...))
}

func (h h5PlayableDateDo) Group(cols ...field.Expr) IH5PlayableDateDo {
	return h.withDO(h.DO.Group(cols...))
}

func (h h5PlayableDateDo) Having(conds ...gen.Condition) IH5PlayableDateDo {
	return h.withDO(h.DO.Having(conds...))
}

func (h h5PlayableDateDo) Limit(limit int) IH5PlayableDateDo {
	return h.withDO(h.DO.Limit(limit))
}

func (h h5PlayableDateDo) Offset(offset int) IH5PlayableDateDo {
	return h.withDO(h.DO.Offset(offset))
}

func (h h5PlayableDateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IH5PlayableDateDo {
	return h.withDO(h.DO.Scopes(funcs...))
}

func (h h5PlayableDateDo) Unscoped() IH5PlayableDateDo {
	return h.withDO(h.DO.Unscoped())
}

func (h h5PlayableDateDo) Create(values ...*model.H5PlayableDate) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Create(values)
}

func (h h5PlayableDateDo) CreateInBatches(values []*model.H5PlayableDate, batchSize int) error {
	return h.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (h h5PlayableDateDo) Save(values ...*model.H5PlayableDate) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Save(values)
}

func (h h5PlayableDateDo) First() (*model.H5PlayableDate, error) {
	if result, err := h.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5PlayableDate), nil
	}
}

func (h h5PlayableDateDo) Take() (*model.H5PlayableDate, error) {
	if result, err := h.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5PlayableDate), nil
	}
}

func (h h5PlayableDateDo) Last() (*model.H5PlayableDate, error) {
	if result, err := h.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5PlayableDate), nil
	}
}

func (h h5PlayableDateDo) Find() ([]*model.H5PlayableDate, error) {
	result, err := h.DO.Find()
	return result.([]*model.H5PlayableDate), err
}

func (h h5PlayableDateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.H5PlayableDate, err error) {
	buf := make([]*model.H5PlayableDate, 0, batchSize)
	err = h.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (h h5PlayableDateDo) FindInBatches(result *[]*model.H5PlayableDate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return h.DO.FindInBatches(result, batchSize, fc)
}

func (h h5PlayableDateDo) Attrs(attrs ...field.AssignExpr) IH5PlayableDateDo {
	return h.withDO(h.DO.Attrs(attrs...))
}

func (h h5PlayableDateDo) Assign(attrs ...field.AssignExpr) IH5PlayableDateDo {
	return h.withDO(h.DO.Assign(attrs...))
}

func (h h5PlayableDateDo) Joins(fields ...field.RelationField) IH5PlayableDateDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Joins(_f))
	}
	return &h
}

func (h h5PlayableDateDo) Preload(fields ...field.RelationField) IH5PlayableDateDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Preload(_f))
	}
	return &h
}

func (h h5PlayableDateDo) FirstOrInit() (*model.H5PlayableDate, error) {
	if result, err := h.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5PlayableDate), nil
	}
}

func (h h5PlayableDateDo) FirstOrCreate() (*model.H5PlayableDate, error) {
	if result, err := h.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5PlayableDate), nil
	}
}

func (h h5PlayableDateDo) FindByPage(offset int, limit int) (result []*model.H5PlayableDate, count int64, err error) {
	result, err = h.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = h.Offset(-1).Limit(-1).Count()
	return
}

func (h h5PlayableDateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = h.Count()
	if err != nil {
		return
	}

	err = h.Offset(offset).Limit(limit).Scan(result)
	return
}

func (h h5PlayableDateDo) Scan(result interface{}) (err error) {
	return h.DO.Scan(result)
}

func (h h5PlayableDateDo) Delete(models ...*model.H5PlayableDate) (result gen.ResultInfo, err error) {
	return h.DO.Delete(models)
}

func (h *h5PlayableDateDo) withDO(do gen.Dao) *h5PlayableDateDo {
	h.DO = *do.(*gen.DO)
	return h
}
