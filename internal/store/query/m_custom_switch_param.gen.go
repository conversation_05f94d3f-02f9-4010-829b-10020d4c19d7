// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMCustomSwitchParam(db *gorm.DB, opts ...gen.DOOption) mCustomSwitchParam {
	_mCustomSwitchParam := mCustomSwitchParam{}

	_mCustomSwitchParam.mCustomSwitchParamDo.UseDB(db, opts...)
	_mCustomSwitchParam.mCustomSwitchParamDo.UseModel(&model.MCustomSwitchParam{})

	tableName := _mCustomSwitchParam.mCustomSwitchParamDo.TableName()
	_mCustomSwitchParam.ALL = field.NewAsterisk(tableName)
	_mCustomSwitchParam.ID = field.NewInt32(tableName, "id")
	_mCustomSwitchParam.CustomSwitchID = field.NewInt32(tableName, "custom_switch_id")
	_mCustomSwitchParam.ParentID = field.NewInt32(tableName, "parent_id")
	_mCustomSwitchParam.Description = field.NewString(tableName, "description")
	_mCustomSwitchParam.ParamType = field.NewInt32(tableName, "param_type")
	_mCustomSwitchParam.ParamData = field.NewString(tableName, "param_data")
	_mCustomSwitchParam.OtherParamData = field.NewString(tableName, "other_param_data")
	_mCustomSwitchParam.DefaultReturn = field.NewInt32(tableName, "default_return")
	_mCustomSwitchParam.SortOrder = field.NewInt32(tableName, "sort_order")
	_mCustomSwitchParam.CreatedAt = field.NewInt64(tableName, "created_at")
	_mCustomSwitchParam.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mCustomSwitchParam.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mCustomSwitchParam.fillFieldMap()

	return _mCustomSwitchParam
}

type mCustomSwitchParam struct {
	mCustomSwitchParamDo

	ALL            field.Asterisk
	ID             field.Int32
	CustomSwitchID field.Int32
	ParentID       field.Int32
	Description    field.String // 描述
	ParamType      field.Int32  // 参数类型:  1. 平台 2. 版本 3. 用户昵称 4. IP 5. 区域 6. UniqueId 平台ID 7. 渠道 8. 场景值 9. 自定义参数 10. 其他
	ParamData      field.String
	OtherParamData field.String
	DefaultReturn  field.Int32
	SortOrder      field.Int32
	CreatedAt      field.Int64
	UpdatedAt      field.Int64
	IsDeleted      field.Bool

	fieldMap map[string]field.Expr
}

func (m mCustomSwitchParam) Table(newTableName string) *mCustomSwitchParam {
	m.mCustomSwitchParamDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mCustomSwitchParam) As(alias string) *mCustomSwitchParam {
	m.mCustomSwitchParamDo.DO = *(m.mCustomSwitchParamDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mCustomSwitchParam) updateTableName(table string) *mCustomSwitchParam {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.CustomSwitchID = field.NewInt32(table, "custom_switch_id")
	m.ParentID = field.NewInt32(table, "parent_id")
	m.Description = field.NewString(table, "description")
	m.ParamType = field.NewInt32(table, "param_type")
	m.ParamData = field.NewString(table, "param_data")
	m.OtherParamData = field.NewString(table, "other_param_data")
	m.DefaultReturn = field.NewInt32(table, "default_return")
	m.SortOrder = field.NewInt32(table, "sort_order")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mCustomSwitchParam) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mCustomSwitchParam) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 12)
	m.fieldMap["id"] = m.ID
	m.fieldMap["custom_switch_id"] = m.CustomSwitchID
	m.fieldMap["parent_id"] = m.ParentID
	m.fieldMap["description"] = m.Description
	m.fieldMap["param_type"] = m.ParamType
	m.fieldMap["param_data"] = m.ParamData
	m.fieldMap["other_param_data"] = m.OtherParamData
	m.fieldMap["default_return"] = m.DefaultReturn
	m.fieldMap["sort_order"] = m.SortOrder
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mCustomSwitchParam) clone(db *gorm.DB) mCustomSwitchParam {
	m.mCustomSwitchParamDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mCustomSwitchParam) replaceDB(db *gorm.DB) mCustomSwitchParam {
	m.mCustomSwitchParamDo.ReplaceDB(db)
	return m
}

type mCustomSwitchParamDo struct{ gen.DO }

type IMCustomSwitchParamDo interface {
	gen.SubQuery
	Debug() IMCustomSwitchParamDo
	WithContext(ctx context.Context) IMCustomSwitchParamDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMCustomSwitchParamDo
	WriteDB() IMCustomSwitchParamDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMCustomSwitchParamDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMCustomSwitchParamDo
	Not(conds ...gen.Condition) IMCustomSwitchParamDo
	Or(conds ...gen.Condition) IMCustomSwitchParamDo
	Select(conds ...field.Expr) IMCustomSwitchParamDo
	Where(conds ...gen.Condition) IMCustomSwitchParamDo
	Order(conds ...field.Expr) IMCustomSwitchParamDo
	Distinct(cols ...field.Expr) IMCustomSwitchParamDo
	Omit(cols ...field.Expr) IMCustomSwitchParamDo
	Join(table schema.Tabler, on ...field.Expr) IMCustomSwitchParamDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMCustomSwitchParamDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMCustomSwitchParamDo
	Group(cols ...field.Expr) IMCustomSwitchParamDo
	Having(conds ...gen.Condition) IMCustomSwitchParamDo
	Limit(limit int) IMCustomSwitchParamDo
	Offset(offset int) IMCustomSwitchParamDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMCustomSwitchParamDo
	Unscoped() IMCustomSwitchParamDo
	Create(values ...*model.MCustomSwitchParam) error
	CreateInBatches(values []*model.MCustomSwitchParam, batchSize int) error
	Save(values ...*model.MCustomSwitchParam) error
	First() (*model.MCustomSwitchParam, error)
	Take() (*model.MCustomSwitchParam, error)
	Last() (*model.MCustomSwitchParam, error)
	Find() ([]*model.MCustomSwitchParam, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCustomSwitchParam, err error)
	FindInBatches(result *[]*model.MCustomSwitchParam, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MCustomSwitchParam) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMCustomSwitchParamDo
	Assign(attrs ...field.AssignExpr) IMCustomSwitchParamDo
	Joins(fields ...field.RelationField) IMCustomSwitchParamDo
	Preload(fields ...field.RelationField) IMCustomSwitchParamDo
	FirstOrInit() (*model.MCustomSwitchParam, error)
	FirstOrCreate() (*model.MCustomSwitchParam, error)
	FindByPage(offset int, limit int) (result []*model.MCustomSwitchParam, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMCustomSwitchParamDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mCustomSwitchParamDo) Debug() IMCustomSwitchParamDo {
	return m.withDO(m.DO.Debug())
}

func (m mCustomSwitchParamDo) WithContext(ctx context.Context) IMCustomSwitchParamDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mCustomSwitchParamDo) ReadDB() IMCustomSwitchParamDo {
	return m.Clauses(dbresolver.Read)
}

func (m mCustomSwitchParamDo) WriteDB() IMCustomSwitchParamDo {
	return m.Clauses(dbresolver.Write)
}

func (m mCustomSwitchParamDo) Session(config *gorm.Session) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Session(config))
}

func (m mCustomSwitchParamDo) Clauses(conds ...clause.Expression) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mCustomSwitchParamDo) Returning(value interface{}, columns ...string) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mCustomSwitchParamDo) Not(conds ...gen.Condition) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mCustomSwitchParamDo) Or(conds ...gen.Condition) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mCustomSwitchParamDo) Select(conds ...field.Expr) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mCustomSwitchParamDo) Where(conds ...gen.Condition) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mCustomSwitchParamDo) Order(conds ...field.Expr) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mCustomSwitchParamDo) Distinct(cols ...field.Expr) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mCustomSwitchParamDo) Omit(cols ...field.Expr) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mCustomSwitchParamDo) Join(table schema.Tabler, on ...field.Expr) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mCustomSwitchParamDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMCustomSwitchParamDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mCustomSwitchParamDo) RightJoin(table schema.Tabler, on ...field.Expr) IMCustomSwitchParamDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mCustomSwitchParamDo) Group(cols ...field.Expr) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mCustomSwitchParamDo) Having(conds ...gen.Condition) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mCustomSwitchParamDo) Limit(limit int) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mCustomSwitchParamDo) Offset(offset int) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mCustomSwitchParamDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mCustomSwitchParamDo) Unscoped() IMCustomSwitchParamDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mCustomSwitchParamDo) Create(values ...*model.MCustomSwitchParam) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mCustomSwitchParamDo) CreateInBatches(values []*model.MCustomSwitchParam, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mCustomSwitchParamDo) Save(values ...*model.MCustomSwitchParam) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mCustomSwitchParamDo) First() (*model.MCustomSwitchParam, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomSwitchParam), nil
	}
}

func (m mCustomSwitchParamDo) Take() (*model.MCustomSwitchParam, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomSwitchParam), nil
	}
}

func (m mCustomSwitchParamDo) Last() (*model.MCustomSwitchParam, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomSwitchParam), nil
	}
}

func (m mCustomSwitchParamDo) Find() ([]*model.MCustomSwitchParam, error) {
	result, err := m.DO.Find()
	return result.([]*model.MCustomSwitchParam), err
}

func (m mCustomSwitchParamDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCustomSwitchParam, err error) {
	buf := make([]*model.MCustomSwitchParam, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mCustomSwitchParamDo) FindInBatches(result *[]*model.MCustomSwitchParam, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mCustomSwitchParamDo) Attrs(attrs ...field.AssignExpr) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mCustomSwitchParamDo) Assign(attrs ...field.AssignExpr) IMCustomSwitchParamDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mCustomSwitchParamDo) Joins(fields ...field.RelationField) IMCustomSwitchParamDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mCustomSwitchParamDo) Preload(fields ...field.RelationField) IMCustomSwitchParamDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mCustomSwitchParamDo) FirstOrInit() (*model.MCustomSwitchParam, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomSwitchParam), nil
	}
}

func (m mCustomSwitchParamDo) FirstOrCreate() (*model.MCustomSwitchParam, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomSwitchParam), nil
	}
}

func (m mCustomSwitchParamDo) FindByPage(offset int, limit int) (result []*model.MCustomSwitchParam, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mCustomSwitchParamDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mCustomSwitchParamDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mCustomSwitchParamDo) Delete(models ...*model.MCustomSwitchParam) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mCustomSwitchParamDo) withDO(do gen.Dao) *mCustomSwitchParamDo {
	m.DO = *do.(*gen.DO)
	return m
}
