// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMSensitiveWord(db *gorm.DB, opts ...gen.DOOption) mSensitiveWord {
	_mSensitiveWord := mSensitiveWord{}

	_mSensitiveWord.mSensitiveWordDo.UseDB(db, opts...)
	_mSensitiveWord.mSensitiveWordDo.UseModel(&model.MSensitiveWord{})

	tableName := _mSensitiveWord.mSensitiveWordDo.TableName()
	_mSensitiveWord.ALL = field.NewAsterisk(tableName)
	_mSensitiveWord.ID = field.NewInt32(tableName, "id")
	_mSensitiveWord.GameID = field.NewString(tableName, "game_id")
	_mSensitiveWord.Level = field.NewInt32(tableName, "level")
	_mSensitiveWord.Content = field.NewString(tableName, "content")
	_mSensitiveWord.CreatedAt = field.NewInt64(tableName, "created_at")
	_mSensitiveWord.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mSensitiveWord.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mSensitiveWord.fillFieldMap()

	return _mSensitiveWord
}

type mSensitiveWord struct {
	mSensitiveWordDo

	ALL       field.Asterisk
	ID        field.Int32
	GameID    field.String
	Level     field.Int32  // 等级
	Content   field.String // 敏感词
	CreatedAt field.Int64
	UpdatedAt field.Int64
	IsDeleted field.Bool

	fieldMap map[string]field.Expr
}

func (m mSensitiveWord) Table(newTableName string) *mSensitiveWord {
	m.mSensitiveWordDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mSensitiveWord) As(alias string) *mSensitiveWord {
	m.mSensitiveWordDo.DO = *(m.mSensitiveWordDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mSensitiveWord) updateTableName(table string) *mSensitiveWord {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.Level = field.NewInt32(table, "level")
	m.Content = field.NewString(table, "content")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mSensitiveWord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mSensitiveWord) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 7)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["level"] = m.Level
	m.fieldMap["content"] = m.Content
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mSensitiveWord) clone(db *gorm.DB) mSensitiveWord {
	m.mSensitiveWordDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mSensitiveWord) replaceDB(db *gorm.DB) mSensitiveWord {
	m.mSensitiveWordDo.ReplaceDB(db)
	return m
}

type mSensitiveWordDo struct{ gen.DO }

type IMSensitiveWordDo interface {
	gen.SubQuery
	Debug() IMSensitiveWordDo
	WithContext(ctx context.Context) IMSensitiveWordDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMSensitiveWordDo
	WriteDB() IMSensitiveWordDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMSensitiveWordDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMSensitiveWordDo
	Not(conds ...gen.Condition) IMSensitiveWordDo
	Or(conds ...gen.Condition) IMSensitiveWordDo
	Select(conds ...field.Expr) IMSensitiveWordDo
	Where(conds ...gen.Condition) IMSensitiveWordDo
	Order(conds ...field.Expr) IMSensitiveWordDo
	Distinct(cols ...field.Expr) IMSensitiveWordDo
	Omit(cols ...field.Expr) IMSensitiveWordDo
	Join(table schema.Tabler, on ...field.Expr) IMSensitiveWordDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMSensitiveWordDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMSensitiveWordDo
	Group(cols ...field.Expr) IMSensitiveWordDo
	Having(conds ...gen.Condition) IMSensitiveWordDo
	Limit(limit int) IMSensitiveWordDo
	Offset(offset int) IMSensitiveWordDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMSensitiveWordDo
	Unscoped() IMSensitiveWordDo
	Create(values ...*model.MSensitiveWord) error
	CreateInBatches(values []*model.MSensitiveWord, batchSize int) error
	Save(values ...*model.MSensitiveWord) error
	First() (*model.MSensitiveWord, error)
	Take() (*model.MSensitiveWord, error)
	Last() (*model.MSensitiveWord, error)
	Find() ([]*model.MSensitiveWord, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MSensitiveWord, err error)
	FindInBatches(result *[]*model.MSensitiveWord, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MSensitiveWord) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMSensitiveWordDo
	Assign(attrs ...field.AssignExpr) IMSensitiveWordDo
	Joins(fields ...field.RelationField) IMSensitiveWordDo
	Preload(fields ...field.RelationField) IMSensitiveWordDo
	FirstOrInit() (*model.MSensitiveWord, error)
	FirstOrCreate() (*model.MSensitiveWord, error)
	FindByPage(offset int, limit int) (result []*model.MSensitiveWord, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMSensitiveWordDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mSensitiveWordDo) Debug() IMSensitiveWordDo {
	return m.withDO(m.DO.Debug())
}

func (m mSensitiveWordDo) WithContext(ctx context.Context) IMSensitiveWordDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mSensitiveWordDo) ReadDB() IMSensitiveWordDo {
	return m.Clauses(dbresolver.Read)
}

func (m mSensitiveWordDo) WriteDB() IMSensitiveWordDo {
	return m.Clauses(dbresolver.Write)
}

func (m mSensitiveWordDo) Session(config *gorm.Session) IMSensitiveWordDo {
	return m.withDO(m.DO.Session(config))
}

func (m mSensitiveWordDo) Clauses(conds ...clause.Expression) IMSensitiveWordDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mSensitiveWordDo) Returning(value interface{}, columns ...string) IMSensitiveWordDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mSensitiveWordDo) Not(conds ...gen.Condition) IMSensitiveWordDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mSensitiveWordDo) Or(conds ...gen.Condition) IMSensitiveWordDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mSensitiveWordDo) Select(conds ...field.Expr) IMSensitiveWordDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mSensitiveWordDo) Where(conds ...gen.Condition) IMSensitiveWordDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mSensitiveWordDo) Order(conds ...field.Expr) IMSensitiveWordDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mSensitiveWordDo) Distinct(cols ...field.Expr) IMSensitiveWordDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mSensitiveWordDo) Omit(cols ...field.Expr) IMSensitiveWordDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mSensitiveWordDo) Join(table schema.Tabler, on ...field.Expr) IMSensitiveWordDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mSensitiveWordDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMSensitiveWordDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mSensitiveWordDo) RightJoin(table schema.Tabler, on ...field.Expr) IMSensitiveWordDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mSensitiveWordDo) Group(cols ...field.Expr) IMSensitiveWordDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mSensitiveWordDo) Having(conds ...gen.Condition) IMSensitiveWordDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mSensitiveWordDo) Limit(limit int) IMSensitiveWordDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mSensitiveWordDo) Offset(offset int) IMSensitiveWordDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mSensitiveWordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMSensitiveWordDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mSensitiveWordDo) Unscoped() IMSensitiveWordDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mSensitiveWordDo) Create(values ...*model.MSensitiveWord) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mSensitiveWordDo) CreateInBatches(values []*model.MSensitiveWord, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mSensitiveWordDo) Save(values ...*model.MSensitiveWord) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mSensitiveWordDo) First() (*model.MSensitiveWord, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSensitiveWord), nil
	}
}

func (m mSensitiveWordDo) Take() (*model.MSensitiveWord, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSensitiveWord), nil
	}
}

func (m mSensitiveWordDo) Last() (*model.MSensitiveWord, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSensitiveWord), nil
	}
}

func (m mSensitiveWordDo) Find() ([]*model.MSensitiveWord, error) {
	result, err := m.DO.Find()
	return result.([]*model.MSensitiveWord), err
}

func (m mSensitiveWordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MSensitiveWord, err error) {
	buf := make([]*model.MSensitiveWord, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mSensitiveWordDo) FindInBatches(result *[]*model.MSensitiveWord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mSensitiveWordDo) Attrs(attrs ...field.AssignExpr) IMSensitiveWordDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mSensitiveWordDo) Assign(attrs ...field.AssignExpr) IMSensitiveWordDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mSensitiveWordDo) Joins(fields ...field.RelationField) IMSensitiveWordDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mSensitiveWordDo) Preload(fields ...field.RelationField) IMSensitiveWordDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mSensitiveWordDo) FirstOrInit() (*model.MSensitiveWord, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSensitiveWord), nil
	}
}

func (m mSensitiveWordDo) FirstOrCreate() (*model.MSensitiveWord, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MSensitiveWord), nil
	}
}

func (m mSensitiveWordDo) FindByPage(offset int, limit int) (result []*model.MSensitiveWord, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mSensitiveWordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mSensitiveWordDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mSensitiveWordDo) Delete(models ...*model.MSensitiveWord) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mSensitiveWordDo) withDO(do gen.Dao) *mSensitiveWordDo {
	m.DO = *do.(*gen.DO)
	return m
}
