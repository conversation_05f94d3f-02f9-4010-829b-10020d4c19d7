// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMQuestionSystemPrompt(db *gorm.DB, opts ...gen.DOOption) mQuestionSystemPrompt {
	_mQuestionSystemPrompt := mQuestionSystemPrompt{}

	_mQuestionSystemPrompt.mQuestionSystemPromptDo.UseDB(db, opts...)
	_mQuestionSystemPrompt.mQuestionSystemPromptDo.UseModel(&model.MQuestionSystemPrompt{})

	tableName := _mQuestionSystemPrompt.mQuestionSystemPromptDo.TableName()
	_mQuestionSystemPrompt.ALL = field.NewAsterisk(tableName)
	_mQuestionSystemPrompt.ID = field.NewInt32(tableName, "id")
	_mQuestionSystemPrompt.Content = field.NewString(tableName, "content")
	_mQuestionSystemPrompt.CreatorID = field.NewString(tableName, "creator_id")
	_mQuestionSystemPrompt.CreatedAt = field.NewInt64(tableName, "created_at")
	_mQuestionSystemPrompt.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mQuestionSystemPrompt.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mQuestionSystemPrompt.fillFieldMap()

	return _mQuestionSystemPrompt
}

type mQuestionSystemPrompt struct {
	mQuestionSystemPromptDo

	ALL       field.Asterisk
	ID        field.Int32
	Content   field.String // 提示词内容
	CreatorID field.String // 创建人id
	CreatedAt field.Int64
	UpdatedAt field.Int64
	IsDeleted field.Bool

	fieldMap map[string]field.Expr
}

func (m mQuestionSystemPrompt) Table(newTableName string) *mQuestionSystemPrompt {
	m.mQuestionSystemPromptDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mQuestionSystemPrompt) As(alias string) *mQuestionSystemPrompt {
	m.mQuestionSystemPromptDo.DO = *(m.mQuestionSystemPromptDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mQuestionSystemPrompt) updateTableName(table string) *mQuestionSystemPrompt {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.Content = field.NewString(table, "content")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mQuestionSystemPrompt) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mQuestionSystemPrompt) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 6)
	m.fieldMap["id"] = m.ID
	m.fieldMap["content"] = m.Content
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mQuestionSystemPrompt) clone(db *gorm.DB) mQuestionSystemPrompt {
	m.mQuestionSystemPromptDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mQuestionSystemPrompt) replaceDB(db *gorm.DB) mQuestionSystemPrompt {
	m.mQuestionSystemPromptDo.ReplaceDB(db)
	return m
}

type mQuestionSystemPromptDo struct{ gen.DO }

type IMQuestionSystemPromptDo interface {
	gen.SubQuery
	Debug() IMQuestionSystemPromptDo
	WithContext(ctx context.Context) IMQuestionSystemPromptDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMQuestionSystemPromptDo
	WriteDB() IMQuestionSystemPromptDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMQuestionSystemPromptDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMQuestionSystemPromptDo
	Not(conds ...gen.Condition) IMQuestionSystemPromptDo
	Or(conds ...gen.Condition) IMQuestionSystemPromptDo
	Select(conds ...field.Expr) IMQuestionSystemPromptDo
	Where(conds ...gen.Condition) IMQuestionSystemPromptDo
	Order(conds ...field.Expr) IMQuestionSystemPromptDo
	Distinct(cols ...field.Expr) IMQuestionSystemPromptDo
	Omit(cols ...field.Expr) IMQuestionSystemPromptDo
	Join(table schema.Tabler, on ...field.Expr) IMQuestionSystemPromptDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMQuestionSystemPromptDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMQuestionSystemPromptDo
	Group(cols ...field.Expr) IMQuestionSystemPromptDo
	Having(conds ...gen.Condition) IMQuestionSystemPromptDo
	Limit(limit int) IMQuestionSystemPromptDo
	Offset(offset int) IMQuestionSystemPromptDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMQuestionSystemPromptDo
	Unscoped() IMQuestionSystemPromptDo
	Create(values ...*model.MQuestionSystemPrompt) error
	CreateInBatches(values []*model.MQuestionSystemPrompt, batchSize int) error
	Save(values ...*model.MQuestionSystemPrompt) error
	First() (*model.MQuestionSystemPrompt, error)
	Take() (*model.MQuestionSystemPrompt, error)
	Last() (*model.MQuestionSystemPrompt, error)
	Find() ([]*model.MQuestionSystemPrompt, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MQuestionSystemPrompt, err error)
	FindInBatches(result *[]*model.MQuestionSystemPrompt, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MQuestionSystemPrompt) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMQuestionSystemPromptDo
	Assign(attrs ...field.AssignExpr) IMQuestionSystemPromptDo
	Joins(fields ...field.RelationField) IMQuestionSystemPromptDo
	Preload(fields ...field.RelationField) IMQuestionSystemPromptDo
	FirstOrInit() (*model.MQuestionSystemPrompt, error)
	FirstOrCreate() (*model.MQuestionSystemPrompt, error)
	FindByPage(offset int, limit int) (result []*model.MQuestionSystemPrompt, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMQuestionSystemPromptDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mQuestionSystemPromptDo) Debug() IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Debug())
}

func (m mQuestionSystemPromptDo) WithContext(ctx context.Context) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mQuestionSystemPromptDo) ReadDB() IMQuestionSystemPromptDo {
	return m.Clauses(dbresolver.Read)
}

func (m mQuestionSystemPromptDo) WriteDB() IMQuestionSystemPromptDo {
	return m.Clauses(dbresolver.Write)
}

func (m mQuestionSystemPromptDo) Session(config *gorm.Session) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Session(config))
}

func (m mQuestionSystemPromptDo) Clauses(conds ...clause.Expression) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mQuestionSystemPromptDo) Returning(value interface{}, columns ...string) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mQuestionSystemPromptDo) Not(conds ...gen.Condition) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mQuestionSystemPromptDo) Or(conds ...gen.Condition) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mQuestionSystemPromptDo) Select(conds ...field.Expr) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mQuestionSystemPromptDo) Where(conds ...gen.Condition) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mQuestionSystemPromptDo) Order(conds ...field.Expr) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mQuestionSystemPromptDo) Distinct(cols ...field.Expr) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mQuestionSystemPromptDo) Omit(cols ...field.Expr) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mQuestionSystemPromptDo) Join(table schema.Tabler, on ...field.Expr) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mQuestionSystemPromptDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mQuestionSystemPromptDo) RightJoin(table schema.Tabler, on ...field.Expr) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mQuestionSystemPromptDo) Group(cols ...field.Expr) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mQuestionSystemPromptDo) Having(conds ...gen.Condition) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mQuestionSystemPromptDo) Limit(limit int) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mQuestionSystemPromptDo) Offset(offset int) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mQuestionSystemPromptDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mQuestionSystemPromptDo) Unscoped() IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mQuestionSystemPromptDo) Create(values ...*model.MQuestionSystemPrompt) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mQuestionSystemPromptDo) CreateInBatches(values []*model.MQuestionSystemPrompt, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mQuestionSystemPromptDo) Save(values ...*model.MQuestionSystemPrompt) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mQuestionSystemPromptDo) First() (*model.MQuestionSystemPrompt, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionSystemPrompt), nil
	}
}

func (m mQuestionSystemPromptDo) Take() (*model.MQuestionSystemPrompt, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionSystemPrompt), nil
	}
}

func (m mQuestionSystemPromptDo) Last() (*model.MQuestionSystemPrompt, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionSystemPrompt), nil
	}
}

func (m mQuestionSystemPromptDo) Find() ([]*model.MQuestionSystemPrompt, error) {
	result, err := m.DO.Find()
	return result.([]*model.MQuestionSystemPrompt), err
}

func (m mQuestionSystemPromptDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MQuestionSystemPrompt, err error) {
	buf := make([]*model.MQuestionSystemPrompt, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mQuestionSystemPromptDo) FindInBatches(result *[]*model.MQuestionSystemPrompt, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mQuestionSystemPromptDo) Attrs(attrs ...field.AssignExpr) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mQuestionSystemPromptDo) Assign(attrs ...field.AssignExpr) IMQuestionSystemPromptDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mQuestionSystemPromptDo) Joins(fields ...field.RelationField) IMQuestionSystemPromptDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mQuestionSystemPromptDo) Preload(fields ...field.RelationField) IMQuestionSystemPromptDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mQuestionSystemPromptDo) FirstOrInit() (*model.MQuestionSystemPrompt, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionSystemPrompt), nil
	}
}

func (m mQuestionSystemPromptDo) FirstOrCreate() (*model.MQuestionSystemPrompt, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MQuestionSystemPrompt), nil
	}
}

func (m mQuestionSystemPromptDo) FindByPage(offset int, limit int) (result []*model.MQuestionSystemPrompt, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mQuestionSystemPromptDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mQuestionSystemPromptDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mQuestionSystemPromptDo) Delete(models ...*model.MQuestionSystemPrompt) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mQuestionSystemPromptDo) withDO(do gen.Dao) *mQuestionSystemPromptDo {
	m.DO = *do.(*gen.DO)
	return m
}
