// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMUser(db *gorm.DB, opts ...gen.DOOption) mUser {
	_mUser := mUser{}

	_mUser.mUserDo.UseDB(db, opts...)
	_mUser.mUserDo.UseModel(&model.MUser{})

	tableName := _mUser.mUserDo.TableName()
	_mUser.ALL = field.NewAsterisk(tableName)
	_mUser.ID = field.NewInt32(tableName, "id")
	_mUser.UserID = field.NewString(tableName, "user_id")
	_mUser.Username = field.NewString(tableName, "username")
	_mUser.Password = field.NewString(tableName, "password")
	_mUser.Name = field.NewString(tableName, "name")
	_mUser.Phone = field.NewString(tableName, "phone")
	_mUser.Status = field.NewInt32(tableName, "status")
	_mUser.IsAdmin = field.NewBool(tableName, "is_admin")
	_mUser.CreatedAt = field.NewInt64(tableName, "created_at")
	_mUser.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mUser.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mUser.fillFieldMap()

	return _mUser
}

// mUser 用户表
type mUser struct {
	mUserDo

	ALL       field.Asterisk
	ID        field.Int32
	UserID    field.String
	Username  field.String // 用户名称
	Password  field.String
	Name      field.String // 用户名
	Phone     field.String // 手机号
	Status    field.Int32  // 状态 1 启用 2 关闭
	IsAdmin   field.Bool   // 是否是管理员（具有所有权限）默认为普通用户
	CreatedAt field.Int64
	UpdatedAt field.Int64
	IsDeleted field.Bool

	fieldMap map[string]field.Expr
}

func (m mUser) Table(newTableName string) *mUser {
	m.mUserDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mUser) As(alias string) *mUser {
	m.mUserDo.DO = *(m.mUserDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mUser) updateTableName(table string) *mUser {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.UserID = field.NewString(table, "user_id")
	m.Username = field.NewString(table, "username")
	m.Password = field.NewString(table, "password")
	m.Name = field.NewString(table, "name")
	m.Phone = field.NewString(table, "phone")
	m.Status = field.NewInt32(table, "status")
	m.IsAdmin = field.NewBool(table, "is_admin")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mUser) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 11)
	m.fieldMap["id"] = m.ID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["username"] = m.Username
	m.fieldMap["password"] = m.Password
	m.fieldMap["name"] = m.Name
	m.fieldMap["phone"] = m.Phone
	m.fieldMap["status"] = m.Status
	m.fieldMap["is_admin"] = m.IsAdmin
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mUser) clone(db *gorm.DB) mUser {
	m.mUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mUser) replaceDB(db *gorm.DB) mUser {
	m.mUserDo.ReplaceDB(db)
	return m
}

type mUserDo struct{ gen.DO }

type IMUserDo interface {
	gen.SubQuery
	Debug() IMUserDo
	WithContext(ctx context.Context) IMUserDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMUserDo
	WriteDB() IMUserDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMUserDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMUserDo
	Not(conds ...gen.Condition) IMUserDo
	Or(conds ...gen.Condition) IMUserDo
	Select(conds ...field.Expr) IMUserDo
	Where(conds ...gen.Condition) IMUserDo
	Order(conds ...field.Expr) IMUserDo
	Distinct(cols ...field.Expr) IMUserDo
	Omit(cols ...field.Expr) IMUserDo
	Join(table schema.Tabler, on ...field.Expr) IMUserDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMUserDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMUserDo
	Group(cols ...field.Expr) IMUserDo
	Having(conds ...gen.Condition) IMUserDo
	Limit(limit int) IMUserDo
	Offset(offset int) IMUserDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMUserDo
	Unscoped() IMUserDo
	Create(values ...*model.MUser) error
	CreateInBatches(values []*model.MUser, batchSize int) error
	Save(values ...*model.MUser) error
	First() (*model.MUser, error)
	Take() (*model.MUser, error)
	Last() (*model.MUser, error)
	Find() ([]*model.MUser, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MUser, err error)
	FindInBatches(result *[]*model.MUser, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MUser) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMUserDo
	Assign(attrs ...field.AssignExpr) IMUserDo
	Joins(fields ...field.RelationField) IMUserDo
	Preload(fields ...field.RelationField) IMUserDo
	FirstOrInit() (*model.MUser, error)
	FirstOrCreate() (*model.MUser, error)
	FindByPage(offset int, limit int) (result []*model.MUser, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMUserDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mUserDo) Debug() IMUserDo {
	return m.withDO(m.DO.Debug())
}

func (m mUserDo) WithContext(ctx context.Context) IMUserDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mUserDo) ReadDB() IMUserDo {
	return m.Clauses(dbresolver.Read)
}

func (m mUserDo) WriteDB() IMUserDo {
	return m.Clauses(dbresolver.Write)
}

func (m mUserDo) Session(config *gorm.Session) IMUserDo {
	return m.withDO(m.DO.Session(config))
}

func (m mUserDo) Clauses(conds ...clause.Expression) IMUserDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mUserDo) Returning(value interface{}, columns ...string) IMUserDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mUserDo) Not(conds ...gen.Condition) IMUserDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mUserDo) Or(conds ...gen.Condition) IMUserDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mUserDo) Select(conds ...field.Expr) IMUserDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mUserDo) Where(conds ...gen.Condition) IMUserDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mUserDo) Order(conds ...field.Expr) IMUserDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mUserDo) Distinct(cols ...field.Expr) IMUserDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mUserDo) Omit(cols ...field.Expr) IMUserDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mUserDo) Join(table schema.Tabler, on ...field.Expr) IMUserDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMUserDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mUserDo) RightJoin(table schema.Tabler, on ...field.Expr) IMUserDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mUserDo) Group(cols ...field.Expr) IMUserDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mUserDo) Having(conds ...gen.Condition) IMUserDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mUserDo) Limit(limit int) IMUserDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mUserDo) Offset(offset int) IMUserDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMUserDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mUserDo) Unscoped() IMUserDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mUserDo) Create(values ...*model.MUser) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mUserDo) CreateInBatches(values []*model.MUser, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mUserDo) Save(values ...*model.MUser) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mUserDo) First() (*model.MUser, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUser), nil
	}
}

func (m mUserDo) Take() (*model.MUser, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUser), nil
	}
}

func (m mUserDo) Last() (*model.MUser, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUser), nil
	}
}

func (m mUserDo) Find() ([]*model.MUser, error) {
	result, err := m.DO.Find()
	return result.([]*model.MUser), err
}

func (m mUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MUser, err error) {
	buf := make([]*model.MUser, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mUserDo) FindInBatches(result *[]*model.MUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mUserDo) Attrs(attrs ...field.AssignExpr) IMUserDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mUserDo) Assign(attrs ...field.AssignExpr) IMUserDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mUserDo) Joins(fields ...field.RelationField) IMUserDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mUserDo) Preload(fields ...field.RelationField) IMUserDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mUserDo) FirstOrInit() (*model.MUser, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUser), nil
	}
}

func (m mUserDo) FirstOrCreate() (*model.MUser, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MUser), nil
	}
}

func (m mUserDo) FindByPage(offset int, limit int) (result []*model.MUser, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mUserDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mUserDo) Delete(models ...*model.MUser) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mUserDo) withDO(do gen.Dao) *mUserDo {
	m.DO = *do.(*gen.DO)
	return m
}
