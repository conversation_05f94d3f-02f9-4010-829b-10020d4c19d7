// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newAConfigQq(db *gorm.DB, opts ...gen.DOOption) aConfigQq {
	_aConfigQq := aConfigQq{}

	_aConfigQq.aConfigQqDo.UseDB(db, opts...)
	_aConfigQq.aConfigQqDo.UseModel(&model.AConfigQq{})

	tableName := _aConfigQq.aConfigQqDo.TableName()
	_aConfigQq.ALL = field.NewAsterisk(tableName)
	_aConfigQq.ID = field.NewInt32(tableName, "id")
	_aConfigQq.GameID = field.NewString(tableName, "game_id")
	_aConfigQq.AppID = field.NewString(tableName, "app_id")
	_aConfigQq.AppSecret = field.NewString(tableName, "app_secret")
	_aConfigQq.AccessToken = field.NewString(tableName, "access_token")
	_aConfigQq.ExpiresIn = field.NewInt32(tableName, "expires_in")
	_aConfigQq.CreatedAt = field.NewInt64(tableName, "created_at")
	_aConfigQq.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aConfigQq.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aConfigQq.fillFieldMap()

	return _aConfigQq
}

type aConfigQq struct {
	aConfigQqDo

	ALL         field.Asterisk
	ID          field.Int32
	GameID      field.String // 游戏id
	AppID       field.String // QQ应用ID
	AppSecret   field.String // QQ应用密钥
	AccessToken field.String // QQ平台access token
	ExpiresIn   field.Int32  // access token过期时间
	CreatedAt   field.Int64
	UpdatedAt   field.Int64
	IsDeleted   field.Bool

	fieldMap map[string]field.Expr
}

func (a aConfigQq) Table(newTableName string) *aConfigQq {
	a.aConfigQqDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aConfigQq) As(alias string) *aConfigQq {
	a.aConfigQqDo.DO = *(a.aConfigQqDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aConfigQq) updateTableName(table string) *aConfigQq {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.GameID = field.NewString(table, "game_id")
	a.AppID = field.NewString(table, "app_id")
	a.AppSecret = field.NewString(table, "app_secret")
	a.AccessToken = field.NewString(table, "access_token")
	a.ExpiresIn = field.NewInt32(table, "expires_in")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aConfigQq) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aConfigQq) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 9)
	a.fieldMap["id"] = a.ID
	a.fieldMap["game_id"] = a.GameID
	a.fieldMap["app_id"] = a.AppID
	a.fieldMap["app_secret"] = a.AppSecret
	a.fieldMap["access_token"] = a.AccessToken
	a.fieldMap["expires_in"] = a.ExpiresIn
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aConfigQq) clone(db *gorm.DB) aConfigQq {
	a.aConfigQqDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aConfigQq) replaceDB(db *gorm.DB) aConfigQq {
	a.aConfigQqDo.ReplaceDB(db)
	return a
}

type aConfigQqDo struct{ gen.DO }

type IAConfigQqDo interface {
	gen.SubQuery
	Debug() IAConfigQqDo
	WithContext(ctx context.Context) IAConfigQqDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAConfigQqDo
	WriteDB() IAConfigQqDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAConfigQqDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAConfigQqDo
	Not(conds ...gen.Condition) IAConfigQqDo
	Or(conds ...gen.Condition) IAConfigQqDo
	Select(conds ...field.Expr) IAConfigQqDo
	Where(conds ...gen.Condition) IAConfigQqDo
	Order(conds ...field.Expr) IAConfigQqDo
	Distinct(cols ...field.Expr) IAConfigQqDo
	Omit(cols ...field.Expr) IAConfigQqDo
	Join(table schema.Tabler, on ...field.Expr) IAConfigQqDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAConfigQqDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAConfigQqDo
	Group(cols ...field.Expr) IAConfigQqDo
	Having(conds ...gen.Condition) IAConfigQqDo
	Limit(limit int) IAConfigQqDo
	Offset(offset int) IAConfigQqDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAConfigQqDo
	Unscoped() IAConfigQqDo
	Create(values ...*model.AConfigQq) error
	CreateInBatches(values []*model.AConfigQq, batchSize int) error
	Save(values ...*model.AConfigQq) error
	First() (*model.AConfigQq, error)
	Take() (*model.AConfigQq, error)
	Last() (*model.AConfigQq, error)
	Find() ([]*model.AConfigQq, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AConfigQq, err error)
	FindInBatches(result *[]*model.AConfigQq, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AConfigQq) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAConfigQqDo
	Assign(attrs ...field.AssignExpr) IAConfigQqDo
	Joins(fields ...field.RelationField) IAConfigQqDo
	Preload(fields ...field.RelationField) IAConfigQqDo
	FirstOrInit() (*model.AConfigQq, error)
	FirstOrCreate() (*model.AConfigQq, error)
	FindByPage(offset int, limit int) (result []*model.AConfigQq, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAConfigQqDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aConfigQqDo) Debug() IAConfigQqDo {
	return a.withDO(a.DO.Debug())
}

func (a aConfigQqDo) WithContext(ctx context.Context) IAConfigQqDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aConfigQqDo) ReadDB() IAConfigQqDo {
	return a.Clauses(dbresolver.Read)
}

func (a aConfigQqDo) WriteDB() IAConfigQqDo {
	return a.Clauses(dbresolver.Write)
}

func (a aConfigQqDo) Session(config *gorm.Session) IAConfigQqDo {
	return a.withDO(a.DO.Session(config))
}

func (a aConfigQqDo) Clauses(conds ...clause.Expression) IAConfigQqDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aConfigQqDo) Returning(value interface{}, columns ...string) IAConfigQqDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aConfigQqDo) Not(conds ...gen.Condition) IAConfigQqDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aConfigQqDo) Or(conds ...gen.Condition) IAConfigQqDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aConfigQqDo) Select(conds ...field.Expr) IAConfigQqDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aConfigQqDo) Where(conds ...gen.Condition) IAConfigQqDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aConfigQqDo) Order(conds ...field.Expr) IAConfigQqDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aConfigQqDo) Distinct(cols ...field.Expr) IAConfigQqDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aConfigQqDo) Omit(cols ...field.Expr) IAConfigQqDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aConfigQqDo) Join(table schema.Tabler, on ...field.Expr) IAConfigQqDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aConfigQqDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAConfigQqDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aConfigQqDo) RightJoin(table schema.Tabler, on ...field.Expr) IAConfigQqDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aConfigQqDo) Group(cols ...field.Expr) IAConfigQqDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aConfigQqDo) Having(conds ...gen.Condition) IAConfigQqDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aConfigQqDo) Limit(limit int) IAConfigQqDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aConfigQqDo) Offset(offset int) IAConfigQqDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aConfigQqDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAConfigQqDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aConfigQqDo) Unscoped() IAConfigQqDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aConfigQqDo) Create(values ...*model.AConfigQq) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aConfigQqDo) CreateInBatches(values []*model.AConfigQq, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aConfigQqDo) Save(values ...*model.AConfigQq) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aConfigQqDo) First() (*model.AConfigQq, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigQq), nil
	}
}

func (a aConfigQqDo) Take() (*model.AConfigQq, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigQq), nil
	}
}

func (a aConfigQqDo) Last() (*model.AConfigQq, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigQq), nil
	}
}

func (a aConfigQqDo) Find() ([]*model.AConfigQq, error) {
	result, err := a.DO.Find()
	return result.([]*model.AConfigQq), err
}

func (a aConfigQqDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AConfigQq, err error) {
	buf := make([]*model.AConfigQq, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aConfigQqDo) FindInBatches(result *[]*model.AConfigQq, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aConfigQqDo) Attrs(attrs ...field.AssignExpr) IAConfigQqDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aConfigQqDo) Assign(attrs ...field.AssignExpr) IAConfigQqDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aConfigQqDo) Joins(fields ...field.RelationField) IAConfigQqDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aConfigQqDo) Preload(fields ...field.RelationField) IAConfigQqDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aConfigQqDo) FirstOrInit() (*model.AConfigQq, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigQq), nil
	}
}

func (a aConfigQqDo) FirstOrCreate() (*model.AConfigQq, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigQq), nil
	}
}

func (a aConfigQqDo) FindByPage(offset int, limit int) (result []*model.AConfigQq, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aConfigQqDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aConfigQqDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aConfigQqDo) Delete(models ...*model.AConfigQq) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aConfigQqDo) withDO(do gen.Dao) *aConfigQqDo {
	a.DO = *do.(*gen.DO)
	return a
}
