// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newConfigSubscribe(db *gorm.DB, opts ...gen.DOOption) configSubscribe {
	_configSubscribe := configSubscribe{}

	_configSubscribe.configSubscribeDo.UseDB(db, opts...)
	_configSubscribe.configSubscribeDo.UseModel(&model.ConfigSubscribe{})

	tableName := _configSubscribe.configSubscribeDo.TableName()
	_configSubscribe.ALL = field.NewAsterisk(tableName)
	_configSubscribe.ID = field.NewInt32(tableName, "id")
	_configSubscribe.GameID = field.NewString(tableName, "game_id")
	_configSubscribe.AppID = field.NewString(tableName, "app_id")
	_configSubscribe.AppSercet = field.NewString(tableName, "app_sercet")
	_configSubscribe.AccessToken = field.NewString(tableName, "access_token")
	_configSubscribe.Ticket = field.NewString(tableName, "ticket")
	_configSubscribe.ExpiresIn = field.NewInt32(tableName, "expires_in")
	_configSubscribe.TicketExpiresIn = field.NewInt32(tableName, "ticket_expires_in")
	_configSubscribe.CreatedAt = field.NewInt64(tableName, "created_at")
	_configSubscribe.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_configSubscribe.fillFieldMap()

	return _configSubscribe
}

type configSubscribe struct {
	configSubscribeDo

	ALL             field.Asterisk
	ID              field.Int32
	GameID          field.String // 游戏id
	AppID           field.String // 订阅号id
	AppSercet       field.String // 密钥
	AccessToken     field.String
	Ticket          field.String // jsapi ticket
	ExpiresIn       field.Int32  // token过期时间
	TicketExpiresIn field.Int32  // ticket 过期时间
	CreatedAt       field.Int64
	UpdatedAt       field.Int64

	fieldMap map[string]field.Expr
}

func (c configSubscribe) Table(newTableName string) *configSubscribe {
	c.configSubscribeDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c configSubscribe) As(alias string) *configSubscribe {
	c.configSubscribeDo.DO = *(c.configSubscribeDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *configSubscribe) updateTableName(table string) *configSubscribe {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt32(table, "id")
	c.GameID = field.NewString(table, "game_id")
	c.AppID = field.NewString(table, "app_id")
	c.AppSercet = field.NewString(table, "app_sercet")
	c.AccessToken = field.NewString(table, "access_token")
	c.Ticket = field.NewString(table, "ticket")
	c.ExpiresIn = field.NewInt32(table, "expires_in")
	c.TicketExpiresIn = field.NewInt32(table, "ticket_expires_in")
	c.CreatedAt = field.NewInt64(table, "created_at")
	c.UpdatedAt = field.NewInt64(table, "updated_at")

	c.fillFieldMap()

	return c
}

func (c *configSubscribe) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *configSubscribe) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 10)
	c.fieldMap["id"] = c.ID
	c.fieldMap["game_id"] = c.GameID
	c.fieldMap["app_id"] = c.AppID
	c.fieldMap["app_sercet"] = c.AppSercet
	c.fieldMap["access_token"] = c.AccessToken
	c.fieldMap["ticket"] = c.Ticket
	c.fieldMap["expires_in"] = c.ExpiresIn
	c.fieldMap["ticket_expires_in"] = c.TicketExpiresIn
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
}

func (c configSubscribe) clone(db *gorm.DB) configSubscribe {
	c.configSubscribeDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c configSubscribe) replaceDB(db *gorm.DB) configSubscribe {
	c.configSubscribeDo.ReplaceDB(db)
	return c
}

type configSubscribeDo struct{ gen.DO }

type IConfigSubscribeDo interface {
	gen.SubQuery
	Debug() IConfigSubscribeDo
	WithContext(ctx context.Context) IConfigSubscribeDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IConfigSubscribeDo
	WriteDB() IConfigSubscribeDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IConfigSubscribeDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IConfigSubscribeDo
	Not(conds ...gen.Condition) IConfigSubscribeDo
	Or(conds ...gen.Condition) IConfigSubscribeDo
	Select(conds ...field.Expr) IConfigSubscribeDo
	Where(conds ...gen.Condition) IConfigSubscribeDo
	Order(conds ...field.Expr) IConfigSubscribeDo
	Distinct(cols ...field.Expr) IConfigSubscribeDo
	Omit(cols ...field.Expr) IConfigSubscribeDo
	Join(table schema.Tabler, on ...field.Expr) IConfigSubscribeDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IConfigSubscribeDo
	RightJoin(table schema.Tabler, on ...field.Expr) IConfigSubscribeDo
	Group(cols ...field.Expr) IConfigSubscribeDo
	Having(conds ...gen.Condition) IConfigSubscribeDo
	Limit(limit int) IConfigSubscribeDo
	Offset(offset int) IConfigSubscribeDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IConfigSubscribeDo
	Unscoped() IConfigSubscribeDo
	Create(values ...*model.ConfigSubscribe) error
	CreateInBatches(values []*model.ConfigSubscribe, batchSize int) error
	Save(values ...*model.ConfigSubscribe) error
	First() (*model.ConfigSubscribe, error)
	Take() (*model.ConfigSubscribe, error)
	Last() (*model.ConfigSubscribe, error)
	Find() ([]*model.ConfigSubscribe, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ConfigSubscribe, err error)
	FindInBatches(result *[]*model.ConfigSubscribe, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ConfigSubscribe) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IConfigSubscribeDo
	Assign(attrs ...field.AssignExpr) IConfigSubscribeDo
	Joins(fields ...field.RelationField) IConfigSubscribeDo
	Preload(fields ...field.RelationField) IConfigSubscribeDo
	FirstOrInit() (*model.ConfigSubscribe, error)
	FirstOrCreate() (*model.ConfigSubscribe, error)
	FindByPage(offset int, limit int) (result []*model.ConfigSubscribe, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IConfigSubscribeDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c configSubscribeDo) Debug() IConfigSubscribeDo {
	return c.withDO(c.DO.Debug())
}

func (c configSubscribeDo) WithContext(ctx context.Context) IConfigSubscribeDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c configSubscribeDo) ReadDB() IConfigSubscribeDo {
	return c.Clauses(dbresolver.Read)
}

func (c configSubscribeDo) WriteDB() IConfigSubscribeDo {
	return c.Clauses(dbresolver.Write)
}

func (c configSubscribeDo) Session(config *gorm.Session) IConfigSubscribeDo {
	return c.withDO(c.DO.Session(config))
}

func (c configSubscribeDo) Clauses(conds ...clause.Expression) IConfigSubscribeDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c configSubscribeDo) Returning(value interface{}, columns ...string) IConfigSubscribeDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c configSubscribeDo) Not(conds ...gen.Condition) IConfigSubscribeDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c configSubscribeDo) Or(conds ...gen.Condition) IConfigSubscribeDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c configSubscribeDo) Select(conds ...field.Expr) IConfigSubscribeDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c configSubscribeDo) Where(conds ...gen.Condition) IConfigSubscribeDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c configSubscribeDo) Order(conds ...field.Expr) IConfigSubscribeDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c configSubscribeDo) Distinct(cols ...field.Expr) IConfigSubscribeDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c configSubscribeDo) Omit(cols ...field.Expr) IConfigSubscribeDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c configSubscribeDo) Join(table schema.Tabler, on ...field.Expr) IConfigSubscribeDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c configSubscribeDo) LeftJoin(table schema.Tabler, on ...field.Expr) IConfigSubscribeDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c configSubscribeDo) RightJoin(table schema.Tabler, on ...field.Expr) IConfigSubscribeDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c configSubscribeDo) Group(cols ...field.Expr) IConfigSubscribeDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c configSubscribeDo) Having(conds ...gen.Condition) IConfigSubscribeDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c configSubscribeDo) Limit(limit int) IConfigSubscribeDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c configSubscribeDo) Offset(offset int) IConfigSubscribeDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c configSubscribeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IConfigSubscribeDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c configSubscribeDo) Unscoped() IConfigSubscribeDo {
	return c.withDO(c.DO.Unscoped())
}

func (c configSubscribeDo) Create(values ...*model.ConfigSubscribe) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c configSubscribeDo) CreateInBatches(values []*model.ConfigSubscribe, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c configSubscribeDo) Save(values ...*model.ConfigSubscribe) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c configSubscribeDo) First() (*model.ConfigSubscribe, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConfigSubscribe), nil
	}
}

func (c configSubscribeDo) Take() (*model.ConfigSubscribe, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConfigSubscribe), nil
	}
}

func (c configSubscribeDo) Last() (*model.ConfigSubscribe, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConfigSubscribe), nil
	}
}

func (c configSubscribeDo) Find() ([]*model.ConfigSubscribe, error) {
	result, err := c.DO.Find()
	return result.([]*model.ConfigSubscribe), err
}

func (c configSubscribeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ConfigSubscribe, err error) {
	buf := make([]*model.ConfigSubscribe, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c configSubscribeDo) FindInBatches(result *[]*model.ConfigSubscribe, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c configSubscribeDo) Attrs(attrs ...field.AssignExpr) IConfigSubscribeDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c configSubscribeDo) Assign(attrs ...field.AssignExpr) IConfigSubscribeDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c configSubscribeDo) Joins(fields ...field.RelationField) IConfigSubscribeDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c configSubscribeDo) Preload(fields ...field.RelationField) IConfigSubscribeDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c configSubscribeDo) FirstOrInit() (*model.ConfigSubscribe, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConfigSubscribe), nil
	}
}

func (c configSubscribeDo) FirstOrCreate() (*model.ConfigSubscribe, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ConfigSubscribe), nil
	}
}

func (c configSubscribeDo) FindByPage(offset int, limit int) (result []*model.ConfigSubscribe, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c configSubscribeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c configSubscribeDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c configSubscribeDo) Delete(models ...*model.ConfigSubscribe) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *configSubscribeDo) withDO(do gen.Dao) *configSubscribeDo {
	c.DO = *do.(*gen.DO)
	return c
}
