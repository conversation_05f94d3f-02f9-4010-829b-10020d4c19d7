// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newUserMinigame(db *gorm.DB, opts ...gen.DOOption) userMinigame {
	_userMinigame := userMinigame{}

	_userMinigame.userMinigameDo.UseDB(db, opts...)
	_userMinigame.userMinigameDo.UseModel(&model.UserMinigame{})

	tableName := _userMinigame.userMinigameDo.TableName()
	_userMinigame.ALL = field.NewAsterisk(tableName)
	_userMinigame.ID = field.NewInt32(tableName, "id")
	_userMinigame.UserID = field.NewString(tableName, "user_id")
	_userMinigame.OpenID = field.NewString(tableName, "open_id")
	_userMinigame.UnionID = field.NewString(tableName, "union_id")
	_userMinigame.NickName = field.NewString(tableName, "nick_name")
	_userMinigame.Gender = field.NewInt32(tableName, "gender")
	_userMinigame.City = field.NewString(tableName, "city")
	_userMinigame.Province = field.NewString(tableName, "province")
	_userMinigame.Country = field.NewString(tableName, "country")
	_userMinigame.AvatarURL = field.NewString(tableName, "avatar_url")
	_userMinigame.Language = field.NewString(tableName, "language")
	_userMinigame.WatermarkAppID = field.NewString(tableName, "watermark_app_id")
	_userMinigame.WatermarkTimestamp = field.NewInt64(tableName, "watermark_timestamp")
	_userMinigame.SessionKey = field.NewString(tableName, "session_key")
	_userMinigame.CreatedAt = field.NewInt64(tableName, "created_at")
	_userMinigame.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_userMinigame.IsDeleted = field.NewBool(tableName, "is_deleted")

	_userMinigame.fillFieldMap()

	return _userMinigame
}

type userMinigame struct {
	userMinigameDo

	ALL                field.Asterisk
	ID                 field.Int32
	UserID             field.String // uuid
	OpenID             field.String
	UnionID            field.String
	NickName           field.String // 昵称
	Gender             field.Int32  // 性别
	City               field.String // 城市
	Province           field.String // 省份
	Country            field.String // 国家
	AvatarURL          field.String // 头像url
	Language           field.String // 语言
	WatermarkAppID     field.String // 水印应用id
	WatermarkTimestamp field.Int64  // 水印时间戳
	SessionKey         field.String // 会话密钥
	CreatedAt          field.Int64
	UpdatedAt          field.Int64
	IsDeleted          field.Bool

	fieldMap map[string]field.Expr
}

func (u userMinigame) Table(newTableName string) *userMinigame {
	u.userMinigameDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userMinigame) As(alias string) *userMinigame {
	u.userMinigameDo.DO = *(u.userMinigameDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userMinigame) updateTableName(table string) *userMinigame {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewInt32(table, "id")
	u.UserID = field.NewString(table, "user_id")
	u.OpenID = field.NewString(table, "open_id")
	u.UnionID = field.NewString(table, "union_id")
	u.NickName = field.NewString(table, "nick_name")
	u.Gender = field.NewInt32(table, "gender")
	u.City = field.NewString(table, "city")
	u.Province = field.NewString(table, "province")
	u.Country = field.NewString(table, "country")
	u.AvatarURL = field.NewString(table, "avatar_url")
	u.Language = field.NewString(table, "language")
	u.WatermarkAppID = field.NewString(table, "watermark_app_id")
	u.WatermarkTimestamp = field.NewInt64(table, "watermark_timestamp")
	u.SessionKey = field.NewString(table, "session_key")
	u.CreatedAt = field.NewInt64(table, "created_at")
	u.UpdatedAt = field.NewInt64(table, "updated_at")
	u.IsDeleted = field.NewBool(table, "is_deleted")

	u.fillFieldMap()

	return u
}

func (u *userMinigame) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userMinigame) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 17)
	u.fieldMap["id"] = u.ID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["open_id"] = u.OpenID
	u.fieldMap["union_id"] = u.UnionID
	u.fieldMap["nick_name"] = u.NickName
	u.fieldMap["gender"] = u.Gender
	u.fieldMap["city"] = u.City
	u.fieldMap["province"] = u.Province
	u.fieldMap["country"] = u.Country
	u.fieldMap["avatar_url"] = u.AvatarURL
	u.fieldMap["language"] = u.Language
	u.fieldMap["watermark_app_id"] = u.WatermarkAppID
	u.fieldMap["watermark_timestamp"] = u.WatermarkTimestamp
	u.fieldMap["session_key"] = u.SessionKey
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
	u.fieldMap["is_deleted"] = u.IsDeleted
}

func (u userMinigame) clone(db *gorm.DB) userMinigame {
	u.userMinigameDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userMinigame) replaceDB(db *gorm.DB) userMinigame {
	u.userMinigameDo.ReplaceDB(db)
	return u
}

type userMinigameDo struct{ gen.DO }

type IUserMinigameDo interface {
	gen.SubQuery
	Debug() IUserMinigameDo
	WithContext(ctx context.Context) IUserMinigameDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserMinigameDo
	WriteDB() IUserMinigameDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserMinigameDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserMinigameDo
	Not(conds ...gen.Condition) IUserMinigameDo
	Or(conds ...gen.Condition) IUserMinigameDo
	Select(conds ...field.Expr) IUserMinigameDo
	Where(conds ...gen.Condition) IUserMinigameDo
	Order(conds ...field.Expr) IUserMinigameDo
	Distinct(cols ...field.Expr) IUserMinigameDo
	Omit(cols ...field.Expr) IUserMinigameDo
	Join(table schema.Tabler, on ...field.Expr) IUserMinigameDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserMinigameDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserMinigameDo
	Group(cols ...field.Expr) IUserMinigameDo
	Having(conds ...gen.Condition) IUserMinigameDo
	Limit(limit int) IUserMinigameDo
	Offset(offset int) IUserMinigameDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserMinigameDo
	Unscoped() IUserMinigameDo
	Create(values ...*model.UserMinigame) error
	CreateInBatches(values []*model.UserMinigame, batchSize int) error
	Save(values ...*model.UserMinigame) error
	First() (*model.UserMinigame, error)
	Take() (*model.UserMinigame, error)
	Last() (*model.UserMinigame, error)
	Find() ([]*model.UserMinigame, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserMinigame, err error)
	FindInBatches(result *[]*model.UserMinigame, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.UserMinigame) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserMinigameDo
	Assign(attrs ...field.AssignExpr) IUserMinigameDo
	Joins(fields ...field.RelationField) IUserMinigameDo
	Preload(fields ...field.RelationField) IUserMinigameDo
	FirstOrInit() (*model.UserMinigame, error)
	FirstOrCreate() (*model.UserMinigame, error)
	FindByPage(offset int, limit int) (result []*model.UserMinigame, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserMinigameDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userMinigameDo) Debug() IUserMinigameDo {
	return u.withDO(u.DO.Debug())
}

func (u userMinigameDo) WithContext(ctx context.Context) IUserMinigameDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userMinigameDo) ReadDB() IUserMinigameDo {
	return u.Clauses(dbresolver.Read)
}

func (u userMinigameDo) WriteDB() IUserMinigameDo {
	return u.Clauses(dbresolver.Write)
}

func (u userMinigameDo) Session(config *gorm.Session) IUserMinigameDo {
	return u.withDO(u.DO.Session(config))
}

func (u userMinigameDo) Clauses(conds ...clause.Expression) IUserMinigameDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userMinigameDo) Returning(value interface{}, columns ...string) IUserMinigameDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userMinigameDo) Not(conds ...gen.Condition) IUserMinigameDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userMinigameDo) Or(conds ...gen.Condition) IUserMinigameDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userMinigameDo) Select(conds ...field.Expr) IUserMinigameDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userMinigameDo) Where(conds ...gen.Condition) IUserMinigameDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userMinigameDo) Order(conds ...field.Expr) IUserMinigameDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userMinigameDo) Distinct(cols ...field.Expr) IUserMinigameDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userMinigameDo) Omit(cols ...field.Expr) IUserMinigameDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userMinigameDo) Join(table schema.Tabler, on ...field.Expr) IUserMinigameDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userMinigameDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserMinigameDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userMinigameDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserMinigameDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userMinigameDo) Group(cols ...field.Expr) IUserMinigameDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userMinigameDo) Having(conds ...gen.Condition) IUserMinigameDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userMinigameDo) Limit(limit int) IUserMinigameDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userMinigameDo) Offset(offset int) IUserMinigameDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userMinigameDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserMinigameDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userMinigameDo) Unscoped() IUserMinigameDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userMinigameDo) Create(values ...*model.UserMinigame) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userMinigameDo) CreateInBatches(values []*model.UserMinigame, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userMinigameDo) Save(values ...*model.UserMinigame) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userMinigameDo) First() (*model.UserMinigame, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserMinigame), nil
	}
}

func (u userMinigameDo) Take() (*model.UserMinigame, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserMinigame), nil
	}
}

func (u userMinigameDo) Last() (*model.UserMinigame, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserMinigame), nil
	}
}

func (u userMinigameDo) Find() ([]*model.UserMinigame, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserMinigame), err
}

func (u userMinigameDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserMinigame, err error) {
	buf := make([]*model.UserMinigame, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userMinigameDo) FindInBatches(result *[]*model.UserMinigame, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userMinigameDo) Attrs(attrs ...field.AssignExpr) IUserMinigameDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userMinigameDo) Assign(attrs ...field.AssignExpr) IUserMinigameDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userMinigameDo) Joins(fields ...field.RelationField) IUserMinigameDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userMinigameDo) Preload(fields ...field.RelationField) IUserMinigameDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userMinigameDo) FirstOrInit() (*model.UserMinigame, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserMinigame), nil
	}
}

func (u userMinigameDo) FirstOrCreate() (*model.UserMinigame, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserMinigame), nil
	}
}

func (u userMinigameDo) FindByPage(offset int, limit int) (result []*model.UserMinigame, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userMinigameDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userMinigameDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userMinigameDo) Delete(models ...*model.UserMinigame) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userMinigameDo) withDO(do gen.Dao) *userMinigameDo {
	u.DO = *do.(*gen.DO)
	return u
}
