// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                         = new(Query)
	ACompany                  *aCompany
	AConfigDouyin             *aConfigDouyin
	AConfigDouyinClientToken  *aConfigDouyinClientToken
	AConfigMinigame           *aConfigMinigame
	AConfigMiniprogram        *aConfigMiniprogram
	AConfigQq                 *aConfigQq
	AConfigSubscribe          *aConfigSubscribe
	AGamePlayer               *aGamePlayer
	AGamePlayerDouyin         *aGamePlayerDouyin
	AGood                     *aGood
	AOrder                    *aOrder
	ARobotFilter              *aRobotFilter
	ASubscribeMessage         *aSubscribeMessage
	AUpload                   *aUpload
	AUser                     *aUser
	AUserDouyin               *aUserDouyin
	AUserDouyinWeb            *aUserDouyinWeb
	AUserMinigame             *aUserMinigame
	AUserQq                   *aUserQq
	AUserShareCode            *aUserShareCode
	AUserSubscribe            *aUserSubscribe
	AVoip                     *aVoip
	H5AdminConfig             *h5AdminConfig
	H5AdminUser               *h5AdminUser
	H5AdminUserRechargeLimit  *h5AdminUserRechargeLimit
	MAdPosition               *mAdPosition
	MAdPositionPlatform       *mAdPositionPlatform
	MCaptchaConfig            *mCaptchaConfig
	MChatMessage              *mChatMessage
	MCityCode                 *mCityCode
	MCustomSwitch             *mCustomSwitch
	MCustomSwitchParam        *mCustomSwitchParam
	MCustomerServiceMedium    *mCustomerServiceMedium
	MCustomerServiceMessage   *mCustomerServiceMessage
	MEmailConfig              *mEmailConfig
	MGame                     *mGame
	MMonitorContentConfig     *mMonitorContentConfig
	MMonitorContentOperation  *mMonitorContentOperation
	MMonitorGameContent       *mMonitorGameContent
	MMonitorGameServer        *mMonitorGameServer
	MPermission               *mPermission
	MPermissionDatum          *mPermissionDatum
	MPkgTask                  *mPkgTask
	MQuestionLibrary          *mQuestionLibrary
	MQuestionSystemPrompt     *mQuestionSystemPrompt
	MQuestionWelcomeMessage   *mQuestionWelcomeMessage
	MRedemptionCode           *mRedemptionCode
	MRedemptionCodeEntity     *mRedemptionCodeEntity
	MRedemptionCodeRecord     *mRedemptionCodeRecord
	MReport                   *mReport
	MReportActionConfig       *mReportActionConfig
	MReportItemConfig         *mReportItemConfig
	MReportOperation          *mReportOperation
	MRole                     *mRole
	MRolePermission           *mRolePermission
	MSensitiveWord            *mSensitiveWord
	MSensitiveWordConfig      *mSensitiveWordConfig
	MShare                    *mShare
	MShareRoadblock           *mShareRoadblock
	MStopServiceConfig        *mStopServiceConfig
	MSwitchSceneValue         *mSwitchSceneValue
	MUpload                   *mUpload
	MUser                     *mUser
	MUserBan                  *mUserBan
	MUserRole                 *mUserRole
	MWorkorder                *mWorkorder
	MWorkorderAttachment      *mWorkorderAttachment
	MWorkorderBusySwitch      *mWorkorderBusySwitch
	MWorkorderConfig          *mWorkorderConfig
	MWorkorderFollow          *mWorkorderFollow
	MWorkorderOperation       *mWorkorderOperation
	MWorkorderReply           *mWorkorderReply
	MWorkorderReplyAttachment *mWorkorderReplyAttachment
	MWorkorderReviewSwitch    *mWorkorderReviewSwitch
	MWorkorderTag             *mWorkorderTag
	MWorkorderTagRelation     *mWorkorderTagRelation
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	ACompany = &Q.ACompany
	AConfigDouyin = &Q.AConfigDouyin
	AConfigDouyinClientToken = &Q.AConfigDouyinClientToken
	AConfigMinigame = &Q.AConfigMinigame
	AConfigMiniprogram = &Q.AConfigMiniprogram
	AConfigQq = &Q.AConfigQq
	AConfigSubscribe = &Q.AConfigSubscribe
	AGamePlayer = &Q.AGamePlayer
	AGamePlayerDouyin = &Q.AGamePlayerDouyin
	AGood = &Q.AGood
	AOrder = &Q.AOrder
	ARobotFilter = &Q.ARobotFilter
	ASubscribeMessage = &Q.ASubscribeMessage
	AUpload = &Q.AUpload
	AUser = &Q.AUser
	AUserDouyin = &Q.AUserDouyin
	AUserDouyinWeb = &Q.AUserDouyinWeb
	AUserMinigame = &Q.AUserMinigame
	AUserQq = &Q.AUserQq
	AUserShareCode = &Q.AUserShareCode
	AUserSubscribe = &Q.AUserSubscribe
	AVoip = &Q.AVoip
	H5AdminConfig = &Q.H5AdminConfig
	H5AdminUser = &Q.H5AdminUser
	H5AdminUserRechargeLimit = &Q.H5AdminUserRechargeLimit
	MAdPosition = &Q.MAdPosition
	MAdPositionPlatform = &Q.MAdPositionPlatform
	MCaptchaConfig = &Q.MCaptchaConfig
	MChatMessage = &Q.MChatMessage
	MCityCode = &Q.MCityCode
	MCustomSwitch = &Q.MCustomSwitch
	MCustomSwitchParam = &Q.MCustomSwitchParam
	MCustomerServiceMedium = &Q.MCustomerServiceMedium
	MCustomerServiceMessage = &Q.MCustomerServiceMessage
	MEmailConfig = &Q.MEmailConfig
	MGame = &Q.MGame
	MMonitorContentConfig = &Q.MMonitorContentConfig
	MMonitorContentOperation = &Q.MMonitorContentOperation
	MMonitorGameContent = &Q.MMonitorGameContent
	MMonitorGameServer = &Q.MMonitorGameServer
	MPermission = &Q.MPermission
	MPermissionDatum = &Q.MPermissionDatum
	MPkgTask = &Q.MPkgTask
	MQuestionLibrary = &Q.MQuestionLibrary
	MQuestionSystemPrompt = &Q.MQuestionSystemPrompt
	MQuestionWelcomeMessage = &Q.MQuestionWelcomeMessage
	MRedemptionCode = &Q.MRedemptionCode
	MRedemptionCodeEntity = &Q.MRedemptionCodeEntity
	MRedemptionCodeRecord = &Q.MRedemptionCodeRecord
	MReport = &Q.MReport
	MReportActionConfig = &Q.MReportActionConfig
	MReportItemConfig = &Q.MReportItemConfig
	MReportOperation = &Q.MReportOperation
	MRole = &Q.MRole
	MRolePermission = &Q.MRolePermission
	MSensitiveWord = &Q.MSensitiveWord
	MSensitiveWordConfig = &Q.MSensitiveWordConfig
	MShare = &Q.MShare
	MShareRoadblock = &Q.MShareRoadblock
	MStopServiceConfig = &Q.MStopServiceConfig
	MSwitchSceneValue = &Q.MSwitchSceneValue
	MUpload = &Q.MUpload
	MUser = &Q.MUser
	MUserBan = &Q.MUserBan
	MUserRole = &Q.MUserRole
	MWorkorder = &Q.MWorkorder
	MWorkorderAttachment = &Q.MWorkorderAttachment
	MWorkorderBusySwitch = &Q.MWorkorderBusySwitch
	MWorkorderConfig = &Q.MWorkorderConfig
	MWorkorderFollow = &Q.MWorkorderFollow
	MWorkorderOperation = &Q.MWorkorderOperation
	MWorkorderReply = &Q.MWorkorderReply
	MWorkorderReplyAttachment = &Q.MWorkorderReplyAttachment
	MWorkorderReviewSwitch = &Q.MWorkorderReviewSwitch
	MWorkorderTag = &Q.MWorkorderTag
	MWorkorderTagRelation = &Q.MWorkorderTagRelation
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                        db,
		ACompany:                  newACompany(db, opts...),
		AConfigDouyin:             newAConfigDouyin(db, opts...),
		AConfigDouyinClientToken:  newAConfigDouyinClientToken(db, opts...),
		AConfigMinigame:           newAConfigMinigame(db, opts...),
		AConfigMiniprogram:        newAConfigMiniprogram(db, opts...),
		AConfigQq:                 newAConfigQq(db, opts...),
		AConfigSubscribe:          newAConfigSubscribe(db, opts...),
		AGamePlayer:               newAGamePlayer(db, opts...),
		AGamePlayerDouyin:         newAGamePlayerDouyin(db, opts...),
		AGood:                     newAGood(db, opts...),
		AOrder:                    newAOrder(db, opts...),
		ARobotFilter:              newARobotFilter(db, opts...),
		ASubscribeMessage:         newASubscribeMessage(db, opts...),
		AUpload:                   newAUpload(db, opts...),
		AUser:                     newAUser(db, opts...),
		AUserDouyin:               newAUserDouyin(db, opts...),
		AUserDouyinWeb:            newAUserDouyinWeb(db, opts...),
		AUserMinigame:             newAUserMinigame(db, opts...),
		AUserQq:                   newAUserQq(db, opts...),
		AUserShareCode:            newAUserShareCode(db, opts...),
		AUserSubscribe:            newAUserSubscribe(db, opts...),
		AVoip:                     newAVoip(db, opts...),
		H5AdminConfig:             newH5AdminConfig(db, opts...),
		H5AdminUser:               newH5AdminUser(db, opts...),
		H5AdminUserRechargeLimit:  newH5AdminUserRechargeLimit(db, opts...),
		MAdPosition:               newMAdPosition(db, opts...),
		MAdPositionPlatform:       newMAdPositionPlatform(db, opts...),
		MCaptchaConfig:            newMCaptchaConfig(db, opts...),
		MChatMessage:              newMChatMessage(db, opts...),
		MCityCode:                 newMCityCode(db, opts...),
		MCustomSwitch:             newMCustomSwitch(db, opts...),
		MCustomSwitchParam:        newMCustomSwitchParam(db, opts...),
		MCustomerServiceMedium:    newMCustomerServiceMedium(db, opts...),
		MCustomerServiceMessage:   newMCustomerServiceMessage(db, opts...),
		MEmailConfig:              newMEmailConfig(db, opts...),
		MGame:                     newMGame(db, opts...),
		MMonitorContentConfig:     newMMonitorContentConfig(db, opts...),
		MMonitorContentOperation:  newMMonitorContentOperation(db, opts...),
		MMonitorGameContent:       newMMonitorGameContent(db, opts...),
		MMonitorGameServer:        newMMonitorGameServer(db, opts...),
		MPermission:               newMPermission(db, opts...),
		MPermissionDatum:          newMPermissionDatum(db, opts...),
		MPkgTask:                  newMPkgTask(db, opts...),
		MQuestionLibrary:          newMQuestionLibrary(db, opts...),
		MQuestionSystemPrompt:     newMQuestionSystemPrompt(db, opts...),
		MQuestionWelcomeMessage:   newMQuestionWelcomeMessage(db, opts...),
		MRedemptionCode:           newMRedemptionCode(db, opts...),
		MRedemptionCodeEntity:     newMRedemptionCodeEntity(db, opts...),
		MRedemptionCodeRecord:     newMRedemptionCodeRecord(db, opts...),
		MReport:                   newMReport(db, opts...),
		MReportActionConfig:       newMReportActionConfig(db, opts...),
		MReportItemConfig:         newMReportItemConfig(db, opts...),
		MReportOperation:          newMReportOperation(db, opts...),
		MRole:                     newMRole(db, opts...),
		MRolePermission:           newMRolePermission(db, opts...),
		MSensitiveWord:            newMSensitiveWord(db, opts...),
		MSensitiveWordConfig:      newMSensitiveWordConfig(db, opts...),
		MShare:                    newMShare(db, opts...),
		MShareRoadblock:           newMShareRoadblock(db, opts...),
		MStopServiceConfig:        newMStopServiceConfig(db, opts...),
		MSwitchSceneValue:         newMSwitchSceneValue(db, opts...),
		MUpload:                   newMUpload(db, opts...),
		MUser:                     newMUser(db, opts...),
		MUserBan:                  newMUserBan(db, opts...),
		MUserRole:                 newMUserRole(db, opts...),
		MWorkorder:                newMWorkorder(db, opts...),
		MWorkorderAttachment:      newMWorkorderAttachment(db, opts...),
		MWorkorderBusySwitch:      newMWorkorderBusySwitch(db, opts...),
		MWorkorderConfig:          newMWorkorderConfig(db, opts...),
		MWorkorderFollow:          newMWorkorderFollow(db, opts...),
		MWorkorderOperation:       newMWorkorderOperation(db, opts...),
		MWorkorderReply:           newMWorkorderReply(db, opts...),
		MWorkorderReplyAttachment: newMWorkorderReplyAttachment(db, opts...),
		MWorkorderReviewSwitch:    newMWorkorderReviewSwitch(db, opts...),
		MWorkorderTag:             newMWorkorderTag(db, opts...),
		MWorkorderTagRelation:     newMWorkorderTagRelation(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	ACompany                  aCompany
	AConfigDouyin             aConfigDouyin
	AConfigDouyinClientToken  aConfigDouyinClientToken
	AConfigMinigame           aConfigMinigame
	AConfigMiniprogram        aConfigMiniprogram
	AConfigQq                 aConfigQq
	AConfigSubscribe          aConfigSubscribe
	AGamePlayer               aGamePlayer
	AGamePlayerDouyin         aGamePlayerDouyin
	AGood                     aGood
	AOrder                    aOrder
	ARobotFilter              aRobotFilter
	ASubscribeMessage         aSubscribeMessage
	AUpload                   aUpload
	AUser                     aUser
	AUserDouyin               aUserDouyin
	AUserDouyinWeb            aUserDouyinWeb
	AUserMinigame             aUserMinigame
	AUserQq                   aUserQq
	AUserShareCode            aUserShareCode
	AUserSubscribe            aUserSubscribe
	AVoip                     aVoip
	H5AdminConfig             h5AdminConfig
	H5AdminUser               h5AdminUser
	H5AdminUserRechargeLimit  h5AdminUserRechargeLimit
	MAdPosition               mAdPosition
	MAdPositionPlatform       mAdPositionPlatform
	MCaptchaConfig            mCaptchaConfig
	MChatMessage              mChatMessage
	MCityCode                 mCityCode
	MCustomSwitch             mCustomSwitch
	MCustomSwitchParam        mCustomSwitchParam
	MCustomerServiceMedium    mCustomerServiceMedium
	MCustomerServiceMessage   mCustomerServiceMessage
	MEmailConfig              mEmailConfig
	MGame                     mGame
	MMonitorContentConfig     mMonitorContentConfig
	MMonitorContentOperation  mMonitorContentOperation
	MMonitorGameContent       mMonitorGameContent
	MMonitorGameServer        mMonitorGameServer
	MPermission               mPermission
	MPermissionDatum          mPermissionDatum
	MPkgTask                  mPkgTask
	MQuestionLibrary          mQuestionLibrary
	MQuestionSystemPrompt     mQuestionSystemPrompt
	MQuestionWelcomeMessage   mQuestionWelcomeMessage
	MRedemptionCode           mRedemptionCode
	MRedemptionCodeEntity     mRedemptionCodeEntity
	MRedemptionCodeRecord     mRedemptionCodeRecord
	MReport                   mReport
	MReportActionConfig       mReportActionConfig
	MReportItemConfig         mReportItemConfig
	MReportOperation          mReportOperation
	MRole                     mRole
	MRolePermission           mRolePermission
	MSensitiveWord            mSensitiveWord
	MSensitiveWordConfig      mSensitiveWordConfig
	MShare                    mShare
	MShareRoadblock           mShareRoadblock
	MStopServiceConfig        mStopServiceConfig
	MSwitchSceneValue         mSwitchSceneValue
	MUpload                   mUpload
	MUser                     mUser
	MUserBan                  mUserBan
	MUserRole                 mUserRole
	MWorkorder                mWorkorder
	MWorkorderAttachment      mWorkorderAttachment
	MWorkorderBusySwitch      mWorkorderBusySwitch
	MWorkorderConfig          mWorkorderConfig
	MWorkorderFollow          mWorkorderFollow
	MWorkorderOperation       mWorkorderOperation
	MWorkorderReply           mWorkorderReply
	MWorkorderReplyAttachment mWorkorderReplyAttachment
	MWorkorderReviewSwitch    mWorkorderReviewSwitch
	MWorkorderTag             mWorkorderTag
	MWorkorderTagRelation     mWorkorderTagRelation
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                        db,
		ACompany:                  q.ACompany.clone(db),
		AConfigDouyin:             q.AConfigDouyin.clone(db),
		AConfigDouyinClientToken:  q.AConfigDouyinClientToken.clone(db),
		AConfigMinigame:           q.AConfigMinigame.clone(db),
		AConfigMiniprogram:        q.AConfigMiniprogram.clone(db),
		AConfigQq:                 q.AConfigQq.clone(db),
		AConfigSubscribe:          q.AConfigSubscribe.clone(db),
		AGamePlayer:               q.AGamePlayer.clone(db),
		AGamePlayerDouyin:         q.AGamePlayerDouyin.clone(db),
		AGood:                     q.AGood.clone(db),
		AOrder:                    q.AOrder.clone(db),
		ARobotFilter:              q.ARobotFilter.clone(db),
		ASubscribeMessage:         q.ASubscribeMessage.clone(db),
		AUpload:                   q.AUpload.clone(db),
		AUser:                     q.AUser.clone(db),
		AUserDouyin:               q.AUserDouyin.clone(db),
		AUserDouyinWeb:            q.AUserDouyinWeb.clone(db),
		AUserMinigame:             q.AUserMinigame.clone(db),
		AUserQq:                   q.AUserQq.clone(db),
		AUserShareCode:            q.AUserShareCode.clone(db),
		AUserSubscribe:            q.AUserSubscribe.clone(db),
		AVoip:                     q.AVoip.clone(db),
		H5AdminConfig:             q.H5AdminConfig.clone(db),
		H5AdminUser:               q.H5AdminUser.clone(db),
		H5AdminUserRechargeLimit:  q.H5AdminUserRechargeLimit.clone(db),
		MAdPosition:               q.MAdPosition.clone(db),
		MAdPositionPlatform:       q.MAdPositionPlatform.clone(db),
		MCaptchaConfig:            q.MCaptchaConfig.clone(db),
		MChatMessage:              q.MChatMessage.clone(db),
		MCityCode:                 q.MCityCode.clone(db),
		MCustomSwitch:             q.MCustomSwitch.clone(db),
		MCustomSwitchParam:        q.MCustomSwitchParam.clone(db),
		MCustomerServiceMedium:    q.MCustomerServiceMedium.clone(db),
		MCustomerServiceMessage:   q.MCustomerServiceMessage.clone(db),
		MEmailConfig:              q.MEmailConfig.clone(db),
		MGame:                     q.MGame.clone(db),
		MMonitorContentConfig:     q.MMonitorContentConfig.clone(db),
		MMonitorContentOperation:  q.MMonitorContentOperation.clone(db),
		MMonitorGameContent:       q.MMonitorGameContent.clone(db),
		MMonitorGameServer:        q.MMonitorGameServer.clone(db),
		MPermission:               q.MPermission.clone(db),
		MPermissionDatum:          q.MPermissionDatum.clone(db),
		MPkgTask:                  q.MPkgTask.clone(db),
		MQuestionLibrary:          q.MQuestionLibrary.clone(db),
		MQuestionSystemPrompt:     q.MQuestionSystemPrompt.clone(db),
		MQuestionWelcomeMessage:   q.MQuestionWelcomeMessage.clone(db),
		MRedemptionCode:           q.MRedemptionCode.clone(db),
		MRedemptionCodeEntity:     q.MRedemptionCodeEntity.clone(db),
		MRedemptionCodeRecord:     q.MRedemptionCodeRecord.clone(db),
		MReport:                   q.MReport.clone(db),
		MReportActionConfig:       q.MReportActionConfig.clone(db),
		MReportItemConfig:         q.MReportItemConfig.clone(db),
		MReportOperation:          q.MReportOperation.clone(db),
		MRole:                     q.MRole.clone(db),
		MRolePermission:           q.MRolePermission.clone(db),
		MSensitiveWord:            q.MSensitiveWord.clone(db),
		MSensitiveWordConfig:      q.MSensitiveWordConfig.clone(db),
		MShare:                    q.MShare.clone(db),
		MShareRoadblock:           q.MShareRoadblock.clone(db),
		MStopServiceConfig:        q.MStopServiceConfig.clone(db),
		MSwitchSceneValue:         q.MSwitchSceneValue.clone(db),
		MUpload:                   q.MUpload.clone(db),
		MUser:                     q.MUser.clone(db),
		MUserBan:                  q.MUserBan.clone(db),
		MUserRole:                 q.MUserRole.clone(db),
		MWorkorder:                q.MWorkorder.clone(db),
		MWorkorderAttachment:      q.MWorkorderAttachment.clone(db),
		MWorkorderBusySwitch:      q.MWorkorderBusySwitch.clone(db),
		MWorkorderConfig:          q.MWorkorderConfig.clone(db),
		MWorkorderFollow:          q.MWorkorderFollow.clone(db),
		MWorkorderOperation:       q.MWorkorderOperation.clone(db),
		MWorkorderReply:           q.MWorkorderReply.clone(db),
		MWorkorderReplyAttachment: q.MWorkorderReplyAttachment.clone(db),
		MWorkorderReviewSwitch:    q.MWorkorderReviewSwitch.clone(db),
		MWorkorderTag:             q.MWorkorderTag.clone(db),
		MWorkorderTagRelation:     q.MWorkorderTagRelation.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                        db,
		ACompany:                  q.ACompany.replaceDB(db),
		AConfigDouyin:             q.AConfigDouyin.replaceDB(db),
		AConfigDouyinClientToken:  q.AConfigDouyinClientToken.replaceDB(db),
		AConfigMinigame:           q.AConfigMinigame.replaceDB(db),
		AConfigMiniprogram:        q.AConfigMiniprogram.replaceDB(db),
		AConfigQq:                 q.AConfigQq.replaceDB(db),
		AConfigSubscribe:          q.AConfigSubscribe.replaceDB(db),
		AGamePlayer:               q.AGamePlayer.replaceDB(db),
		AGamePlayerDouyin:         q.AGamePlayerDouyin.replaceDB(db),
		AGood:                     q.AGood.replaceDB(db),
		AOrder:                    q.AOrder.replaceDB(db),
		ARobotFilter:              q.ARobotFilter.replaceDB(db),
		ASubscribeMessage:         q.ASubscribeMessage.replaceDB(db),
		AUpload:                   q.AUpload.replaceDB(db),
		AUser:                     q.AUser.replaceDB(db),
		AUserDouyin:               q.AUserDouyin.replaceDB(db),
		AUserDouyinWeb:            q.AUserDouyinWeb.replaceDB(db),
		AUserMinigame:             q.AUserMinigame.replaceDB(db),
		AUserQq:                   q.AUserQq.replaceDB(db),
		AUserShareCode:            q.AUserShareCode.replaceDB(db),
		AUserSubscribe:            q.AUserSubscribe.replaceDB(db),
		AVoip:                     q.AVoip.replaceDB(db),
		H5AdminConfig:             q.H5AdminConfig.replaceDB(db),
		H5AdminUser:               q.H5AdminUser.replaceDB(db),
		H5AdminUserRechargeLimit:  q.H5AdminUserRechargeLimit.replaceDB(db),
		MAdPosition:               q.MAdPosition.replaceDB(db),
		MAdPositionPlatform:       q.MAdPositionPlatform.replaceDB(db),
		MCaptchaConfig:            q.MCaptchaConfig.replaceDB(db),
		MChatMessage:              q.MChatMessage.replaceDB(db),
		MCityCode:                 q.MCityCode.replaceDB(db),
		MCustomSwitch:             q.MCustomSwitch.replaceDB(db),
		MCustomSwitchParam:        q.MCustomSwitchParam.replaceDB(db),
		MCustomerServiceMedium:    q.MCustomerServiceMedium.replaceDB(db),
		MCustomerServiceMessage:   q.MCustomerServiceMessage.replaceDB(db),
		MEmailConfig:              q.MEmailConfig.replaceDB(db),
		MGame:                     q.MGame.replaceDB(db),
		MMonitorContentConfig:     q.MMonitorContentConfig.replaceDB(db),
		MMonitorContentOperation:  q.MMonitorContentOperation.replaceDB(db),
		MMonitorGameContent:       q.MMonitorGameContent.replaceDB(db),
		MMonitorGameServer:        q.MMonitorGameServer.replaceDB(db),
		MPermission:               q.MPermission.replaceDB(db),
		MPermissionDatum:          q.MPermissionDatum.replaceDB(db),
		MPkgTask:                  q.MPkgTask.replaceDB(db),
		MQuestionLibrary:          q.MQuestionLibrary.replaceDB(db),
		MQuestionSystemPrompt:     q.MQuestionSystemPrompt.replaceDB(db),
		MQuestionWelcomeMessage:   q.MQuestionWelcomeMessage.replaceDB(db),
		MRedemptionCode:           q.MRedemptionCode.replaceDB(db),
		MRedemptionCodeEntity:     q.MRedemptionCodeEntity.replaceDB(db),
		MRedemptionCodeRecord:     q.MRedemptionCodeRecord.replaceDB(db),
		MReport:                   q.MReport.replaceDB(db),
		MReportActionConfig:       q.MReportActionConfig.replaceDB(db),
		MReportItemConfig:         q.MReportItemConfig.replaceDB(db),
		MReportOperation:          q.MReportOperation.replaceDB(db),
		MRole:                     q.MRole.replaceDB(db),
		MRolePermission:           q.MRolePermission.replaceDB(db),
		MSensitiveWord:            q.MSensitiveWord.replaceDB(db),
		MSensitiveWordConfig:      q.MSensitiveWordConfig.replaceDB(db),
		MShare:                    q.MShare.replaceDB(db),
		MShareRoadblock:           q.MShareRoadblock.replaceDB(db),
		MStopServiceConfig:        q.MStopServiceConfig.replaceDB(db),
		MSwitchSceneValue:         q.MSwitchSceneValue.replaceDB(db),
		MUpload:                   q.MUpload.replaceDB(db),
		MUser:                     q.MUser.replaceDB(db),
		MUserBan:                  q.MUserBan.replaceDB(db),
		MUserRole:                 q.MUserRole.replaceDB(db),
		MWorkorder:                q.MWorkorder.replaceDB(db),
		MWorkorderAttachment:      q.MWorkorderAttachment.replaceDB(db),
		MWorkorderBusySwitch:      q.MWorkorderBusySwitch.replaceDB(db),
		MWorkorderConfig:          q.MWorkorderConfig.replaceDB(db),
		MWorkorderFollow:          q.MWorkorderFollow.replaceDB(db),
		MWorkorderOperation:       q.MWorkorderOperation.replaceDB(db),
		MWorkorderReply:           q.MWorkorderReply.replaceDB(db),
		MWorkorderReplyAttachment: q.MWorkorderReplyAttachment.replaceDB(db),
		MWorkorderReviewSwitch:    q.MWorkorderReviewSwitch.replaceDB(db),
		MWorkorderTag:             q.MWorkorderTag.replaceDB(db),
		MWorkorderTagRelation:     q.MWorkorderTagRelation.replaceDB(db),
	}
}

type queryCtx struct {
	ACompany                  IACompanyDo
	AConfigDouyin             IAConfigDouyinDo
	AConfigDouyinClientToken  IAConfigDouyinClientTokenDo
	AConfigMinigame           IAConfigMinigameDo
	AConfigMiniprogram        IAConfigMiniprogramDo
	AConfigQq                 IAConfigQqDo
	AConfigSubscribe          IAConfigSubscribeDo
	AGamePlayer               IAGamePlayerDo
	AGamePlayerDouyin         IAGamePlayerDouyinDo
	AGood                     IAGoodDo
	AOrder                    IAOrderDo
	ARobotFilter              IARobotFilterDo
	ASubscribeMessage         IASubscribeMessageDo
	AUpload                   IAUploadDo
	AUser                     IAUserDo
	AUserDouyin               IAUserDouyinDo
	AUserDouyinWeb            IAUserDouyinWebDo
	AUserMinigame             IAUserMinigameDo
	AUserQq                   IAUserQqDo
	AUserShareCode            IAUserShareCodeDo
	AUserSubscribe            IAUserSubscribeDo
	AVoip                     IAVoipDo
	H5AdminConfig             IH5AdminConfigDo
	H5AdminUser               IH5AdminUserDo
	H5AdminUserRechargeLimit  IH5AdminUserRechargeLimitDo
	MAdPosition               IMAdPositionDo
	MAdPositionPlatform       IMAdPositionPlatformDo
	MCaptchaConfig            IMCaptchaConfigDo
	MChatMessage              IMChatMessageDo
	MCityCode                 IMCityCodeDo
	MCustomSwitch             IMCustomSwitchDo
	MCustomSwitchParam        IMCustomSwitchParamDo
	MCustomerServiceMedium    IMCustomerServiceMediumDo
	MCustomerServiceMessage   IMCustomerServiceMessageDo
	MEmailConfig              IMEmailConfigDo
	MGame                     IMGameDo
	MMonitorContentConfig     IMMonitorContentConfigDo
	MMonitorContentOperation  IMMonitorContentOperationDo
	MMonitorGameContent       IMMonitorGameContentDo
	MMonitorGameServer        IMMonitorGameServerDo
	MPermission               IMPermissionDo
	MPermissionDatum          IMPermissionDatumDo
	MPkgTask                  IMPkgTaskDo
	MQuestionLibrary          IMQuestionLibraryDo
	MQuestionSystemPrompt     IMQuestionSystemPromptDo
	MQuestionWelcomeMessage   IMQuestionWelcomeMessageDo
	MRedemptionCode           IMRedemptionCodeDo
	MRedemptionCodeEntity     IMRedemptionCodeEntityDo
	MRedemptionCodeRecord     IMRedemptionCodeRecordDo
	MReport                   IMReportDo
	MReportActionConfig       IMReportActionConfigDo
	MReportItemConfig         IMReportItemConfigDo
	MReportOperation          IMReportOperationDo
	MRole                     IMRoleDo
	MRolePermission           IMRolePermissionDo
	MSensitiveWord            IMSensitiveWordDo
	MSensitiveWordConfig      IMSensitiveWordConfigDo
	MShare                    IMShareDo
	MShareRoadblock           IMShareRoadblockDo
	MStopServiceConfig        IMStopServiceConfigDo
	MSwitchSceneValue         IMSwitchSceneValueDo
	MUpload                   IMUploadDo
	MUser                     IMUserDo
	MUserBan                  IMUserBanDo
	MUserRole                 IMUserRoleDo
	MWorkorder                IMWorkorderDo
	MWorkorderAttachment      IMWorkorderAttachmentDo
	MWorkorderBusySwitch      IMWorkorderBusySwitchDo
	MWorkorderConfig          IMWorkorderConfigDo
	MWorkorderFollow          IMWorkorderFollowDo
	MWorkorderOperation       IMWorkorderOperationDo
	MWorkorderReply           IMWorkorderReplyDo
	MWorkorderReplyAttachment IMWorkorderReplyAttachmentDo
	MWorkorderReviewSwitch    IMWorkorderReviewSwitchDo
	MWorkorderTag             IMWorkorderTagDo
	MWorkorderTagRelation     IMWorkorderTagRelationDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		ACompany:                  q.ACompany.WithContext(ctx),
		AConfigDouyin:             q.AConfigDouyin.WithContext(ctx),
		AConfigDouyinClientToken:  q.AConfigDouyinClientToken.WithContext(ctx),
		AConfigMinigame:           q.AConfigMinigame.WithContext(ctx),
		AConfigMiniprogram:        q.AConfigMiniprogram.WithContext(ctx),
		AConfigQq:                 q.AConfigQq.WithContext(ctx),
		AConfigSubscribe:          q.AConfigSubscribe.WithContext(ctx),
		AGamePlayer:               q.AGamePlayer.WithContext(ctx),
		AGamePlayerDouyin:         q.AGamePlayerDouyin.WithContext(ctx),
		AGood:                     q.AGood.WithContext(ctx),
		AOrder:                    q.AOrder.WithContext(ctx),
		ARobotFilter:              q.ARobotFilter.WithContext(ctx),
		ASubscribeMessage:         q.ASubscribeMessage.WithContext(ctx),
		AUpload:                   q.AUpload.WithContext(ctx),
		AUser:                     q.AUser.WithContext(ctx),
		AUserDouyin:               q.AUserDouyin.WithContext(ctx),
		AUserDouyinWeb:            q.AUserDouyinWeb.WithContext(ctx),
		AUserMinigame:             q.AUserMinigame.WithContext(ctx),
		AUserQq:                   q.AUserQq.WithContext(ctx),
		AUserShareCode:            q.AUserShareCode.WithContext(ctx),
		AUserSubscribe:            q.AUserSubscribe.WithContext(ctx),
		AVoip:                     q.AVoip.WithContext(ctx),
		H5AdminConfig:             q.H5AdminConfig.WithContext(ctx),
		H5AdminUser:               q.H5AdminUser.WithContext(ctx),
		H5AdminUserRechargeLimit:  q.H5AdminUserRechargeLimit.WithContext(ctx),
		MAdPosition:               q.MAdPosition.WithContext(ctx),
		MAdPositionPlatform:       q.MAdPositionPlatform.WithContext(ctx),
		MCaptchaConfig:            q.MCaptchaConfig.WithContext(ctx),
		MChatMessage:              q.MChatMessage.WithContext(ctx),
		MCityCode:                 q.MCityCode.WithContext(ctx),
		MCustomSwitch:             q.MCustomSwitch.WithContext(ctx),
		MCustomSwitchParam:        q.MCustomSwitchParam.WithContext(ctx),
		MCustomerServiceMedium:    q.MCustomerServiceMedium.WithContext(ctx),
		MCustomerServiceMessage:   q.MCustomerServiceMessage.WithContext(ctx),
		MEmailConfig:              q.MEmailConfig.WithContext(ctx),
		MGame:                     q.MGame.WithContext(ctx),
		MMonitorContentConfig:     q.MMonitorContentConfig.WithContext(ctx),
		MMonitorContentOperation:  q.MMonitorContentOperation.WithContext(ctx),
		MMonitorGameContent:       q.MMonitorGameContent.WithContext(ctx),
		MMonitorGameServer:        q.MMonitorGameServer.WithContext(ctx),
		MPermission:               q.MPermission.WithContext(ctx),
		MPermissionDatum:          q.MPermissionDatum.WithContext(ctx),
		MPkgTask:                  q.MPkgTask.WithContext(ctx),
		MQuestionLibrary:          q.MQuestionLibrary.WithContext(ctx),
		MQuestionSystemPrompt:     q.MQuestionSystemPrompt.WithContext(ctx),
		MQuestionWelcomeMessage:   q.MQuestionWelcomeMessage.WithContext(ctx),
		MRedemptionCode:           q.MRedemptionCode.WithContext(ctx),
		MRedemptionCodeEntity:     q.MRedemptionCodeEntity.WithContext(ctx),
		MRedemptionCodeRecord:     q.MRedemptionCodeRecord.WithContext(ctx),
		MReport:                   q.MReport.WithContext(ctx),
		MReportActionConfig:       q.MReportActionConfig.WithContext(ctx),
		MReportItemConfig:         q.MReportItemConfig.WithContext(ctx),
		MReportOperation:          q.MReportOperation.WithContext(ctx),
		MRole:                     q.MRole.WithContext(ctx),
		MRolePermission:           q.MRolePermission.WithContext(ctx),
		MSensitiveWord:            q.MSensitiveWord.WithContext(ctx),
		MSensitiveWordConfig:      q.MSensitiveWordConfig.WithContext(ctx),
		MShare:                    q.MShare.WithContext(ctx),
		MShareRoadblock:           q.MShareRoadblock.WithContext(ctx),
		MStopServiceConfig:        q.MStopServiceConfig.WithContext(ctx),
		MSwitchSceneValue:         q.MSwitchSceneValue.WithContext(ctx),
		MUpload:                   q.MUpload.WithContext(ctx),
		MUser:                     q.MUser.WithContext(ctx),
		MUserBan:                  q.MUserBan.WithContext(ctx),
		MUserRole:                 q.MUserRole.WithContext(ctx),
		MWorkorder:                q.MWorkorder.WithContext(ctx),
		MWorkorderAttachment:      q.MWorkorderAttachment.WithContext(ctx),
		MWorkorderBusySwitch:      q.MWorkorderBusySwitch.WithContext(ctx),
		MWorkorderConfig:          q.MWorkorderConfig.WithContext(ctx),
		MWorkorderFollow:          q.MWorkorderFollow.WithContext(ctx),
		MWorkorderOperation:       q.MWorkorderOperation.WithContext(ctx),
		MWorkorderReply:           q.MWorkorderReply.WithContext(ctx),
		MWorkorderReplyAttachment: q.MWorkorderReplyAttachment.WithContext(ctx),
		MWorkorderReviewSwitch:    q.MWorkorderReviewSwitch.WithContext(ctx),
		MWorkorderTag:             q.MWorkorderTag.WithContext(ctx),
		MWorkorderTagRelation:     q.MWorkorderTagRelation.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
