// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMWorkorderReplyAttachment(db *gorm.DB, opts ...gen.DOOption) mWorkorderReplyAttachment {
	_mWorkorderReplyAttachment := mWorkorderReplyAttachment{}

	_mWorkorderReplyAttachment.mWorkorderReplyAttachmentDo.UseDB(db, opts...)
	_mWorkorderReplyAttachment.mWorkorderReplyAttachmentDo.UseModel(&model.MWorkorderReplyAttachment{})

	tableName := _mWorkorderReplyAttachment.mWorkorderReplyAttachmentDo.TableName()
	_mWorkorderReplyAttachment.ALL = field.NewAsterisk(tableName)
	_mWorkorderReplyAttachment.ID = field.NewInt32(tableName, "id")
	_mWorkorderReplyAttachment.ReplyID = field.NewInt32(tableName, "reply_id")
	_mWorkorderReplyAttachment.OrderID = field.NewString(tableName, "order_id")
	_mWorkorderReplyAttachment.FileURL = field.NewString(tableName, "file_url")
	_mWorkorderReplyAttachment.FileType = field.NewInt32(tableName, "file_type")
	_mWorkorderReplyAttachment.CreatedAt = field.NewInt64(tableName, "created_at")
	_mWorkorderReplyAttachment.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mWorkorderReplyAttachment.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mWorkorderReplyAttachment.fillFieldMap()

	return _mWorkorderReplyAttachment
}

// mWorkorderReplyAttachment 工单回复附件表
type mWorkorderReplyAttachment struct {
	mWorkorderReplyAttachmentDo

	ALL       field.Asterisk
	ID        field.Int32
	ReplyID   field.Int32  // 回复ID
	OrderID   field.String // 工单ID
	FileURL   field.String // 文件URL
	FileType  field.Int32  // 文件类型: 1-图片, 2-视频
	CreatedAt field.Int64
	UpdatedAt field.Int64
	IsDeleted field.Bool

	fieldMap map[string]field.Expr
}

func (m mWorkorderReplyAttachment) Table(newTableName string) *mWorkorderReplyAttachment {
	m.mWorkorderReplyAttachmentDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mWorkorderReplyAttachment) As(alias string) *mWorkorderReplyAttachment {
	m.mWorkorderReplyAttachmentDo.DO = *(m.mWorkorderReplyAttachmentDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mWorkorderReplyAttachment) updateTableName(table string) *mWorkorderReplyAttachment {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.ReplyID = field.NewInt32(table, "reply_id")
	m.OrderID = field.NewString(table, "order_id")
	m.FileURL = field.NewString(table, "file_url")
	m.FileType = field.NewInt32(table, "file_type")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mWorkorderReplyAttachment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mWorkorderReplyAttachment) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 8)
	m.fieldMap["id"] = m.ID
	m.fieldMap["reply_id"] = m.ReplyID
	m.fieldMap["order_id"] = m.OrderID
	m.fieldMap["file_url"] = m.FileURL
	m.fieldMap["file_type"] = m.FileType
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mWorkorderReplyAttachment) clone(db *gorm.DB) mWorkorderReplyAttachment {
	m.mWorkorderReplyAttachmentDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mWorkorderReplyAttachment) replaceDB(db *gorm.DB) mWorkorderReplyAttachment {
	m.mWorkorderReplyAttachmentDo.ReplaceDB(db)
	return m
}

type mWorkorderReplyAttachmentDo struct{ gen.DO }

type IMWorkorderReplyAttachmentDo interface {
	gen.SubQuery
	Debug() IMWorkorderReplyAttachmentDo
	WithContext(ctx context.Context) IMWorkorderReplyAttachmentDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMWorkorderReplyAttachmentDo
	WriteDB() IMWorkorderReplyAttachmentDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMWorkorderReplyAttachmentDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMWorkorderReplyAttachmentDo
	Not(conds ...gen.Condition) IMWorkorderReplyAttachmentDo
	Or(conds ...gen.Condition) IMWorkorderReplyAttachmentDo
	Select(conds ...field.Expr) IMWorkorderReplyAttachmentDo
	Where(conds ...gen.Condition) IMWorkorderReplyAttachmentDo
	Order(conds ...field.Expr) IMWorkorderReplyAttachmentDo
	Distinct(cols ...field.Expr) IMWorkorderReplyAttachmentDo
	Omit(cols ...field.Expr) IMWorkorderReplyAttachmentDo
	Join(table schema.Tabler, on ...field.Expr) IMWorkorderReplyAttachmentDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderReplyAttachmentDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderReplyAttachmentDo
	Group(cols ...field.Expr) IMWorkorderReplyAttachmentDo
	Having(conds ...gen.Condition) IMWorkorderReplyAttachmentDo
	Limit(limit int) IMWorkorderReplyAttachmentDo
	Offset(offset int) IMWorkorderReplyAttachmentDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderReplyAttachmentDo
	Unscoped() IMWorkorderReplyAttachmentDo
	Create(values ...*model.MWorkorderReplyAttachment) error
	CreateInBatches(values []*model.MWorkorderReplyAttachment, batchSize int) error
	Save(values ...*model.MWorkorderReplyAttachment) error
	First() (*model.MWorkorderReplyAttachment, error)
	Take() (*model.MWorkorderReplyAttachment, error)
	Last() (*model.MWorkorderReplyAttachment, error)
	Find() ([]*model.MWorkorderReplyAttachment, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderReplyAttachment, err error)
	FindInBatches(result *[]*model.MWorkorderReplyAttachment, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MWorkorderReplyAttachment) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMWorkorderReplyAttachmentDo
	Assign(attrs ...field.AssignExpr) IMWorkorderReplyAttachmentDo
	Joins(fields ...field.RelationField) IMWorkorderReplyAttachmentDo
	Preload(fields ...field.RelationField) IMWorkorderReplyAttachmentDo
	FirstOrInit() (*model.MWorkorderReplyAttachment, error)
	FirstOrCreate() (*model.MWorkorderReplyAttachment, error)
	FindByPage(offset int, limit int) (result []*model.MWorkorderReplyAttachment, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMWorkorderReplyAttachmentDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mWorkorderReplyAttachmentDo) Debug() IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Debug())
}

func (m mWorkorderReplyAttachmentDo) WithContext(ctx context.Context) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mWorkorderReplyAttachmentDo) ReadDB() IMWorkorderReplyAttachmentDo {
	return m.Clauses(dbresolver.Read)
}

func (m mWorkorderReplyAttachmentDo) WriteDB() IMWorkorderReplyAttachmentDo {
	return m.Clauses(dbresolver.Write)
}

func (m mWorkorderReplyAttachmentDo) Session(config *gorm.Session) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Session(config))
}

func (m mWorkorderReplyAttachmentDo) Clauses(conds ...clause.Expression) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mWorkorderReplyAttachmentDo) Returning(value interface{}, columns ...string) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mWorkorderReplyAttachmentDo) Not(conds ...gen.Condition) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mWorkorderReplyAttachmentDo) Or(conds ...gen.Condition) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mWorkorderReplyAttachmentDo) Select(conds ...field.Expr) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mWorkorderReplyAttachmentDo) Where(conds ...gen.Condition) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mWorkorderReplyAttachmentDo) Order(conds ...field.Expr) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mWorkorderReplyAttachmentDo) Distinct(cols ...field.Expr) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mWorkorderReplyAttachmentDo) Omit(cols ...field.Expr) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mWorkorderReplyAttachmentDo) Join(table schema.Tabler, on ...field.Expr) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mWorkorderReplyAttachmentDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mWorkorderReplyAttachmentDo) RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mWorkorderReplyAttachmentDo) Group(cols ...field.Expr) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mWorkorderReplyAttachmentDo) Having(conds ...gen.Condition) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mWorkorderReplyAttachmentDo) Limit(limit int) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mWorkorderReplyAttachmentDo) Offset(offset int) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mWorkorderReplyAttachmentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mWorkorderReplyAttachmentDo) Unscoped() IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mWorkorderReplyAttachmentDo) Create(values ...*model.MWorkorderReplyAttachment) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mWorkorderReplyAttachmentDo) CreateInBatches(values []*model.MWorkorderReplyAttachment, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mWorkorderReplyAttachmentDo) Save(values ...*model.MWorkorderReplyAttachment) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mWorkorderReplyAttachmentDo) First() (*model.MWorkorderReplyAttachment, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReplyAttachment), nil
	}
}

func (m mWorkorderReplyAttachmentDo) Take() (*model.MWorkorderReplyAttachment, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReplyAttachment), nil
	}
}

func (m mWorkorderReplyAttachmentDo) Last() (*model.MWorkorderReplyAttachment, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReplyAttachment), nil
	}
}

func (m mWorkorderReplyAttachmentDo) Find() ([]*model.MWorkorderReplyAttachment, error) {
	result, err := m.DO.Find()
	return result.([]*model.MWorkorderReplyAttachment), err
}

func (m mWorkorderReplyAttachmentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderReplyAttachment, err error) {
	buf := make([]*model.MWorkorderReplyAttachment, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mWorkorderReplyAttachmentDo) FindInBatches(result *[]*model.MWorkorderReplyAttachment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mWorkorderReplyAttachmentDo) Attrs(attrs ...field.AssignExpr) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mWorkorderReplyAttachmentDo) Assign(attrs ...field.AssignExpr) IMWorkorderReplyAttachmentDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mWorkorderReplyAttachmentDo) Joins(fields ...field.RelationField) IMWorkorderReplyAttachmentDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mWorkorderReplyAttachmentDo) Preload(fields ...field.RelationField) IMWorkorderReplyAttachmentDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mWorkorderReplyAttachmentDo) FirstOrInit() (*model.MWorkorderReplyAttachment, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReplyAttachment), nil
	}
}

func (m mWorkorderReplyAttachmentDo) FirstOrCreate() (*model.MWorkorderReplyAttachment, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderReplyAttachment), nil
	}
}

func (m mWorkorderReplyAttachmentDo) FindByPage(offset int, limit int) (result []*model.MWorkorderReplyAttachment, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mWorkorderReplyAttachmentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mWorkorderReplyAttachmentDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mWorkorderReplyAttachmentDo) Delete(models ...*model.MWorkorderReplyAttachment) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mWorkorderReplyAttachmentDo) withDO(do gen.Dao) *mWorkorderReplyAttachmentDo {
	m.DO = *do.(*gen.DO)
	return m
}
