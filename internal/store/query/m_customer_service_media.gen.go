// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMCustomerServiceMedium(db *gorm.DB, opts ...gen.DOOption) mCustomerServiceMedium {
	_mCustomerServiceMedium := mCustomerServiceMedium{}

	_mCustomerServiceMedium.mCustomerServiceMediumDo.UseDB(db, opts...)
	_mCustomerServiceMedium.mCustomerServiceMediumDo.UseModel(&model.MCustomerServiceMedium{})

	tableName := _mCustomerServiceMedium.mCustomerServiceMediumDo.TableName()
	_mCustomerServiceMedium.ALL = field.NewAsterisk(tableName)
	_mCustomerServiceMedium.ID = field.NewInt32(tableName, "id")
	_mCustomerServiceMedium.GameID = field.NewString(tableName, "game_id")
	_mCustomerServiceMedium.URL = field.NewString(tableName, "url")
	_mCustomerServiceMedium.FileType = field.NewString(tableName, "file_type")
	_mCustomerServiceMedium.MediaID = field.NewString(tableName, "media_id")
	_mCustomerServiceMedium.CreatedAt = field.NewInt64(tableName, "created_at")
	_mCustomerServiceMedium.ExpiredAt = field.NewInt64(tableName, "expired_at")

	_mCustomerServiceMedium.fillFieldMap()

	return _mCustomerServiceMedium
}

type mCustomerServiceMedium struct {
	mCustomerServiceMediumDo

	ALL       field.Asterisk
	ID        field.Int32
	GameID    field.String
	URL       field.String
	FileType  field.String
	MediaID   field.String
	CreatedAt field.Int64
	ExpiredAt field.Int64

	fieldMap map[string]field.Expr
}

func (m mCustomerServiceMedium) Table(newTableName string) *mCustomerServiceMedium {
	m.mCustomerServiceMediumDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mCustomerServiceMedium) As(alias string) *mCustomerServiceMedium {
	m.mCustomerServiceMediumDo.DO = *(m.mCustomerServiceMediumDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mCustomerServiceMedium) updateTableName(table string) *mCustomerServiceMedium {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.URL = field.NewString(table, "url")
	m.FileType = field.NewString(table, "file_type")
	m.MediaID = field.NewString(table, "media_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.ExpiredAt = field.NewInt64(table, "expired_at")

	m.fillFieldMap()

	return m
}

func (m *mCustomerServiceMedium) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mCustomerServiceMedium) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 7)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["url"] = m.URL
	m.fieldMap["file_type"] = m.FileType
	m.fieldMap["media_id"] = m.MediaID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["expired_at"] = m.ExpiredAt
}

func (m mCustomerServiceMedium) clone(db *gorm.DB) mCustomerServiceMedium {
	m.mCustomerServiceMediumDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mCustomerServiceMedium) replaceDB(db *gorm.DB) mCustomerServiceMedium {
	m.mCustomerServiceMediumDo.ReplaceDB(db)
	return m
}

type mCustomerServiceMediumDo struct{ gen.DO }

type IMCustomerServiceMediumDo interface {
	gen.SubQuery
	Debug() IMCustomerServiceMediumDo
	WithContext(ctx context.Context) IMCustomerServiceMediumDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMCustomerServiceMediumDo
	WriteDB() IMCustomerServiceMediumDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMCustomerServiceMediumDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMCustomerServiceMediumDo
	Not(conds ...gen.Condition) IMCustomerServiceMediumDo
	Or(conds ...gen.Condition) IMCustomerServiceMediumDo
	Select(conds ...field.Expr) IMCustomerServiceMediumDo
	Where(conds ...gen.Condition) IMCustomerServiceMediumDo
	Order(conds ...field.Expr) IMCustomerServiceMediumDo
	Distinct(cols ...field.Expr) IMCustomerServiceMediumDo
	Omit(cols ...field.Expr) IMCustomerServiceMediumDo
	Join(table schema.Tabler, on ...field.Expr) IMCustomerServiceMediumDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMCustomerServiceMediumDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMCustomerServiceMediumDo
	Group(cols ...field.Expr) IMCustomerServiceMediumDo
	Having(conds ...gen.Condition) IMCustomerServiceMediumDo
	Limit(limit int) IMCustomerServiceMediumDo
	Offset(offset int) IMCustomerServiceMediumDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMCustomerServiceMediumDo
	Unscoped() IMCustomerServiceMediumDo
	Create(values ...*model.MCustomerServiceMedium) error
	CreateInBatches(values []*model.MCustomerServiceMedium, batchSize int) error
	Save(values ...*model.MCustomerServiceMedium) error
	First() (*model.MCustomerServiceMedium, error)
	Take() (*model.MCustomerServiceMedium, error)
	Last() (*model.MCustomerServiceMedium, error)
	Find() ([]*model.MCustomerServiceMedium, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCustomerServiceMedium, err error)
	FindInBatches(result *[]*model.MCustomerServiceMedium, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MCustomerServiceMedium) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMCustomerServiceMediumDo
	Assign(attrs ...field.AssignExpr) IMCustomerServiceMediumDo
	Joins(fields ...field.RelationField) IMCustomerServiceMediumDo
	Preload(fields ...field.RelationField) IMCustomerServiceMediumDo
	FirstOrInit() (*model.MCustomerServiceMedium, error)
	FirstOrCreate() (*model.MCustomerServiceMedium, error)
	FindByPage(offset int, limit int) (result []*model.MCustomerServiceMedium, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMCustomerServiceMediumDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mCustomerServiceMediumDo) Debug() IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Debug())
}

func (m mCustomerServiceMediumDo) WithContext(ctx context.Context) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mCustomerServiceMediumDo) ReadDB() IMCustomerServiceMediumDo {
	return m.Clauses(dbresolver.Read)
}

func (m mCustomerServiceMediumDo) WriteDB() IMCustomerServiceMediumDo {
	return m.Clauses(dbresolver.Write)
}

func (m mCustomerServiceMediumDo) Session(config *gorm.Session) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Session(config))
}

func (m mCustomerServiceMediumDo) Clauses(conds ...clause.Expression) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mCustomerServiceMediumDo) Returning(value interface{}, columns ...string) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mCustomerServiceMediumDo) Not(conds ...gen.Condition) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mCustomerServiceMediumDo) Or(conds ...gen.Condition) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mCustomerServiceMediumDo) Select(conds ...field.Expr) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mCustomerServiceMediumDo) Where(conds ...gen.Condition) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mCustomerServiceMediumDo) Order(conds ...field.Expr) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mCustomerServiceMediumDo) Distinct(cols ...field.Expr) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mCustomerServiceMediumDo) Omit(cols ...field.Expr) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mCustomerServiceMediumDo) Join(table schema.Tabler, on ...field.Expr) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mCustomerServiceMediumDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mCustomerServiceMediumDo) RightJoin(table schema.Tabler, on ...field.Expr) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mCustomerServiceMediumDo) Group(cols ...field.Expr) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mCustomerServiceMediumDo) Having(conds ...gen.Condition) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mCustomerServiceMediumDo) Limit(limit int) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mCustomerServiceMediumDo) Offset(offset int) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mCustomerServiceMediumDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mCustomerServiceMediumDo) Unscoped() IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mCustomerServiceMediumDo) Create(values ...*model.MCustomerServiceMedium) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mCustomerServiceMediumDo) CreateInBatches(values []*model.MCustomerServiceMedium, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mCustomerServiceMediumDo) Save(values ...*model.MCustomerServiceMedium) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mCustomerServiceMediumDo) First() (*model.MCustomerServiceMedium, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceMedium), nil
	}
}

func (m mCustomerServiceMediumDo) Take() (*model.MCustomerServiceMedium, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceMedium), nil
	}
}

func (m mCustomerServiceMediumDo) Last() (*model.MCustomerServiceMedium, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceMedium), nil
	}
}

func (m mCustomerServiceMediumDo) Find() ([]*model.MCustomerServiceMedium, error) {
	result, err := m.DO.Find()
	return result.([]*model.MCustomerServiceMedium), err
}

func (m mCustomerServiceMediumDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCustomerServiceMedium, err error) {
	buf := make([]*model.MCustomerServiceMedium, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mCustomerServiceMediumDo) FindInBatches(result *[]*model.MCustomerServiceMedium, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mCustomerServiceMediumDo) Attrs(attrs ...field.AssignExpr) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mCustomerServiceMediumDo) Assign(attrs ...field.AssignExpr) IMCustomerServiceMediumDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mCustomerServiceMediumDo) Joins(fields ...field.RelationField) IMCustomerServiceMediumDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mCustomerServiceMediumDo) Preload(fields ...field.RelationField) IMCustomerServiceMediumDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mCustomerServiceMediumDo) FirstOrInit() (*model.MCustomerServiceMedium, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceMedium), nil
	}
}

func (m mCustomerServiceMediumDo) FirstOrCreate() (*model.MCustomerServiceMedium, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceMedium), nil
	}
}

func (m mCustomerServiceMediumDo) FindByPage(offset int, limit int) (result []*model.MCustomerServiceMedium, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mCustomerServiceMediumDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mCustomerServiceMediumDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mCustomerServiceMediumDo) Delete(models ...*model.MCustomerServiceMedium) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mCustomerServiceMediumDo) withDO(do gen.Dao) *mCustomerServiceMediumDo {
	m.DO = *do.(*gen.DO)
	return m
}
