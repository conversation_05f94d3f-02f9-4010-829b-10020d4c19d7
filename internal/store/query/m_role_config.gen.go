// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMRoleConfig(db *gorm.DB, opts ...gen.DOOption) mRoleConfig {
	_mRoleConfig := mRoleConfig{}

	_mRoleConfig.mRoleConfigDo.UseDB(db, opts...)
	_mRoleConfig.mRoleConfigDo.UseModel(&model.MRoleConfig{})

	tableName := _mRoleConfig.mRoleConfigDo.TableName()
	_mRoleConfig.ALL = field.NewAsterisk(tableName)
	_mRoleConfig.ID = field.NewInt32(tableName, "id")
	_mRoleConfig.Code = field.NewString(tableName, "code")
	_mRoleConfig.Name = field.NewString(tableName, "name")
	_mRoleConfig.Type = field.NewInt32(tableName, "type")
	_mRoleConfig.Description = field.NewString(tableName, "description")
	_mRoleConfig.IsPreset = field.NewBool(tableName, "is_preset")
	_mRoleConfig.SortOrder = field.NewInt32(tableName, "sort_order")
	_mRoleConfig.CreatedAt = field.NewInt64(tableName, "created_at")
	_mRoleConfig.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mRoleConfig.fillFieldMap()

	return _mRoleConfig
}

// mRoleConfig 角色配置表
type mRoleConfig struct {
	mRoleConfigDo

	ALL         field.Asterisk
	ID          field.Int32
	Code        field.String // 角色编码
	Name        field.String // 角色名称
	Type        field.Int32  // 角色类型 1:系统角色 2:游戏角色
	Description field.String // 角色描述
	IsPreset    field.Bool   // 是否预设角色 1:是 0:否
	SortOrder   field.Int32  // 排序号
	CreatedAt   field.Int64
	UpdatedAt   field.Int64

	fieldMap map[string]field.Expr
}

func (m mRoleConfig) Table(newTableName string) *mRoleConfig {
	m.mRoleConfigDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mRoleConfig) As(alias string) *mRoleConfig {
	m.mRoleConfigDo.DO = *(m.mRoleConfigDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mRoleConfig) updateTableName(table string) *mRoleConfig {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.Code = field.NewString(table, "code")
	m.Name = field.NewString(table, "name")
	m.Type = field.NewInt32(table, "type")
	m.Description = field.NewString(table, "description")
	m.IsPreset = field.NewBool(table, "is_preset")
	m.SortOrder = field.NewInt32(table, "sort_order")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mRoleConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mRoleConfig) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 9)
	m.fieldMap["id"] = m.ID
	m.fieldMap["code"] = m.Code
	m.fieldMap["name"] = m.Name
	m.fieldMap["type"] = m.Type
	m.fieldMap["description"] = m.Description
	m.fieldMap["is_preset"] = m.IsPreset
	m.fieldMap["sort_order"] = m.SortOrder
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mRoleConfig) clone(db *gorm.DB) mRoleConfig {
	m.mRoleConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mRoleConfig) replaceDB(db *gorm.DB) mRoleConfig {
	m.mRoleConfigDo.ReplaceDB(db)
	return m
}

type mRoleConfigDo struct{ gen.DO }

type IMRoleConfigDo interface {
	gen.SubQuery
	Debug() IMRoleConfigDo
	WithContext(ctx context.Context) IMRoleConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMRoleConfigDo
	WriteDB() IMRoleConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMRoleConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMRoleConfigDo
	Not(conds ...gen.Condition) IMRoleConfigDo
	Or(conds ...gen.Condition) IMRoleConfigDo
	Select(conds ...field.Expr) IMRoleConfigDo
	Where(conds ...gen.Condition) IMRoleConfigDo
	Order(conds ...field.Expr) IMRoleConfigDo
	Distinct(cols ...field.Expr) IMRoleConfigDo
	Omit(cols ...field.Expr) IMRoleConfigDo
	Join(table schema.Tabler, on ...field.Expr) IMRoleConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMRoleConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMRoleConfigDo
	Group(cols ...field.Expr) IMRoleConfigDo
	Having(conds ...gen.Condition) IMRoleConfigDo
	Limit(limit int) IMRoleConfigDo
	Offset(offset int) IMRoleConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMRoleConfigDo
	Unscoped() IMRoleConfigDo
	Create(values ...*model.MRoleConfig) error
	CreateInBatches(values []*model.MRoleConfig, batchSize int) error
	Save(values ...*model.MRoleConfig) error
	First() (*model.MRoleConfig, error)
	Take() (*model.MRoleConfig, error)
	Last() (*model.MRoleConfig, error)
	Find() ([]*model.MRoleConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MRoleConfig, err error)
	FindInBatches(result *[]*model.MRoleConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MRoleConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMRoleConfigDo
	Assign(attrs ...field.AssignExpr) IMRoleConfigDo
	Joins(fields ...field.RelationField) IMRoleConfigDo
	Preload(fields ...field.RelationField) IMRoleConfigDo
	FirstOrInit() (*model.MRoleConfig, error)
	FirstOrCreate() (*model.MRoleConfig, error)
	FindByPage(offset int, limit int) (result []*model.MRoleConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMRoleConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mRoleConfigDo) Debug() IMRoleConfigDo {
	return m.withDO(m.DO.Debug())
}

func (m mRoleConfigDo) WithContext(ctx context.Context) IMRoleConfigDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mRoleConfigDo) ReadDB() IMRoleConfigDo {
	return m.Clauses(dbresolver.Read)
}

func (m mRoleConfigDo) WriteDB() IMRoleConfigDo {
	return m.Clauses(dbresolver.Write)
}

func (m mRoleConfigDo) Session(config *gorm.Session) IMRoleConfigDo {
	return m.withDO(m.DO.Session(config))
}

func (m mRoleConfigDo) Clauses(conds ...clause.Expression) IMRoleConfigDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mRoleConfigDo) Returning(value interface{}, columns ...string) IMRoleConfigDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mRoleConfigDo) Not(conds ...gen.Condition) IMRoleConfigDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mRoleConfigDo) Or(conds ...gen.Condition) IMRoleConfigDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mRoleConfigDo) Select(conds ...field.Expr) IMRoleConfigDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mRoleConfigDo) Where(conds ...gen.Condition) IMRoleConfigDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mRoleConfigDo) Order(conds ...field.Expr) IMRoleConfigDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mRoleConfigDo) Distinct(cols ...field.Expr) IMRoleConfigDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mRoleConfigDo) Omit(cols ...field.Expr) IMRoleConfigDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mRoleConfigDo) Join(table schema.Tabler, on ...field.Expr) IMRoleConfigDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mRoleConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMRoleConfigDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mRoleConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) IMRoleConfigDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mRoleConfigDo) Group(cols ...field.Expr) IMRoleConfigDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mRoleConfigDo) Having(conds ...gen.Condition) IMRoleConfigDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mRoleConfigDo) Limit(limit int) IMRoleConfigDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mRoleConfigDo) Offset(offset int) IMRoleConfigDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mRoleConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMRoleConfigDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mRoleConfigDo) Unscoped() IMRoleConfigDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mRoleConfigDo) Create(values ...*model.MRoleConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mRoleConfigDo) CreateInBatches(values []*model.MRoleConfig, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mRoleConfigDo) Save(values ...*model.MRoleConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mRoleConfigDo) First() (*model.MRoleConfig, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRoleConfig), nil
	}
}

func (m mRoleConfigDo) Take() (*model.MRoleConfig, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRoleConfig), nil
	}
}

func (m mRoleConfigDo) Last() (*model.MRoleConfig, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRoleConfig), nil
	}
}

func (m mRoleConfigDo) Find() ([]*model.MRoleConfig, error) {
	result, err := m.DO.Find()
	return result.([]*model.MRoleConfig), err
}

func (m mRoleConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MRoleConfig, err error) {
	buf := make([]*model.MRoleConfig, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mRoleConfigDo) FindInBatches(result *[]*model.MRoleConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mRoleConfigDo) Attrs(attrs ...field.AssignExpr) IMRoleConfigDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mRoleConfigDo) Assign(attrs ...field.AssignExpr) IMRoleConfigDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mRoleConfigDo) Joins(fields ...field.RelationField) IMRoleConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mRoleConfigDo) Preload(fields ...field.RelationField) IMRoleConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mRoleConfigDo) FirstOrInit() (*model.MRoleConfig, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRoleConfig), nil
	}
}

func (m mRoleConfigDo) FirstOrCreate() (*model.MRoleConfig, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRoleConfig), nil
	}
}

func (m mRoleConfigDo) FindByPage(offset int, limit int) (result []*model.MRoleConfig, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mRoleConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mRoleConfigDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mRoleConfigDo) Delete(models ...*model.MRoleConfig) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mRoleConfigDo) withDO(do gen.Dao) *mRoleConfigDo {
	m.DO = *do.(*gen.DO)
	return m
}
