// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newAConfigAlipay(db *gorm.DB, opts ...gen.DOOption) aConfigAlipay {
	_aConfigAlipay := aConfigAlipay{}

	_aConfigAlipay.aConfigAlipayDo.UseDB(db, opts...)
	_aConfigAlipay.aConfigAlipayDo.UseModel(&model.AConfigAlipay{})

	tableName := _aConfigAlipay.aConfigAlipayDo.TableName()
	_aConfigAlipay.ALL = field.NewAsterisk(tableName)
	_aConfigAlipay.ID = field.NewInt32(tableName, "id")
	_aConfigAlipay.GameID = field.NewString(tableName, "game_id")
	_aConfigAlipay.AppID = field.NewString(tableName, "app_id")
	_aConfigAlipay.PrivateKey = field.NewString(tableName, "private_key")
	_aConfigAlipay.PublicKey = field.NewString(tableName, "public_key")
	_aConfigAlipay.Description = field.NewString(tableName, "description")
	_aConfigAlipay.CreatedAt = field.NewInt64(tableName, "created_at")
	_aConfigAlipay.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aConfigAlipay.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aConfigAlipay.fillFieldMap()

	return _aConfigAlipay
}

// aConfigAlipay 支付宝支付配置表，参考 m_game 规范
type aConfigAlipay struct {
	aConfigAlipayDo

	ALL         field.Asterisk
	ID          field.Int32 // 主键ID
	GameID      field.String
	AppID       field.String // 支付宝应用ID
	PrivateKey  field.String // 支付宝私钥
	PublicKey   field.String // 支付宝公钥
	Description field.String // 配置描述
	CreatedAt   field.Int64  // 创建时间（Unix毫秒）
	UpdatedAt   field.Int64  // 更新时间（Unix毫秒）
	IsDeleted   field.Bool   // 删除标记：0-未删除，1-已删除

	fieldMap map[string]field.Expr
}

func (a aConfigAlipay) Table(newTableName string) *aConfigAlipay {
	a.aConfigAlipayDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aConfigAlipay) As(alias string) *aConfigAlipay {
	a.aConfigAlipayDo.DO = *(a.aConfigAlipayDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aConfigAlipay) updateTableName(table string) *aConfigAlipay {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.GameID = field.NewString(table, "game_id")
	a.AppID = field.NewString(table, "app_id")
	a.PrivateKey = field.NewString(table, "private_key")
	a.PublicKey = field.NewString(table, "public_key")
	a.Description = field.NewString(table, "description")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aConfigAlipay) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aConfigAlipay) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 9)
	a.fieldMap["id"] = a.ID
	a.fieldMap["game_id"] = a.GameID
	a.fieldMap["app_id"] = a.AppID
	a.fieldMap["private_key"] = a.PrivateKey
	a.fieldMap["public_key"] = a.PublicKey
	a.fieldMap["description"] = a.Description
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aConfigAlipay) clone(db *gorm.DB) aConfigAlipay {
	a.aConfigAlipayDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aConfigAlipay) replaceDB(db *gorm.DB) aConfigAlipay {
	a.aConfigAlipayDo.ReplaceDB(db)
	return a
}

type aConfigAlipayDo struct{ gen.DO }

type IAConfigAlipayDo interface {
	gen.SubQuery
	Debug() IAConfigAlipayDo
	WithContext(ctx context.Context) IAConfigAlipayDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAConfigAlipayDo
	WriteDB() IAConfigAlipayDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAConfigAlipayDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAConfigAlipayDo
	Not(conds ...gen.Condition) IAConfigAlipayDo
	Or(conds ...gen.Condition) IAConfigAlipayDo
	Select(conds ...field.Expr) IAConfigAlipayDo
	Where(conds ...gen.Condition) IAConfigAlipayDo
	Order(conds ...field.Expr) IAConfigAlipayDo
	Distinct(cols ...field.Expr) IAConfigAlipayDo
	Omit(cols ...field.Expr) IAConfigAlipayDo
	Join(table schema.Tabler, on ...field.Expr) IAConfigAlipayDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAConfigAlipayDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAConfigAlipayDo
	Group(cols ...field.Expr) IAConfigAlipayDo
	Having(conds ...gen.Condition) IAConfigAlipayDo
	Limit(limit int) IAConfigAlipayDo
	Offset(offset int) IAConfigAlipayDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAConfigAlipayDo
	Unscoped() IAConfigAlipayDo
	Create(values ...*model.AConfigAlipay) error
	CreateInBatches(values []*model.AConfigAlipay, batchSize int) error
	Save(values ...*model.AConfigAlipay) error
	First() (*model.AConfigAlipay, error)
	Take() (*model.AConfigAlipay, error)
	Last() (*model.AConfigAlipay, error)
	Find() ([]*model.AConfigAlipay, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AConfigAlipay, err error)
	FindInBatches(result *[]*model.AConfigAlipay, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AConfigAlipay) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAConfigAlipayDo
	Assign(attrs ...field.AssignExpr) IAConfigAlipayDo
	Joins(fields ...field.RelationField) IAConfigAlipayDo
	Preload(fields ...field.RelationField) IAConfigAlipayDo
	FirstOrInit() (*model.AConfigAlipay, error)
	FirstOrCreate() (*model.AConfigAlipay, error)
	FindByPage(offset int, limit int) (result []*model.AConfigAlipay, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAConfigAlipayDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aConfigAlipayDo) Debug() IAConfigAlipayDo {
	return a.withDO(a.DO.Debug())
}

func (a aConfigAlipayDo) WithContext(ctx context.Context) IAConfigAlipayDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aConfigAlipayDo) ReadDB() IAConfigAlipayDo {
	return a.Clauses(dbresolver.Read)
}

func (a aConfigAlipayDo) WriteDB() IAConfigAlipayDo {
	return a.Clauses(dbresolver.Write)
}

func (a aConfigAlipayDo) Session(config *gorm.Session) IAConfigAlipayDo {
	return a.withDO(a.DO.Session(config))
}

func (a aConfigAlipayDo) Clauses(conds ...clause.Expression) IAConfigAlipayDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aConfigAlipayDo) Returning(value interface{}, columns ...string) IAConfigAlipayDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aConfigAlipayDo) Not(conds ...gen.Condition) IAConfigAlipayDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aConfigAlipayDo) Or(conds ...gen.Condition) IAConfigAlipayDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aConfigAlipayDo) Select(conds ...field.Expr) IAConfigAlipayDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aConfigAlipayDo) Where(conds ...gen.Condition) IAConfigAlipayDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aConfigAlipayDo) Order(conds ...field.Expr) IAConfigAlipayDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aConfigAlipayDo) Distinct(cols ...field.Expr) IAConfigAlipayDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aConfigAlipayDo) Omit(cols ...field.Expr) IAConfigAlipayDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aConfigAlipayDo) Join(table schema.Tabler, on ...field.Expr) IAConfigAlipayDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aConfigAlipayDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAConfigAlipayDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aConfigAlipayDo) RightJoin(table schema.Tabler, on ...field.Expr) IAConfigAlipayDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aConfigAlipayDo) Group(cols ...field.Expr) IAConfigAlipayDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aConfigAlipayDo) Having(conds ...gen.Condition) IAConfigAlipayDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aConfigAlipayDo) Limit(limit int) IAConfigAlipayDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aConfigAlipayDo) Offset(offset int) IAConfigAlipayDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aConfigAlipayDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAConfigAlipayDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aConfigAlipayDo) Unscoped() IAConfigAlipayDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aConfigAlipayDo) Create(values ...*model.AConfigAlipay) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aConfigAlipayDo) CreateInBatches(values []*model.AConfigAlipay, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aConfigAlipayDo) Save(values ...*model.AConfigAlipay) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aConfigAlipayDo) First() (*model.AConfigAlipay, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigAlipay), nil
	}
}

func (a aConfigAlipayDo) Take() (*model.AConfigAlipay, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigAlipay), nil
	}
}

func (a aConfigAlipayDo) Last() (*model.AConfigAlipay, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigAlipay), nil
	}
}

func (a aConfigAlipayDo) Find() ([]*model.AConfigAlipay, error) {
	result, err := a.DO.Find()
	return result.([]*model.AConfigAlipay), err
}

func (a aConfigAlipayDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AConfigAlipay, err error) {
	buf := make([]*model.AConfigAlipay, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aConfigAlipayDo) FindInBatches(result *[]*model.AConfigAlipay, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aConfigAlipayDo) Attrs(attrs ...field.AssignExpr) IAConfigAlipayDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aConfigAlipayDo) Assign(attrs ...field.AssignExpr) IAConfigAlipayDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aConfigAlipayDo) Joins(fields ...field.RelationField) IAConfigAlipayDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aConfigAlipayDo) Preload(fields ...field.RelationField) IAConfigAlipayDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aConfigAlipayDo) FirstOrInit() (*model.AConfigAlipay, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigAlipay), nil
	}
}

func (a aConfigAlipayDo) FirstOrCreate() (*model.AConfigAlipay, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigAlipay), nil
	}
}

func (a aConfigAlipayDo) FindByPage(offset int, limit int) (result []*model.AConfigAlipay, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aConfigAlipayDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aConfigAlipayDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aConfigAlipayDo) Delete(models ...*model.AConfigAlipay) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aConfigAlipayDo) withDO(do gen.Dao) *aConfigAlipayDo {
	a.DO = *do.(*gen.DO)
	return a
}
