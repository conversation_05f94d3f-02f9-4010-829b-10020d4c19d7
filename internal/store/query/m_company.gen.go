// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMCompany(db *gorm.DB, opts ...gen.DOOption) mCompany {
	_mCompany := mCompany{}

	_mCompany.mCompanyDo.UseDB(db, opts...)
	_mCompany.mCompanyDo.UseModel(&model.MCompany{})

	tableName := _mCompany.mCompanyDo.TableName()
	_mCompany.ALL = field.NewAsterisk(tableName)
	_mCompany.ID = field.NewInt32(tableName, "id")
	_mCompany.GameID = field.NewString(tableName, "game_id")
	_mCompany.WechatPayPrivateKey = field.NewString(tableName, "wechat_pay_private_key")
	_mCompany.WechatPayMchID = field.NewString(tableName, "wechat_pay_mch_id")
	_mCompany.WechatPayMchNum = field.NewString(tableName, "wechat_pay_mch_num")
	_mCompany.WechatPayAPIKey = field.NewString(tableName, "wechat_pay_api_key")

	_mCompany.fillFieldMap()

	return _mCompany
}

type mCompany struct {
	mCompanyDo

	ALL                 field.Asterisk
	ID                  field.Int32
	GameID              field.String // 游戏 id
	WechatPayPrivateKey field.String // 微信私钥
	WechatPayMchID      field.String // 商户 id
	WechatPayMchNum     field.String // 商户证书序列号
	WechatPayAPIKey     field.String // api key

	fieldMap map[string]field.Expr
}

func (m mCompany) Table(newTableName string) *mCompany {
	m.mCompanyDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mCompany) As(alias string) *mCompany {
	m.mCompanyDo.DO = *(m.mCompanyDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mCompany) updateTableName(table string) *mCompany {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.WechatPayPrivateKey = field.NewString(table, "wechat_pay_private_key")
	m.WechatPayMchID = field.NewString(table, "wechat_pay_mch_id")
	m.WechatPayMchNum = field.NewString(table, "wechat_pay_mch_num")
	m.WechatPayAPIKey = field.NewString(table, "wechat_pay_api_key")

	m.fillFieldMap()

	return m
}

func (m *mCompany) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mCompany) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 6)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["wechat_pay_private_key"] = m.WechatPayPrivateKey
	m.fieldMap["wechat_pay_mch_id"] = m.WechatPayMchID
	m.fieldMap["wechat_pay_mch_num"] = m.WechatPayMchNum
	m.fieldMap["wechat_pay_api_key"] = m.WechatPayAPIKey
}

func (m mCompany) clone(db *gorm.DB) mCompany {
	m.mCompanyDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mCompany) replaceDB(db *gorm.DB) mCompany {
	m.mCompanyDo.ReplaceDB(db)
	return m
}

type mCompanyDo struct{ gen.DO }

type IMCompanyDo interface {
	gen.SubQuery
	Debug() IMCompanyDo
	WithContext(ctx context.Context) IMCompanyDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMCompanyDo
	WriteDB() IMCompanyDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMCompanyDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMCompanyDo
	Not(conds ...gen.Condition) IMCompanyDo
	Or(conds ...gen.Condition) IMCompanyDo
	Select(conds ...field.Expr) IMCompanyDo
	Where(conds ...gen.Condition) IMCompanyDo
	Order(conds ...field.Expr) IMCompanyDo
	Distinct(cols ...field.Expr) IMCompanyDo
	Omit(cols ...field.Expr) IMCompanyDo
	Join(table schema.Tabler, on ...field.Expr) IMCompanyDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMCompanyDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMCompanyDo
	Group(cols ...field.Expr) IMCompanyDo
	Having(conds ...gen.Condition) IMCompanyDo
	Limit(limit int) IMCompanyDo
	Offset(offset int) IMCompanyDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMCompanyDo
	Unscoped() IMCompanyDo
	Create(values ...*model.MCompany) error
	CreateInBatches(values []*model.MCompany, batchSize int) error
	Save(values ...*model.MCompany) error
	First() (*model.MCompany, error)
	Take() (*model.MCompany, error)
	Last() (*model.MCompany, error)
	Find() ([]*model.MCompany, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCompany, err error)
	FindInBatches(result *[]*model.MCompany, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MCompany) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMCompanyDo
	Assign(attrs ...field.AssignExpr) IMCompanyDo
	Joins(fields ...field.RelationField) IMCompanyDo
	Preload(fields ...field.RelationField) IMCompanyDo
	FirstOrInit() (*model.MCompany, error)
	FirstOrCreate() (*model.MCompany, error)
	FindByPage(offset int, limit int) (result []*model.MCompany, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMCompanyDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mCompanyDo) Debug() IMCompanyDo {
	return m.withDO(m.DO.Debug())
}

func (m mCompanyDo) WithContext(ctx context.Context) IMCompanyDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mCompanyDo) ReadDB() IMCompanyDo {
	return m.Clauses(dbresolver.Read)
}

func (m mCompanyDo) WriteDB() IMCompanyDo {
	return m.Clauses(dbresolver.Write)
}

func (m mCompanyDo) Session(config *gorm.Session) IMCompanyDo {
	return m.withDO(m.DO.Session(config))
}

func (m mCompanyDo) Clauses(conds ...clause.Expression) IMCompanyDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mCompanyDo) Returning(value interface{}, columns ...string) IMCompanyDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mCompanyDo) Not(conds ...gen.Condition) IMCompanyDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mCompanyDo) Or(conds ...gen.Condition) IMCompanyDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mCompanyDo) Select(conds ...field.Expr) IMCompanyDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mCompanyDo) Where(conds ...gen.Condition) IMCompanyDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mCompanyDo) Order(conds ...field.Expr) IMCompanyDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mCompanyDo) Distinct(cols ...field.Expr) IMCompanyDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mCompanyDo) Omit(cols ...field.Expr) IMCompanyDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mCompanyDo) Join(table schema.Tabler, on ...field.Expr) IMCompanyDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mCompanyDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMCompanyDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mCompanyDo) RightJoin(table schema.Tabler, on ...field.Expr) IMCompanyDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mCompanyDo) Group(cols ...field.Expr) IMCompanyDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mCompanyDo) Having(conds ...gen.Condition) IMCompanyDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mCompanyDo) Limit(limit int) IMCompanyDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mCompanyDo) Offset(offset int) IMCompanyDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mCompanyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMCompanyDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mCompanyDo) Unscoped() IMCompanyDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mCompanyDo) Create(values ...*model.MCompany) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mCompanyDo) CreateInBatches(values []*model.MCompany, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mCompanyDo) Save(values ...*model.MCompany) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mCompanyDo) First() (*model.MCompany, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCompany), nil
	}
}

func (m mCompanyDo) Take() (*model.MCompany, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCompany), nil
	}
}

func (m mCompanyDo) Last() (*model.MCompany, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCompany), nil
	}
}

func (m mCompanyDo) Find() ([]*model.MCompany, error) {
	result, err := m.DO.Find()
	return result.([]*model.MCompany), err
}

func (m mCompanyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCompany, err error) {
	buf := make([]*model.MCompany, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mCompanyDo) FindInBatches(result *[]*model.MCompany, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mCompanyDo) Attrs(attrs ...field.AssignExpr) IMCompanyDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mCompanyDo) Assign(attrs ...field.AssignExpr) IMCompanyDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mCompanyDo) Joins(fields ...field.RelationField) IMCompanyDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mCompanyDo) Preload(fields ...field.RelationField) IMCompanyDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mCompanyDo) FirstOrInit() (*model.MCompany, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCompany), nil
	}
}

func (m mCompanyDo) FirstOrCreate() (*model.MCompany, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCompany), nil
	}
}

func (m mCompanyDo) FindByPage(offset int, limit int) (result []*model.MCompany, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mCompanyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mCompanyDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mCompanyDo) Delete(models ...*model.MCompany) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mCompanyDo) withDO(do gen.Dao) *mCompanyDo {
	m.DO = *do.(*gen.DO)
	return m
}
