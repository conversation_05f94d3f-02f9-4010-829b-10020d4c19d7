// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMWorkorder(db *gorm.DB, opts ...gen.DOOption) mWorkorder {
	_mWorkorder := mWorkorder{}

	_mWorkorder.mWorkorderDo.UseDB(db, opts...)
	_mWorkorder.mWorkorderDo.UseModel(&model.MWorkorder{})

	tableName := _mWorkorder.mWorkorderDo.TableName()
	_mWorkorder.ALL = field.NewAsterisk(tableName)
	_mWorkorder.ID = field.NewInt32(tableName, "id")
	_mWorkorder.OrderID = field.NewString(tableName, "order_id")
	_mWorkorder.GameID = field.NewString(tableName, "game_id")
	_mWorkorder.Source = field.NewString(tableName, "source")
	_mWorkorder.UserID = field.NewString(tableName, "user_id")
	_mWorkorder.OpenID = field.NewString(tableName, "open_id")
	_mWorkorder.MiniprogramOpenID = field.NewString(tableName, "miniprogram_open_id")
	_mWorkorder.Content = field.NewString(tableName, "content")
	_mWorkorder.Priority = field.NewInt32(tableName, "priority")
	_mWorkorder.Status = field.NewInt32(tableName, "status")
	_mWorkorder.Category = field.NewString(tableName, "category")
	_mWorkorder.AcceptUserID = field.NewString(tableName, "accept_user_id")
	_mWorkorder.AcceptUsername = field.NewString(tableName, "accept_username")
	_mWorkorder.AcceptTime = field.NewInt64(tableName, "accept_time")
	_mWorkorder.CompleteUserID = field.NewString(tableName, "complete_user_id")
	_mWorkorder.CompleteUsername = field.NewString(tableName, "complete_username")
	_mWorkorder.CompleteTime = field.NewInt64(tableName, "complete_time")
	_mWorkorder.Remark = field.NewString(tableName, "remark")
	_mWorkorder.HasNewReply = field.NewBool(tableName, "has_new_reply")
	_mWorkorder.HasRead = field.NewBool(tableName, "has_read")
	_mWorkorder.LastReplyUserType = field.NewInt32(tableName, "last_reply_user_type")
	_mWorkorder.LastReplyTime = field.NewInt64(tableName, "last_reply_time")
	_mWorkorder.RoleID = field.NewString(tableName, "role_id")
	_mWorkorder.PlayerID = field.NewString(tableName, "player_id")
	_mWorkorder.PlayerName = field.NewString(tableName, "player_name")
	_mWorkorder.PlayerLevel = field.NewInt32(tableName, "player_level")
	_mWorkorder.RechargeTotalAmount = field.NewInt32(tableName, "recharge_total_amount")
	_mWorkorder.Zone = field.NewString(tableName, "zone")
	_mWorkorder.CustomData = field.NewString(tableName, "custom_data")
	_mWorkorder.SceneValue = field.NewString(tableName, "scene_value")
	_mWorkorder.DeviceBrand = field.NewString(tableName, "device_brand")
	_mWorkorder.DeviceModel = field.NewString(tableName, "device_model")
	_mWorkorder.SystemVersion = field.NewString(tableName, "system_version")
	_mWorkorder.WxVersion = field.NewString(tableName, "wx_version")
	_mWorkorder.RechargeAmount = field.NewFloat64(tableName, "recharge_amount")
	_mWorkorder.Region = field.NewString(tableName, "region")
	_mWorkorder.IssueAt = field.NewInt64(tableName, "issue_at")
	_mWorkorder.CreatedAt = field.NewInt64(tableName, "created_at")
	_mWorkorder.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mWorkorder.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mWorkorder.fillFieldMap()

	return _mWorkorder
}

// mWorkorder 工单表
type mWorkorder struct {
	mWorkorderDo

	ALL                 field.Asterisk
	ID                  field.Int32
	OrderID             field.String  // 工单ID
	GameID              field.String  // 游戏ID
	Source              field.String  // 工单来源: wechat-微信, douyin-抖音
	UserID              field.String  // 用户ID
	OpenID              field.String  // OpenID
	MiniprogramOpenID   field.String  // 小程序open_id
	Content             field.String  // 工单内容/问题描述
	Priority            field.Int32   // 优先级: 1-一般, 2-高, 3-紧急
	Status              field.Int32   // 状态: 1-待接单, 2-受理中, 3-已完结
	Category            field.String  // 工单分类
	AcceptUserID        field.String  // 受理人ID
	AcceptUsername      field.String  // 受理人用户名
	AcceptTime          field.Int64   // 受理时间
	CompleteUserID      field.String  // 完结人ID
	CompleteUsername    field.String  // 完结人用户名
	CompleteTime        field.Int64   // 完结时间
	Remark              field.String  // 备注
	HasNewReply         field.Bool    // 是否有新回复: 0-否, 1-是
	HasRead             field.Bool    // 是否已读: 0-否, 1-是
	LastReplyUserType   field.Int32   // 最后回复用户类型: 0-无, 1-用户, 2-客服
	LastReplyTime       field.Int64   // 最后回复时间
	RoleID              field.String  // 角色ID
	PlayerID            field.String  // 玩家ID
	PlayerName          field.String  // 玩家名称
	PlayerLevel         field.Int32   // 玩家等级
	RechargeTotalAmount field.Int32   // 充值总额
	Zone                field.String  // 区服
	CustomData          field.String  // 自定义数据
	SceneValue          field.String  // 场景值（抖音）
	DeviceBrand         field.String  // 设备品牌
	DeviceModel         field.String  // 设备型号
	SystemVersion       field.String  // 系统版本
	WxVersion           field.String  // 微信版本
	RechargeAmount      field.Float64 // 充值金额
	Region              field.String  // 地区
	IssueAt             field.Int64   // 问题发生时间
	CreatedAt           field.Int64
	UpdatedAt           field.Int64
	IsDeleted           field.Bool

	fieldMap map[string]field.Expr
}

func (m mWorkorder) Table(newTableName string) *mWorkorder {
	m.mWorkorderDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mWorkorder) As(alias string) *mWorkorder {
	m.mWorkorderDo.DO = *(m.mWorkorderDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mWorkorder) updateTableName(table string) *mWorkorder {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.OrderID = field.NewString(table, "order_id")
	m.GameID = field.NewString(table, "game_id")
	m.Source = field.NewString(table, "source")
	m.UserID = field.NewString(table, "user_id")
	m.OpenID = field.NewString(table, "open_id")
	m.MiniprogramOpenID = field.NewString(table, "miniprogram_open_id")
	m.Content = field.NewString(table, "content")
	m.Priority = field.NewInt32(table, "priority")
	m.Status = field.NewInt32(table, "status")
	m.Category = field.NewString(table, "category")
	m.AcceptUserID = field.NewString(table, "accept_user_id")
	m.AcceptUsername = field.NewString(table, "accept_username")
	m.AcceptTime = field.NewInt64(table, "accept_time")
	m.CompleteUserID = field.NewString(table, "complete_user_id")
	m.CompleteUsername = field.NewString(table, "complete_username")
	m.CompleteTime = field.NewInt64(table, "complete_time")
	m.Remark = field.NewString(table, "remark")
	m.HasNewReply = field.NewBool(table, "has_new_reply")
	m.HasRead = field.NewBool(table, "has_read")
	m.LastReplyUserType = field.NewInt32(table, "last_reply_user_type")
	m.LastReplyTime = field.NewInt64(table, "last_reply_time")
	m.RoleID = field.NewString(table, "role_id")
	m.PlayerID = field.NewString(table, "player_id")
	m.PlayerName = field.NewString(table, "player_name")
	m.PlayerLevel = field.NewInt32(table, "player_level")
	m.RechargeTotalAmount = field.NewInt32(table, "recharge_total_amount")
	m.Zone = field.NewString(table, "zone")
	m.CustomData = field.NewString(table, "custom_data")
	m.SceneValue = field.NewString(table, "scene_value")
	m.DeviceBrand = field.NewString(table, "device_brand")
	m.DeviceModel = field.NewString(table, "device_model")
	m.SystemVersion = field.NewString(table, "system_version")
	m.WxVersion = field.NewString(table, "wx_version")
	m.RechargeAmount = field.NewFloat64(table, "recharge_amount")
	m.Region = field.NewString(table, "region")
	m.IssueAt = field.NewInt64(table, "issue_at")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mWorkorder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mWorkorder) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 40)
	m.fieldMap["id"] = m.ID
	m.fieldMap["order_id"] = m.OrderID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["source"] = m.Source
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["open_id"] = m.OpenID
	m.fieldMap["miniprogram_open_id"] = m.MiniprogramOpenID
	m.fieldMap["content"] = m.Content
	m.fieldMap["priority"] = m.Priority
	m.fieldMap["status"] = m.Status
	m.fieldMap["category"] = m.Category
	m.fieldMap["accept_user_id"] = m.AcceptUserID
	m.fieldMap["accept_username"] = m.AcceptUsername
	m.fieldMap["accept_time"] = m.AcceptTime
	m.fieldMap["complete_user_id"] = m.CompleteUserID
	m.fieldMap["complete_username"] = m.CompleteUsername
	m.fieldMap["complete_time"] = m.CompleteTime
	m.fieldMap["remark"] = m.Remark
	m.fieldMap["has_new_reply"] = m.HasNewReply
	m.fieldMap["has_read"] = m.HasRead
	m.fieldMap["last_reply_user_type"] = m.LastReplyUserType
	m.fieldMap["last_reply_time"] = m.LastReplyTime
	m.fieldMap["role_id"] = m.RoleID
	m.fieldMap["player_id"] = m.PlayerID
	m.fieldMap["player_name"] = m.PlayerName
	m.fieldMap["player_level"] = m.PlayerLevel
	m.fieldMap["recharge_total_amount"] = m.RechargeTotalAmount
	m.fieldMap["zone"] = m.Zone
	m.fieldMap["custom_data"] = m.CustomData
	m.fieldMap["scene_value"] = m.SceneValue
	m.fieldMap["device_brand"] = m.DeviceBrand
	m.fieldMap["device_model"] = m.DeviceModel
	m.fieldMap["system_version"] = m.SystemVersion
	m.fieldMap["wx_version"] = m.WxVersion
	m.fieldMap["recharge_amount"] = m.RechargeAmount
	m.fieldMap["region"] = m.Region
	m.fieldMap["issue_at"] = m.IssueAt
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mWorkorder) clone(db *gorm.DB) mWorkorder {
	m.mWorkorderDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mWorkorder) replaceDB(db *gorm.DB) mWorkorder {
	m.mWorkorderDo.ReplaceDB(db)
	return m
}

type mWorkorderDo struct{ gen.DO }

type IMWorkorderDo interface {
	gen.SubQuery
	Debug() IMWorkorderDo
	WithContext(ctx context.Context) IMWorkorderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMWorkorderDo
	WriteDB() IMWorkorderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMWorkorderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMWorkorderDo
	Not(conds ...gen.Condition) IMWorkorderDo
	Or(conds ...gen.Condition) IMWorkorderDo
	Select(conds ...field.Expr) IMWorkorderDo
	Where(conds ...gen.Condition) IMWorkorderDo
	Order(conds ...field.Expr) IMWorkorderDo
	Distinct(cols ...field.Expr) IMWorkorderDo
	Omit(cols ...field.Expr) IMWorkorderDo
	Join(table schema.Tabler, on ...field.Expr) IMWorkorderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderDo
	Group(cols ...field.Expr) IMWorkorderDo
	Having(conds ...gen.Condition) IMWorkorderDo
	Limit(limit int) IMWorkorderDo
	Offset(offset int) IMWorkorderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderDo
	Unscoped() IMWorkorderDo
	Create(values ...*model.MWorkorder) error
	CreateInBatches(values []*model.MWorkorder, batchSize int) error
	Save(values ...*model.MWorkorder) error
	First() (*model.MWorkorder, error)
	Take() (*model.MWorkorder, error)
	Last() (*model.MWorkorder, error)
	Find() ([]*model.MWorkorder, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorder, err error)
	FindInBatches(result *[]*model.MWorkorder, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MWorkorder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMWorkorderDo
	Assign(attrs ...field.AssignExpr) IMWorkorderDo
	Joins(fields ...field.RelationField) IMWorkorderDo
	Preload(fields ...field.RelationField) IMWorkorderDo
	FirstOrInit() (*model.MWorkorder, error)
	FirstOrCreate() (*model.MWorkorder, error)
	FindByPage(offset int, limit int) (result []*model.MWorkorder, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMWorkorderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mWorkorderDo) Debug() IMWorkorderDo {
	return m.withDO(m.DO.Debug())
}

func (m mWorkorderDo) WithContext(ctx context.Context) IMWorkorderDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mWorkorderDo) ReadDB() IMWorkorderDo {
	return m.Clauses(dbresolver.Read)
}

func (m mWorkorderDo) WriteDB() IMWorkorderDo {
	return m.Clauses(dbresolver.Write)
}

func (m mWorkorderDo) Session(config *gorm.Session) IMWorkorderDo {
	return m.withDO(m.DO.Session(config))
}

func (m mWorkorderDo) Clauses(conds ...clause.Expression) IMWorkorderDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mWorkorderDo) Returning(value interface{}, columns ...string) IMWorkorderDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mWorkorderDo) Not(conds ...gen.Condition) IMWorkorderDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mWorkorderDo) Or(conds ...gen.Condition) IMWorkorderDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mWorkorderDo) Select(conds ...field.Expr) IMWorkorderDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mWorkorderDo) Where(conds ...gen.Condition) IMWorkorderDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mWorkorderDo) Order(conds ...field.Expr) IMWorkorderDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mWorkorderDo) Distinct(cols ...field.Expr) IMWorkorderDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mWorkorderDo) Omit(cols ...field.Expr) IMWorkorderDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mWorkorderDo) Join(table schema.Tabler, on ...field.Expr) IMWorkorderDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mWorkorderDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mWorkorderDo) RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mWorkorderDo) Group(cols ...field.Expr) IMWorkorderDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mWorkorderDo) Having(conds ...gen.Condition) IMWorkorderDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mWorkorderDo) Limit(limit int) IMWorkorderDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mWorkorderDo) Offset(offset int) IMWorkorderDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mWorkorderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mWorkorderDo) Unscoped() IMWorkorderDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mWorkorderDo) Create(values ...*model.MWorkorder) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mWorkorderDo) CreateInBatches(values []*model.MWorkorder, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mWorkorderDo) Save(values ...*model.MWorkorder) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mWorkorderDo) First() (*model.MWorkorder, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorder), nil
	}
}

func (m mWorkorderDo) Take() (*model.MWorkorder, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorder), nil
	}
}

func (m mWorkorderDo) Last() (*model.MWorkorder, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorder), nil
	}
}

func (m mWorkorderDo) Find() ([]*model.MWorkorder, error) {
	result, err := m.DO.Find()
	return result.([]*model.MWorkorder), err
}

func (m mWorkorderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorder, err error) {
	buf := make([]*model.MWorkorder, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mWorkorderDo) FindInBatches(result *[]*model.MWorkorder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mWorkorderDo) Attrs(attrs ...field.AssignExpr) IMWorkorderDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mWorkorderDo) Assign(attrs ...field.AssignExpr) IMWorkorderDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mWorkorderDo) Joins(fields ...field.RelationField) IMWorkorderDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mWorkorderDo) Preload(fields ...field.RelationField) IMWorkorderDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mWorkorderDo) FirstOrInit() (*model.MWorkorder, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorder), nil
	}
}

func (m mWorkorderDo) FirstOrCreate() (*model.MWorkorder, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorder), nil
	}
}

func (m mWorkorderDo) FindByPage(offset int, limit int) (result []*model.MWorkorder, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mWorkorderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mWorkorderDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mWorkorderDo) Delete(models ...*model.MWorkorder) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mWorkorderDo) withDO(do gen.Dao) *mWorkorderDo {
	m.DO = *do.(*gen.DO)
	return m
}
