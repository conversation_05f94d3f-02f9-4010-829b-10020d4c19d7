// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newH5AdminConfig(db *gorm.DB, opts ...gen.DOOption) h5AdminConfig {
	_h5AdminConfig := h5AdminConfig{}

	_h5AdminConfig.h5AdminConfigDo.UseDB(db, opts...)
	_h5AdminConfig.h5AdminConfigDo.UseModel(&model.H5AdminConfig{})

	tableName := _h5AdminConfig.h5AdminConfigDo.TableName()
	_h5AdminConfig.ALL = field.NewAsterisk(tableName)
	_h5AdminConfig.ID = field.NewInt32(tableName, "id")
	_h5AdminConfig.IsSpecifyTime = field.NewBool(tableName, "is_specify_time")
	_h5AdminConfig.TimestampDiff = field.NewInt64(tableName, "timestamp_diff")
	_h5AdminConfig.CreatedAt = field.NewInt64(tableName, "created_at")
	_h5AdminConfig.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_h5AdminConfig.fillFieldMap()

	return _h5AdminConfig
}

// h5AdminConfig H5打包管理后台配置
type h5AdminConfig struct {
	h5AdminConfigDo

	ALL           field.Asterisk
	ID            field.Int32 // 主键ID
	IsSpecifyTime field.Bool  // 指定时间
	TimestampDiff field.Int64
	CreatedAt     field.Int64 // 创建时间
	UpdatedAt     field.Int64 // 更新时间

	fieldMap map[string]field.Expr
}

func (h h5AdminConfig) Table(newTableName string) *h5AdminConfig {
	h.h5AdminConfigDo.UseTable(newTableName)
	return h.updateTableName(newTableName)
}

func (h h5AdminConfig) As(alias string) *h5AdminConfig {
	h.h5AdminConfigDo.DO = *(h.h5AdminConfigDo.As(alias).(*gen.DO))
	return h.updateTableName(alias)
}

func (h *h5AdminConfig) updateTableName(table string) *h5AdminConfig {
	h.ALL = field.NewAsterisk(table)
	h.ID = field.NewInt32(table, "id")
	h.IsSpecifyTime = field.NewBool(table, "is_specify_time")
	h.TimestampDiff = field.NewInt64(table, "timestamp_diff")
	h.CreatedAt = field.NewInt64(table, "created_at")
	h.UpdatedAt = field.NewInt64(table, "updated_at")

	h.fillFieldMap()

	return h
}

func (h *h5AdminConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := h.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (h *h5AdminConfig) fillFieldMap() {
	h.fieldMap = make(map[string]field.Expr, 5)
	h.fieldMap["id"] = h.ID
	h.fieldMap["is_specify_time"] = h.IsSpecifyTime
	h.fieldMap["timestamp_diff"] = h.TimestampDiff
	h.fieldMap["created_at"] = h.CreatedAt
	h.fieldMap["updated_at"] = h.UpdatedAt
}

func (h h5AdminConfig) clone(db *gorm.DB) h5AdminConfig {
	h.h5AdminConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return h
}

func (h h5AdminConfig) replaceDB(db *gorm.DB) h5AdminConfig {
	h.h5AdminConfigDo.ReplaceDB(db)
	return h
}

type h5AdminConfigDo struct{ gen.DO }

type IH5AdminConfigDo interface {
	gen.SubQuery
	Debug() IH5AdminConfigDo
	WithContext(ctx context.Context) IH5AdminConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IH5AdminConfigDo
	WriteDB() IH5AdminConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IH5AdminConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IH5AdminConfigDo
	Not(conds ...gen.Condition) IH5AdminConfigDo
	Or(conds ...gen.Condition) IH5AdminConfigDo
	Select(conds ...field.Expr) IH5AdminConfigDo
	Where(conds ...gen.Condition) IH5AdminConfigDo
	Order(conds ...field.Expr) IH5AdminConfigDo
	Distinct(cols ...field.Expr) IH5AdminConfigDo
	Omit(cols ...field.Expr) IH5AdminConfigDo
	Join(table schema.Tabler, on ...field.Expr) IH5AdminConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IH5AdminConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) IH5AdminConfigDo
	Group(cols ...field.Expr) IH5AdminConfigDo
	Having(conds ...gen.Condition) IH5AdminConfigDo
	Limit(limit int) IH5AdminConfigDo
	Offset(offset int) IH5AdminConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IH5AdminConfigDo
	Unscoped() IH5AdminConfigDo
	Create(values ...*model.H5AdminConfig) error
	CreateInBatches(values []*model.H5AdminConfig, batchSize int) error
	Save(values ...*model.H5AdminConfig) error
	First() (*model.H5AdminConfig, error)
	Take() (*model.H5AdminConfig, error)
	Last() (*model.H5AdminConfig, error)
	Find() ([]*model.H5AdminConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.H5AdminConfig, err error)
	FindInBatches(result *[]*model.H5AdminConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.H5AdminConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IH5AdminConfigDo
	Assign(attrs ...field.AssignExpr) IH5AdminConfigDo
	Joins(fields ...field.RelationField) IH5AdminConfigDo
	Preload(fields ...field.RelationField) IH5AdminConfigDo
	FirstOrInit() (*model.H5AdminConfig, error)
	FirstOrCreate() (*model.H5AdminConfig, error)
	FindByPage(offset int, limit int) (result []*model.H5AdminConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IH5AdminConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (h h5AdminConfigDo) Debug() IH5AdminConfigDo {
	return h.withDO(h.DO.Debug())
}

func (h h5AdminConfigDo) WithContext(ctx context.Context) IH5AdminConfigDo {
	return h.withDO(h.DO.WithContext(ctx))
}

func (h h5AdminConfigDo) ReadDB() IH5AdminConfigDo {
	return h.Clauses(dbresolver.Read)
}

func (h h5AdminConfigDo) WriteDB() IH5AdminConfigDo {
	return h.Clauses(dbresolver.Write)
}

func (h h5AdminConfigDo) Session(config *gorm.Session) IH5AdminConfigDo {
	return h.withDO(h.DO.Session(config))
}

func (h h5AdminConfigDo) Clauses(conds ...clause.Expression) IH5AdminConfigDo {
	return h.withDO(h.DO.Clauses(conds...))
}

func (h h5AdminConfigDo) Returning(value interface{}, columns ...string) IH5AdminConfigDo {
	return h.withDO(h.DO.Returning(value, columns...))
}

func (h h5AdminConfigDo) Not(conds ...gen.Condition) IH5AdminConfigDo {
	return h.withDO(h.DO.Not(conds...))
}

func (h h5AdminConfigDo) Or(conds ...gen.Condition) IH5AdminConfigDo {
	return h.withDO(h.DO.Or(conds...))
}

func (h h5AdminConfigDo) Select(conds ...field.Expr) IH5AdminConfigDo {
	return h.withDO(h.DO.Select(conds...))
}

func (h h5AdminConfigDo) Where(conds ...gen.Condition) IH5AdminConfigDo {
	return h.withDO(h.DO.Where(conds...))
}

func (h h5AdminConfigDo) Order(conds ...field.Expr) IH5AdminConfigDo {
	return h.withDO(h.DO.Order(conds...))
}

func (h h5AdminConfigDo) Distinct(cols ...field.Expr) IH5AdminConfigDo {
	return h.withDO(h.DO.Distinct(cols...))
}

func (h h5AdminConfigDo) Omit(cols ...field.Expr) IH5AdminConfigDo {
	return h.withDO(h.DO.Omit(cols...))
}

func (h h5AdminConfigDo) Join(table schema.Tabler, on ...field.Expr) IH5AdminConfigDo {
	return h.withDO(h.DO.Join(table, on...))
}

func (h h5AdminConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) IH5AdminConfigDo {
	return h.withDO(h.DO.LeftJoin(table, on...))
}

func (h h5AdminConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) IH5AdminConfigDo {
	return h.withDO(h.DO.RightJoin(table, on...))
}

func (h h5AdminConfigDo) Group(cols ...field.Expr) IH5AdminConfigDo {
	return h.withDO(h.DO.Group(cols...))
}

func (h h5AdminConfigDo) Having(conds ...gen.Condition) IH5AdminConfigDo {
	return h.withDO(h.DO.Having(conds...))
}

func (h h5AdminConfigDo) Limit(limit int) IH5AdminConfigDo {
	return h.withDO(h.DO.Limit(limit))
}

func (h h5AdminConfigDo) Offset(offset int) IH5AdminConfigDo {
	return h.withDO(h.DO.Offset(offset))
}

func (h h5AdminConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IH5AdminConfigDo {
	return h.withDO(h.DO.Scopes(funcs...))
}

func (h h5AdminConfigDo) Unscoped() IH5AdminConfigDo {
	return h.withDO(h.DO.Unscoped())
}

func (h h5AdminConfigDo) Create(values ...*model.H5AdminConfig) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Create(values)
}

func (h h5AdminConfigDo) CreateInBatches(values []*model.H5AdminConfig, batchSize int) error {
	return h.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (h h5AdminConfigDo) Save(values ...*model.H5AdminConfig) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Save(values)
}

func (h h5AdminConfigDo) First() (*model.H5AdminConfig, error) {
	if result, err := h.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminConfig), nil
	}
}

func (h h5AdminConfigDo) Take() (*model.H5AdminConfig, error) {
	if result, err := h.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminConfig), nil
	}
}

func (h h5AdminConfigDo) Last() (*model.H5AdminConfig, error) {
	if result, err := h.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminConfig), nil
	}
}

func (h h5AdminConfigDo) Find() ([]*model.H5AdminConfig, error) {
	result, err := h.DO.Find()
	return result.([]*model.H5AdminConfig), err
}

func (h h5AdminConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.H5AdminConfig, err error) {
	buf := make([]*model.H5AdminConfig, 0, batchSize)
	err = h.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (h h5AdminConfigDo) FindInBatches(result *[]*model.H5AdminConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return h.DO.FindInBatches(result, batchSize, fc)
}

func (h h5AdminConfigDo) Attrs(attrs ...field.AssignExpr) IH5AdminConfigDo {
	return h.withDO(h.DO.Attrs(attrs...))
}

func (h h5AdminConfigDo) Assign(attrs ...field.AssignExpr) IH5AdminConfigDo {
	return h.withDO(h.DO.Assign(attrs...))
}

func (h h5AdminConfigDo) Joins(fields ...field.RelationField) IH5AdminConfigDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Joins(_f))
	}
	return &h
}

func (h h5AdminConfigDo) Preload(fields ...field.RelationField) IH5AdminConfigDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Preload(_f))
	}
	return &h
}

func (h h5AdminConfigDo) FirstOrInit() (*model.H5AdminConfig, error) {
	if result, err := h.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminConfig), nil
	}
}

func (h h5AdminConfigDo) FirstOrCreate() (*model.H5AdminConfig, error) {
	if result, err := h.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminConfig), nil
	}
}

func (h h5AdminConfigDo) FindByPage(offset int, limit int) (result []*model.H5AdminConfig, count int64, err error) {
	result, err = h.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = h.Offset(-1).Limit(-1).Count()
	return
}

func (h h5AdminConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = h.Count()
	if err != nil {
		return
	}

	err = h.Offset(offset).Limit(limit).Scan(result)
	return
}

func (h h5AdminConfigDo) Scan(result interface{}) (err error) {
	return h.DO.Scan(result)
}

func (h h5AdminConfigDo) Delete(models ...*model.H5AdminConfig) (result gen.ResultInfo, err error) {
	return h.DO.Delete(models)
}

func (h *h5AdminConfigDo) withDO(do gen.Dao) *h5AdminConfigDo {
	h.DO = *do.(*gen.DO)
	return h
}
