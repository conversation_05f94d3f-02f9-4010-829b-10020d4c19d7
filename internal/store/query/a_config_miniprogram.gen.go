// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newAConfigMiniprogram(db *gorm.DB, opts ...gen.DOOption) aConfigMiniprogram {
	_aConfigMiniprogram := aConfigMiniprogram{}

	_aConfigMiniprogram.aConfigMiniprogramDo.UseDB(db, opts...)
	_aConfigMiniprogram.aConfigMiniprogramDo.UseModel(&model.AConfigMiniprogram{})

	tableName := _aConfigMiniprogram.aConfigMiniprogramDo.TableName()
	_aConfigMiniprogram.ALL = field.NewAsterisk(tableName)
	_aConfigMiniprogram.ID = field.NewInt32(tableName, "id")
	_aConfigMiniprogram.AppID = field.NewString(tableName, "app_id")
	_aConfigMiniprogram.AppSecret = field.NewString(tableName, "app_secret")
	_aConfigMiniprogram.AccessToken = field.NewString(tableName, "access_token")
	_aConfigMiniprogram.ExpiresIn = field.NewInt32(tableName, "expires_in")
	_aConfigMiniprogram.IsUpdatedSys = field.NewInt32(tableName, "is_updated_sys")
	_aConfigMiniprogram.URLLink = field.NewString(tableName, "url_link")
	_aConfigMiniprogram.URLLinkCreatedAt = field.NewInt64(tableName, "url_link_created_at")
	_aConfigMiniprogram.CreatedAt = field.NewInt64(tableName, "created_at")
	_aConfigMiniprogram.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_aConfigMiniprogram.fillFieldMap()

	return _aConfigMiniprogram
}

type aConfigMiniprogram struct {
	aConfigMiniprogramDo

	ALL              field.Asterisk
	ID               field.Int32
	AppID            field.String
	AppSecret        field.String
	AccessToken      field.String
	ExpiresIn        field.Int32 // 有效期（秒）
	IsUpdatedSys     field.Int32
	URLLink          field.String
	URLLinkCreatedAt field.Int64
	CreatedAt        field.Int64
	UpdatedAt        field.Int64

	fieldMap map[string]field.Expr
}

func (a aConfigMiniprogram) Table(newTableName string) *aConfigMiniprogram {
	a.aConfigMiniprogramDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aConfigMiniprogram) As(alias string) *aConfigMiniprogram {
	a.aConfigMiniprogramDo.DO = *(a.aConfigMiniprogramDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aConfigMiniprogram) updateTableName(table string) *aConfigMiniprogram {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.AppID = field.NewString(table, "app_id")
	a.AppSecret = field.NewString(table, "app_secret")
	a.AccessToken = field.NewString(table, "access_token")
	a.ExpiresIn = field.NewInt32(table, "expires_in")
	a.IsUpdatedSys = field.NewInt32(table, "is_updated_sys")
	a.URLLink = field.NewString(table, "url_link")
	a.URLLinkCreatedAt = field.NewInt64(table, "url_link_created_at")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")

	a.fillFieldMap()

	return a
}

func (a *aConfigMiniprogram) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aConfigMiniprogram) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 10)
	a.fieldMap["id"] = a.ID
	a.fieldMap["app_id"] = a.AppID
	a.fieldMap["app_secret"] = a.AppSecret
	a.fieldMap["access_token"] = a.AccessToken
	a.fieldMap["expires_in"] = a.ExpiresIn
	a.fieldMap["is_updated_sys"] = a.IsUpdatedSys
	a.fieldMap["url_link"] = a.URLLink
	a.fieldMap["url_link_created_at"] = a.URLLinkCreatedAt
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
}

func (a aConfigMiniprogram) clone(db *gorm.DB) aConfigMiniprogram {
	a.aConfigMiniprogramDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aConfigMiniprogram) replaceDB(db *gorm.DB) aConfigMiniprogram {
	a.aConfigMiniprogramDo.ReplaceDB(db)
	return a
}

type aConfigMiniprogramDo struct{ gen.DO }

type IAConfigMiniprogramDo interface {
	gen.SubQuery
	Debug() IAConfigMiniprogramDo
	WithContext(ctx context.Context) IAConfigMiniprogramDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAConfigMiniprogramDo
	WriteDB() IAConfigMiniprogramDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAConfigMiniprogramDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAConfigMiniprogramDo
	Not(conds ...gen.Condition) IAConfigMiniprogramDo
	Or(conds ...gen.Condition) IAConfigMiniprogramDo
	Select(conds ...field.Expr) IAConfigMiniprogramDo
	Where(conds ...gen.Condition) IAConfigMiniprogramDo
	Order(conds ...field.Expr) IAConfigMiniprogramDo
	Distinct(cols ...field.Expr) IAConfigMiniprogramDo
	Omit(cols ...field.Expr) IAConfigMiniprogramDo
	Join(table schema.Tabler, on ...field.Expr) IAConfigMiniprogramDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAConfigMiniprogramDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAConfigMiniprogramDo
	Group(cols ...field.Expr) IAConfigMiniprogramDo
	Having(conds ...gen.Condition) IAConfigMiniprogramDo
	Limit(limit int) IAConfigMiniprogramDo
	Offset(offset int) IAConfigMiniprogramDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAConfigMiniprogramDo
	Unscoped() IAConfigMiniprogramDo
	Create(values ...*model.AConfigMiniprogram) error
	CreateInBatches(values []*model.AConfigMiniprogram, batchSize int) error
	Save(values ...*model.AConfigMiniprogram) error
	First() (*model.AConfigMiniprogram, error)
	Take() (*model.AConfigMiniprogram, error)
	Last() (*model.AConfigMiniprogram, error)
	Find() ([]*model.AConfigMiniprogram, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AConfigMiniprogram, err error)
	FindInBatches(result *[]*model.AConfigMiniprogram, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AConfigMiniprogram) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAConfigMiniprogramDo
	Assign(attrs ...field.AssignExpr) IAConfigMiniprogramDo
	Joins(fields ...field.RelationField) IAConfigMiniprogramDo
	Preload(fields ...field.RelationField) IAConfigMiniprogramDo
	FirstOrInit() (*model.AConfigMiniprogram, error)
	FirstOrCreate() (*model.AConfigMiniprogram, error)
	FindByPage(offset int, limit int) (result []*model.AConfigMiniprogram, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAConfigMiniprogramDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aConfigMiniprogramDo) Debug() IAConfigMiniprogramDo {
	return a.withDO(a.DO.Debug())
}

func (a aConfigMiniprogramDo) WithContext(ctx context.Context) IAConfigMiniprogramDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aConfigMiniprogramDo) ReadDB() IAConfigMiniprogramDo {
	return a.Clauses(dbresolver.Read)
}

func (a aConfigMiniprogramDo) WriteDB() IAConfigMiniprogramDo {
	return a.Clauses(dbresolver.Write)
}

func (a aConfigMiniprogramDo) Session(config *gorm.Session) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Session(config))
}

func (a aConfigMiniprogramDo) Clauses(conds ...clause.Expression) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aConfigMiniprogramDo) Returning(value interface{}, columns ...string) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aConfigMiniprogramDo) Not(conds ...gen.Condition) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aConfigMiniprogramDo) Or(conds ...gen.Condition) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aConfigMiniprogramDo) Select(conds ...field.Expr) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aConfigMiniprogramDo) Where(conds ...gen.Condition) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aConfigMiniprogramDo) Order(conds ...field.Expr) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aConfigMiniprogramDo) Distinct(cols ...field.Expr) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aConfigMiniprogramDo) Omit(cols ...field.Expr) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aConfigMiniprogramDo) Join(table schema.Tabler, on ...field.Expr) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aConfigMiniprogramDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAConfigMiniprogramDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aConfigMiniprogramDo) RightJoin(table schema.Tabler, on ...field.Expr) IAConfigMiniprogramDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aConfigMiniprogramDo) Group(cols ...field.Expr) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aConfigMiniprogramDo) Having(conds ...gen.Condition) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aConfigMiniprogramDo) Limit(limit int) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aConfigMiniprogramDo) Offset(offset int) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aConfigMiniprogramDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aConfigMiniprogramDo) Unscoped() IAConfigMiniprogramDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aConfigMiniprogramDo) Create(values ...*model.AConfigMiniprogram) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aConfigMiniprogramDo) CreateInBatches(values []*model.AConfigMiniprogram, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aConfigMiniprogramDo) Save(values ...*model.AConfigMiniprogram) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aConfigMiniprogramDo) First() (*model.AConfigMiniprogram, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigMiniprogram), nil
	}
}

func (a aConfigMiniprogramDo) Take() (*model.AConfigMiniprogram, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigMiniprogram), nil
	}
}

func (a aConfigMiniprogramDo) Last() (*model.AConfigMiniprogram, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigMiniprogram), nil
	}
}

func (a aConfigMiniprogramDo) Find() ([]*model.AConfigMiniprogram, error) {
	result, err := a.DO.Find()
	return result.([]*model.AConfigMiniprogram), err
}

func (a aConfigMiniprogramDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AConfigMiniprogram, err error) {
	buf := make([]*model.AConfigMiniprogram, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aConfigMiniprogramDo) FindInBatches(result *[]*model.AConfigMiniprogram, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aConfigMiniprogramDo) Attrs(attrs ...field.AssignExpr) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aConfigMiniprogramDo) Assign(attrs ...field.AssignExpr) IAConfigMiniprogramDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aConfigMiniprogramDo) Joins(fields ...field.RelationField) IAConfigMiniprogramDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aConfigMiniprogramDo) Preload(fields ...field.RelationField) IAConfigMiniprogramDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aConfigMiniprogramDo) FirstOrInit() (*model.AConfigMiniprogram, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigMiniprogram), nil
	}
}

func (a aConfigMiniprogramDo) FirstOrCreate() (*model.AConfigMiniprogram, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigMiniprogram), nil
	}
}

func (a aConfigMiniprogramDo) FindByPage(offset int, limit int) (result []*model.AConfigMiniprogram, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aConfigMiniprogramDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aConfigMiniprogramDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aConfigMiniprogramDo) Delete(models ...*model.AConfigMiniprogram) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aConfigMiniprogramDo) withDO(do gen.Dao) *aConfigMiniprogramDo {
	a.DO = *do.(*gen.DO)
	return a
}
