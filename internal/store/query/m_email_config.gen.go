// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMEmailConfig(db *gorm.DB, opts ...gen.DOOption) mEmailConfig {
	_mEmailConfig := mEmailConfig{}

	_mEmailConfig.mEmailConfigDo.UseDB(db, opts...)
	_mEmailConfig.mEmailConfigDo.UseModel(&model.MEmailConfig{})

	tableName := _mEmailConfig.mEmailConfigDo.TableName()
	_mEmailConfig.ALL = field.NewAsterisk(tableName)
	_mEmailConfig.ID = field.NewInt32(tableName, "id")
	_mEmailConfig.ConfigType = field.NewString(tableName, "config_type")
	_mEmailConfig.EmailAddress = field.NewString(tableName, "email_address")
	_mEmailConfig.EmailName = field.NewString(tableName, "email_name")
	_mEmailConfig.CreatorID = field.NewString(tableName, "creator_id")
	_mEmailConfig.CreatedAt = field.NewInt64(tableName, "created_at")
	_mEmailConfig.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mEmailConfig.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mEmailConfig.fillFieldMap()

	return _mEmailConfig
}

// mEmailConfig 邮箱配置表
type mEmailConfig struct {
	mEmailConfigDo

	ALL          field.Asterisk
	ID           field.Int32  // 主键
	ConfigType   field.String // 配置类型,如workorder_stats-工单统计
	EmailAddress field.String // 邮箱地址
	EmailName    field.String // 邮箱备注名称
	CreatorID    field.String // 创建人ID
	CreatedAt    field.Int64  // 创建时间
	UpdatedAt    field.Int64  // 更新时间
	IsDeleted    field.Bool   // 是否删除

	fieldMap map[string]field.Expr
}

func (m mEmailConfig) Table(newTableName string) *mEmailConfig {
	m.mEmailConfigDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mEmailConfig) As(alias string) *mEmailConfig {
	m.mEmailConfigDo.DO = *(m.mEmailConfigDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mEmailConfig) updateTableName(table string) *mEmailConfig {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.ConfigType = field.NewString(table, "config_type")
	m.EmailAddress = field.NewString(table, "email_address")
	m.EmailName = field.NewString(table, "email_name")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mEmailConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mEmailConfig) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 8)
	m.fieldMap["id"] = m.ID
	m.fieldMap["config_type"] = m.ConfigType
	m.fieldMap["email_address"] = m.EmailAddress
	m.fieldMap["email_name"] = m.EmailName
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mEmailConfig) clone(db *gorm.DB) mEmailConfig {
	m.mEmailConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mEmailConfig) replaceDB(db *gorm.DB) mEmailConfig {
	m.mEmailConfigDo.ReplaceDB(db)
	return m
}

type mEmailConfigDo struct{ gen.DO }

type IMEmailConfigDo interface {
	gen.SubQuery
	Debug() IMEmailConfigDo
	WithContext(ctx context.Context) IMEmailConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMEmailConfigDo
	WriteDB() IMEmailConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMEmailConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMEmailConfigDo
	Not(conds ...gen.Condition) IMEmailConfigDo
	Or(conds ...gen.Condition) IMEmailConfigDo
	Select(conds ...field.Expr) IMEmailConfigDo
	Where(conds ...gen.Condition) IMEmailConfigDo
	Order(conds ...field.Expr) IMEmailConfigDo
	Distinct(cols ...field.Expr) IMEmailConfigDo
	Omit(cols ...field.Expr) IMEmailConfigDo
	Join(table schema.Tabler, on ...field.Expr) IMEmailConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMEmailConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMEmailConfigDo
	Group(cols ...field.Expr) IMEmailConfigDo
	Having(conds ...gen.Condition) IMEmailConfigDo
	Limit(limit int) IMEmailConfigDo
	Offset(offset int) IMEmailConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMEmailConfigDo
	Unscoped() IMEmailConfigDo
	Create(values ...*model.MEmailConfig) error
	CreateInBatches(values []*model.MEmailConfig, batchSize int) error
	Save(values ...*model.MEmailConfig) error
	First() (*model.MEmailConfig, error)
	Take() (*model.MEmailConfig, error)
	Last() (*model.MEmailConfig, error)
	Find() ([]*model.MEmailConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MEmailConfig, err error)
	FindInBatches(result *[]*model.MEmailConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MEmailConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMEmailConfigDo
	Assign(attrs ...field.AssignExpr) IMEmailConfigDo
	Joins(fields ...field.RelationField) IMEmailConfigDo
	Preload(fields ...field.RelationField) IMEmailConfigDo
	FirstOrInit() (*model.MEmailConfig, error)
	FirstOrCreate() (*model.MEmailConfig, error)
	FindByPage(offset int, limit int) (result []*model.MEmailConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMEmailConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mEmailConfigDo) Debug() IMEmailConfigDo {
	return m.withDO(m.DO.Debug())
}

func (m mEmailConfigDo) WithContext(ctx context.Context) IMEmailConfigDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mEmailConfigDo) ReadDB() IMEmailConfigDo {
	return m.Clauses(dbresolver.Read)
}

func (m mEmailConfigDo) WriteDB() IMEmailConfigDo {
	return m.Clauses(dbresolver.Write)
}

func (m mEmailConfigDo) Session(config *gorm.Session) IMEmailConfigDo {
	return m.withDO(m.DO.Session(config))
}

func (m mEmailConfigDo) Clauses(conds ...clause.Expression) IMEmailConfigDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mEmailConfigDo) Returning(value interface{}, columns ...string) IMEmailConfigDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mEmailConfigDo) Not(conds ...gen.Condition) IMEmailConfigDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mEmailConfigDo) Or(conds ...gen.Condition) IMEmailConfigDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mEmailConfigDo) Select(conds ...field.Expr) IMEmailConfigDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mEmailConfigDo) Where(conds ...gen.Condition) IMEmailConfigDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mEmailConfigDo) Order(conds ...field.Expr) IMEmailConfigDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mEmailConfigDo) Distinct(cols ...field.Expr) IMEmailConfigDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mEmailConfigDo) Omit(cols ...field.Expr) IMEmailConfigDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mEmailConfigDo) Join(table schema.Tabler, on ...field.Expr) IMEmailConfigDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mEmailConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMEmailConfigDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mEmailConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) IMEmailConfigDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mEmailConfigDo) Group(cols ...field.Expr) IMEmailConfigDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mEmailConfigDo) Having(conds ...gen.Condition) IMEmailConfigDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mEmailConfigDo) Limit(limit int) IMEmailConfigDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mEmailConfigDo) Offset(offset int) IMEmailConfigDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mEmailConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMEmailConfigDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mEmailConfigDo) Unscoped() IMEmailConfigDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mEmailConfigDo) Create(values ...*model.MEmailConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mEmailConfigDo) CreateInBatches(values []*model.MEmailConfig, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mEmailConfigDo) Save(values ...*model.MEmailConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mEmailConfigDo) First() (*model.MEmailConfig, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MEmailConfig), nil
	}
}

func (m mEmailConfigDo) Take() (*model.MEmailConfig, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MEmailConfig), nil
	}
}

func (m mEmailConfigDo) Last() (*model.MEmailConfig, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MEmailConfig), nil
	}
}

func (m mEmailConfigDo) Find() ([]*model.MEmailConfig, error) {
	result, err := m.DO.Find()
	return result.([]*model.MEmailConfig), err
}

func (m mEmailConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MEmailConfig, err error) {
	buf := make([]*model.MEmailConfig, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mEmailConfigDo) FindInBatches(result *[]*model.MEmailConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mEmailConfigDo) Attrs(attrs ...field.AssignExpr) IMEmailConfigDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mEmailConfigDo) Assign(attrs ...field.AssignExpr) IMEmailConfigDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mEmailConfigDo) Joins(fields ...field.RelationField) IMEmailConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mEmailConfigDo) Preload(fields ...field.RelationField) IMEmailConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mEmailConfigDo) FirstOrInit() (*model.MEmailConfig, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MEmailConfig), nil
	}
}

func (m mEmailConfigDo) FirstOrCreate() (*model.MEmailConfig, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MEmailConfig), nil
	}
}

func (m mEmailConfigDo) FindByPage(offset int, limit int) (result []*model.MEmailConfig, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mEmailConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mEmailConfigDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mEmailConfigDo) Delete(models ...*model.MEmailConfig) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mEmailConfigDo) withDO(do gen.Dao) *mEmailConfigDo {
	m.DO = *do.(*gen.DO)
	return m
}
