// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMContentConfig(db *gorm.DB, opts ...gen.DOOption) mContentConfig {
	_mContentConfig := mContentConfig{}

	_mContentConfig.mContentConfigDo.UseDB(db, opts...)
	_mContentConfig.mContentConfigDo.UseModel(&model.MContentConfig{})

	tableName := _mContentConfig.mContentConfigDo.TableName()
	_mContentConfig.ALL = field.NewAsterisk(tableName)
	_mContentConfig.ID = field.NewInt32(tableName, "id")
	_mContentConfig.ConfigKey = field.NewString(tableName, "config_key")
	_mContentConfig.ConfigValue = field.NewString(tableName, "config_value")
	_mContentConfig.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mContentConfig.fillFieldMap()

	return _mContentConfig
}

// mContentConfig 内容监控配置表
type mContentConfig struct {
	mContentConfigDo

	ALL         field.Asterisk
	ID          field.Int32  // 主键ID
	ConfigKey   field.String // 配置键
	ConfigValue field.String // 配置值
	UpdatedAt   field.Int64  // 更新时间戳

	fieldMap map[string]field.Expr
}

func (m mContentConfig) Table(newTableName string) *mContentConfig {
	m.mContentConfigDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mContentConfig) As(alias string) *mContentConfig {
	m.mContentConfigDo.DO = *(m.mContentConfigDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mContentConfig) updateTableName(table string) *mContentConfig {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.ConfigKey = field.NewString(table, "config_key")
	m.ConfigValue = field.NewString(table, "config_value")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mContentConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mContentConfig) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 4)
	m.fieldMap["id"] = m.ID
	m.fieldMap["config_key"] = m.ConfigKey
	m.fieldMap["config_value"] = m.ConfigValue
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mContentConfig) clone(db *gorm.DB) mContentConfig {
	m.mContentConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mContentConfig) replaceDB(db *gorm.DB) mContentConfig {
	m.mContentConfigDo.ReplaceDB(db)
	return m
}

type mContentConfigDo struct{ gen.DO }

type IMContentConfigDo interface {
	gen.SubQuery
	Debug() IMContentConfigDo
	WithContext(ctx context.Context) IMContentConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMContentConfigDo
	WriteDB() IMContentConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMContentConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMContentConfigDo
	Not(conds ...gen.Condition) IMContentConfigDo
	Or(conds ...gen.Condition) IMContentConfigDo
	Select(conds ...field.Expr) IMContentConfigDo
	Where(conds ...gen.Condition) IMContentConfigDo
	Order(conds ...field.Expr) IMContentConfigDo
	Distinct(cols ...field.Expr) IMContentConfigDo
	Omit(cols ...field.Expr) IMContentConfigDo
	Join(table schema.Tabler, on ...field.Expr) IMContentConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMContentConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMContentConfigDo
	Group(cols ...field.Expr) IMContentConfigDo
	Having(conds ...gen.Condition) IMContentConfigDo
	Limit(limit int) IMContentConfigDo
	Offset(offset int) IMContentConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMContentConfigDo
	Unscoped() IMContentConfigDo
	Create(values ...*model.MContentConfig) error
	CreateInBatches(values []*model.MContentConfig, batchSize int) error
	Save(values ...*model.MContentConfig) error
	First() (*model.MContentConfig, error)
	Take() (*model.MContentConfig, error)
	Last() (*model.MContentConfig, error)
	Find() ([]*model.MContentConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MContentConfig, err error)
	FindInBatches(result *[]*model.MContentConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MContentConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMContentConfigDo
	Assign(attrs ...field.AssignExpr) IMContentConfigDo
	Joins(fields ...field.RelationField) IMContentConfigDo
	Preload(fields ...field.RelationField) IMContentConfigDo
	FirstOrInit() (*model.MContentConfig, error)
	FirstOrCreate() (*model.MContentConfig, error)
	FindByPage(offset int, limit int) (result []*model.MContentConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMContentConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mContentConfigDo) Debug() IMContentConfigDo {
	return m.withDO(m.DO.Debug())
}

func (m mContentConfigDo) WithContext(ctx context.Context) IMContentConfigDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mContentConfigDo) ReadDB() IMContentConfigDo {
	return m.Clauses(dbresolver.Read)
}

func (m mContentConfigDo) WriteDB() IMContentConfigDo {
	return m.Clauses(dbresolver.Write)
}

func (m mContentConfigDo) Session(config *gorm.Session) IMContentConfigDo {
	return m.withDO(m.DO.Session(config))
}

func (m mContentConfigDo) Clauses(conds ...clause.Expression) IMContentConfigDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mContentConfigDo) Returning(value interface{}, columns ...string) IMContentConfigDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mContentConfigDo) Not(conds ...gen.Condition) IMContentConfigDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mContentConfigDo) Or(conds ...gen.Condition) IMContentConfigDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mContentConfigDo) Select(conds ...field.Expr) IMContentConfigDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mContentConfigDo) Where(conds ...gen.Condition) IMContentConfigDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mContentConfigDo) Order(conds ...field.Expr) IMContentConfigDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mContentConfigDo) Distinct(cols ...field.Expr) IMContentConfigDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mContentConfigDo) Omit(cols ...field.Expr) IMContentConfigDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mContentConfigDo) Join(table schema.Tabler, on ...field.Expr) IMContentConfigDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mContentConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMContentConfigDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mContentConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) IMContentConfigDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mContentConfigDo) Group(cols ...field.Expr) IMContentConfigDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mContentConfigDo) Having(conds ...gen.Condition) IMContentConfigDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mContentConfigDo) Limit(limit int) IMContentConfigDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mContentConfigDo) Offset(offset int) IMContentConfigDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mContentConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMContentConfigDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mContentConfigDo) Unscoped() IMContentConfigDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mContentConfigDo) Create(values ...*model.MContentConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mContentConfigDo) CreateInBatches(values []*model.MContentConfig, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mContentConfigDo) Save(values ...*model.MContentConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mContentConfigDo) First() (*model.MContentConfig, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MContentConfig), nil
	}
}

func (m mContentConfigDo) Take() (*model.MContentConfig, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MContentConfig), nil
	}
}

func (m mContentConfigDo) Last() (*model.MContentConfig, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MContentConfig), nil
	}
}

func (m mContentConfigDo) Find() ([]*model.MContentConfig, error) {
	result, err := m.DO.Find()
	return result.([]*model.MContentConfig), err
}

func (m mContentConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MContentConfig, err error) {
	buf := make([]*model.MContentConfig, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mContentConfigDo) FindInBatches(result *[]*model.MContentConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mContentConfigDo) Attrs(attrs ...field.AssignExpr) IMContentConfigDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mContentConfigDo) Assign(attrs ...field.AssignExpr) IMContentConfigDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mContentConfigDo) Joins(fields ...field.RelationField) IMContentConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mContentConfigDo) Preload(fields ...field.RelationField) IMContentConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mContentConfigDo) FirstOrInit() (*model.MContentConfig, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MContentConfig), nil
	}
}

func (m mContentConfigDo) FirstOrCreate() (*model.MContentConfig, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MContentConfig), nil
	}
}

func (m mContentConfigDo) FindByPage(offset int, limit int) (result []*model.MContentConfig, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mContentConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mContentConfigDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mContentConfigDo) Delete(models ...*model.MContentConfig) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mContentConfigDo) withDO(do gen.Dao) *mContentConfigDo {
	m.DO = *do.(*gen.DO)
	return m
}
