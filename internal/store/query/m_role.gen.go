// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMRole(db *gorm.DB, opts ...gen.DOOption) mRole {
	_mRole := mRole{}

	_mRole.mRoleDo.UseDB(db, opts...)
	_mRole.mRoleDo.UseModel(&model.MRole{})

	tableName := _mRole.mRoleDo.TableName()
	_mRole.ALL = field.NewAsterisk(tableName)
	_mRole.ID = field.NewInt32(tableName, "id")
	_mRole.Code = field.NewString(tableName, "code")
	_mRole.Name = field.NewString(tableName, "name")
	_mRole.Description = field.NewString(tableName, "description")
	_mRole.CreatorID = field.NewString(tableName, "creator_id")
	_mRole.SystemID = field.NewInt32(tableName, "system_id")
	_mRole.CreatedAt = field.NewInt64(tableName, "created_at")
	_mRole.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mRole.fillFieldMap()

	return _mRole
}

// mRole 角色表
type mRole struct {
	mRoleDo

	ALL         field.Asterisk
	ID          field.Int32
	Code        field.String
	Name        field.String // 角色名称
	Description field.String // 角色描述
	CreatorID   field.String // 创建人ID
	SystemID    field.Int32
	CreatedAt   field.Int64
	UpdatedAt   field.Int64

	fieldMap map[string]field.Expr
}

func (m mRole) Table(newTableName string) *mRole {
	m.mRoleDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mRole) As(alias string) *mRole {
	m.mRoleDo.DO = *(m.mRoleDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mRole) updateTableName(table string) *mRole {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.Code = field.NewString(table, "code")
	m.Name = field.NewString(table, "name")
	m.Description = field.NewString(table, "description")
	m.CreatorID = field.NewString(table, "creator_id")
	m.SystemID = field.NewInt32(table, "system_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mRole) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mRole) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 8)
	m.fieldMap["id"] = m.ID
	m.fieldMap["code"] = m.Code
	m.fieldMap["name"] = m.Name
	m.fieldMap["description"] = m.Description
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["system_id"] = m.SystemID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mRole) clone(db *gorm.DB) mRole {
	m.mRoleDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mRole) replaceDB(db *gorm.DB) mRole {
	m.mRoleDo.ReplaceDB(db)
	return m
}

type mRoleDo struct{ gen.DO }

type IMRoleDo interface {
	gen.SubQuery
	Debug() IMRoleDo
	WithContext(ctx context.Context) IMRoleDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMRoleDo
	WriteDB() IMRoleDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMRoleDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMRoleDo
	Not(conds ...gen.Condition) IMRoleDo
	Or(conds ...gen.Condition) IMRoleDo
	Select(conds ...field.Expr) IMRoleDo
	Where(conds ...gen.Condition) IMRoleDo
	Order(conds ...field.Expr) IMRoleDo
	Distinct(cols ...field.Expr) IMRoleDo
	Omit(cols ...field.Expr) IMRoleDo
	Join(table schema.Tabler, on ...field.Expr) IMRoleDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMRoleDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMRoleDo
	Group(cols ...field.Expr) IMRoleDo
	Having(conds ...gen.Condition) IMRoleDo
	Limit(limit int) IMRoleDo
	Offset(offset int) IMRoleDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMRoleDo
	Unscoped() IMRoleDo
	Create(values ...*model.MRole) error
	CreateInBatches(values []*model.MRole, batchSize int) error
	Save(values ...*model.MRole) error
	First() (*model.MRole, error)
	Take() (*model.MRole, error)
	Last() (*model.MRole, error)
	Find() ([]*model.MRole, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MRole, err error)
	FindInBatches(result *[]*model.MRole, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MRole) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMRoleDo
	Assign(attrs ...field.AssignExpr) IMRoleDo
	Joins(fields ...field.RelationField) IMRoleDo
	Preload(fields ...field.RelationField) IMRoleDo
	FirstOrInit() (*model.MRole, error)
	FirstOrCreate() (*model.MRole, error)
	FindByPage(offset int, limit int) (result []*model.MRole, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMRoleDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mRoleDo) Debug() IMRoleDo {
	return m.withDO(m.DO.Debug())
}

func (m mRoleDo) WithContext(ctx context.Context) IMRoleDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mRoleDo) ReadDB() IMRoleDo {
	return m.Clauses(dbresolver.Read)
}

func (m mRoleDo) WriteDB() IMRoleDo {
	return m.Clauses(dbresolver.Write)
}

func (m mRoleDo) Session(config *gorm.Session) IMRoleDo {
	return m.withDO(m.DO.Session(config))
}

func (m mRoleDo) Clauses(conds ...clause.Expression) IMRoleDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mRoleDo) Returning(value interface{}, columns ...string) IMRoleDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mRoleDo) Not(conds ...gen.Condition) IMRoleDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mRoleDo) Or(conds ...gen.Condition) IMRoleDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mRoleDo) Select(conds ...field.Expr) IMRoleDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mRoleDo) Where(conds ...gen.Condition) IMRoleDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mRoleDo) Order(conds ...field.Expr) IMRoleDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mRoleDo) Distinct(cols ...field.Expr) IMRoleDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mRoleDo) Omit(cols ...field.Expr) IMRoleDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mRoleDo) Join(table schema.Tabler, on ...field.Expr) IMRoleDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mRoleDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMRoleDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mRoleDo) RightJoin(table schema.Tabler, on ...field.Expr) IMRoleDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mRoleDo) Group(cols ...field.Expr) IMRoleDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mRoleDo) Having(conds ...gen.Condition) IMRoleDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mRoleDo) Limit(limit int) IMRoleDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mRoleDo) Offset(offset int) IMRoleDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mRoleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMRoleDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mRoleDo) Unscoped() IMRoleDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mRoleDo) Create(values ...*model.MRole) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mRoleDo) CreateInBatches(values []*model.MRole, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mRoleDo) Save(values ...*model.MRole) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mRoleDo) First() (*model.MRole, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRole), nil
	}
}

func (m mRoleDo) Take() (*model.MRole, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRole), nil
	}
}

func (m mRoleDo) Last() (*model.MRole, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRole), nil
	}
}

func (m mRoleDo) Find() ([]*model.MRole, error) {
	result, err := m.DO.Find()
	return result.([]*model.MRole), err
}

func (m mRoleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MRole, err error) {
	buf := make([]*model.MRole, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mRoleDo) FindInBatches(result *[]*model.MRole, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mRoleDo) Attrs(attrs ...field.AssignExpr) IMRoleDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mRoleDo) Assign(attrs ...field.AssignExpr) IMRoleDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mRoleDo) Joins(fields ...field.RelationField) IMRoleDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mRoleDo) Preload(fields ...field.RelationField) IMRoleDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mRoleDo) FirstOrInit() (*model.MRole, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRole), nil
	}
}

func (m mRoleDo) FirstOrCreate() (*model.MRole, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRole), nil
	}
}

func (m mRoleDo) FindByPage(offset int, limit int) (result []*model.MRole, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mRoleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mRoleDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mRoleDo) Delete(models ...*model.MRole) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mRoleDo) withDO(do gen.Dao) *mRoleDo {
	m.DO = *do.(*gen.DO)
	return m
}
