// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMWorkorderTag(db *gorm.DB, opts ...gen.DOOption) mWorkorderTag {
	_mWorkorderTag := mWorkorderTag{}

	_mWorkorderTag.mWorkorderTagDo.UseDB(db, opts...)
	_mWorkorderTag.mWorkorderTagDo.UseModel(&model.MWorkorderTag{})

	tableName := _mWorkorderTag.mWorkorderTagDo.TableName()
	_mWorkorderTag.ALL = field.NewAsterisk(tableName)
	_mWorkorderTag.ID = field.NewInt32(tableName, "id")
	_mWorkorderTag.TagName = field.NewString(tableName, "tag_name")
	_mWorkorderTag.CreatedAt = field.NewInt64(tableName, "created_at")
	_mWorkorderTag.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mWorkorderTag.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mWorkorderTag.fillFieldMap()

	return _mWorkorderTag
}

// mWorkorderTag 工单标签表
type mWorkorderTag struct {
	mWorkorderTagDo

	ALL       field.Asterisk
	ID        field.Int32
	TagName   field.String // 标签名称
	CreatedAt field.Int64
	UpdatedAt field.Int64
	IsDeleted field.Bool

	fieldMap map[string]field.Expr
}

func (m mWorkorderTag) Table(newTableName string) *mWorkorderTag {
	m.mWorkorderTagDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mWorkorderTag) As(alias string) *mWorkorderTag {
	m.mWorkorderTagDo.DO = *(m.mWorkorderTagDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mWorkorderTag) updateTableName(table string) *mWorkorderTag {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.TagName = field.NewString(table, "tag_name")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mWorkorderTag) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mWorkorderTag) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 5)
	m.fieldMap["id"] = m.ID
	m.fieldMap["tag_name"] = m.TagName
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mWorkorderTag) clone(db *gorm.DB) mWorkorderTag {
	m.mWorkorderTagDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mWorkorderTag) replaceDB(db *gorm.DB) mWorkorderTag {
	m.mWorkorderTagDo.ReplaceDB(db)
	return m
}

type mWorkorderTagDo struct{ gen.DO }

type IMWorkorderTagDo interface {
	gen.SubQuery
	Debug() IMWorkorderTagDo
	WithContext(ctx context.Context) IMWorkorderTagDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMWorkorderTagDo
	WriteDB() IMWorkorderTagDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMWorkorderTagDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMWorkorderTagDo
	Not(conds ...gen.Condition) IMWorkorderTagDo
	Or(conds ...gen.Condition) IMWorkorderTagDo
	Select(conds ...field.Expr) IMWorkorderTagDo
	Where(conds ...gen.Condition) IMWorkorderTagDo
	Order(conds ...field.Expr) IMWorkorderTagDo
	Distinct(cols ...field.Expr) IMWorkorderTagDo
	Omit(cols ...field.Expr) IMWorkorderTagDo
	Join(table schema.Tabler, on ...field.Expr) IMWorkorderTagDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderTagDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderTagDo
	Group(cols ...field.Expr) IMWorkorderTagDo
	Having(conds ...gen.Condition) IMWorkorderTagDo
	Limit(limit int) IMWorkorderTagDo
	Offset(offset int) IMWorkorderTagDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderTagDo
	Unscoped() IMWorkorderTagDo
	Create(values ...*model.MWorkorderTag) error
	CreateInBatches(values []*model.MWorkorderTag, batchSize int) error
	Save(values ...*model.MWorkorderTag) error
	First() (*model.MWorkorderTag, error)
	Take() (*model.MWorkorderTag, error)
	Last() (*model.MWorkorderTag, error)
	Find() ([]*model.MWorkorderTag, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderTag, err error)
	FindInBatches(result *[]*model.MWorkorderTag, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MWorkorderTag) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMWorkorderTagDo
	Assign(attrs ...field.AssignExpr) IMWorkorderTagDo
	Joins(fields ...field.RelationField) IMWorkorderTagDo
	Preload(fields ...field.RelationField) IMWorkorderTagDo
	FirstOrInit() (*model.MWorkorderTag, error)
	FirstOrCreate() (*model.MWorkorderTag, error)
	FindByPage(offset int, limit int) (result []*model.MWorkorderTag, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMWorkorderTagDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mWorkorderTagDo) Debug() IMWorkorderTagDo {
	return m.withDO(m.DO.Debug())
}

func (m mWorkorderTagDo) WithContext(ctx context.Context) IMWorkorderTagDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mWorkorderTagDo) ReadDB() IMWorkorderTagDo {
	return m.Clauses(dbresolver.Read)
}

func (m mWorkorderTagDo) WriteDB() IMWorkorderTagDo {
	return m.Clauses(dbresolver.Write)
}

func (m mWorkorderTagDo) Session(config *gorm.Session) IMWorkorderTagDo {
	return m.withDO(m.DO.Session(config))
}

func (m mWorkorderTagDo) Clauses(conds ...clause.Expression) IMWorkorderTagDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mWorkorderTagDo) Returning(value interface{}, columns ...string) IMWorkorderTagDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mWorkorderTagDo) Not(conds ...gen.Condition) IMWorkorderTagDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mWorkorderTagDo) Or(conds ...gen.Condition) IMWorkorderTagDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mWorkorderTagDo) Select(conds ...field.Expr) IMWorkorderTagDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mWorkorderTagDo) Where(conds ...gen.Condition) IMWorkorderTagDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mWorkorderTagDo) Order(conds ...field.Expr) IMWorkorderTagDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mWorkorderTagDo) Distinct(cols ...field.Expr) IMWorkorderTagDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mWorkorderTagDo) Omit(cols ...field.Expr) IMWorkorderTagDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mWorkorderTagDo) Join(table schema.Tabler, on ...field.Expr) IMWorkorderTagDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mWorkorderTagDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMWorkorderTagDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mWorkorderTagDo) RightJoin(table schema.Tabler, on ...field.Expr) IMWorkorderTagDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mWorkorderTagDo) Group(cols ...field.Expr) IMWorkorderTagDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mWorkorderTagDo) Having(conds ...gen.Condition) IMWorkorderTagDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mWorkorderTagDo) Limit(limit int) IMWorkorderTagDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mWorkorderTagDo) Offset(offset int) IMWorkorderTagDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mWorkorderTagDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMWorkorderTagDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mWorkorderTagDo) Unscoped() IMWorkorderTagDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mWorkorderTagDo) Create(values ...*model.MWorkorderTag) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mWorkorderTagDo) CreateInBatches(values []*model.MWorkorderTag, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mWorkorderTagDo) Save(values ...*model.MWorkorderTag) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mWorkorderTagDo) First() (*model.MWorkorderTag, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTag), nil
	}
}

func (m mWorkorderTagDo) Take() (*model.MWorkorderTag, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTag), nil
	}
}

func (m mWorkorderTagDo) Last() (*model.MWorkorderTag, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTag), nil
	}
}

func (m mWorkorderTagDo) Find() ([]*model.MWorkorderTag, error) {
	result, err := m.DO.Find()
	return result.([]*model.MWorkorderTag), err
}

func (m mWorkorderTagDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MWorkorderTag, err error) {
	buf := make([]*model.MWorkorderTag, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mWorkorderTagDo) FindInBatches(result *[]*model.MWorkorderTag, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mWorkorderTagDo) Attrs(attrs ...field.AssignExpr) IMWorkorderTagDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mWorkorderTagDo) Assign(attrs ...field.AssignExpr) IMWorkorderTagDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mWorkorderTagDo) Joins(fields ...field.RelationField) IMWorkorderTagDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mWorkorderTagDo) Preload(fields ...field.RelationField) IMWorkorderTagDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mWorkorderTagDo) FirstOrInit() (*model.MWorkorderTag, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTag), nil
	}
}

func (m mWorkorderTagDo) FirstOrCreate() (*model.MWorkorderTag, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MWorkorderTag), nil
	}
}

func (m mWorkorderTagDo) FindByPage(offset int, limit int) (result []*model.MWorkorderTag, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mWorkorderTagDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mWorkorderTagDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mWorkorderTagDo) Delete(models ...*model.MWorkorderTag) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mWorkorderTagDo) withDO(do gen.Dao) *mWorkorderTagDo {
	m.DO = *do.(*gen.DO)
	return m
}
