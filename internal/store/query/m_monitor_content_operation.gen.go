// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMMonitorContentOperation(db *gorm.DB, opts ...gen.DOOption) mMonitorContentOperation {
	_mMonitorContentOperation := mMonitorContentOperation{}

	_mMonitorContentOperation.mMonitorContentOperationDo.UseDB(db, opts...)
	_mMonitorContentOperation.mMonitorContentOperationDo.UseModel(&model.MMonitorContentOperation{})

	tableName := _mMonitorContentOperation.mMonitorContentOperationDo.TableName()
	_mMonitorContentOperation.ALL = field.NewAsterisk(tableName)
	_mMonitorContentOperation.ID = field.NewInt32(tableName, "id")
	_mMonitorContentOperation.GameID = field.NewString(tableName, "game_id")
	_mMonitorContentOperation.ContentID = field.NewString(tableName, "content_id")
	_mMonitorContentOperation.UserID = field.NewString(tableName, "user_id")
	_mMonitorContentOperation.RoleID = field.NewString(tableName, "role_id")
	_mMonitorContentOperation.Action = field.NewInt32(tableName, "action")
	_mMonitorContentOperation.ActionParam = field.NewString(tableName, "action_param")
	_mMonitorContentOperation.HandleTimeAt = field.NewInt64(tableName, "handle_time_at")
	_mMonitorContentOperation.Description = field.NewString(tableName, "description")
	_mMonitorContentOperation.RecordDescription = field.NewString(tableName, "record_description")
	_mMonitorContentOperation.Content = field.NewString(tableName, "content")
	_mMonitorContentOperation.CreatorID = field.NewString(tableName, "creator_id")
	_mMonitorContentOperation.CreatedAt = field.NewInt64(tableName, "created_at")
	_mMonitorContentOperation.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mMonitorContentOperation.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mMonitorContentOperation.fillFieldMap()

	return _mMonitorContentOperation
}

// mMonitorContentOperation 内容操作记录表
type mMonitorContentOperation struct {
	mMonitorContentOperationDo

	ALL               field.Asterisk
	ID                field.Int32 // 主键ID
	GameID            field.String
	ContentID         field.String // 关联的内容ID
	UserID            field.String // 平台ID
	RoleID            field.String
	Action            field.Int32  // 处理动作索引
	ActionParam       field.String // 处理参数(JSON格式)
	HandleTimeAt      field.Int64  // 处理时间
	Description       field.String
	RecordDescription field.String // 处理描述
	Content           field.String // 文本内容
	CreatorID         field.String // 处理人ID
	CreatedAt         field.Int64  // 创建时间
	UpdatedAt         field.Int64  // 更新时间
	IsDeleted         field.Bool   // 是否删除

	fieldMap map[string]field.Expr
}

func (m mMonitorContentOperation) Table(newTableName string) *mMonitorContentOperation {
	m.mMonitorContentOperationDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mMonitorContentOperation) As(alias string) *mMonitorContentOperation {
	m.mMonitorContentOperationDo.DO = *(m.mMonitorContentOperationDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mMonitorContentOperation) updateTableName(table string) *mMonitorContentOperation {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.ContentID = field.NewString(table, "content_id")
	m.UserID = field.NewString(table, "user_id")
	m.RoleID = field.NewString(table, "role_id")
	m.Action = field.NewInt32(table, "action")
	m.ActionParam = field.NewString(table, "action_param")
	m.HandleTimeAt = field.NewInt64(table, "handle_time_at")
	m.Description = field.NewString(table, "description")
	m.RecordDescription = field.NewString(table, "record_description")
	m.Content = field.NewString(table, "content")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mMonitorContentOperation) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mMonitorContentOperation) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 15)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["content_id"] = m.ContentID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["role_id"] = m.RoleID
	m.fieldMap["action"] = m.Action
	m.fieldMap["action_param"] = m.ActionParam
	m.fieldMap["handle_time_at"] = m.HandleTimeAt
	m.fieldMap["description"] = m.Description
	m.fieldMap["record_description"] = m.RecordDescription
	m.fieldMap["content"] = m.Content
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mMonitorContentOperation) clone(db *gorm.DB) mMonitorContentOperation {
	m.mMonitorContentOperationDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mMonitorContentOperation) replaceDB(db *gorm.DB) mMonitorContentOperation {
	m.mMonitorContentOperationDo.ReplaceDB(db)
	return m
}

type mMonitorContentOperationDo struct{ gen.DO }

type IMMonitorContentOperationDo interface {
	gen.SubQuery
	Debug() IMMonitorContentOperationDo
	WithContext(ctx context.Context) IMMonitorContentOperationDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMMonitorContentOperationDo
	WriteDB() IMMonitorContentOperationDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMMonitorContentOperationDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMMonitorContentOperationDo
	Not(conds ...gen.Condition) IMMonitorContentOperationDo
	Or(conds ...gen.Condition) IMMonitorContentOperationDo
	Select(conds ...field.Expr) IMMonitorContentOperationDo
	Where(conds ...gen.Condition) IMMonitorContentOperationDo
	Order(conds ...field.Expr) IMMonitorContentOperationDo
	Distinct(cols ...field.Expr) IMMonitorContentOperationDo
	Omit(cols ...field.Expr) IMMonitorContentOperationDo
	Join(table schema.Tabler, on ...field.Expr) IMMonitorContentOperationDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMMonitorContentOperationDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMMonitorContentOperationDo
	Group(cols ...field.Expr) IMMonitorContentOperationDo
	Having(conds ...gen.Condition) IMMonitorContentOperationDo
	Limit(limit int) IMMonitorContentOperationDo
	Offset(offset int) IMMonitorContentOperationDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMMonitorContentOperationDo
	Unscoped() IMMonitorContentOperationDo
	Create(values ...*model.MMonitorContentOperation) error
	CreateInBatches(values []*model.MMonitorContentOperation, batchSize int) error
	Save(values ...*model.MMonitorContentOperation) error
	First() (*model.MMonitorContentOperation, error)
	Take() (*model.MMonitorContentOperation, error)
	Last() (*model.MMonitorContentOperation, error)
	Find() ([]*model.MMonitorContentOperation, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MMonitorContentOperation, err error)
	FindInBatches(result *[]*model.MMonitorContentOperation, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MMonitorContentOperation) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMMonitorContentOperationDo
	Assign(attrs ...field.AssignExpr) IMMonitorContentOperationDo
	Joins(fields ...field.RelationField) IMMonitorContentOperationDo
	Preload(fields ...field.RelationField) IMMonitorContentOperationDo
	FirstOrInit() (*model.MMonitorContentOperation, error)
	FirstOrCreate() (*model.MMonitorContentOperation, error)
	FindByPage(offset int, limit int) (result []*model.MMonitorContentOperation, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMMonitorContentOperationDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mMonitorContentOperationDo) Debug() IMMonitorContentOperationDo {
	return m.withDO(m.DO.Debug())
}

func (m mMonitorContentOperationDo) WithContext(ctx context.Context) IMMonitorContentOperationDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mMonitorContentOperationDo) ReadDB() IMMonitorContentOperationDo {
	return m.Clauses(dbresolver.Read)
}

func (m mMonitorContentOperationDo) WriteDB() IMMonitorContentOperationDo {
	return m.Clauses(dbresolver.Write)
}

func (m mMonitorContentOperationDo) Session(config *gorm.Session) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Session(config))
}

func (m mMonitorContentOperationDo) Clauses(conds ...clause.Expression) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mMonitorContentOperationDo) Returning(value interface{}, columns ...string) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mMonitorContentOperationDo) Not(conds ...gen.Condition) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mMonitorContentOperationDo) Or(conds ...gen.Condition) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mMonitorContentOperationDo) Select(conds ...field.Expr) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mMonitorContentOperationDo) Where(conds ...gen.Condition) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mMonitorContentOperationDo) Order(conds ...field.Expr) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mMonitorContentOperationDo) Distinct(cols ...field.Expr) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mMonitorContentOperationDo) Omit(cols ...field.Expr) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mMonitorContentOperationDo) Join(table schema.Tabler, on ...field.Expr) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mMonitorContentOperationDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMMonitorContentOperationDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mMonitorContentOperationDo) RightJoin(table schema.Tabler, on ...field.Expr) IMMonitorContentOperationDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mMonitorContentOperationDo) Group(cols ...field.Expr) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mMonitorContentOperationDo) Having(conds ...gen.Condition) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mMonitorContentOperationDo) Limit(limit int) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mMonitorContentOperationDo) Offset(offset int) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mMonitorContentOperationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mMonitorContentOperationDo) Unscoped() IMMonitorContentOperationDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mMonitorContentOperationDo) Create(values ...*model.MMonitorContentOperation) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mMonitorContentOperationDo) CreateInBatches(values []*model.MMonitorContentOperation, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mMonitorContentOperationDo) Save(values ...*model.MMonitorContentOperation) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mMonitorContentOperationDo) First() (*model.MMonitorContentOperation, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentOperation), nil
	}
}

func (m mMonitorContentOperationDo) Take() (*model.MMonitorContentOperation, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentOperation), nil
	}
}

func (m mMonitorContentOperationDo) Last() (*model.MMonitorContentOperation, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentOperation), nil
	}
}

func (m mMonitorContentOperationDo) Find() ([]*model.MMonitorContentOperation, error) {
	result, err := m.DO.Find()
	return result.([]*model.MMonitorContentOperation), err
}

func (m mMonitorContentOperationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MMonitorContentOperation, err error) {
	buf := make([]*model.MMonitorContentOperation, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mMonitorContentOperationDo) FindInBatches(result *[]*model.MMonitorContentOperation, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mMonitorContentOperationDo) Attrs(attrs ...field.AssignExpr) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mMonitorContentOperationDo) Assign(attrs ...field.AssignExpr) IMMonitorContentOperationDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mMonitorContentOperationDo) Joins(fields ...field.RelationField) IMMonitorContentOperationDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mMonitorContentOperationDo) Preload(fields ...field.RelationField) IMMonitorContentOperationDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mMonitorContentOperationDo) FirstOrInit() (*model.MMonitorContentOperation, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentOperation), nil
	}
}

func (m mMonitorContentOperationDo) FirstOrCreate() (*model.MMonitorContentOperation, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorContentOperation), nil
	}
}

func (m mMonitorContentOperationDo) FindByPage(offset int, limit int) (result []*model.MMonitorContentOperation, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mMonitorContentOperationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mMonitorContentOperationDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mMonitorContentOperationDo) Delete(models ...*model.MMonitorContentOperation) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mMonitorContentOperationDo) withDO(do gen.Dao) *mMonitorContentOperationDo {
	m.DO = *do.(*gen.DO)
	return m
}
