// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newH5AdminUser(db *gorm.DB, opts ...gen.DOOption) h5AdminUser {
	_h5AdminUser := h5AdminUser{}

	_h5AdminUser.h5AdminUserDo.UseDB(db, opts...)
	_h5AdminUser.h5AdminUserDo.UseModel(&model.H5AdminUser{})

	tableName := _h5AdminUser.h5AdminUserDo.TableName()
	_h5AdminUser.ALL = field.NewAsterisk(tableName)
	_h5AdminUser.ID = field.NewInt32(tableName, "id")
	_h5AdminUser.UserID = field.NewString(tableName, "user_id")
	_h5AdminUser.Username = field.NewString(tableName, "username")
	_h5AdminUser.Password = field.NewString(tableName, "password")
	_h5AdminUser.IsRealNameAuth = field.NewBool(tableName, "is_real_name_auth")
	_h5AdminUser.IsMinors = field.NewBool(tableName, "is_minors")
	_h5AdminUser.BirthDateAt = field.NewInt64(tableName, "birth_date_at")
	_h5AdminUser.CreatedAt = field.NewInt64(tableName, "created_at")
	_h5AdminUser.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_h5AdminUser.fillFieldMap()

	return _h5AdminUser
}

// h5AdminUser H5打包管理后台用户表
type h5AdminUser struct {
	h5AdminUserDo

	ALL            field.Asterisk
	ID             field.Int32 // 主键ID
	UserID         field.String
	Username       field.String // 用户名
	Password       field.String // 密码(加密存储)
	IsRealNameAuth field.Bool
	IsMinors       field.Bool
	BirthDateAt    field.Int64 // 生日时间戳
	CreatedAt      field.Int64 // 创建时间
	UpdatedAt      field.Int64 // 更新时间

	fieldMap map[string]field.Expr
}

func (h h5AdminUser) Table(newTableName string) *h5AdminUser {
	h.h5AdminUserDo.UseTable(newTableName)
	return h.updateTableName(newTableName)
}

func (h h5AdminUser) As(alias string) *h5AdminUser {
	h.h5AdminUserDo.DO = *(h.h5AdminUserDo.As(alias).(*gen.DO))
	return h.updateTableName(alias)
}

func (h *h5AdminUser) updateTableName(table string) *h5AdminUser {
	h.ALL = field.NewAsterisk(table)
	h.ID = field.NewInt32(table, "id")
	h.UserID = field.NewString(table, "user_id")
	h.Username = field.NewString(table, "username")
	h.Password = field.NewString(table, "password")
	h.IsRealNameAuth = field.NewBool(table, "is_real_name_auth")
	h.IsMinors = field.NewBool(table, "is_minors")
	h.BirthDateAt = field.NewInt64(table, "birth_date_at")
	h.CreatedAt = field.NewInt64(table, "created_at")
	h.UpdatedAt = field.NewInt64(table, "updated_at")

	h.fillFieldMap()

	return h
}

func (h *h5AdminUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := h.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (h *h5AdminUser) fillFieldMap() {
	h.fieldMap = make(map[string]field.Expr, 9)
	h.fieldMap["id"] = h.ID
	h.fieldMap["user_id"] = h.UserID
	h.fieldMap["username"] = h.Username
	h.fieldMap["password"] = h.Password
	h.fieldMap["is_real_name_auth"] = h.IsRealNameAuth
	h.fieldMap["is_minors"] = h.IsMinors
	h.fieldMap["birth_date_at"] = h.BirthDateAt
	h.fieldMap["created_at"] = h.CreatedAt
	h.fieldMap["updated_at"] = h.UpdatedAt
}

func (h h5AdminUser) clone(db *gorm.DB) h5AdminUser {
	h.h5AdminUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return h
}

func (h h5AdminUser) replaceDB(db *gorm.DB) h5AdminUser {
	h.h5AdminUserDo.ReplaceDB(db)
	return h
}

type h5AdminUserDo struct{ gen.DO }

type IH5AdminUserDo interface {
	gen.SubQuery
	Debug() IH5AdminUserDo
	WithContext(ctx context.Context) IH5AdminUserDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IH5AdminUserDo
	WriteDB() IH5AdminUserDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IH5AdminUserDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IH5AdminUserDo
	Not(conds ...gen.Condition) IH5AdminUserDo
	Or(conds ...gen.Condition) IH5AdminUserDo
	Select(conds ...field.Expr) IH5AdminUserDo
	Where(conds ...gen.Condition) IH5AdminUserDo
	Order(conds ...field.Expr) IH5AdminUserDo
	Distinct(cols ...field.Expr) IH5AdminUserDo
	Omit(cols ...field.Expr) IH5AdminUserDo
	Join(table schema.Tabler, on ...field.Expr) IH5AdminUserDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IH5AdminUserDo
	RightJoin(table schema.Tabler, on ...field.Expr) IH5AdminUserDo
	Group(cols ...field.Expr) IH5AdminUserDo
	Having(conds ...gen.Condition) IH5AdminUserDo
	Limit(limit int) IH5AdminUserDo
	Offset(offset int) IH5AdminUserDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IH5AdminUserDo
	Unscoped() IH5AdminUserDo
	Create(values ...*model.H5AdminUser) error
	CreateInBatches(values []*model.H5AdminUser, batchSize int) error
	Save(values ...*model.H5AdminUser) error
	First() (*model.H5AdminUser, error)
	Take() (*model.H5AdminUser, error)
	Last() (*model.H5AdminUser, error)
	Find() ([]*model.H5AdminUser, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.H5AdminUser, err error)
	FindInBatches(result *[]*model.H5AdminUser, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.H5AdminUser) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IH5AdminUserDo
	Assign(attrs ...field.AssignExpr) IH5AdminUserDo
	Joins(fields ...field.RelationField) IH5AdminUserDo
	Preload(fields ...field.RelationField) IH5AdminUserDo
	FirstOrInit() (*model.H5AdminUser, error)
	FirstOrCreate() (*model.H5AdminUser, error)
	FindByPage(offset int, limit int) (result []*model.H5AdminUser, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IH5AdminUserDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (h h5AdminUserDo) Debug() IH5AdminUserDo {
	return h.withDO(h.DO.Debug())
}

func (h h5AdminUserDo) WithContext(ctx context.Context) IH5AdminUserDo {
	return h.withDO(h.DO.WithContext(ctx))
}

func (h h5AdminUserDo) ReadDB() IH5AdminUserDo {
	return h.Clauses(dbresolver.Read)
}

func (h h5AdminUserDo) WriteDB() IH5AdminUserDo {
	return h.Clauses(dbresolver.Write)
}

func (h h5AdminUserDo) Session(config *gorm.Session) IH5AdminUserDo {
	return h.withDO(h.DO.Session(config))
}

func (h h5AdminUserDo) Clauses(conds ...clause.Expression) IH5AdminUserDo {
	return h.withDO(h.DO.Clauses(conds...))
}

func (h h5AdminUserDo) Returning(value interface{}, columns ...string) IH5AdminUserDo {
	return h.withDO(h.DO.Returning(value, columns...))
}

func (h h5AdminUserDo) Not(conds ...gen.Condition) IH5AdminUserDo {
	return h.withDO(h.DO.Not(conds...))
}

func (h h5AdminUserDo) Or(conds ...gen.Condition) IH5AdminUserDo {
	return h.withDO(h.DO.Or(conds...))
}

func (h h5AdminUserDo) Select(conds ...field.Expr) IH5AdminUserDo {
	return h.withDO(h.DO.Select(conds...))
}

func (h h5AdminUserDo) Where(conds ...gen.Condition) IH5AdminUserDo {
	return h.withDO(h.DO.Where(conds...))
}

func (h h5AdminUserDo) Order(conds ...field.Expr) IH5AdminUserDo {
	return h.withDO(h.DO.Order(conds...))
}

func (h h5AdminUserDo) Distinct(cols ...field.Expr) IH5AdminUserDo {
	return h.withDO(h.DO.Distinct(cols...))
}

func (h h5AdminUserDo) Omit(cols ...field.Expr) IH5AdminUserDo {
	return h.withDO(h.DO.Omit(cols...))
}

func (h h5AdminUserDo) Join(table schema.Tabler, on ...field.Expr) IH5AdminUserDo {
	return h.withDO(h.DO.Join(table, on...))
}

func (h h5AdminUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) IH5AdminUserDo {
	return h.withDO(h.DO.LeftJoin(table, on...))
}

func (h h5AdminUserDo) RightJoin(table schema.Tabler, on ...field.Expr) IH5AdminUserDo {
	return h.withDO(h.DO.RightJoin(table, on...))
}

func (h h5AdminUserDo) Group(cols ...field.Expr) IH5AdminUserDo {
	return h.withDO(h.DO.Group(cols...))
}

func (h h5AdminUserDo) Having(conds ...gen.Condition) IH5AdminUserDo {
	return h.withDO(h.DO.Having(conds...))
}

func (h h5AdminUserDo) Limit(limit int) IH5AdminUserDo {
	return h.withDO(h.DO.Limit(limit))
}

func (h h5AdminUserDo) Offset(offset int) IH5AdminUserDo {
	return h.withDO(h.DO.Offset(offset))
}

func (h h5AdminUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IH5AdminUserDo {
	return h.withDO(h.DO.Scopes(funcs...))
}

func (h h5AdminUserDo) Unscoped() IH5AdminUserDo {
	return h.withDO(h.DO.Unscoped())
}

func (h h5AdminUserDo) Create(values ...*model.H5AdminUser) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Create(values)
}

func (h h5AdminUserDo) CreateInBatches(values []*model.H5AdminUser, batchSize int) error {
	return h.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (h h5AdminUserDo) Save(values ...*model.H5AdminUser) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Save(values)
}

func (h h5AdminUserDo) First() (*model.H5AdminUser, error) {
	if result, err := h.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminUser), nil
	}
}

func (h h5AdminUserDo) Take() (*model.H5AdminUser, error) {
	if result, err := h.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminUser), nil
	}
}

func (h h5AdminUserDo) Last() (*model.H5AdminUser, error) {
	if result, err := h.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminUser), nil
	}
}

func (h h5AdminUserDo) Find() ([]*model.H5AdminUser, error) {
	result, err := h.DO.Find()
	return result.([]*model.H5AdminUser), err
}

func (h h5AdminUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.H5AdminUser, err error) {
	buf := make([]*model.H5AdminUser, 0, batchSize)
	err = h.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (h h5AdminUserDo) FindInBatches(result *[]*model.H5AdminUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return h.DO.FindInBatches(result, batchSize, fc)
}

func (h h5AdminUserDo) Attrs(attrs ...field.AssignExpr) IH5AdminUserDo {
	return h.withDO(h.DO.Attrs(attrs...))
}

func (h h5AdminUserDo) Assign(attrs ...field.AssignExpr) IH5AdminUserDo {
	return h.withDO(h.DO.Assign(attrs...))
}

func (h h5AdminUserDo) Joins(fields ...field.RelationField) IH5AdminUserDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Joins(_f))
	}
	return &h
}

func (h h5AdminUserDo) Preload(fields ...field.RelationField) IH5AdminUserDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Preload(_f))
	}
	return &h
}

func (h h5AdminUserDo) FirstOrInit() (*model.H5AdminUser, error) {
	if result, err := h.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminUser), nil
	}
}

func (h h5AdminUserDo) FirstOrCreate() (*model.H5AdminUser, error) {
	if result, err := h.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.H5AdminUser), nil
	}
}

func (h h5AdminUserDo) FindByPage(offset int, limit int) (result []*model.H5AdminUser, count int64, err error) {
	result, err = h.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = h.Offset(-1).Limit(-1).Count()
	return
}

func (h h5AdminUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = h.Count()
	if err != nil {
		return
	}

	err = h.Offset(offset).Limit(limit).Scan(result)
	return
}

func (h h5AdminUserDo) Scan(result interface{}) (err error) {
	return h.DO.Scan(result)
}

func (h h5AdminUserDo) Delete(models ...*model.H5AdminUser) (result gen.ResultInfo, err error) {
	return h.DO.Delete(models)
}

func (h *h5AdminUserDo) withDO(do gen.Dao) *h5AdminUserDo {
	h.DO = *do.(*gen.DO)
	return h
}
