// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMPermissionDatum(db *gorm.DB, opts ...gen.DOOption) mPermissionDatum {
	_mPermissionDatum := mPermissionDatum{}

	_mPermissionDatum.mPermissionDatumDo.UseDB(db, opts...)
	_mPermissionDatum.mPermissionDatumDo.UseModel(&model.MPermissionDatum{})

	tableName := _mPermissionDatum.mPermissionDatumDo.TableName()
	_mPermissionDatum.ALL = field.NewAsterisk(tableName)
	_mPermissionDatum.ID = field.NewInt32(tableName, "id")
	_mPermissionDatum.UserID = field.NewString(tableName, "user_id")
	_mPermissionDatum.EntityID = field.NewString(tableName, "entity_id")
	_mPermissionDatum.RoleID = field.NewString(tableName, "role_id")
	_mPermissionDatum.Type = field.NewInt32(tableName, "type")
	_mPermissionDatum.CreatedAt = field.NewInt64(tableName, "created_at")
	_mPermissionDatum.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mPermissionDatum.fillFieldMap()

	return _mPermissionDatum
}

// mPermissionDatum 数据权限表
type mPermissionDatum struct {
	mPermissionDatumDo

	ALL       field.Asterisk
	ID        field.Int32
	UserID    field.String // 用户ID
	EntityID  field.String // 实体ID(game_id)
	RoleID    field.String // 角色id数组
	Type      field.Int32  // 权限类型: 1=读取 2=编辑 3=管理
	CreatedAt field.Int64
	UpdatedAt field.Int64

	fieldMap map[string]field.Expr
}

func (m mPermissionDatum) Table(newTableName string) *mPermissionDatum {
	m.mPermissionDatumDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mPermissionDatum) As(alias string) *mPermissionDatum {
	m.mPermissionDatumDo.DO = *(m.mPermissionDatumDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mPermissionDatum) updateTableName(table string) *mPermissionDatum {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.UserID = field.NewString(table, "user_id")
	m.EntityID = field.NewString(table, "entity_id")
	m.RoleID = field.NewString(table, "role_id")
	m.Type = field.NewInt32(table, "type")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mPermissionDatum) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mPermissionDatum) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 7)
	m.fieldMap["id"] = m.ID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["entity_id"] = m.EntityID
	m.fieldMap["role_id"] = m.RoleID
	m.fieldMap["type"] = m.Type
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mPermissionDatum) clone(db *gorm.DB) mPermissionDatum {
	m.mPermissionDatumDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mPermissionDatum) replaceDB(db *gorm.DB) mPermissionDatum {
	m.mPermissionDatumDo.ReplaceDB(db)
	return m
}

type mPermissionDatumDo struct{ gen.DO }

type IMPermissionDatumDo interface {
	gen.SubQuery
	Debug() IMPermissionDatumDo
	WithContext(ctx context.Context) IMPermissionDatumDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMPermissionDatumDo
	WriteDB() IMPermissionDatumDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMPermissionDatumDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMPermissionDatumDo
	Not(conds ...gen.Condition) IMPermissionDatumDo
	Or(conds ...gen.Condition) IMPermissionDatumDo
	Select(conds ...field.Expr) IMPermissionDatumDo
	Where(conds ...gen.Condition) IMPermissionDatumDo
	Order(conds ...field.Expr) IMPermissionDatumDo
	Distinct(cols ...field.Expr) IMPermissionDatumDo
	Omit(cols ...field.Expr) IMPermissionDatumDo
	Join(table schema.Tabler, on ...field.Expr) IMPermissionDatumDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMPermissionDatumDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMPermissionDatumDo
	Group(cols ...field.Expr) IMPermissionDatumDo
	Having(conds ...gen.Condition) IMPermissionDatumDo
	Limit(limit int) IMPermissionDatumDo
	Offset(offset int) IMPermissionDatumDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMPermissionDatumDo
	Unscoped() IMPermissionDatumDo
	Create(values ...*model.MPermissionDatum) error
	CreateInBatches(values []*model.MPermissionDatum, batchSize int) error
	Save(values ...*model.MPermissionDatum) error
	First() (*model.MPermissionDatum, error)
	Take() (*model.MPermissionDatum, error)
	Last() (*model.MPermissionDatum, error)
	Find() ([]*model.MPermissionDatum, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MPermissionDatum, err error)
	FindInBatches(result *[]*model.MPermissionDatum, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MPermissionDatum) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMPermissionDatumDo
	Assign(attrs ...field.AssignExpr) IMPermissionDatumDo
	Joins(fields ...field.RelationField) IMPermissionDatumDo
	Preload(fields ...field.RelationField) IMPermissionDatumDo
	FirstOrInit() (*model.MPermissionDatum, error)
	FirstOrCreate() (*model.MPermissionDatum, error)
	FindByPage(offset int, limit int) (result []*model.MPermissionDatum, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMPermissionDatumDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mPermissionDatumDo) Debug() IMPermissionDatumDo {
	return m.withDO(m.DO.Debug())
}

func (m mPermissionDatumDo) WithContext(ctx context.Context) IMPermissionDatumDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mPermissionDatumDo) ReadDB() IMPermissionDatumDo {
	return m.Clauses(dbresolver.Read)
}

func (m mPermissionDatumDo) WriteDB() IMPermissionDatumDo {
	return m.Clauses(dbresolver.Write)
}

func (m mPermissionDatumDo) Session(config *gorm.Session) IMPermissionDatumDo {
	return m.withDO(m.DO.Session(config))
}

func (m mPermissionDatumDo) Clauses(conds ...clause.Expression) IMPermissionDatumDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mPermissionDatumDo) Returning(value interface{}, columns ...string) IMPermissionDatumDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mPermissionDatumDo) Not(conds ...gen.Condition) IMPermissionDatumDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mPermissionDatumDo) Or(conds ...gen.Condition) IMPermissionDatumDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mPermissionDatumDo) Select(conds ...field.Expr) IMPermissionDatumDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mPermissionDatumDo) Where(conds ...gen.Condition) IMPermissionDatumDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mPermissionDatumDo) Order(conds ...field.Expr) IMPermissionDatumDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mPermissionDatumDo) Distinct(cols ...field.Expr) IMPermissionDatumDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mPermissionDatumDo) Omit(cols ...field.Expr) IMPermissionDatumDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mPermissionDatumDo) Join(table schema.Tabler, on ...field.Expr) IMPermissionDatumDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mPermissionDatumDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMPermissionDatumDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mPermissionDatumDo) RightJoin(table schema.Tabler, on ...field.Expr) IMPermissionDatumDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mPermissionDatumDo) Group(cols ...field.Expr) IMPermissionDatumDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mPermissionDatumDo) Having(conds ...gen.Condition) IMPermissionDatumDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mPermissionDatumDo) Limit(limit int) IMPermissionDatumDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mPermissionDatumDo) Offset(offset int) IMPermissionDatumDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mPermissionDatumDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMPermissionDatumDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mPermissionDatumDo) Unscoped() IMPermissionDatumDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mPermissionDatumDo) Create(values ...*model.MPermissionDatum) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mPermissionDatumDo) CreateInBatches(values []*model.MPermissionDatum, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mPermissionDatumDo) Save(values ...*model.MPermissionDatum) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mPermissionDatumDo) First() (*model.MPermissionDatum, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermissionDatum), nil
	}
}

func (m mPermissionDatumDo) Take() (*model.MPermissionDatum, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermissionDatum), nil
	}
}

func (m mPermissionDatumDo) Last() (*model.MPermissionDatum, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermissionDatum), nil
	}
}

func (m mPermissionDatumDo) Find() ([]*model.MPermissionDatum, error) {
	result, err := m.DO.Find()
	return result.([]*model.MPermissionDatum), err
}

func (m mPermissionDatumDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MPermissionDatum, err error) {
	buf := make([]*model.MPermissionDatum, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mPermissionDatumDo) FindInBatches(result *[]*model.MPermissionDatum, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mPermissionDatumDo) Attrs(attrs ...field.AssignExpr) IMPermissionDatumDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mPermissionDatumDo) Assign(attrs ...field.AssignExpr) IMPermissionDatumDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mPermissionDatumDo) Joins(fields ...field.RelationField) IMPermissionDatumDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mPermissionDatumDo) Preload(fields ...field.RelationField) IMPermissionDatumDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mPermissionDatumDo) FirstOrInit() (*model.MPermissionDatum, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermissionDatum), nil
	}
}

func (m mPermissionDatumDo) FirstOrCreate() (*model.MPermissionDatum, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPermissionDatum), nil
	}
}

func (m mPermissionDatumDo) FindByPage(offset int, limit int) (result []*model.MPermissionDatum, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mPermissionDatumDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mPermissionDatumDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mPermissionDatumDo) Delete(models ...*model.MPermissionDatum) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mPermissionDatumDo) withDO(do gen.Dao) *mPermissionDatumDo {
	m.DO = *do.(*gen.DO)
	return m
}
