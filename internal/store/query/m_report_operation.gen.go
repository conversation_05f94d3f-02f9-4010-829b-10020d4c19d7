// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMReportOperation(db *gorm.DB, opts ...gen.DOOption) mReportOperation {
	_mReportOperation := mReportOperation{}

	_mReportOperation.mReportOperationDo.UseDB(db, opts...)
	_mReportOperation.mReportOperationDo.UseModel(&model.MReportOperation{})

	tableName := _mReportOperation.mReportOperationDo.TableName()
	_mReportOperation.ALL = field.NewAsterisk(tableName)
	_mReportOperation.ID = field.NewInt32(tableName, "id")
	_mReportOperation.ReportsID = field.NewInt32(tableName, "reports_id")
	_mReportOperation.Action = field.NewInt32(tableName, "action")
	_mReportOperation.CreatorID = field.NewString(tableName, "creator_id")
	_mReportOperation.ReportedPlatformID = field.NewString(tableName, "reported_platform_id")
	_mReportOperation.ReportedPlayerID = field.NewString(tableName, "reported_player_id")
	_mReportOperation.ActionParam = field.NewString(tableName, "action_param")
	_mReportOperation.HandleTimeAt = field.NewInt64(tableName, "handle_time_at")
	_mReportOperation.CallbackStatus = field.NewInt32(tableName, "callback_status")
	_mReportOperation.RecordDescription = field.NewString(tableName, "record_description")
	_mReportOperation.CreatedAt = field.NewInt64(tableName, "created_at")
	_mReportOperation.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mReportOperation.fillFieldMap()

	return _mReportOperation
}

// mReportOperation 举报处理记录表
type mReportOperation struct {
	mReportOperationDo

	ALL                field.Asterisk
	ID                 field.Int32  // 主键ID
	ReportsID          field.Int32  // 关联的举报ID
	Action             field.Int32  // 处理动作 1:禁言 2:警告 3:封号 4:封账号
	CreatorID          field.String // 处理人ID
	ReportedPlatformID field.String // 被处理人平台ID
	ReportedPlayerID   field.String // 被处理人玩家ID
	ActionParam        field.String // 处理参数(JSON格式)
	HandleTimeAt       field.Int64  // 处理时间
	CallbackStatus     field.Int32  // 0 初始状态 1 回调成功 2 回调失败
	RecordDescription  field.String // 处理描述
	CreatedAt          field.Int64  // 创建时间
	UpdatedAt          field.Int64  // 更新时间

	fieldMap map[string]field.Expr
}

func (m mReportOperation) Table(newTableName string) *mReportOperation {
	m.mReportOperationDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mReportOperation) As(alias string) *mReportOperation {
	m.mReportOperationDo.DO = *(m.mReportOperationDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mReportOperation) updateTableName(table string) *mReportOperation {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.ReportsID = field.NewInt32(table, "reports_id")
	m.Action = field.NewInt32(table, "action")
	m.CreatorID = field.NewString(table, "creator_id")
	m.ReportedPlatformID = field.NewString(table, "reported_platform_id")
	m.ReportedPlayerID = field.NewString(table, "reported_player_id")
	m.ActionParam = field.NewString(table, "action_param")
	m.HandleTimeAt = field.NewInt64(table, "handle_time_at")
	m.CallbackStatus = field.NewInt32(table, "callback_status")
	m.RecordDescription = field.NewString(table, "record_description")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mReportOperation) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mReportOperation) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 12)
	m.fieldMap["id"] = m.ID
	m.fieldMap["reports_id"] = m.ReportsID
	m.fieldMap["action"] = m.Action
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["reported_platform_id"] = m.ReportedPlatformID
	m.fieldMap["reported_player_id"] = m.ReportedPlayerID
	m.fieldMap["action_param"] = m.ActionParam
	m.fieldMap["handle_time_at"] = m.HandleTimeAt
	m.fieldMap["callback_status"] = m.CallbackStatus
	m.fieldMap["record_description"] = m.RecordDescription
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mReportOperation) clone(db *gorm.DB) mReportOperation {
	m.mReportOperationDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mReportOperation) replaceDB(db *gorm.DB) mReportOperation {
	m.mReportOperationDo.ReplaceDB(db)
	return m
}

type mReportOperationDo struct{ gen.DO }

type IMReportOperationDo interface {
	gen.SubQuery
	Debug() IMReportOperationDo
	WithContext(ctx context.Context) IMReportOperationDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMReportOperationDo
	WriteDB() IMReportOperationDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMReportOperationDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMReportOperationDo
	Not(conds ...gen.Condition) IMReportOperationDo
	Or(conds ...gen.Condition) IMReportOperationDo
	Select(conds ...field.Expr) IMReportOperationDo
	Where(conds ...gen.Condition) IMReportOperationDo
	Order(conds ...field.Expr) IMReportOperationDo
	Distinct(cols ...field.Expr) IMReportOperationDo
	Omit(cols ...field.Expr) IMReportOperationDo
	Join(table schema.Tabler, on ...field.Expr) IMReportOperationDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMReportOperationDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMReportOperationDo
	Group(cols ...field.Expr) IMReportOperationDo
	Having(conds ...gen.Condition) IMReportOperationDo
	Limit(limit int) IMReportOperationDo
	Offset(offset int) IMReportOperationDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMReportOperationDo
	Unscoped() IMReportOperationDo
	Create(values ...*model.MReportOperation) error
	CreateInBatches(values []*model.MReportOperation, batchSize int) error
	Save(values ...*model.MReportOperation) error
	First() (*model.MReportOperation, error)
	Take() (*model.MReportOperation, error)
	Last() (*model.MReportOperation, error)
	Find() ([]*model.MReportOperation, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MReportOperation, err error)
	FindInBatches(result *[]*model.MReportOperation, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MReportOperation) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMReportOperationDo
	Assign(attrs ...field.AssignExpr) IMReportOperationDo
	Joins(fields ...field.RelationField) IMReportOperationDo
	Preload(fields ...field.RelationField) IMReportOperationDo
	FirstOrInit() (*model.MReportOperation, error)
	FirstOrCreate() (*model.MReportOperation, error)
	FindByPage(offset int, limit int) (result []*model.MReportOperation, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMReportOperationDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mReportOperationDo) Debug() IMReportOperationDo {
	return m.withDO(m.DO.Debug())
}

func (m mReportOperationDo) WithContext(ctx context.Context) IMReportOperationDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mReportOperationDo) ReadDB() IMReportOperationDo {
	return m.Clauses(dbresolver.Read)
}

func (m mReportOperationDo) WriteDB() IMReportOperationDo {
	return m.Clauses(dbresolver.Write)
}

func (m mReportOperationDo) Session(config *gorm.Session) IMReportOperationDo {
	return m.withDO(m.DO.Session(config))
}

func (m mReportOperationDo) Clauses(conds ...clause.Expression) IMReportOperationDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mReportOperationDo) Returning(value interface{}, columns ...string) IMReportOperationDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mReportOperationDo) Not(conds ...gen.Condition) IMReportOperationDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mReportOperationDo) Or(conds ...gen.Condition) IMReportOperationDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mReportOperationDo) Select(conds ...field.Expr) IMReportOperationDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mReportOperationDo) Where(conds ...gen.Condition) IMReportOperationDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mReportOperationDo) Order(conds ...field.Expr) IMReportOperationDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mReportOperationDo) Distinct(cols ...field.Expr) IMReportOperationDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mReportOperationDo) Omit(cols ...field.Expr) IMReportOperationDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mReportOperationDo) Join(table schema.Tabler, on ...field.Expr) IMReportOperationDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mReportOperationDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMReportOperationDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mReportOperationDo) RightJoin(table schema.Tabler, on ...field.Expr) IMReportOperationDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mReportOperationDo) Group(cols ...field.Expr) IMReportOperationDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mReportOperationDo) Having(conds ...gen.Condition) IMReportOperationDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mReportOperationDo) Limit(limit int) IMReportOperationDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mReportOperationDo) Offset(offset int) IMReportOperationDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mReportOperationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMReportOperationDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mReportOperationDo) Unscoped() IMReportOperationDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mReportOperationDo) Create(values ...*model.MReportOperation) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mReportOperationDo) CreateInBatches(values []*model.MReportOperation, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mReportOperationDo) Save(values ...*model.MReportOperation) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mReportOperationDo) First() (*model.MReportOperation, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportOperation), nil
	}
}

func (m mReportOperationDo) Take() (*model.MReportOperation, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportOperation), nil
	}
}

func (m mReportOperationDo) Last() (*model.MReportOperation, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportOperation), nil
	}
}

func (m mReportOperationDo) Find() ([]*model.MReportOperation, error) {
	result, err := m.DO.Find()
	return result.([]*model.MReportOperation), err
}

func (m mReportOperationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MReportOperation, err error) {
	buf := make([]*model.MReportOperation, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mReportOperationDo) FindInBatches(result *[]*model.MReportOperation, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mReportOperationDo) Attrs(attrs ...field.AssignExpr) IMReportOperationDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mReportOperationDo) Assign(attrs ...field.AssignExpr) IMReportOperationDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mReportOperationDo) Joins(fields ...field.RelationField) IMReportOperationDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mReportOperationDo) Preload(fields ...field.RelationField) IMReportOperationDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mReportOperationDo) FirstOrInit() (*model.MReportOperation, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportOperation), nil
	}
}

func (m mReportOperationDo) FirstOrCreate() (*model.MReportOperation, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportOperation), nil
	}
}

func (m mReportOperationDo) FindByPage(offset int, limit int) (result []*model.MReportOperation, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mReportOperationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mReportOperationDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mReportOperationDo) Delete(models ...*model.MReportOperation) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mReportOperationDo) withDO(do gen.Dao) *mReportOperationDo {
	m.DO = *do.(*gen.DO)
	return m
}
