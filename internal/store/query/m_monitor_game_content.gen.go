// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMMonitorGameContent(db *gorm.DB, opts ...gen.DOOption) mMonitorGameContent {
	_mMonitorGameContent := mMonitorGameContent{}

	_mMonitorGameContent.mMonitorGameContentDo.UseDB(db, opts...)
	_mMonitorGameContent.mMonitorGameContentDo.UseModel(&model.MMonitorGameContent{})

	tableName := _mMonitorGameContent.mMonitorGameContentDo.TableName()
	_mMonitorGameContent.ALL = field.NewAsterisk(tableName)
	_mMonitorGameContent.ID = field.NewInt32(tableName, "id")
	_mMonitorGameContent.GameID = field.NewString(tableName, "game_id")
	_mMonitorGameContent.ContentID = field.NewString(tableName, "content_id")
	_mMonitorGameContent.UserID = field.NewString(tableName, "user_id")
	_mMonitorGameContent.SessionFrom = field.NewString(tableName, "session_from")
	_mMonitorGameContent.SourceType = field.NewString(tableName, "source_type")
	_mMonitorGameContent.ServerID = field.NewString(tableName, "server_id")
	_mMonitorGameContent.ServerName = field.NewString(tableName, "server_name")
	_mMonitorGameContent.RoleID = field.NewString(tableName, "role_id")
	_mMonitorGameContent.RoleName = field.NewString(tableName, "role_name")
	_mMonitorGameContent.RoleLevel = field.NewInt32(tableName, "role_level")
	_mMonitorGameContent.AllianceID = field.NewString(tableName, "alliance_id")
	_mMonitorGameContent.AllianceName = field.NewString(tableName, "alliance_name")
	_mMonitorGameContent.IsAllianceLeader = field.NewInt32(tableName, "is_alliance_leader")
	_mMonitorGameContent.Status = field.NewInt32(tableName, "status")
	_mMonitorGameContent.Content = field.NewString(tableName, "content")
	_mMonitorGameContent.ExpireAt = field.NewInt64(tableName, "expire_at")
	_mMonitorGameContent.CreatorID = field.NewString(tableName, "creator_id")
	_mMonitorGameContent.CreatedAt = field.NewInt64(tableName, "created_at")
	_mMonitorGameContent.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mMonitorGameContent.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mMonitorGameContent.fillFieldMap()

	return _mMonitorGameContent
}

// mMonitorGameContent 游戏内容监控表
type mMonitorGameContent struct {
	mMonitorGameContentDo

	ALL              field.Asterisk
	ID               field.Int32  // 主键ID
	GameID           field.String // 游戏ID
	ContentID        field.String // 内容唯一标识
	UserID           field.String // 平台用户ID
	SessionFrom      field.String // 透传参数
	SourceType       field.String // 文本来源类型
	ServerID         field.String // 区服ID
	ServerName       field.String // 区服名称
	RoleID           field.String // 角色ID
	RoleName         field.String // 角色名称
	RoleLevel        field.Int32  // 角色等级
	AllianceID       field.String // 公会ID
	AllianceName     field.String // 公会名称
	IsAllianceLeader field.Int32  // 是否公会长
	Status           field.Int32  // 0: 待处理,1: 已处理
	Content          field.String // 内容文本
	ExpireAt         field.Int64  // 过期时间戳
	CreatorID        field.String // 创建人id
	CreatedAt        field.Int64  // 创建时间戳
	UpdatedAt        field.Int64  // 更新时间戳
	IsDeleted        field.Bool   // 是否删除

	fieldMap map[string]field.Expr
}

func (m mMonitorGameContent) Table(newTableName string) *mMonitorGameContent {
	m.mMonitorGameContentDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mMonitorGameContent) As(alias string) *mMonitorGameContent {
	m.mMonitorGameContentDo.DO = *(m.mMonitorGameContentDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mMonitorGameContent) updateTableName(table string) *mMonitorGameContent {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.ContentID = field.NewString(table, "content_id")
	m.UserID = field.NewString(table, "user_id")
	m.SessionFrom = field.NewString(table, "session_from")
	m.SourceType = field.NewString(table, "source_type")
	m.ServerID = field.NewString(table, "server_id")
	m.ServerName = field.NewString(table, "server_name")
	m.RoleID = field.NewString(table, "role_id")
	m.RoleName = field.NewString(table, "role_name")
	m.RoleLevel = field.NewInt32(table, "role_level")
	m.AllianceID = field.NewString(table, "alliance_id")
	m.AllianceName = field.NewString(table, "alliance_name")
	m.IsAllianceLeader = field.NewInt32(table, "is_alliance_leader")
	m.Status = field.NewInt32(table, "status")
	m.Content = field.NewString(table, "content")
	m.ExpireAt = field.NewInt64(table, "expire_at")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mMonitorGameContent) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mMonitorGameContent) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 21)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["content_id"] = m.ContentID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["session_from"] = m.SessionFrom
	m.fieldMap["source_type"] = m.SourceType
	m.fieldMap["server_id"] = m.ServerID
	m.fieldMap["server_name"] = m.ServerName
	m.fieldMap["role_id"] = m.RoleID
	m.fieldMap["role_name"] = m.RoleName
	m.fieldMap["role_level"] = m.RoleLevel
	m.fieldMap["alliance_id"] = m.AllianceID
	m.fieldMap["alliance_name"] = m.AllianceName
	m.fieldMap["is_alliance_leader"] = m.IsAllianceLeader
	m.fieldMap["status"] = m.Status
	m.fieldMap["content"] = m.Content
	m.fieldMap["expire_at"] = m.ExpireAt
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mMonitorGameContent) clone(db *gorm.DB) mMonitorGameContent {
	m.mMonitorGameContentDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mMonitorGameContent) replaceDB(db *gorm.DB) mMonitorGameContent {
	m.mMonitorGameContentDo.ReplaceDB(db)
	return m
}

type mMonitorGameContentDo struct{ gen.DO }

type IMMonitorGameContentDo interface {
	gen.SubQuery
	Debug() IMMonitorGameContentDo
	WithContext(ctx context.Context) IMMonitorGameContentDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMMonitorGameContentDo
	WriteDB() IMMonitorGameContentDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMMonitorGameContentDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMMonitorGameContentDo
	Not(conds ...gen.Condition) IMMonitorGameContentDo
	Or(conds ...gen.Condition) IMMonitorGameContentDo
	Select(conds ...field.Expr) IMMonitorGameContentDo
	Where(conds ...gen.Condition) IMMonitorGameContentDo
	Order(conds ...field.Expr) IMMonitorGameContentDo
	Distinct(cols ...field.Expr) IMMonitorGameContentDo
	Omit(cols ...field.Expr) IMMonitorGameContentDo
	Join(table schema.Tabler, on ...field.Expr) IMMonitorGameContentDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMMonitorGameContentDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMMonitorGameContentDo
	Group(cols ...field.Expr) IMMonitorGameContentDo
	Having(conds ...gen.Condition) IMMonitorGameContentDo
	Limit(limit int) IMMonitorGameContentDo
	Offset(offset int) IMMonitorGameContentDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMMonitorGameContentDo
	Unscoped() IMMonitorGameContentDo
	Create(values ...*model.MMonitorGameContent) error
	CreateInBatches(values []*model.MMonitorGameContent, batchSize int) error
	Save(values ...*model.MMonitorGameContent) error
	First() (*model.MMonitorGameContent, error)
	Take() (*model.MMonitorGameContent, error)
	Last() (*model.MMonitorGameContent, error)
	Find() ([]*model.MMonitorGameContent, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MMonitorGameContent, err error)
	FindInBatches(result *[]*model.MMonitorGameContent, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MMonitorGameContent) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMMonitorGameContentDo
	Assign(attrs ...field.AssignExpr) IMMonitorGameContentDo
	Joins(fields ...field.RelationField) IMMonitorGameContentDo
	Preload(fields ...field.RelationField) IMMonitorGameContentDo
	FirstOrInit() (*model.MMonitorGameContent, error)
	FirstOrCreate() (*model.MMonitorGameContent, error)
	FindByPage(offset int, limit int) (result []*model.MMonitorGameContent, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMMonitorGameContentDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mMonitorGameContentDo) Debug() IMMonitorGameContentDo {
	return m.withDO(m.DO.Debug())
}

func (m mMonitorGameContentDo) WithContext(ctx context.Context) IMMonitorGameContentDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mMonitorGameContentDo) ReadDB() IMMonitorGameContentDo {
	return m.Clauses(dbresolver.Read)
}

func (m mMonitorGameContentDo) WriteDB() IMMonitorGameContentDo {
	return m.Clauses(dbresolver.Write)
}

func (m mMonitorGameContentDo) Session(config *gorm.Session) IMMonitorGameContentDo {
	return m.withDO(m.DO.Session(config))
}

func (m mMonitorGameContentDo) Clauses(conds ...clause.Expression) IMMonitorGameContentDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mMonitorGameContentDo) Returning(value interface{}, columns ...string) IMMonitorGameContentDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mMonitorGameContentDo) Not(conds ...gen.Condition) IMMonitorGameContentDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mMonitorGameContentDo) Or(conds ...gen.Condition) IMMonitorGameContentDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mMonitorGameContentDo) Select(conds ...field.Expr) IMMonitorGameContentDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mMonitorGameContentDo) Where(conds ...gen.Condition) IMMonitorGameContentDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mMonitorGameContentDo) Order(conds ...field.Expr) IMMonitorGameContentDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mMonitorGameContentDo) Distinct(cols ...field.Expr) IMMonitorGameContentDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mMonitorGameContentDo) Omit(cols ...field.Expr) IMMonitorGameContentDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mMonitorGameContentDo) Join(table schema.Tabler, on ...field.Expr) IMMonitorGameContentDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mMonitorGameContentDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMMonitorGameContentDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mMonitorGameContentDo) RightJoin(table schema.Tabler, on ...field.Expr) IMMonitorGameContentDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mMonitorGameContentDo) Group(cols ...field.Expr) IMMonitorGameContentDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mMonitorGameContentDo) Having(conds ...gen.Condition) IMMonitorGameContentDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mMonitorGameContentDo) Limit(limit int) IMMonitorGameContentDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mMonitorGameContentDo) Offset(offset int) IMMonitorGameContentDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mMonitorGameContentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMMonitorGameContentDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mMonitorGameContentDo) Unscoped() IMMonitorGameContentDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mMonitorGameContentDo) Create(values ...*model.MMonitorGameContent) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mMonitorGameContentDo) CreateInBatches(values []*model.MMonitorGameContent, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mMonitorGameContentDo) Save(values ...*model.MMonitorGameContent) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mMonitorGameContentDo) First() (*model.MMonitorGameContent, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorGameContent), nil
	}
}

func (m mMonitorGameContentDo) Take() (*model.MMonitorGameContent, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorGameContent), nil
	}
}

func (m mMonitorGameContentDo) Last() (*model.MMonitorGameContent, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorGameContent), nil
	}
}

func (m mMonitorGameContentDo) Find() ([]*model.MMonitorGameContent, error) {
	result, err := m.DO.Find()
	return result.([]*model.MMonitorGameContent), err
}

func (m mMonitorGameContentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MMonitorGameContent, err error) {
	buf := make([]*model.MMonitorGameContent, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mMonitorGameContentDo) FindInBatches(result *[]*model.MMonitorGameContent, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mMonitorGameContentDo) Attrs(attrs ...field.AssignExpr) IMMonitorGameContentDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mMonitorGameContentDo) Assign(attrs ...field.AssignExpr) IMMonitorGameContentDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mMonitorGameContentDo) Joins(fields ...field.RelationField) IMMonitorGameContentDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mMonitorGameContentDo) Preload(fields ...field.RelationField) IMMonitorGameContentDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mMonitorGameContentDo) FirstOrInit() (*model.MMonitorGameContent, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorGameContent), nil
	}
}

func (m mMonitorGameContentDo) FirstOrCreate() (*model.MMonitorGameContent, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MMonitorGameContent), nil
	}
}

func (m mMonitorGameContentDo) FindByPage(offset int, limit int) (result []*model.MMonitorGameContent, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mMonitorGameContentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mMonitorGameContentDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mMonitorGameContentDo) Delete(models ...*model.MMonitorGameContent) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mMonitorGameContentDo) withDO(do gen.Dao) *mMonitorGameContentDo {
	m.DO = *do.(*gen.DO)
	return m
}
