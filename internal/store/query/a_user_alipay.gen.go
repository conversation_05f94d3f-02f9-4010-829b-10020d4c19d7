// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newAUserAlipay(db *gorm.DB, opts ...gen.DOOption) aUserAlipay {
	_aUserAlipay := aUserAlipay{}

	_aUserAlipay.aUserAlipayDo.UseDB(db, opts...)
	_aUserAlipay.aUserAlipayDo.UseModel(&model.AUserAlipay{})

	tableName := _aUserAlipay.aUserAlipayDo.TableName()
	_aUserAlipay.ALL = field.NewAsterisk(tableName)
	_aUserAlipay.ID = field.NewInt32(tableName, "id")
	_aUserAlipay.UserID = field.NewString(tableName, "user_id")
	_aUserAlipay.OpenID = field.NewString(tableName, "open_id")
	_aUserAlipay.NickName = field.NewString(tableName, "nick_name")
	_aUserAlipay.AvatarURL = field.NewString(tableName, "avatar_url")
	_aUserAlipay.Gender = field.NewInt32(tableName, "gender")
	_aUserAlipay.City = field.NewString(tableName, "city")
	_aUserAlipay.Province = field.NewString(tableName, "province")
	_aUserAlipay.Country = field.NewString(tableName, "country")
	_aUserAlipay.CreatedAt = field.NewInt64(tableName, "created_at")
	_aUserAlipay.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aUserAlipay.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aUserAlipay.fillFieldMap()

	return _aUserAlipay
}

// aUserAlipay 支付宝用户信息表
type aUserAlipay struct {
	aUserAlipayDo

	ALL       field.Asterisk
	ID        field.Int32
	UserID    field.String
	OpenID    field.String
	NickName  field.String
	AvatarURL field.String
	Gender    field.Int32
	City      field.String
	Province  field.String
	Country   field.String
	CreatedAt field.Int64
	UpdatedAt field.Int64
	IsDeleted field.Bool

	fieldMap map[string]field.Expr
}

func (a aUserAlipay) Table(newTableName string) *aUserAlipay {
	a.aUserAlipayDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aUserAlipay) As(alias string) *aUserAlipay {
	a.aUserAlipayDo.DO = *(a.aUserAlipayDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aUserAlipay) updateTableName(table string) *aUserAlipay {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.UserID = field.NewString(table, "user_id")
	a.OpenID = field.NewString(table, "open_id")
	a.NickName = field.NewString(table, "nick_name")
	a.AvatarURL = field.NewString(table, "avatar_url")
	a.Gender = field.NewInt32(table, "gender")
	a.City = field.NewString(table, "city")
	a.Province = field.NewString(table, "province")
	a.Country = field.NewString(table, "country")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aUserAlipay) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aUserAlipay) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 12)
	a.fieldMap["id"] = a.ID
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["open_id"] = a.OpenID
	a.fieldMap["nick_name"] = a.NickName
	a.fieldMap["avatar_url"] = a.AvatarURL
	a.fieldMap["gender"] = a.Gender
	a.fieldMap["city"] = a.City
	a.fieldMap["province"] = a.Province
	a.fieldMap["country"] = a.Country
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aUserAlipay) clone(db *gorm.DB) aUserAlipay {
	a.aUserAlipayDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aUserAlipay) replaceDB(db *gorm.DB) aUserAlipay {
	a.aUserAlipayDo.ReplaceDB(db)
	return a
}

type aUserAlipayDo struct{ gen.DO }

type IAUserAlipayDo interface {
	gen.SubQuery
	Debug() IAUserAlipayDo
	WithContext(ctx context.Context) IAUserAlipayDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAUserAlipayDo
	WriteDB() IAUserAlipayDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAUserAlipayDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAUserAlipayDo
	Not(conds ...gen.Condition) IAUserAlipayDo
	Or(conds ...gen.Condition) IAUserAlipayDo
	Select(conds ...field.Expr) IAUserAlipayDo
	Where(conds ...gen.Condition) IAUserAlipayDo
	Order(conds ...field.Expr) IAUserAlipayDo
	Distinct(cols ...field.Expr) IAUserAlipayDo
	Omit(cols ...field.Expr) IAUserAlipayDo
	Join(table schema.Tabler, on ...field.Expr) IAUserAlipayDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAUserAlipayDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAUserAlipayDo
	Group(cols ...field.Expr) IAUserAlipayDo
	Having(conds ...gen.Condition) IAUserAlipayDo
	Limit(limit int) IAUserAlipayDo
	Offset(offset int) IAUserAlipayDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserAlipayDo
	Unscoped() IAUserAlipayDo
	Create(values ...*model.AUserAlipay) error
	CreateInBatches(values []*model.AUserAlipay, batchSize int) error
	Save(values ...*model.AUserAlipay) error
	First() (*model.AUserAlipay, error)
	Take() (*model.AUserAlipay, error)
	Last() (*model.AUserAlipay, error)
	Find() ([]*model.AUserAlipay, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUserAlipay, err error)
	FindInBatches(result *[]*model.AUserAlipay, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AUserAlipay) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAUserAlipayDo
	Assign(attrs ...field.AssignExpr) IAUserAlipayDo
	Joins(fields ...field.RelationField) IAUserAlipayDo
	Preload(fields ...field.RelationField) IAUserAlipayDo
	FirstOrInit() (*model.AUserAlipay, error)
	FirstOrCreate() (*model.AUserAlipay, error)
	FindByPage(offset int, limit int) (result []*model.AUserAlipay, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAUserAlipayDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aUserAlipayDo) Debug() IAUserAlipayDo {
	return a.withDO(a.DO.Debug())
}

func (a aUserAlipayDo) WithContext(ctx context.Context) IAUserAlipayDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aUserAlipayDo) ReadDB() IAUserAlipayDo {
	return a.Clauses(dbresolver.Read)
}

func (a aUserAlipayDo) WriteDB() IAUserAlipayDo {
	return a.Clauses(dbresolver.Write)
}

func (a aUserAlipayDo) Session(config *gorm.Session) IAUserAlipayDo {
	return a.withDO(a.DO.Session(config))
}

func (a aUserAlipayDo) Clauses(conds ...clause.Expression) IAUserAlipayDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aUserAlipayDo) Returning(value interface{}, columns ...string) IAUserAlipayDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aUserAlipayDo) Not(conds ...gen.Condition) IAUserAlipayDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aUserAlipayDo) Or(conds ...gen.Condition) IAUserAlipayDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aUserAlipayDo) Select(conds ...field.Expr) IAUserAlipayDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aUserAlipayDo) Where(conds ...gen.Condition) IAUserAlipayDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aUserAlipayDo) Order(conds ...field.Expr) IAUserAlipayDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aUserAlipayDo) Distinct(cols ...field.Expr) IAUserAlipayDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aUserAlipayDo) Omit(cols ...field.Expr) IAUserAlipayDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aUserAlipayDo) Join(table schema.Tabler, on ...field.Expr) IAUserAlipayDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aUserAlipayDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAUserAlipayDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aUserAlipayDo) RightJoin(table schema.Tabler, on ...field.Expr) IAUserAlipayDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aUserAlipayDo) Group(cols ...field.Expr) IAUserAlipayDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aUserAlipayDo) Having(conds ...gen.Condition) IAUserAlipayDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aUserAlipayDo) Limit(limit int) IAUserAlipayDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aUserAlipayDo) Offset(offset int) IAUserAlipayDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aUserAlipayDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserAlipayDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aUserAlipayDo) Unscoped() IAUserAlipayDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aUserAlipayDo) Create(values ...*model.AUserAlipay) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aUserAlipayDo) CreateInBatches(values []*model.AUserAlipay, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aUserAlipayDo) Save(values ...*model.AUserAlipay) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aUserAlipayDo) First() (*model.AUserAlipay, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserAlipay), nil
	}
}

func (a aUserAlipayDo) Take() (*model.AUserAlipay, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserAlipay), nil
	}
}

func (a aUserAlipayDo) Last() (*model.AUserAlipay, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserAlipay), nil
	}
}

func (a aUserAlipayDo) Find() ([]*model.AUserAlipay, error) {
	result, err := a.DO.Find()
	return result.([]*model.AUserAlipay), err
}

func (a aUserAlipayDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUserAlipay, err error) {
	buf := make([]*model.AUserAlipay, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aUserAlipayDo) FindInBatches(result *[]*model.AUserAlipay, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aUserAlipayDo) Attrs(attrs ...field.AssignExpr) IAUserAlipayDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aUserAlipayDo) Assign(attrs ...field.AssignExpr) IAUserAlipayDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aUserAlipayDo) Joins(fields ...field.RelationField) IAUserAlipayDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aUserAlipayDo) Preload(fields ...field.RelationField) IAUserAlipayDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aUserAlipayDo) FirstOrInit() (*model.AUserAlipay, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserAlipay), nil
	}
}

func (a aUserAlipayDo) FirstOrCreate() (*model.AUserAlipay, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserAlipay), nil
	}
}

func (a aUserAlipayDo) FindByPage(offset int, limit int) (result []*model.AUserAlipay, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aUserAlipayDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aUserAlipayDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aUserAlipayDo) Delete(models ...*model.AUserAlipay) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aUserAlipayDo) withDO(do gen.Dao) *aUserAlipayDo {
	a.DO = *do.(*gen.DO)
	return a
}
