// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMGameServer(db *gorm.DB, opts ...gen.DOOption) mGameServer {
	_mGameServer := mGameServer{}

	_mGameServer.mGameServerDo.UseDB(db, opts...)
	_mGameServer.mGameServerDo.UseModel(&model.MGameServer{})

	tableName := _mGameServer.mGameServerDo.TableName()
	_mGameServer.ALL = field.NewAsterisk(tableName)
	_mGameServer.ID = field.NewInt32(tableName, "id")
	_mGameServer.ServerID = field.NewString(tableName, "server_id")
	_mGameServer.ServerName = field.NewString(tableName, "server_name")
	_mGameServer.PlatformID = field.NewString(tableName, "platform_id")
	_mGameServer.CreatedAt = field.NewInt64(tableName, "created_at")
	_mGameServer.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mGameServer.fillFieldMap()

	return _mGameServer
}

// mGameServer 游戏区服信息表
type mGameServer struct {
	mGameServerDo

	ALL        field.Asterisk
	ID         field.Int32  // 主键ID
	ServerID   field.String // 区服ID
	ServerName field.String // 区服名称
	PlatformID field.String // 平台ID
	CreatedAt  field.Int64  // 创建时间戳
	UpdatedAt  field.Int64  // 更新时间戳

	fieldMap map[string]field.Expr
}

func (m mGameServer) Table(newTableName string) *mGameServer {
	m.mGameServerDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mGameServer) As(alias string) *mGameServer {
	m.mGameServerDo.DO = *(m.mGameServerDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mGameServer) updateTableName(table string) *mGameServer {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.ServerID = field.NewString(table, "server_id")
	m.ServerName = field.NewString(table, "server_name")
	m.PlatformID = field.NewString(table, "platform_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mGameServer) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mGameServer) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 6)
	m.fieldMap["id"] = m.ID
	m.fieldMap["server_id"] = m.ServerID
	m.fieldMap["server_name"] = m.ServerName
	m.fieldMap["platform_id"] = m.PlatformID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mGameServer) clone(db *gorm.DB) mGameServer {
	m.mGameServerDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mGameServer) replaceDB(db *gorm.DB) mGameServer {
	m.mGameServerDo.ReplaceDB(db)
	return m
}

type mGameServerDo struct{ gen.DO }

type IMGameServerDo interface {
	gen.SubQuery
	Debug() IMGameServerDo
	WithContext(ctx context.Context) IMGameServerDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMGameServerDo
	WriteDB() IMGameServerDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMGameServerDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMGameServerDo
	Not(conds ...gen.Condition) IMGameServerDo
	Or(conds ...gen.Condition) IMGameServerDo
	Select(conds ...field.Expr) IMGameServerDo
	Where(conds ...gen.Condition) IMGameServerDo
	Order(conds ...field.Expr) IMGameServerDo
	Distinct(cols ...field.Expr) IMGameServerDo
	Omit(cols ...field.Expr) IMGameServerDo
	Join(table schema.Tabler, on ...field.Expr) IMGameServerDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMGameServerDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMGameServerDo
	Group(cols ...field.Expr) IMGameServerDo
	Having(conds ...gen.Condition) IMGameServerDo
	Limit(limit int) IMGameServerDo
	Offset(offset int) IMGameServerDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMGameServerDo
	Unscoped() IMGameServerDo
	Create(values ...*model.MGameServer) error
	CreateInBatches(values []*model.MGameServer, batchSize int) error
	Save(values ...*model.MGameServer) error
	First() (*model.MGameServer, error)
	Take() (*model.MGameServer, error)
	Last() (*model.MGameServer, error)
	Find() ([]*model.MGameServer, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MGameServer, err error)
	FindInBatches(result *[]*model.MGameServer, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MGameServer) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMGameServerDo
	Assign(attrs ...field.AssignExpr) IMGameServerDo
	Joins(fields ...field.RelationField) IMGameServerDo
	Preload(fields ...field.RelationField) IMGameServerDo
	FirstOrInit() (*model.MGameServer, error)
	FirstOrCreate() (*model.MGameServer, error)
	FindByPage(offset int, limit int) (result []*model.MGameServer, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMGameServerDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mGameServerDo) Debug() IMGameServerDo {
	return m.withDO(m.DO.Debug())
}

func (m mGameServerDo) WithContext(ctx context.Context) IMGameServerDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mGameServerDo) ReadDB() IMGameServerDo {
	return m.Clauses(dbresolver.Read)
}

func (m mGameServerDo) WriteDB() IMGameServerDo {
	return m.Clauses(dbresolver.Write)
}

func (m mGameServerDo) Session(config *gorm.Session) IMGameServerDo {
	return m.withDO(m.DO.Session(config))
}

func (m mGameServerDo) Clauses(conds ...clause.Expression) IMGameServerDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mGameServerDo) Returning(value interface{}, columns ...string) IMGameServerDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mGameServerDo) Not(conds ...gen.Condition) IMGameServerDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mGameServerDo) Or(conds ...gen.Condition) IMGameServerDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mGameServerDo) Select(conds ...field.Expr) IMGameServerDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mGameServerDo) Where(conds ...gen.Condition) IMGameServerDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mGameServerDo) Order(conds ...field.Expr) IMGameServerDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mGameServerDo) Distinct(cols ...field.Expr) IMGameServerDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mGameServerDo) Omit(cols ...field.Expr) IMGameServerDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mGameServerDo) Join(table schema.Tabler, on ...field.Expr) IMGameServerDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mGameServerDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMGameServerDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mGameServerDo) RightJoin(table schema.Tabler, on ...field.Expr) IMGameServerDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mGameServerDo) Group(cols ...field.Expr) IMGameServerDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mGameServerDo) Having(conds ...gen.Condition) IMGameServerDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mGameServerDo) Limit(limit int) IMGameServerDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mGameServerDo) Offset(offset int) IMGameServerDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mGameServerDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMGameServerDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mGameServerDo) Unscoped() IMGameServerDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mGameServerDo) Create(values ...*model.MGameServer) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mGameServerDo) CreateInBatches(values []*model.MGameServer, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mGameServerDo) Save(values ...*model.MGameServer) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mGameServerDo) First() (*model.MGameServer, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGameServer), nil
	}
}

func (m mGameServerDo) Take() (*model.MGameServer, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGameServer), nil
	}
}

func (m mGameServerDo) Last() (*model.MGameServer, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGameServer), nil
	}
}

func (m mGameServerDo) Find() ([]*model.MGameServer, error) {
	result, err := m.DO.Find()
	return result.([]*model.MGameServer), err
}

func (m mGameServerDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MGameServer, err error) {
	buf := make([]*model.MGameServer, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mGameServerDo) FindInBatches(result *[]*model.MGameServer, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mGameServerDo) Attrs(attrs ...field.AssignExpr) IMGameServerDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mGameServerDo) Assign(attrs ...field.AssignExpr) IMGameServerDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mGameServerDo) Joins(fields ...field.RelationField) IMGameServerDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mGameServerDo) Preload(fields ...field.RelationField) IMGameServerDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mGameServerDo) FirstOrInit() (*model.MGameServer, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGameServer), nil
	}
}

func (m mGameServerDo) FirstOrCreate() (*model.MGameServer, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MGameServer), nil
	}
}

func (m mGameServerDo) FindByPage(offset int, limit int) (result []*model.MGameServer, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mGameServerDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mGameServerDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mGameServerDo) Delete(models ...*model.MGameServer) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mGameServerDo) withDO(do gen.Dao) *mGameServerDo {
	m.DO = *do.(*gen.DO)
	return m
}
