// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMPkgTask(db *gorm.DB, opts ...gen.DOOption) mPkgTask {
	_mPkgTask := mPkgTask{}

	_mPkgTask.mPkgTaskDo.UseDB(db, opts...)
	_mPkgTask.mPkgTaskDo.UseModel(&model.MPkgTask{})

	tableName := _mPkgTask.mPkgTaskDo.TableName()
	_mPkgTask.ALL = field.NewAsterisk(tableName)
	_mPkgTask.ID = field.NewInt64(tableName, "id")
	_mPkgTask.GameNameZh = field.NewString(tableName, "game_name_zh")
	_mPkgTask.GameNameEn = field.NewString(tableName, "game_name_en")
	_mPkgTask.GameURL = field.NewString(tableName, "game_url")
	_mPkgTask.GameVersion = field.NewString(tableName, "game_version")
	_mPkgTask.VersionTextColor = field.NewString(tableName, "version_text_color")
	_mPkgTask.VersionText = field.NewString(tableName, "version_text")
	_mPkgTask.GameOrientation = field.NewInt32(tableName, "game_orientation")
	_mPkgTask.GameIcon = field.NewString(tableName, "game_icon")
	_mPkgTask.LaunchBg = field.NewString(tableName, "launch_bg")
	_mPkgTask.LaunchPopupText = field.NewString(tableName, "launch_popup_text")
	_mPkgTask.AgeRating = field.NewInt32(tableName, "age_rating")
	_mPkgTask.AgeRatingPosition = field.NewInt32(tableName, "age_rating_position")
	_mPkgTask.AgeRatingDesc = field.NewString(tableName, "age_rating_desc")
	_mPkgTask.AppropriateAge = field.NewInt32(tableName, "appropriate_age")
	_mPkgTask.ShowSplashDialog = field.NewInt32(tableName, "show_splash_dialog")
	_mPkgTask.LoadLocalWeb = field.NewString(tableName, "load_local_web")
	_mPkgTask.SplashTips = field.NewString(tableName, "splash_tips")
	_mPkgTask.AllowRegister = field.NewInt32(tableName, "allow_register")
	_mPkgTask.MinorPlayTimeType = field.NewInt32(tableName, "minor_play_time_type")
	_mPkgTask.MinorPlayTimeConfig = field.NewString(tableName, "minor_play_time_config")
	_mPkgTask.DownloadURL = field.NewString(tableName, "download_url")
	_mPkgTask.Status = field.NewInt32(tableName, "status")
	_mPkgTask.CreatorID = field.NewString(tableName, "creator_id")
	_mPkgTask.CreatedAt = field.NewInt64(tableName, "created_at")
	_mPkgTask.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mPkgTask.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mPkgTask.fillFieldMap()

	return _mPkgTask
}

type mPkgTask struct {
	mPkgTaskDo

	ALL                 field.Asterisk
	ID                  field.Int64
	GameNameZh          field.String // 游戏中文名
	GameNameEn          field.String // 游戏英文名
	GameURL             field.String // 游戏URL
	GameVersion         field.String // 游戏版本号
	VersionTextColor    field.String // 版本号颜色
	VersionText         field.String // 版本号文案
	GameOrientation     field.Int32  // 游戏方向(1:竖向,2:横向)
	GameIcon            field.String // 游戏图标路径
	LaunchBg            field.String // 启动背景图路径
	LaunchPopupText     field.String // 开屏弹窗文案
	AgeRating           field.Int32  // 适龄提示年龄(1:8+,2:12+,3:16+)
	AgeRatingPosition   field.Int32  // 适龄提示位置(0左上 1右上 2左下 3右下)
	AgeRatingDesc       field.String // 适龄说明文案
	AppropriateAge      field.Int32  // 适龄年龄
	ShowSplashDialog    field.Int32  // 开屏弹窗开关
	LoadLocalWeb        field.String // 是否加载本地Web
	SplashTips          field.String // 开屏防沉迷提示文案
	AllowRegister       field.Int32  // 是否开放注册(0:不开放,1:开放)
	MinorPlayTimeType   field.Int32  // 未成年可游玩时间类型(1:默认,2:自定义)
	MinorPlayTimeConfig field.String // 未成年可游玩时间配置(JSON格式)
	DownloadURL         field.String // 下载地址
	Status              field.Int32  // 状态(0:待打包,1:打包中,2:打包完成)
	CreatorID           field.String // 创建人ID
	CreatedAt           field.Int64
	UpdatedAt           field.Int64
	IsDeleted           field.Bool

	fieldMap map[string]field.Expr
}

func (m mPkgTask) Table(newTableName string) *mPkgTask {
	m.mPkgTaskDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mPkgTask) As(alias string) *mPkgTask {
	m.mPkgTaskDo.DO = *(m.mPkgTaskDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mPkgTask) updateTableName(table string) *mPkgTask {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt64(table, "id")
	m.GameNameZh = field.NewString(table, "game_name_zh")
	m.GameNameEn = field.NewString(table, "game_name_en")
	m.GameURL = field.NewString(table, "game_url")
	m.GameVersion = field.NewString(table, "game_version")
	m.VersionTextColor = field.NewString(table, "version_text_color")
	m.VersionText = field.NewString(table, "version_text")
	m.GameOrientation = field.NewInt32(table, "game_orientation")
	m.GameIcon = field.NewString(table, "game_icon")
	m.LaunchBg = field.NewString(table, "launch_bg")
	m.LaunchPopupText = field.NewString(table, "launch_popup_text")
	m.AgeRating = field.NewInt32(table, "age_rating")
	m.AgeRatingPosition = field.NewInt32(table, "age_rating_position")
	m.AgeRatingDesc = field.NewString(table, "age_rating_desc")
	m.AppropriateAge = field.NewInt32(table, "appropriate_age")
	m.ShowSplashDialog = field.NewInt32(table, "show_splash_dialog")
	m.LoadLocalWeb = field.NewString(table, "load_local_web")
	m.SplashTips = field.NewString(table, "splash_tips")
	m.AllowRegister = field.NewInt32(table, "allow_register")
	m.MinorPlayTimeType = field.NewInt32(table, "minor_play_time_type")
	m.MinorPlayTimeConfig = field.NewString(table, "minor_play_time_config")
	m.DownloadURL = field.NewString(table, "download_url")
	m.Status = field.NewInt32(table, "status")
	m.CreatorID = field.NewString(table, "creator_id")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mPkgTask) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mPkgTask) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 27)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_name_zh"] = m.GameNameZh
	m.fieldMap["game_name_en"] = m.GameNameEn
	m.fieldMap["game_url"] = m.GameURL
	m.fieldMap["game_version"] = m.GameVersion
	m.fieldMap["version_text_color"] = m.VersionTextColor
	m.fieldMap["version_text"] = m.VersionText
	m.fieldMap["game_orientation"] = m.GameOrientation
	m.fieldMap["game_icon"] = m.GameIcon
	m.fieldMap["launch_bg"] = m.LaunchBg
	m.fieldMap["launch_popup_text"] = m.LaunchPopupText
	m.fieldMap["age_rating"] = m.AgeRating
	m.fieldMap["age_rating_position"] = m.AgeRatingPosition
	m.fieldMap["age_rating_desc"] = m.AgeRatingDesc
	m.fieldMap["appropriate_age"] = m.AppropriateAge
	m.fieldMap["show_splash_dialog"] = m.ShowSplashDialog
	m.fieldMap["load_local_web"] = m.LoadLocalWeb
	m.fieldMap["splash_tips"] = m.SplashTips
	m.fieldMap["allow_register"] = m.AllowRegister
	m.fieldMap["minor_play_time_type"] = m.MinorPlayTimeType
	m.fieldMap["minor_play_time_config"] = m.MinorPlayTimeConfig
	m.fieldMap["download_url"] = m.DownloadURL
	m.fieldMap["status"] = m.Status
	m.fieldMap["creator_id"] = m.CreatorID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mPkgTask) clone(db *gorm.DB) mPkgTask {
	m.mPkgTaskDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mPkgTask) replaceDB(db *gorm.DB) mPkgTask {
	m.mPkgTaskDo.ReplaceDB(db)
	return m
}

type mPkgTaskDo struct{ gen.DO }

type IMPkgTaskDo interface {
	gen.SubQuery
	Debug() IMPkgTaskDo
	WithContext(ctx context.Context) IMPkgTaskDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMPkgTaskDo
	WriteDB() IMPkgTaskDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMPkgTaskDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMPkgTaskDo
	Not(conds ...gen.Condition) IMPkgTaskDo
	Or(conds ...gen.Condition) IMPkgTaskDo
	Select(conds ...field.Expr) IMPkgTaskDo
	Where(conds ...gen.Condition) IMPkgTaskDo
	Order(conds ...field.Expr) IMPkgTaskDo
	Distinct(cols ...field.Expr) IMPkgTaskDo
	Omit(cols ...field.Expr) IMPkgTaskDo
	Join(table schema.Tabler, on ...field.Expr) IMPkgTaskDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMPkgTaskDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMPkgTaskDo
	Group(cols ...field.Expr) IMPkgTaskDo
	Having(conds ...gen.Condition) IMPkgTaskDo
	Limit(limit int) IMPkgTaskDo
	Offset(offset int) IMPkgTaskDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMPkgTaskDo
	Unscoped() IMPkgTaskDo
	Create(values ...*model.MPkgTask) error
	CreateInBatches(values []*model.MPkgTask, batchSize int) error
	Save(values ...*model.MPkgTask) error
	First() (*model.MPkgTask, error)
	Take() (*model.MPkgTask, error)
	Last() (*model.MPkgTask, error)
	Find() ([]*model.MPkgTask, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MPkgTask, err error)
	FindInBatches(result *[]*model.MPkgTask, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MPkgTask) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMPkgTaskDo
	Assign(attrs ...field.AssignExpr) IMPkgTaskDo
	Joins(fields ...field.RelationField) IMPkgTaskDo
	Preload(fields ...field.RelationField) IMPkgTaskDo
	FirstOrInit() (*model.MPkgTask, error)
	FirstOrCreate() (*model.MPkgTask, error)
	FindByPage(offset int, limit int) (result []*model.MPkgTask, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMPkgTaskDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mPkgTaskDo) Debug() IMPkgTaskDo {
	return m.withDO(m.DO.Debug())
}

func (m mPkgTaskDo) WithContext(ctx context.Context) IMPkgTaskDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mPkgTaskDo) ReadDB() IMPkgTaskDo {
	return m.Clauses(dbresolver.Read)
}

func (m mPkgTaskDo) WriteDB() IMPkgTaskDo {
	return m.Clauses(dbresolver.Write)
}

func (m mPkgTaskDo) Session(config *gorm.Session) IMPkgTaskDo {
	return m.withDO(m.DO.Session(config))
}

func (m mPkgTaskDo) Clauses(conds ...clause.Expression) IMPkgTaskDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mPkgTaskDo) Returning(value interface{}, columns ...string) IMPkgTaskDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mPkgTaskDo) Not(conds ...gen.Condition) IMPkgTaskDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mPkgTaskDo) Or(conds ...gen.Condition) IMPkgTaskDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mPkgTaskDo) Select(conds ...field.Expr) IMPkgTaskDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mPkgTaskDo) Where(conds ...gen.Condition) IMPkgTaskDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mPkgTaskDo) Order(conds ...field.Expr) IMPkgTaskDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mPkgTaskDo) Distinct(cols ...field.Expr) IMPkgTaskDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mPkgTaskDo) Omit(cols ...field.Expr) IMPkgTaskDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mPkgTaskDo) Join(table schema.Tabler, on ...field.Expr) IMPkgTaskDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mPkgTaskDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMPkgTaskDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mPkgTaskDo) RightJoin(table schema.Tabler, on ...field.Expr) IMPkgTaskDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mPkgTaskDo) Group(cols ...field.Expr) IMPkgTaskDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mPkgTaskDo) Having(conds ...gen.Condition) IMPkgTaskDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mPkgTaskDo) Limit(limit int) IMPkgTaskDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mPkgTaskDo) Offset(offset int) IMPkgTaskDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mPkgTaskDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMPkgTaskDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mPkgTaskDo) Unscoped() IMPkgTaskDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mPkgTaskDo) Create(values ...*model.MPkgTask) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mPkgTaskDo) CreateInBatches(values []*model.MPkgTask, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mPkgTaskDo) Save(values ...*model.MPkgTask) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mPkgTaskDo) First() (*model.MPkgTask, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPkgTask), nil
	}
}

func (m mPkgTaskDo) Take() (*model.MPkgTask, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPkgTask), nil
	}
}

func (m mPkgTaskDo) Last() (*model.MPkgTask, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPkgTask), nil
	}
}

func (m mPkgTaskDo) Find() ([]*model.MPkgTask, error) {
	result, err := m.DO.Find()
	return result.([]*model.MPkgTask), err
}

func (m mPkgTaskDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MPkgTask, err error) {
	buf := make([]*model.MPkgTask, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mPkgTaskDo) FindInBatches(result *[]*model.MPkgTask, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mPkgTaskDo) Attrs(attrs ...field.AssignExpr) IMPkgTaskDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mPkgTaskDo) Assign(attrs ...field.AssignExpr) IMPkgTaskDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mPkgTaskDo) Joins(fields ...field.RelationField) IMPkgTaskDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mPkgTaskDo) Preload(fields ...field.RelationField) IMPkgTaskDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mPkgTaskDo) FirstOrInit() (*model.MPkgTask, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPkgTask), nil
	}
}

func (m mPkgTaskDo) FirstOrCreate() (*model.MPkgTask, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MPkgTask), nil
	}
}

func (m mPkgTaskDo) FindByPage(offset int, limit int) (result []*model.MPkgTask, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mPkgTaskDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mPkgTaskDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mPkgTaskDo) Delete(models ...*model.MPkgTask) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mPkgTaskDo) withDO(do gen.Dao) *mPkgTaskDo {
	m.DO = *do.(*gen.DO)
	return m
}
