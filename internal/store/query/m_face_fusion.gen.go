// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMFaceFusion(db *gorm.DB, opts ...gen.DOOption) mFaceFusion {
	_mFaceFusion := mFaceFusion{}

	_mFaceFusion.mFaceFusionDo.UseDB(db, opts...)
	_mFaceFusion.mFaceFusionDo.UseModel(&model.MFaceFusion{})

	tableName := _mFaceFusion.mFaceFusionDo.TableName()
	_mFaceFusion.ALL = field.NewAsterisk(tableName)
	_mFaceFusion.ID = field.NewInt32(tableName, "id")
	_mFaceFusion.GameID = field.NewString(tableName, "game_id")
	_mFaceFusion.UserID = field.NewString(tableName, "user_id")
	_mFaceFusion.TaskID = field.NewString(tableName, "task_id")
	_mFaceFusion.ModelID = field.NewString(tableName, "model_id")
	_mFaceFusion.ProjectID = field.NewString(tableName, "project_id")
	_mFaceFusion.RequestID = field.NewString(tableName, "request_id")
	_mFaceFusion.ImageURL = field.NewString(tableName, "image_url")
	_mFaceFusion.OssURL = field.NewString(tableName, "oss_url")
	_mFaceFusion.Status = field.NewString(tableName, "status")
	_mFaceFusion.Message = field.NewString(tableName, "message")
	_mFaceFusion.MergeInfos = field.NewString(tableName, "merge_infos")
	_mFaceFusion.FuseFaceDegree = field.NewInt32(tableName, "fuse_face_degree")
	_mFaceFusion.FuseProfileDegree = field.NewInt32(tableName, "fuse_profile_degree")
	_mFaceFusion.LogoAdd = field.NewInt32(tableName, "logo_add")
	_mFaceFusion.LogoParam = field.NewString(tableName, "logo_param")
	_mFaceFusion.FuseParam = field.NewString(tableName, "fuse_param")
	_mFaceFusion.RspImgType = field.NewString(tableName, "rsp_img_type")
	_mFaceFusion.CreatedAt = field.NewInt64(tableName, "created_at")
	_mFaceFusion.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mFaceFusion.ProcessedAt = field.NewInt64(tableName, "processed_at")

	_mFaceFusion.fillFieldMap()

	return _mFaceFusion
}

// mFaceFusion 人脸融合记录表
type mFaceFusion struct {
	mFaceFusionDo

	ALL               field.Asterisk
	ID                field.Int32  // 主键ID
	GameID            field.String // 游戏ID
	UserID            field.String // 用户ID
	TaskID            field.String // 任务ID
	ModelID           field.String // 素材模板ID
	ProjectID         field.String // 项目ID
	RequestID         field.String // 腾讯云请求ID
	ImageURL          field.String // 融合后图片OSS URL
	OssURL            field.String // 融合后图片OSS内部URL
	Status            field.String // 处理状态：processing/success/failed
	Message           field.String // 状态消息
	MergeInfos        field.String // 融合参数JSON
	FuseFaceDegree    field.Int32  // 人脸融合度，取值范围[0,100]
	FuseProfileDegree field.Int32  // 五官融合度，取值范围[0,1]
	LogoAdd           field.Int32  // 是否添加logo，0不添加，1添加
	LogoParam         field.String // 水印参数
	FuseParam         field.String // 融合参数
	RspImgType        field.String // 返回图片类型：base64/url
	CreatedAt         field.Int64  // 创建时间戳（毫秒）
	UpdatedAt         field.Int64  // 更新时间戳（毫秒）
	ProcessedAt       field.Int64  // 处理完成时间戳（毫秒）

	fieldMap map[string]field.Expr
}

func (m mFaceFusion) Table(newTableName string) *mFaceFusion {
	m.mFaceFusionDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mFaceFusion) As(alias string) *mFaceFusion {
	m.mFaceFusionDo.DO = *(m.mFaceFusionDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mFaceFusion) updateTableName(table string) *mFaceFusion {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.UserID = field.NewString(table, "user_id")
	m.TaskID = field.NewString(table, "task_id")
	m.ModelID = field.NewString(table, "model_id")
	m.ProjectID = field.NewString(table, "project_id")
	m.RequestID = field.NewString(table, "request_id")
	m.ImageURL = field.NewString(table, "image_url")
	m.OssURL = field.NewString(table, "oss_url")
	m.Status = field.NewString(table, "status")
	m.Message = field.NewString(table, "message")
	m.MergeInfos = field.NewString(table, "merge_infos")
	m.FuseFaceDegree = field.NewInt32(table, "fuse_face_degree")
	m.FuseProfileDegree = field.NewInt32(table, "fuse_profile_degree")
	m.LogoAdd = field.NewInt32(table, "logo_add")
	m.LogoParam = field.NewString(table, "logo_param")
	m.FuseParam = field.NewString(table, "fuse_param")
	m.RspImgType = field.NewString(table, "rsp_img_type")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.ProcessedAt = field.NewInt64(table, "processed_at")

	m.fillFieldMap()

	return m
}

func (m *mFaceFusion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mFaceFusion) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 21)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["task_id"] = m.TaskID
	m.fieldMap["model_id"] = m.ModelID
	m.fieldMap["project_id"] = m.ProjectID
	m.fieldMap["request_id"] = m.RequestID
	m.fieldMap["image_url"] = m.ImageURL
	m.fieldMap["oss_url"] = m.OssURL
	m.fieldMap["status"] = m.Status
	m.fieldMap["message"] = m.Message
	m.fieldMap["merge_infos"] = m.MergeInfos
	m.fieldMap["fuse_face_degree"] = m.FuseFaceDegree
	m.fieldMap["fuse_profile_degree"] = m.FuseProfileDegree
	m.fieldMap["logo_add"] = m.LogoAdd
	m.fieldMap["logo_param"] = m.LogoParam
	m.fieldMap["fuse_param"] = m.FuseParam
	m.fieldMap["rsp_img_type"] = m.RspImgType
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["processed_at"] = m.ProcessedAt
}

func (m mFaceFusion) clone(db *gorm.DB) mFaceFusion {
	m.mFaceFusionDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mFaceFusion) replaceDB(db *gorm.DB) mFaceFusion {
	m.mFaceFusionDo.ReplaceDB(db)
	return m
}

type mFaceFusionDo struct{ gen.DO }

type IMFaceFusionDo interface {
	gen.SubQuery
	Debug() IMFaceFusionDo
	WithContext(ctx context.Context) IMFaceFusionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMFaceFusionDo
	WriteDB() IMFaceFusionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMFaceFusionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMFaceFusionDo
	Not(conds ...gen.Condition) IMFaceFusionDo
	Or(conds ...gen.Condition) IMFaceFusionDo
	Select(conds ...field.Expr) IMFaceFusionDo
	Where(conds ...gen.Condition) IMFaceFusionDo
	Order(conds ...field.Expr) IMFaceFusionDo
	Distinct(cols ...field.Expr) IMFaceFusionDo
	Omit(cols ...field.Expr) IMFaceFusionDo
	Join(table schema.Tabler, on ...field.Expr) IMFaceFusionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMFaceFusionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMFaceFusionDo
	Group(cols ...field.Expr) IMFaceFusionDo
	Having(conds ...gen.Condition) IMFaceFusionDo
	Limit(limit int) IMFaceFusionDo
	Offset(offset int) IMFaceFusionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMFaceFusionDo
	Unscoped() IMFaceFusionDo
	Create(values ...*model.MFaceFusion) error
	CreateInBatches(values []*model.MFaceFusion, batchSize int) error
	Save(values ...*model.MFaceFusion) error
	First() (*model.MFaceFusion, error)
	Take() (*model.MFaceFusion, error)
	Last() (*model.MFaceFusion, error)
	Find() ([]*model.MFaceFusion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MFaceFusion, err error)
	FindInBatches(result *[]*model.MFaceFusion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MFaceFusion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMFaceFusionDo
	Assign(attrs ...field.AssignExpr) IMFaceFusionDo
	Joins(fields ...field.RelationField) IMFaceFusionDo
	Preload(fields ...field.RelationField) IMFaceFusionDo
	FirstOrInit() (*model.MFaceFusion, error)
	FirstOrCreate() (*model.MFaceFusion, error)
	FindByPage(offset int, limit int) (result []*model.MFaceFusion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMFaceFusionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mFaceFusionDo) Debug() IMFaceFusionDo {
	return m.withDO(m.DO.Debug())
}

func (m mFaceFusionDo) WithContext(ctx context.Context) IMFaceFusionDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mFaceFusionDo) ReadDB() IMFaceFusionDo {
	return m.Clauses(dbresolver.Read)
}

func (m mFaceFusionDo) WriteDB() IMFaceFusionDo {
	return m.Clauses(dbresolver.Write)
}

func (m mFaceFusionDo) Session(config *gorm.Session) IMFaceFusionDo {
	return m.withDO(m.DO.Session(config))
}

func (m mFaceFusionDo) Clauses(conds ...clause.Expression) IMFaceFusionDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mFaceFusionDo) Returning(value interface{}, columns ...string) IMFaceFusionDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mFaceFusionDo) Not(conds ...gen.Condition) IMFaceFusionDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mFaceFusionDo) Or(conds ...gen.Condition) IMFaceFusionDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mFaceFusionDo) Select(conds ...field.Expr) IMFaceFusionDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mFaceFusionDo) Where(conds ...gen.Condition) IMFaceFusionDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mFaceFusionDo) Order(conds ...field.Expr) IMFaceFusionDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mFaceFusionDo) Distinct(cols ...field.Expr) IMFaceFusionDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mFaceFusionDo) Omit(cols ...field.Expr) IMFaceFusionDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mFaceFusionDo) Join(table schema.Tabler, on ...field.Expr) IMFaceFusionDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mFaceFusionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMFaceFusionDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mFaceFusionDo) RightJoin(table schema.Tabler, on ...field.Expr) IMFaceFusionDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mFaceFusionDo) Group(cols ...field.Expr) IMFaceFusionDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mFaceFusionDo) Having(conds ...gen.Condition) IMFaceFusionDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mFaceFusionDo) Limit(limit int) IMFaceFusionDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mFaceFusionDo) Offset(offset int) IMFaceFusionDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mFaceFusionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMFaceFusionDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mFaceFusionDo) Unscoped() IMFaceFusionDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mFaceFusionDo) Create(values ...*model.MFaceFusion) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mFaceFusionDo) CreateInBatches(values []*model.MFaceFusion, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mFaceFusionDo) Save(values ...*model.MFaceFusion) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mFaceFusionDo) First() (*model.MFaceFusion, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MFaceFusion), nil
	}
}

func (m mFaceFusionDo) Take() (*model.MFaceFusion, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MFaceFusion), nil
	}
}

func (m mFaceFusionDo) Last() (*model.MFaceFusion, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MFaceFusion), nil
	}
}

func (m mFaceFusionDo) Find() ([]*model.MFaceFusion, error) {
	result, err := m.DO.Find()
	return result.([]*model.MFaceFusion), err
}

func (m mFaceFusionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MFaceFusion, err error) {
	buf := make([]*model.MFaceFusion, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mFaceFusionDo) FindInBatches(result *[]*model.MFaceFusion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mFaceFusionDo) Attrs(attrs ...field.AssignExpr) IMFaceFusionDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mFaceFusionDo) Assign(attrs ...field.AssignExpr) IMFaceFusionDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mFaceFusionDo) Joins(fields ...field.RelationField) IMFaceFusionDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mFaceFusionDo) Preload(fields ...field.RelationField) IMFaceFusionDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mFaceFusionDo) FirstOrInit() (*model.MFaceFusion, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MFaceFusion), nil
	}
}

func (m mFaceFusionDo) FirstOrCreate() (*model.MFaceFusion, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MFaceFusion), nil
	}
}

func (m mFaceFusionDo) FindByPage(offset int, limit int) (result []*model.MFaceFusion, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mFaceFusionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mFaceFusionDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mFaceFusionDo) Delete(models ...*model.MFaceFusion) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mFaceFusionDo) withDO(do gen.Dao) *mFaceFusionDo {
	m.DO = *do.(*gen.DO)
	return m
}
