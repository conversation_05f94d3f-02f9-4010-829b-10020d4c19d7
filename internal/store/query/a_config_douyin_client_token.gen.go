// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newAConfigDouyinClientToken(db *gorm.DB, opts ...gen.DOOption) aConfigDouyinClientToken {
	_aConfigDouyinClientToken := aConfigDouyinClientToken{}

	_aConfigDouyinClientToken.aConfigDouyinClientTokenDo.UseDB(db, opts...)
	_aConfigDouyinClientToken.aConfigDouyinClientTokenDo.UseModel(&model.AConfigDouyinClientToken{})

	tableName := _aConfigDouyinClientToken.aConfigDouyinClientTokenDo.TableName()
	_aConfigDouyinClientToken.ALL = field.NewAsterisk(tableName)
	_aConfigDouyinClientToken.ID = field.NewInt32(tableName, "id")
	_aConfigDouyinClientToken.AppID = field.NewString(tableName, "app_id")
	_aConfigDouyinClientToken.AppSecret = field.NewString(tableName, "app_secret")
	_aConfigDouyinClientToken.AccessToken = field.NewString(tableName, "access_token")
	_aConfigDouyinClientToken.ExpiresIn = field.NewInt32(tableName, "expires_in")
	_aConfigDouyinClientToken.Ticket = field.NewString(tableName, "ticket")
	_aConfigDouyinClientToken.TicketExpiresIn = field.NewInt32(tableName, "ticket_expires_in")
	_aConfigDouyinClientToken.TokenRefreshedAt = field.NewInt64(tableName, "token_refreshed_at")
	_aConfigDouyinClientToken.CreatedAt = field.NewInt64(tableName, "created_at")
	_aConfigDouyinClientToken.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aConfigDouyinClientToken.IsDeleted = field.NewBool(tableName, "is_deleted")
	_aConfigDouyinClientToken.TicketRefreshedAt = field.NewInt64(tableName, "ticket_refreshed_at")

	_aConfigDouyinClientToken.fillFieldMap()

	return _aConfigDouyinClientToken
}

// aConfigDouyinClientToken 抖音稳定客户端令牌配置表
type aConfigDouyinClientToken struct {
	aConfigDouyinClientTokenDo

	ALL               field.Asterisk
	ID                field.Int32  // 唯一标识符
	AppID             field.String // 抖音开放平台应用ID
	AppSecret         field.String // 抖音开放平台应用密钥
	AccessToken       field.String // 稳定客户端令牌
	ExpiresIn         field.Int32  // Token过期时间(秒)
	Ticket            field.String // 票据
	TicketExpiresIn   field.Int32
	TokenRefreshedAt  field.Int64 // Token刷新时间戳(Unix时间戳毫秒)
	CreatedAt         field.Int64 // 创建时间戳(Unix时间戳毫秒)
	UpdatedAt         field.Int64 // 最后更新时间戳(Unix时间戳毫秒)
	IsDeleted         field.Bool  // 删除标记: 0-未删除, 1-已删除
	TicketRefreshedAt field.Int64 // JSTicket刷新时间戳(Unix时间戳毫秒)

	fieldMap map[string]field.Expr
}

func (a aConfigDouyinClientToken) Table(newTableName string) *aConfigDouyinClientToken {
	a.aConfigDouyinClientTokenDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aConfigDouyinClientToken) As(alias string) *aConfigDouyinClientToken {
	a.aConfigDouyinClientTokenDo.DO = *(a.aConfigDouyinClientTokenDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aConfigDouyinClientToken) updateTableName(table string) *aConfigDouyinClientToken {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.AppID = field.NewString(table, "app_id")
	a.AppSecret = field.NewString(table, "app_secret")
	a.AccessToken = field.NewString(table, "access_token")
	a.ExpiresIn = field.NewInt32(table, "expires_in")
	a.Ticket = field.NewString(table, "ticket")
	a.TicketExpiresIn = field.NewInt32(table, "ticket_expires_in")
	a.TokenRefreshedAt = field.NewInt64(table, "token_refreshed_at")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")
	a.TicketRefreshedAt = field.NewInt64(table, "ticket_refreshed_at")

	a.fillFieldMap()

	return a
}

func (a *aConfigDouyinClientToken) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aConfigDouyinClientToken) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 12)
	a.fieldMap["id"] = a.ID
	a.fieldMap["app_id"] = a.AppID
	a.fieldMap["app_secret"] = a.AppSecret
	a.fieldMap["access_token"] = a.AccessToken
	a.fieldMap["expires_in"] = a.ExpiresIn
	a.fieldMap["ticket"] = a.Ticket
	a.fieldMap["ticket_expires_in"] = a.TicketExpiresIn
	a.fieldMap["token_refreshed_at"] = a.TokenRefreshedAt
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
	a.fieldMap["ticket_refreshed_at"] = a.TicketRefreshedAt
}

func (a aConfigDouyinClientToken) clone(db *gorm.DB) aConfigDouyinClientToken {
	a.aConfigDouyinClientTokenDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aConfigDouyinClientToken) replaceDB(db *gorm.DB) aConfigDouyinClientToken {
	a.aConfigDouyinClientTokenDo.ReplaceDB(db)
	return a
}

type aConfigDouyinClientTokenDo struct{ gen.DO }

type IAConfigDouyinClientTokenDo interface {
	gen.SubQuery
	Debug() IAConfigDouyinClientTokenDo
	WithContext(ctx context.Context) IAConfigDouyinClientTokenDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAConfigDouyinClientTokenDo
	WriteDB() IAConfigDouyinClientTokenDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAConfigDouyinClientTokenDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAConfigDouyinClientTokenDo
	Not(conds ...gen.Condition) IAConfigDouyinClientTokenDo
	Or(conds ...gen.Condition) IAConfigDouyinClientTokenDo
	Select(conds ...field.Expr) IAConfigDouyinClientTokenDo
	Where(conds ...gen.Condition) IAConfigDouyinClientTokenDo
	Order(conds ...field.Expr) IAConfigDouyinClientTokenDo
	Distinct(cols ...field.Expr) IAConfigDouyinClientTokenDo
	Omit(cols ...field.Expr) IAConfigDouyinClientTokenDo
	Join(table schema.Tabler, on ...field.Expr) IAConfigDouyinClientTokenDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAConfigDouyinClientTokenDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAConfigDouyinClientTokenDo
	Group(cols ...field.Expr) IAConfigDouyinClientTokenDo
	Having(conds ...gen.Condition) IAConfigDouyinClientTokenDo
	Limit(limit int) IAConfigDouyinClientTokenDo
	Offset(offset int) IAConfigDouyinClientTokenDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAConfigDouyinClientTokenDo
	Unscoped() IAConfigDouyinClientTokenDo
	Create(values ...*model.AConfigDouyinClientToken) error
	CreateInBatches(values []*model.AConfigDouyinClientToken, batchSize int) error
	Save(values ...*model.AConfigDouyinClientToken) error
	First() (*model.AConfigDouyinClientToken, error)
	Take() (*model.AConfigDouyinClientToken, error)
	Last() (*model.AConfigDouyinClientToken, error)
	Find() ([]*model.AConfigDouyinClientToken, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AConfigDouyinClientToken, err error)
	FindInBatches(result *[]*model.AConfigDouyinClientToken, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AConfigDouyinClientToken) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAConfigDouyinClientTokenDo
	Assign(attrs ...field.AssignExpr) IAConfigDouyinClientTokenDo
	Joins(fields ...field.RelationField) IAConfigDouyinClientTokenDo
	Preload(fields ...field.RelationField) IAConfigDouyinClientTokenDo
	FirstOrInit() (*model.AConfigDouyinClientToken, error)
	FirstOrCreate() (*model.AConfigDouyinClientToken, error)
	FindByPage(offset int, limit int) (result []*model.AConfigDouyinClientToken, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAConfigDouyinClientTokenDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aConfigDouyinClientTokenDo) Debug() IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Debug())
}

func (a aConfigDouyinClientTokenDo) WithContext(ctx context.Context) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aConfigDouyinClientTokenDo) ReadDB() IAConfigDouyinClientTokenDo {
	return a.Clauses(dbresolver.Read)
}

func (a aConfigDouyinClientTokenDo) WriteDB() IAConfigDouyinClientTokenDo {
	return a.Clauses(dbresolver.Write)
}

func (a aConfigDouyinClientTokenDo) Session(config *gorm.Session) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Session(config))
}

func (a aConfigDouyinClientTokenDo) Clauses(conds ...clause.Expression) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aConfigDouyinClientTokenDo) Returning(value interface{}, columns ...string) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aConfigDouyinClientTokenDo) Not(conds ...gen.Condition) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aConfigDouyinClientTokenDo) Or(conds ...gen.Condition) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aConfigDouyinClientTokenDo) Select(conds ...field.Expr) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aConfigDouyinClientTokenDo) Where(conds ...gen.Condition) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aConfigDouyinClientTokenDo) Order(conds ...field.Expr) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aConfigDouyinClientTokenDo) Distinct(cols ...field.Expr) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aConfigDouyinClientTokenDo) Omit(cols ...field.Expr) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aConfigDouyinClientTokenDo) Join(table schema.Tabler, on ...field.Expr) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aConfigDouyinClientTokenDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aConfigDouyinClientTokenDo) RightJoin(table schema.Tabler, on ...field.Expr) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aConfigDouyinClientTokenDo) Group(cols ...field.Expr) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aConfigDouyinClientTokenDo) Having(conds ...gen.Condition) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aConfigDouyinClientTokenDo) Limit(limit int) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aConfigDouyinClientTokenDo) Offset(offset int) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aConfigDouyinClientTokenDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aConfigDouyinClientTokenDo) Unscoped() IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aConfigDouyinClientTokenDo) Create(values ...*model.AConfigDouyinClientToken) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aConfigDouyinClientTokenDo) CreateInBatches(values []*model.AConfigDouyinClientToken, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aConfigDouyinClientTokenDo) Save(values ...*model.AConfigDouyinClientToken) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aConfigDouyinClientTokenDo) First() (*model.AConfigDouyinClientToken, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigDouyinClientToken), nil
	}
}

func (a aConfigDouyinClientTokenDo) Take() (*model.AConfigDouyinClientToken, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigDouyinClientToken), nil
	}
}

func (a aConfigDouyinClientTokenDo) Last() (*model.AConfigDouyinClientToken, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigDouyinClientToken), nil
	}
}

func (a aConfigDouyinClientTokenDo) Find() ([]*model.AConfigDouyinClientToken, error) {
	result, err := a.DO.Find()
	return result.([]*model.AConfigDouyinClientToken), err
}

func (a aConfigDouyinClientTokenDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AConfigDouyinClientToken, err error) {
	buf := make([]*model.AConfigDouyinClientToken, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aConfigDouyinClientTokenDo) FindInBatches(result *[]*model.AConfigDouyinClientToken, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aConfigDouyinClientTokenDo) Attrs(attrs ...field.AssignExpr) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aConfigDouyinClientTokenDo) Assign(attrs ...field.AssignExpr) IAConfigDouyinClientTokenDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aConfigDouyinClientTokenDo) Joins(fields ...field.RelationField) IAConfigDouyinClientTokenDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aConfigDouyinClientTokenDo) Preload(fields ...field.RelationField) IAConfigDouyinClientTokenDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aConfigDouyinClientTokenDo) FirstOrInit() (*model.AConfigDouyinClientToken, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigDouyinClientToken), nil
	}
}

func (a aConfigDouyinClientTokenDo) FirstOrCreate() (*model.AConfigDouyinClientToken, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AConfigDouyinClientToken), nil
	}
}

func (a aConfigDouyinClientTokenDo) FindByPage(offset int, limit int) (result []*model.AConfigDouyinClientToken, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aConfigDouyinClientTokenDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aConfigDouyinClientTokenDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aConfigDouyinClientTokenDo) Delete(models ...*model.AConfigDouyinClientToken) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aConfigDouyinClientTokenDo) withDO(do gen.Dao) *aConfigDouyinClientTokenDo {
	a.DO = *do.(*gen.DO)
	return a
}
