// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newAUpload(db *gorm.DB, opts ...gen.DOOption) aUpload {
	_aUpload := aUpload{}

	_aUpload.aUploadDo.UseDB(db, opts...)
	_aUpload.aUploadDo.UseModel(&model.AUpload{})

	tableName := _aUpload.aUploadDo.TableName()
	_aUpload.ALL = field.NewAsterisk(tableName)
	_aUpload.ID = field.NewInt32(tableName, "id")
	_aUpload.FileName = field.NewString(tableName, "file_name")
	_aUpload.FileSize = field.NewInt64(tableName, "file_size")
	_aUpload.FileType = field.NewString(tableName, "file_type")
	_aUpload.Md5 = field.NewString(tableName, "md5")
	_aUpload.URL = field.NewString(tableName, "url")
	_aUpload.OssURL = field.NewString(tableName, "oss_url")
	_aUpload.OssBucket = field.NewString(tableName, "oss_bucket")
	_aUpload.Description = field.NewString(tableName, "description")
	_aUpload.CreatorID = field.NewString(tableName, "creator_id")
	_aUpload.CreatedAt = field.NewInt64(tableName, "created_at")
	_aUpload.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aUpload.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aUpload.fillFieldMap()

	return _aUpload
}

type aUpload struct {
	aUploadDo

	ALL         field.Asterisk
	ID          field.Int32
	FileName    field.String
	FileSize    field.Int64
	FileType    field.String // 文件后缀类型
	Md5         field.String // md5值
	URL         field.String // 完整url
	OssURL      field.String
	OssBucket   field.String
	Description field.String // 文件描述
	CreatorID   field.String
	CreatedAt   field.Int64
	UpdatedAt   field.Int64
	IsDeleted   field.Bool

	fieldMap map[string]field.Expr
}

func (a aUpload) Table(newTableName string) *aUpload {
	a.aUploadDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aUpload) As(alias string) *aUpload {
	a.aUploadDo.DO = *(a.aUploadDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aUpload) updateTableName(table string) *aUpload {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.FileName = field.NewString(table, "file_name")
	a.FileSize = field.NewInt64(table, "file_size")
	a.FileType = field.NewString(table, "file_type")
	a.Md5 = field.NewString(table, "md5")
	a.URL = field.NewString(table, "url")
	a.OssURL = field.NewString(table, "oss_url")
	a.OssBucket = field.NewString(table, "oss_bucket")
	a.Description = field.NewString(table, "description")
	a.CreatorID = field.NewString(table, "creator_id")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aUpload) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aUpload) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 13)
	a.fieldMap["id"] = a.ID
	a.fieldMap["file_name"] = a.FileName
	a.fieldMap["file_size"] = a.FileSize
	a.fieldMap["file_type"] = a.FileType
	a.fieldMap["md5"] = a.Md5
	a.fieldMap["url"] = a.URL
	a.fieldMap["oss_url"] = a.OssURL
	a.fieldMap["oss_bucket"] = a.OssBucket
	a.fieldMap["description"] = a.Description
	a.fieldMap["creator_id"] = a.CreatorID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aUpload) clone(db *gorm.DB) aUpload {
	a.aUploadDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aUpload) replaceDB(db *gorm.DB) aUpload {
	a.aUploadDo.ReplaceDB(db)
	return a
}

type aUploadDo struct{ gen.DO }

type IAUploadDo interface {
	gen.SubQuery
	Debug() IAUploadDo
	WithContext(ctx context.Context) IAUploadDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAUploadDo
	WriteDB() IAUploadDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAUploadDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAUploadDo
	Not(conds ...gen.Condition) IAUploadDo
	Or(conds ...gen.Condition) IAUploadDo
	Select(conds ...field.Expr) IAUploadDo
	Where(conds ...gen.Condition) IAUploadDo
	Order(conds ...field.Expr) IAUploadDo
	Distinct(cols ...field.Expr) IAUploadDo
	Omit(cols ...field.Expr) IAUploadDo
	Join(table schema.Tabler, on ...field.Expr) IAUploadDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAUploadDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAUploadDo
	Group(cols ...field.Expr) IAUploadDo
	Having(conds ...gen.Condition) IAUploadDo
	Limit(limit int) IAUploadDo
	Offset(offset int) IAUploadDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAUploadDo
	Unscoped() IAUploadDo
	Create(values ...*model.AUpload) error
	CreateInBatches(values []*model.AUpload, batchSize int) error
	Save(values ...*model.AUpload) error
	First() (*model.AUpload, error)
	Take() (*model.AUpload, error)
	Last() (*model.AUpload, error)
	Find() ([]*model.AUpload, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUpload, err error)
	FindInBatches(result *[]*model.AUpload, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AUpload) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAUploadDo
	Assign(attrs ...field.AssignExpr) IAUploadDo
	Joins(fields ...field.RelationField) IAUploadDo
	Preload(fields ...field.RelationField) IAUploadDo
	FirstOrInit() (*model.AUpload, error)
	FirstOrCreate() (*model.AUpload, error)
	FindByPage(offset int, limit int) (result []*model.AUpload, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAUploadDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aUploadDo) Debug() IAUploadDo {
	return a.withDO(a.DO.Debug())
}

func (a aUploadDo) WithContext(ctx context.Context) IAUploadDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aUploadDo) ReadDB() IAUploadDo {
	return a.Clauses(dbresolver.Read)
}

func (a aUploadDo) WriteDB() IAUploadDo {
	return a.Clauses(dbresolver.Write)
}

func (a aUploadDo) Session(config *gorm.Session) IAUploadDo {
	return a.withDO(a.DO.Session(config))
}

func (a aUploadDo) Clauses(conds ...clause.Expression) IAUploadDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aUploadDo) Returning(value interface{}, columns ...string) IAUploadDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aUploadDo) Not(conds ...gen.Condition) IAUploadDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aUploadDo) Or(conds ...gen.Condition) IAUploadDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aUploadDo) Select(conds ...field.Expr) IAUploadDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aUploadDo) Where(conds ...gen.Condition) IAUploadDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aUploadDo) Order(conds ...field.Expr) IAUploadDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aUploadDo) Distinct(cols ...field.Expr) IAUploadDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aUploadDo) Omit(cols ...field.Expr) IAUploadDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aUploadDo) Join(table schema.Tabler, on ...field.Expr) IAUploadDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aUploadDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAUploadDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aUploadDo) RightJoin(table schema.Tabler, on ...field.Expr) IAUploadDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aUploadDo) Group(cols ...field.Expr) IAUploadDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aUploadDo) Having(conds ...gen.Condition) IAUploadDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aUploadDo) Limit(limit int) IAUploadDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aUploadDo) Offset(offset int) IAUploadDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aUploadDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAUploadDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aUploadDo) Unscoped() IAUploadDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aUploadDo) Create(values ...*model.AUpload) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aUploadDo) CreateInBatches(values []*model.AUpload, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aUploadDo) Save(values ...*model.AUpload) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aUploadDo) First() (*model.AUpload, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUpload), nil
	}
}

func (a aUploadDo) Take() (*model.AUpload, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUpload), nil
	}
}

func (a aUploadDo) Last() (*model.AUpload, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUpload), nil
	}
}

func (a aUploadDo) Find() ([]*model.AUpload, error) {
	result, err := a.DO.Find()
	return result.([]*model.AUpload), err
}

func (a aUploadDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUpload, err error) {
	buf := make([]*model.AUpload, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aUploadDo) FindInBatches(result *[]*model.AUpload, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aUploadDo) Attrs(attrs ...field.AssignExpr) IAUploadDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aUploadDo) Assign(attrs ...field.AssignExpr) IAUploadDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aUploadDo) Joins(fields ...field.RelationField) IAUploadDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aUploadDo) Preload(fields ...field.RelationField) IAUploadDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aUploadDo) FirstOrInit() (*model.AUpload, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUpload), nil
	}
}

func (a aUploadDo) FirstOrCreate() (*model.AUpload, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUpload), nil
	}
}

func (a aUploadDo) FindByPage(offset int, limit int) (result []*model.AUpload, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aUploadDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aUploadDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aUploadDo) Delete(models ...*model.AUpload) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aUploadDo) withDO(do gen.Dao) *aUploadDo {
	a.DO = *do.(*gen.DO)
	return a
}
