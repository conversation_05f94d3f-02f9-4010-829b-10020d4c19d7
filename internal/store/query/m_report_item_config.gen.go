// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMReportItemConfig(db *gorm.DB, opts ...gen.DOOption) mReportItemConfig {
	_mReportItemConfig := mReportItemConfig{}

	_mReportItemConfig.mReportItemConfigDo.UseDB(db, opts...)
	_mReportItemConfig.mReportItemConfigDo.UseModel(&model.MReportItemConfig{})

	tableName := _mReportItemConfig.mReportItemConfigDo.TableName()
	_mReportItemConfig.ALL = field.NewAsterisk(tableName)
	_mReportItemConfig.ID = field.NewInt32(tableName, "id")
	_mReportItemConfig.GameID = field.NewString(tableName, "game_id")
	_mReportItemConfig.ItemValue = field.NewInt32(tableName, "item_value")
	_mReportItemConfig.ItemName = field.NewString(tableName, "item_name")
	_mReportItemConfig.Description = field.NewString(tableName, "description")
	_mReportItemConfig.IsPreset = field.NewBool(tableName, "is_preset")
	_mReportItemConfig.CreatedAt = field.NewInt64(tableName, "created_at")
	_mReportItemConfig.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mReportItemConfig.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mReportItemConfig.fillFieldMap()

	return _mReportItemConfig
}

// mReportItemConfig 举报事项配置表
type mReportItemConfig struct {
	mReportItemConfigDo

	ALL         field.Asterisk
	ID          field.Int32  // 主键ID
	GameID      field.String // 游戏ID, 为空表示全局配置
	ItemValue   field.Int32  // 举报事项的数值, SDK上报
	ItemName    field.String // 举报事项名称
	Description field.String // 描述
	IsPreset    field.Bool   // 是否预置, 0:否, 1:是, 预置不可修改
	CreatedAt   field.Int64  // 创建时间戳
	UpdatedAt   field.Int64  // 更新时间戳
	IsDeleted   field.Bool   // 软删除标记 0:未删除 1:已删除

	fieldMap map[string]field.Expr
}

func (m mReportItemConfig) Table(newTableName string) *mReportItemConfig {
	m.mReportItemConfigDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mReportItemConfig) As(alias string) *mReportItemConfig {
	m.mReportItemConfigDo.DO = *(m.mReportItemConfigDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mReportItemConfig) updateTableName(table string) *mReportItemConfig {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.ItemValue = field.NewInt32(table, "item_value")
	m.ItemName = field.NewString(table, "item_name")
	m.Description = field.NewString(table, "description")
	m.IsPreset = field.NewBool(table, "is_preset")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mReportItemConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mReportItemConfig) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 9)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["item_value"] = m.ItemValue
	m.fieldMap["item_name"] = m.ItemName
	m.fieldMap["description"] = m.Description
	m.fieldMap["is_preset"] = m.IsPreset
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mReportItemConfig) clone(db *gorm.DB) mReportItemConfig {
	m.mReportItemConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mReportItemConfig) replaceDB(db *gorm.DB) mReportItemConfig {
	m.mReportItemConfigDo.ReplaceDB(db)
	return m
}

type mReportItemConfigDo struct{ gen.DO }

type IMReportItemConfigDo interface {
	gen.SubQuery
	Debug() IMReportItemConfigDo
	WithContext(ctx context.Context) IMReportItemConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMReportItemConfigDo
	WriteDB() IMReportItemConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMReportItemConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMReportItemConfigDo
	Not(conds ...gen.Condition) IMReportItemConfigDo
	Or(conds ...gen.Condition) IMReportItemConfigDo
	Select(conds ...field.Expr) IMReportItemConfigDo
	Where(conds ...gen.Condition) IMReportItemConfigDo
	Order(conds ...field.Expr) IMReportItemConfigDo
	Distinct(cols ...field.Expr) IMReportItemConfigDo
	Omit(cols ...field.Expr) IMReportItemConfigDo
	Join(table schema.Tabler, on ...field.Expr) IMReportItemConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMReportItemConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMReportItemConfigDo
	Group(cols ...field.Expr) IMReportItemConfigDo
	Having(conds ...gen.Condition) IMReportItemConfigDo
	Limit(limit int) IMReportItemConfigDo
	Offset(offset int) IMReportItemConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMReportItemConfigDo
	Unscoped() IMReportItemConfigDo
	Create(values ...*model.MReportItemConfig) error
	CreateInBatches(values []*model.MReportItemConfig, batchSize int) error
	Save(values ...*model.MReportItemConfig) error
	First() (*model.MReportItemConfig, error)
	Take() (*model.MReportItemConfig, error)
	Last() (*model.MReportItemConfig, error)
	Find() ([]*model.MReportItemConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MReportItemConfig, err error)
	FindInBatches(result *[]*model.MReportItemConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MReportItemConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMReportItemConfigDo
	Assign(attrs ...field.AssignExpr) IMReportItemConfigDo
	Joins(fields ...field.RelationField) IMReportItemConfigDo
	Preload(fields ...field.RelationField) IMReportItemConfigDo
	FirstOrInit() (*model.MReportItemConfig, error)
	FirstOrCreate() (*model.MReportItemConfig, error)
	FindByPage(offset int, limit int) (result []*model.MReportItemConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMReportItemConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mReportItemConfigDo) Debug() IMReportItemConfigDo {
	return m.withDO(m.DO.Debug())
}

func (m mReportItemConfigDo) WithContext(ctx context.Context) IMReportItemConfigDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mReportItemConfigDo) ReadDB() IMReportItemConfigDo {
	return m.Clauses(dbresolver.Read)
}

func (m mReportItemConfigDo) WriteDB() IMReportItemConfigDo {
	return m.Clauses(dbresolver.Write)
}

func (m mReportItemConfigDo) Session(config *gorm.Session) IMReportItemConfigDo {
	return m.withDO(m.DO.Session(config))
}

func (m mReportItemConfigDo) Clauses(conds ...clause.Expression) IMReportItemConfigDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mReportItemConfigDo) Returning(value interface{}, columns ...string) IMReportItemConfigDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mReportItemConfigDo) Not(conds ...gen.Condition) IMReportItemConfigDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mReportItemConfigDo) Or(conds ...gen.Condition) IMReportItemConfigDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mReportItemConfigDo) Select(conds ...field.Expr) IMReportItemConfigDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mReportItemConfigDo) Where(conds ...gen.Condition) IMReportItemConfigDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mReportItemConfigDo) Order(conds ...field.Expr) IMReportItemConfigDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mReportItemConfigDo) Distinct(cols ...field.Expr) IMReportItemConfigDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mReportItemConfigDo) Omit(cols ...field.Expr) IMReportItemConfigDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mReportItemConfigDo) Join(table schema.Tabler, on ...field.Expr) IMReportItemConfigDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mReportItemConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMReportItemConfigDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mReportItemConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) IMReportItemConfigDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mReportItemConfigDo) Group(cols ...field.Expr) IMReportItemConfigDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mReportItemConfigDo) Having(conds ...gen.Condition) IMReportItemConfigDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mReportItemConfigDo) Limit(limit int) IMReportItemConfigDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mReportItemConfigDo) Offset(offset int) IMReportItemConfigDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mReportItemConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMReportItemConfigDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mReportItemConfigDo) Unscoped() IMReportItemConfigDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mReportItemConfigDo) Create(values ...*model.MReportItemConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mReportItemConfigDo) CreateInBatches(values []*model.MReportItemConfig, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mReportItemConfigDo) Save(values ...*model.MReportItemConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mReportItemConfigDo) First() (*model.MReportItemConfig, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportItemConfig), nil
	}
}

func (m mReportItemConfigDo) Take() (*model.MReportItemConfig, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportItemConfig), nil
	}
}

func (m mReportItemConfigDo) Last() (*model.MReportItemConfig, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportItemConfig), nil
	}
}

func (m mReportItemConfigDo) Find() ([]*model.MReportItemConfig, error) {
	result, err := m.DO.Find()
	return result.([]*model.MReportItemConfig), err
}

func (m mReportItemConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MReportItemConfig, err error) {
	buf := make([]*model.MReportItemConfig, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mReportItemConfigDo) FindInBatches(result *[]*model.MReportItemConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mReportItemConfigDo) Attrs(attrs ...field.AssignExpr) IMReportItemConfigDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mReportItemConfigDo) Assign(attrs ...field.AssignExpr) IMReportItemConfigDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mReportItemConfigDo) Joins(fields ...field.RelationField) IMReportItemConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mReportItemConfigDo) Preload(fields ...field.RelationField) IMReportItemConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mReportItemConfigDo) FirstOrInit() (*model.MReportItemConfig, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportItemConfig), nil
	}
}

func (m mReportItemConfigDo) FirstOrCreate() (*model.MReportItemConfig, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportItemConfig), nil
	}
}

func (m mReportItemConfigDo) FindByPage(offset int, limit int) (result []*model.MReportItemConfig, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mReportItemConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mReportItemConfigDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mReportItemConfigDo) Delete(models ...*model.MReportItemConfig) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mReportItemConfigDo) withDO(do gen.Dao) *mReportItemConfigDo {
	m.DO = *do.(*gen.DO)
	return m
}
