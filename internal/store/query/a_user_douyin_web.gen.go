// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newAUserDouyinWeb(db *gorm.DB, opts ...gen.DOOption) aUserDouyinWeb {
	_aUserDouyinWeb := aUserDouyinWeb{}

	_aUserDouyinWeb.aUserDouyinWebDo.UseDB(db, opts...)
	_aUserDouyinWeb.aUserDouyinWebDo.UseModel(&model.AUserDouyinWeb{})

	tableName := _aUserDouyinWeb.aUserDouyinWebDo.TableName()
	_aUserDouyinWeb.ALL = field.NewAsterisk(tableName)
	_aUserDouyinWeb.ClientKey = field.NewString(tableName, "client_key")
	_aUserDouyinWeb.OpenID = field.NewString(tableName, "open_id")
	_aUserDouyinWeb.UnionID = field.NewString(tableName, "union_id")
	_aUserDouyinWeb.Nickname = field.NewString(tableName, "nickname")
	_aUserDouyinWeb.Avatar = field.NewString(tableName, "avatar")
	_aUserDouyinWeb.EAccountRole = field.NewString(tableName, "e_account_role")

	_aUserDouyinWeb.fillFieldMap()

	return _aUserDouyinWeb
}

// aUserDouyinWeb 抖音开放平台-获取用户公开信息（/oauth/userinfo/）
type aUserDouyinWeb struct {
	aUserDouyinWebDo

	ALL          field.Asterisk
	ClientKey    field.String // API data.client_key 应用client_key
	OpenID       field.String // API data.open_id 用户在应用下的唯一标识
	UnionID      field.String // API data.union_id 用户在开发者账号下的唯一标识（可空）
	Nickname     field.String // API data.nickname 用户昵称（可空）
	Avatar       field.String // API data.avatar 用户头像URL（可空）
	EAccountRole field.String // API data.e_account_role 企业号认证身份：EAccountM/EAccountS/EAccountK（可空）

	fieldMap map[string]field.Expr
}

func (a aUserDouyinWeb) Table(newTableName string) *aUserDouyinWeb {
	a.aUserDouyinWebDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aUserDouyinWeb) As(alias string) *aUserDouyinWeb {
	a.aUserDouyinWebDo.DO = *(a.aUserDouyinWebDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aUserDouyinWeb) updateTableName(table string) *aUserDouyinWeb {
	a.ALL = field.NewAsterisk(table)
	a.ClientKey = field.NewString(table, "client_key")
	a.OpenID = field.NewString(table, "open_id")
	a.UnionID = field.NewString(table, "union_id")
	a.Nickname = field.NewString(table, "nickname")
	a.Avatar = field.NewString(table, "avatar")
	a.EAccountRole = field.NewString(table, "e_account_role")

	a.fillFieldMap()

	return a
}

func (a *aUserDouyinWeb) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aUserDouyinWeb) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 6)
	a.fieldMap["client_key"] = a.ClientKey
	a.fieldMap["open_id"] = a.OpenID
	a.fieldMap["union_id"] = a.UnionID
	a.fieldMap["nickname"] = a.Nickname
	a.fieldMap["avatar"] = a.Avatar
	a.fieldMap["e_account_role"] = a.EAccountRole
}

func (a aUserDouyinWeb) clone(db *gorm.DB) aUserDouyinWeb {
	a.aUserDouyinWebDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aUserDouyinWeb) replaceDB(db *gorm.DB) aUserDouyinWeb {
	a.aUserDouyinWebDo.ReplaceDB(db)
	return a
}

type aUserDouyinWebDo struct{ gen.DO }

type IAUserDouyinWebDo interface {
	gen.SubQuery
	Debug() IAUserDouyinWebDo
	WithContext(ctx context.Context) IAUserDouyinWebDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAUserDouyinWebDo
	WriteDB() IAUserDouyinWebDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAUserDouyinWebDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAUserDouyinWebDo
	Not(conds ...gen.Condition) IAUserDouyinWebDo
	Or(conds ...gen.Condition) IAUserDouyinWebDo
	Select(conds ...field.Expr) IAUserDouyinWebDo
	Where(conds ...gen.Condition) IAUserDouyinWebDo
	Order(conds ...field.Expr) IAUserDouyinWebDo
	Distinct(cols ...field.Expr) IAUserDouyinWebDo
	Omit(cols ...field.Expr) IAUserDouyinWebDo
	Join(table schema.Tabler, on ...field.Expr) IAUserDouyinWebDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAUserDouyinWebDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAUserDouyinWebDo
	Group(cols ...field.Expr) IAUserDouyinWebDo
	Having(conds ...gen.Condition) IAUserDouyinWebDo
	Limit(limit int) IAUserDouyinWebDo
	Offset(offset int) IAUserDouyinWebDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserDouyinWebDo
	Unscoped() IAUserDouyinWebDo
	Create(values ...*model.AUserDouyinWeb) error
	CreateInBatches(values []*model.AUserDouyinWeb, batchSize int) error
	Save(values ...*model.AUserDouyinWeb) error
	First() (*model.AUserDouyinWeb, error)
	Take() (*model.AUserDouyinWeb, error)
	Last() (*model.AUserDouyinWeb, error)
	Find() ([]*model.AUserDouyinWeb, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUserDouyinWeb, err error)
	FindInBatches(result *[]*model.AUserDouyinWeb, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AUserDouyinWeb) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAUserDouyinWebDo
	Assign(attrs ...field.AssignExpr) IAUserDouyinWebDo
	Joins(fields ...field.RelationField) IAUserDouyinWebDo
	Preload(fields ...field.RelationField) IAUserDouyinWebDo
	FirstOrInit() (*model.AUserDouyinWeb, error)
	FirstOrCreate() (*model.AUserDouyinWeb, error)
	FindByPage(offset int, limit int) (result []*model.AUserDouyinWeb, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAUserDouyinWebDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aUserDouyinWebDo) Debug() IAUserDouyinWebDo {
	return a.withDO(a.DO.Debug())
}

func (a aUserDouyinWebDo) WithContext(ctx context.Context) IAUserDouyinWebDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aUserDouyinWebDo) ReadDB() IAUserDouyinWebDo {
	return a.Clauses(dbresolver.Read)
}

func (a aUserDouyinWebDo) WriteDB() IAUserDouyinWebDo {
	return a.Clauses(dbresolver.Write)
}

func (a aUserDouyinWebDo) Session(config *gorm.Session) IAUserDouyinWebDo {
	return a.withDO(a.DO.Session(config))
}

func (a aUserDouyinWebDo) Clauses(conds ...clause.Expression) IAUserDouyinWebDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aUserDouyinWebDo) Returning(value interface{}, columns ...string) IAUserDouyinWebDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aUserDouyinWebDo) Not(conds ...gen.Condition) IAUserDouyinWebDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aUserDouyinWebDo) Or(conds ...gen.Condition) IAUserDouyinWebDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aUserDouyinWebDo) Select(conds ...field.Expr) IAUserDouyinWebDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aUserDouyinWebDo) Where(conds ...gen.Condition) IAUserDouyinWebDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aUserDouyinWebDo) Order(conds ...field.Expr) IAUserDouyinWebDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aUserDouyinWebDo) Distinct(cols ...field.Expr) IAUserDouyinWebDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aUserDouyinWebDo) Omit(cols ...field.Expr) IAUserDouyinWebDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aUserDouyinWebDo) Join(table schema.Tabler, on ...field.Expr) IAUserDouyinWebDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aUserDouyinWebDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAUserDouyinWebDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aUserDouyinWebDo) RightJoin(table schema.Tabler, on ...field.Expr) IAUserDouyinWebDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aUserDouyinWebDo) Group(cols ...field.Expr) IAUserDouyinWebDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aUserDouyinWebDo) Having(conds ...gen.Condition) IAUserDouyinWebDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aUserDouyinWebDo) Limit(limit int) IAUserDouyinWebDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aUserDouyinWebDo) Offset(offset int) IAUserDouyinWebDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aUserDouyinWebDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserDouyinWebDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aUserDouyinWebDo) Unscoped() IAUserDouyinWebDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aUserDouyinWebDo) Create(values ...*model.AUserDouyinWeb) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aUserDouyinWebDo) CreateInBatches(values []*model.AUserDouyinWeb, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aUserDouyinWebDo) Save(values ...*model.AUserDouyinWeb) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aUserDouyinWebDo) First() (*model.AUserDouyinWeb, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserDouyinWeb), nil
	}
}

func (a aUserDouyinWebDo) Take() (*model.AUserDouyinWeb, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserDouyinWeb), nil
	}
}

func (a aUserDouyinWebDo) Last() (*model.AUserDouyinWeb, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserDouyinWeb), nil
	}
}

func (a aUserDouyinWebDo) Find() ([]*model.AUserDouyinWeb, error) {
	result, err := a.DO.Find()
	return result.([]*model.AUserDouyinWeb), err
}

func (a aUserDouyinWebDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUserDouyinWeb, err error) {
	buf := make([]*model.AUserDouyinWeb, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aUserDouyinWebDo) FindInBatches(result *[]*model.AUserDouyinWeb, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aUserDouyinWebDo) Attrs(attrs ...field.AssignExpr) IAUserDouyinWebDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aUserDouyinWebDo) Assign(attrs ...field.AssignExpr) IAUserDouyinWebDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aUserDouyinWebDo) Joins(fields ...field.RelationField) IAUserDouyinWebDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aUserDouyinWebDo) Preload(fields ...field.RelationField) IAUserDouyinWebDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aUserDouyinWebDo) FirstOrInit() (*model.AUserDouyinWeb, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserDouyinWeb), nil
	}
}

func (a aUserDouyinWebDo) FirstOrCreate() (*model.AUserDouyinWeb, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserDouyinWeb), nil
	}
}

func (a aUserDouyinWebDo) FindByPage(offset int, limit int) (result []*model.AUserDouyinWeb, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aUserDouyinWebDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aUserDouyinWebDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aUserDouyinWebDo) Delete(models ...*model.AUserDouyinWeb) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aUserDouyinWebDo) withDO(do gen.Dao) *aUserDouyinWebDo {
	a.DO = *do.(*gen.DO)
	return a
}
