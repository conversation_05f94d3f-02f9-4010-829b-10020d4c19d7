// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMReportActionConfig(db *gorm.DB, opts ...gen.DOOption) mReportActionConfig {
	_mReportActionConfig := mReportActionConfig{}

	_mReportActionConfig.mReportActionConfigDo.UseDB(db, opts...)
	_mReportActionConfig.mReportActionConfigDo.UseModel(&model.MReportActionConfig{})

	tableName := _mReportActionConfig.mReportActionConfigDo.TableName()
	_mReportActionConfig.ALL = field.NewAsterisk(tableName)
	_mReportActionConfig.ID = field.NewInt32(tableName, "id")
	_mReportActionConfig.GameID = field.NewString(tableName, "game_id")
	_mReportActionConfig.ActionValue = field.NewInt32(tableName, "action_value")
	_mReportActionConfig.Description = field.NewString(tableName, "description")
	_mReportActionConfig.IsPreset = field.NewBool(tableName, "is_preset")
	_mReportActionConfig.ParamsDefinition = field.NewString(tableName, "params_definition")
	_mReportActionConfig.CreatedAt = field.NewInt64(tableName, "created_at")
	_mReportActionConfig.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mReportActionConfig.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mReportActionConfig.fillFieldMap()

	return _mReportActionConfig
}

// mReportActionConfig 举报处理动作配置表
type mReportActionConfig struct {
	mReportActionConfigDo

	ALL              field.Asterisk
	ID               field.Int32  // 主键ID
	GameID           field.String // 游戏ID, 为空表示全局配置
	ActionValue      field.Int32  // 处理动作的数值
	Description      field.String // 描述
	IsPreset         field.Bool   // 是否预置, 0:否, 1:是, 预置不可修改
	ParamsDefinition field.String // 参数定义列表 [{"param_key":"duration", "param_desc":"封禁时长", "param_type":"text", "is_required":true}]
	CreatedAt        field.Int64  // 创建时间戳
	UpdatedAt        field.Int64  // 更新时间戳
	IsDeleted        field.Bool   // 软删除标记 0:未删除 1:已删除

	fieldMap map[string]field.Expr
}

func (m mReportActionConfig) Table(newTableName string) *mReportActionConfig {
	m.mReportActionConfigDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mReportActionConfig) As(alias string) *mReportActionConfig {
	m.mReportActionConfigDo.DO = *(m.mReportActionConfigDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mReportActionConfig) updateTableName(table string) *mReportActionConfig {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.ActionValue = field.NewInt32(table, "action_value")
	m.Description = field.NewString(table, "description")
	m.IsPreset = field.NewBool(table, "is_preset")
	m.ParamsDefinition = field.NewString(table, "params_definition")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mReportActionConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mReportActionConfig) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 9)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["action_value"] = m.ActionValue
	m.fieldMap["description"] = m.Description
	m.fieldMap["is_preset"] = m.IsPreset
	m.fieldMap["params_definition"] = m.ParamsDefinition
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mReportActionConfig) clone(db *gorm.DB) mReportActionConfig {
	m.mReportActionConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mReportActionConfig) replaceDB(db *gorm.DB) mReportActionConfig {
	m.mReportActionConfigDo.ReplaceDB(db)
	return m
}

type mReportActionConfigDo struct{ gen.DO }

type IMReportActionConfigDo interface {
	gen.SubQuery
	Debug() IMReportActionConfigDo
	WithContext(ctx context.Context) IMReportActionConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMReportActionConfigDo
	WriteDB() IMReportActionConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMReportActionConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMReportActionConfigDo
	Not(conds ...gen.Condition) IMReportActionConfigDo
	Or(conds ...gen.Condition) IMReportActionConfigDo
	Select(conds ...field.Expr) IMReportActionConfigDo
	Where(conds ...gen.Condition) IMReportActionConfigDo
	Order(conds ...field.Expr) IMReportActionConfigDo
	Distinct(cols ...field.Expr) IMReportActionConfigDo
	Omit(cols ...field.Expr) IMReportActionConfigDo
	Join(table schema.Tabler, on ...field.Expr) IMReportActionConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMReportActionConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMReportActionConfigDo
	Group(cols ...field.Expr) IMReportActionConfigDo
	Having(conds ...gen.Condition) IMReportActionConfigDo
	Limit(limit int) IMReportActionConfigDo
	Offset(offset int) IMReportActionConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMReportActionConfigDo
	Unscoped() IMReportActionConfigDo
	Create(values ...*model.MReportActionConfig) error
	CreateInBatches(values []*model.MReportActionConfig, batchSize int) error
	Save(values ...*model.MReportActionConfig) error
	First() (*model.MReportActionConfig, error)
	Take() (*model.MReportActionConfig, error)
	Last() (*model.MReportActionConfig, error)
	Find() ([]*model.MReportActionConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MReportActionConfig, err error)
	FindInBatches(result *[]*model.MReportActionConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MReportActionConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMReportActionConfigDo
	Assign(attrs ...field.AssignExpr) IMReportActionConfigDo
	Joins(fields ...field.RelationField) IMReportActionConfigDo
	Preload(fields ...field.RelationField) IMReportActionConfigDo
	FirstOrInit() (*model.MReportActionConfig, error)
	FirstOrCreate() (*model.MReportActionConfig, error)
	FindByPage(offset int, limit int) (result []*model.MReportActionConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMReportActionConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mReportActionConfigDo) Debug() IMReportActionConfigDo {
	return m.withDO(m.DO.Debug())
}

func (m mReportActionConfigDo) WithContext(ctx context.Context) IMReportActionConfigDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mReportActionConfigDo) ReadDB() IMReportActionConfigDo {
	return m.Clauses(dbresolver.Read)
}

func (m mReportActionConfigDo) WriteDB() IMReportActionConfigDo {
	return m.Clauses(dbresolver.Write)
}

func (m mReportActionConfigDo) Session(config *gorm.Session) IMReportActionConfigDo {
	return m.withDO(m.DO.Session(config))
}

func (m mReportActionConfigDo) Clauses(conds ...clause.Expression) IMReportActionConfigDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mReportActionConfigDo) Returning(value interface{}, columns ...string) IMReportActionConfigDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mReportActionConfigDo) Not(conds ...gen.Condition) IMReportActionConfigDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mReportActionConfigDo) Or(conds ...gen.Condition) IMReportActionConfigDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mReportActionConfigDo) Select(conds ...field.Expr) IMReportActionConfigDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mReportActionConfigDo) Where(conds ...gen.Condition) IMReportActionConfigDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mReportActionConfigDo) Order(conds ...field.Expr) IMReportActionConfigDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mReportActionConfigDo) Distinct(cols ...field.Expr) IMReportActionConfigDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mReportActionConfigDo) Omit(cols ...field.Expr) IMReportActionConfigDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mReportActionConfigDo) Join(table schema.Tabler, on ...field.Expr) IMReportActionConfigDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mReportActionConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMReportActionConfigDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mReportActionConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) IMReportActionConfigDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mReportActionConfigDo) Group(cols ...field.Expr) IMReportActionConfigDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mReportActionConfigDo) Having(conds ...gen.Condition) IMReportActionConfigDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mReportActionConfigDo) Limit(limit int) IMReportActionConfigDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mReportActionConfigDo) Offset(offset int) IMReportActionConfigDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mReportActionConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMReportActionConfigDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mReportActionConfigDo) Unscoped() IMReportActionConfigDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mReportActionConfigDo) Create(values ...*model.MReportActionConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mReportActionConfigDo) CreateInBatches(values []*model.MReportActionConfig, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mReportActionConfigDo) Save(values ...*model.MReportActionConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mReportActionConfigDo) First() (*model.MReportActionConfig, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportActionConfig), nil
	}
}

func (m mReportActionConfigDo) Take() (*model.MReportActionConfig, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportActionConfig), nil
	}
}

func (m mReportActionConfigDo) Last() (*model.MReportActionConfig, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportActionConfig), nil
	}
}

func (m mReportActionConfigDo) Find() ([]*model.MReportActionConfig, error) {
	result, err := m.DO.Find()
	return result.([]*model.MReportActionConfig), err
}

func (m mReportActionConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MReportActionConfig, err error) {
	buf := make([]*model.MReportActionConfig, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mReportActionConfigDo) FindInBatches(result *[]*model.MReportActionConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mReportActionConfigDo) Attrs(attrs ...field.AssignExpr) IMReportActionConfigDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mReportActionConfigDo) Assign(attrs ...field.AssignExpr) IMReportActionConfigDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mReportActionConfigDo) Joins(fields ...field.RelationField) IMReportActionConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mReportActionConfigDo) Preload(fields ...field.RelationField) IMReportActionConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mReportActionConfigDo) FirstOrInit() (*model.MReportActionConfig, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportActionConfig), nil
	}
}

func (m mReportActionConfigDo) FirstOrCreate() (*model.MReportActionConfig, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReportActionConfig), nil
	}
}

func (m mReportActionConfigDo) FindByPage(offset int, limit int) (result []*model.MReportActionConfig, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mReportActionConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mReportActionConfigDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mReportActionConfigDo) Delete(models ...*model.MReportActionConfig) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mReportActionConfigDo) withDO(do gen.Dao) *mReportActionConfigDo {
	m.DO = *do.(*gen.DO)
	return m
}
