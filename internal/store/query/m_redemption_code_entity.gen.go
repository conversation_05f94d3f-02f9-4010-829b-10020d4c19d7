// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMRedemptionCodeEntity(db *gorm.DB, opts ...gen.DOOption) mRedemptionCodeEntity {
	_mRedemptionCodeEntity := mRedemptionCodeEntity{}

	_mRedemptionCodeEntity.mRedemptionCodeEntityDo.UseDB(db, opts...)
	_mRedemptionCodeEntity.mRedemptionCodeEntityDo.UseModel(&model.MRedemptionCodeEntity{})

	tableName := _mRedemptionCodeEntity.mRedemptionCodeEntityDo.TableName()
	_mRedemptionCodeEntity.ALL = field.NewAsterisk(tableName)
	_mRedemptionCodeEntity.ID = field.NewInt32(tableName, "id")
	_mRedemptionCodeEntity.CodeID = field.NewInt32(tableName, "code_id")
	_mRedemptionCodeEntity.Code = field.NewString(tableName, "code")
	_mRedemptionCodeEntity.Status = field.NewInt32(tableName, "status")
	_mRedemptionCodeEntity.SpendUserID = field.NewString(tableName, "spend_user_id")
	_mRedemptionCodeEntity.Frequency = field.NewInt32(tableName, "frequency")
	_mRedemptionCodeEntity.CreatedAt = field.NewInt64(tableName, "created_at")
	_mRedemptionCodeEntity.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mRedemptionCodeEntity.fillFieldMap()

	return _mRedemptionCodeEntity
}

type mRedemptionCodeEntity struct {
	mRedemptionCodeEntityDo

	ALL         field.Asterisk
	ID          field.Int32
	CodeID      field.Int32
	Code        field.String
	Status      field.Int32  // 使用状态 1 未使用 2 已使用 3 已下发，但是未使用
	SpendUserID field.String // 使用的user id
	Frequency   field.Int32  // 兑换次数
	CreatedAt   field.Int64
	UpdatedAt   field.Int64

	fieldMap map[string]field.Expr
}

func (m mRedemptionCodeEntity) Table(newTableName string) *mRedemptionCodeEntity {
	m.mRedemptionCodeEntityDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mRedemptionCodeEntity) As(alias string) *mRedemptionCodeEntity {
	m.mRedemptionCodeEntityDo.DO = *(m.mRedemptionCodeEntityDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mRedemptionCodeEntity) updateTableName(table string) *mRedemptionCodeEntity {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.CodeID = field.NewInt32(table, "code_id")
	m.Code = field.NewString(table, "code")
	m.Status = field.NewInt32(table, "status")
	m.SpendUserID = field.NewString(table, "spend_user_id")
	m.Frequency = field.NewInt32(table, "frequency")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mRedemptionCodeEntity) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mRedemptionCodeEntity) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 8)
	m.fieldMap["id"] = m.ID
	m.fieldMap["code_id"] = m.CodeID
	m.fieldMap["code"] = m.Code
	m.fieldMap["status"] = m.Status
	m.fieldMap["spend_user_id"] = m.SpendUserID
	m.fieldMap["frequency"] = m.Frequency
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mRedemptionCodeEntity) clone(db *gorm.DB) mRedemptionCodeEntity {
	m.mRedemptionCodeEntityDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mRedemptionCodeEntity) replaceDB(db *gorm.DB) mRedemptionCodeEntity {
	m.mRedemptionCodeEntityDo.ReplaceDB(db)
	return m
}

type mRedemptionCodeEntityDo struct{ gen.DO }

type IMRedemptionCodeEntityDo interface {
	gen.SubQuery
	Debug() IMRedemptionCodeEntityDo
	WithContext(ctx context.Context) IMRedemptionCodeEntityDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMRedemptionCodeEntityDo
	WriteDB() IMRedemptionCodeEntityDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMRedemptionCodeEntityDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMRedemptionCodeEntityDo
	Not(conds ...gen.Condition) IMRedemptionCodeEntityDo
	Or(conds ...gen.Condition) IMRedemptionCodeEntityDo
	Select(conds ...field.Expr) IMRedemptionCodeEntityDo
	Where(conds ...gen.Condition) IMRedemptionCodeEntityDo
	Order(conds ...field.Expr) IMRedemptionCodeEntityDo
	Distinct(cols ...field.Expr) IMRedemptionCodeEntityDo
	Omit(cols ...field.Expr) IMRedemptionCodeEntityDo
	Join(table schema.Tabler, on ...field.Expr) IMRedemptionCodeEntityDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMRedemptionCodeEntityDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMRedemptionCodeEntityDo
	Group(cols ...field.Expr) IMRedemptionCodeEntityDo
	Having(conds ...gen.Condition) IMRedemptionCodeEntityDo
	Limit(limit int) IMRedemptionCodeEntityDo
	Offset(offset int) IMRedemptionCodeEntityDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMRedemptionCodeEntityDo
	Unscoped() IMRedemptionCodeEntityDo
	Create(values ...*model.MRedemptionCodeEntity) error
	CreateInBatches(values []*model.MRedemptionCodeEntity, batchSize int) error
	Save(values ...*model.MRedemptionCodeEntity) error
	First() (*model.MRedemptionCodeEntity, error)
	Take() (*model.MRedemptionCodeEntity, error)
	Last() (*model.MRedemptionCodeEntity, error)
	Find() ([]*model.MRedemptionCodeEntity, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MRedemptionCodeEntity, err error)
	FindInBatches(result *[]*model.MRedemptionCodeEntity, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MRedemptionCodeEntity) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMRedemptionCodeEntityDo
	Assign(attrs ...field.AssignExpr) IMRedemptionCodeEntityDo
	Joins(fields ...field.RelationField) IMRedemptionCodeEntityDo
	Preload(fields ...field.RelationField) IMRedemptionCodeEntityDo
	FirstOrInit() (*model.MRedemptionCodeEntity, error)
	FirstOrCreate() (*model.MRedemptionCodeEntity, error)
	FindByPage(offset int, limit int) (result []*model.MRedemptionCodeEntity, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMRedemptionCodeEntityDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mRedemptionCodeEntityDo) Debug() IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Debug())
}

func (m mRedemptionCodeEntityDo) WithContext(ctx context.Context) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mRedemptionCodeEntityDo) ReadDB() IMRedemptionCodeEntityDo {
	return m.Clauses(dbresolver.Read)
}

func (m mRedemptionCodeEntityDo) WriteDB() IMRedemptionCodeEntityDo {
	return m.Clauses(dbresolver.Write)
}

func (m mRedemptionCodeEntityDo) Session(config *gorm.Session) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Session(config))
}

func (m mRedemptionCodeEntityDo) Clauses(conds ...clause.Expression) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mRedemptionCodeEntityDo) Returning(value interface{}, columns ...string) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mRedemptionCodeEntityDo) Not(conds ...gen.Condition) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mRedemptionCodeEntityDo) Or(conds ...gen.Condition) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mRedemptionCodeEntityDo) Select(conds ...field.Expr) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mRedemptionCodeEntityDo) Where(conds ...gen.Condition) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mRedemptionCodeEntityDo) Order(conds ...field.Expr) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mRedemptionCodeEntityDo) Distinct(cols ...field.Expr) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mRedemptionCodeEntityDo) Omit(cols ...field.Expr) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mRedemptionCodeEntityDo) Join(table schema.Tabler, on ...field.Expr) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mRedemptionCodeEntityDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mRedemptionCodeEntityDo) RightJoin(table schema.Tabler, on ...field.Expr) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mRedemptionCodeEntityDo) Group(cols ...field.Expr) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mRedemptionCodeEntityDo) Having(conds ...gen.Condition) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mRedemptionCodeEntityDo) Limit(limit int) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mRedemptionCodeEntityDo) Offset(offset int) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mRedemptionCodeEntityDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mRedemptionCodeEntityDo) Unscoped() IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mRedemptionCodeEntityDo) Create(values ...*model.MRedemptionCodeEntity) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mRedemptionCodeEntityDo) CreateInBatches(values []*model.MRedemptionCodeEntity, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mRedemptionCodeEntityDo) Save(values ...*model.MRedemptionCodeEntity) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mRedemptionCodeEntityDo) First() (*model.MRedemptionCodeEntity, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCodeEntity), nil
	}
}

func (m mRedemptionCodeEntityDo) Take() (*model.MRedemptionCodeEntity, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCodeEntity), nil
	}
}

func (m mRedemptionCodeEntityDo) Last() (*model.MRedemptionCodeEntity, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCodeEntity), nil
	}
}

func (m mRedemptionCodeEntityDo) Find() ([]*model.MRedemptionCodeEntity, error) {
	result, err := m.DO.Find()
	return result.([]*model.MRedemptionCodeEntity), err
}

func (m mRedemptionCodeEntityDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MRedemptionCodeEntity, err error) {
	buf := make([]*model.MRedemptionCodeEntity, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mRedemptionCodeEntityDo) FindInBatches(result *[]*model.MRedemptionCodeEntity, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mRedemptionCodeEntityDo) Attrs(attrs ...field.AssignExpr) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mRedemptionCodeEntityDo) Assign(attrs ...field.AssignExpr) IMRedemptionCodeEntityDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mRedemptionCodeEntityDo) Joins(fields ...field.RelationField) IMRedemptionCodeEntityDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mRedemptionCodeEntityDo) Preload(fields ...field.RelationField) IMRedemptionCodeEntityDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mRedemptionCodeEntityDo) FirstOrInit() (*model.MRedemptionCodeEntity, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCodeEntity), nil
	}
}

func (m mRedemptionCodeEntityDo) FirstOrCreate() (*model.MRedemptionCodeEntity, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MRedemptionCodeEntity), nil
	}
}

func (m mRedemptionCodeEntityDo) FindByPage(offset int, limit int) (result []*model.MRedemptionCodeEntity, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mRedemptionCodeEntityDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mRedemptionCodeEntityDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mRedemptionCodeEntityDo) Delete(models ...*model.MRedemptionCodeEntity) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mRedemptionCodeEntityDo) withDO(do gen.Dao) *mRedemptionCodeEntityDo {
	m.DO = *do.(*gen.DO)
	return m
}
