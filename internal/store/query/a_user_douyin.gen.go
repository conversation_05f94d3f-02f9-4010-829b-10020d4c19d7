// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newAUserDouyin(db *gorm.DB, opts ...gen.DOOption) aUserDouyin {
	_aUserDouyin := aUserDouyin{}

	_aUserDouyin.aUserDouyinDo.UseDB(db, opts...)
	_aUserDouyin.aUserDouyinDo.UseModel(&model.AUserDouyin{})

	tableName := _aUserDouyin.aUserDouyinDo.TableName()
	_aUserDouyin.ALL = field.NewAsterisk(tableName)
	_aUserDouyin.ID = field.NewInt32(tableName, "id")
	_aUserDouyin.UserID = field.NewString(tableName, "user_id")
	_aUserDouyin.OpenID = field.NewString(tableName, "open_id")
	_aUserDouyin.UnionID = field.NewString(tableName, "union_id")
	_aUserDouyin.AnonymousOpenID = field.NewString(tableName, "anonymous_open_id")
	_aUserDouyin.NickName = field.NewString(tableName, "nick_name")
	_aUserDouyin.AvatarURL = field.NewString(tableName, "avatar_url")
	_aUserDouyin.Gender = field.NewInt32(tableName, "gender")
	_aUserDouyin.City = field.NewString(tableName, "city")
	_aUserDouyin.Province = field.NewString(tableName, "province")
	_aUserDouyin.Country = field.NewString(tableName, "country")
	_aUserDouyin.WatermarkAppID = field.NewString(tableName, "watermark_app_id")
	_aUserDouyin.WatermarkTimestamp = field.NewInt64(tableName, "watermark_timestamp")
	_aUserDouyin.RealNameAuth = field.NewString(tableName, "real_name_auth")
	_aUserDouyin.SessionKey = field.NewString(tableName, "session_key")
	_aUserDouyin.CreatedAt = field.NewInt64(tableName, "created_at")
	_aUserDouyin.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aUserDouyin.IsDeleted = field.NewBool(tableName, "is_deleted")

	_aUserDouyin.fillFieldMap()

	return _aUserDouyin
}

type aUserDouyin struct {
	aUserDouyinDo

	ALL                field.Asterisk
	ID                 field.Int32
	UserID             field.String
	OpenID             field.String
	UnionID            field.String
	AnonymousOpenID    field.String
	NickName           field.String
	AvatarURL          field.String
	Gender             field.Int32
	City               field.String
	Province           field.String
	Country            field.String
	WatermarkAppID     field.String
	WatermarkTimestamp field.Int64
	RealNameAuth       field.String // 实名认证情况
	SessionKey         field.String
	CreatedAt          field.Int64
	UpdatedAt          field.Int64
	IsDeleted          field.Bool

	fieldMap map[string]field.Expr
}

func (a aUserDouyin) Table(newTableName string) *aUserDouyin {
	a.aUserDouyinDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aUserDouyin) As(alias string) *aUserDouyin {
	a.aUserDouyinDo.DO = *(a.aUserDouyinDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aUserDouyin) updateTableName(table string) *aUserDouyin {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.UserID = field.NewString(table, "user_id")
	a.OpenID = field.NewString(table, "open_id")
	a.UnionID = field.NewString(table, "union_id")
	a.AnonymousOpenID = field.NewString(table, "anonymous_open_id")
	a.NickName = field.NewString(table, "nick_name")
	a.AvatarURL = field.NewString(table, "avatar_url")
	a.Gender = field.NewInt32(table, "gender")
	a.City = field.NewString(table, "city")
	a.Province = field.NewString(table, "province")
	a.Country = field.NewString(table, "country")
	a.WatermarkAppID = field.NewString(table, "watermark_app_id")
	a.WatermarkTimestamp = field.NewInt64(table, "watermark_timestamp")
	a.RealNameAuth = field.NewString(table, "real_name_auth")
	a.SessionKey = field.NewString(table, "session_key")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.IsDeleted = field.NewBool(table, "is_deleted")

	a.fillFieldMap()

	return a
}

func (a *aUserDouyin) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aUserDouyin) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 18)
	a.fieldMap["id"] = a.ID
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["open_id"] = a.OpenID
	a.fieldMap["union_id"] = a.UnionID
	a.fieldMap["anonymous_open_id"] = a.AnonymousOpenID
	a.fieldMap["nick_name"] = a.NickName
	a.fieldMap["avatar_url"] = a.AvatarURL
	a.fieldMap["gender"] = a.Gender
	a.fieldMap["city"] = a.City
	a.fieldMap["province"] = a.Province
	a.fieldMap["country"] = a.Country
	a.fieldMap["watermark_app_id"] = a.WatermarkAppID
	a.fieldMap["watermark_timestamp"] = a.WatermarkTimestamp
	a.fieldMap["real_name_auth"] = a.RealNameAuth
	a.fieldMap["session_key"] = a.SessionKey
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_deleted"] = a.IsDeleted
}

func (a aUserDouyin) clone(db *gorm.DB) aUserDouyin {
	a.aUserDouyinDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aUserDouyin) replaceDB(db *gorm.DB) aUserDouyin {
	a.aUserDouyinDo.ReplaceDB(db)
	return a
}

type aUserDouyinDo struct{ gen.DO }

type IAUserDouyinDo interface {
	gen.SubQuery
	Debug() IAUserDouyinDo
	WithContext(ctx context.Context) IAUserDouyinDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAUserDouyinDo
	WriteDB() IAUserDouyinDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAUserDouyinDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAUserDouyinDo
	Not(conds ...gen.Condition) IAUserDouyinDo
	Or(conds ...gen.Condition) IAUserDouyinDo
	Select(conds ...field.Expr) IAUserDouyinDo
	Where(conds ...gen.Condition) IAUserDouyinDo
	Order(conds ...field.Expr) IAUserDouyinDo
	Distinct(cols ...field.Expr) IAUserDouyinDo
	Omit(cols ...field.Expr) IAUserDouyinDo
	Join(table schema.Tabler, on ...field.Expr) IAUserDouyinDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAUserDouyinDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAUserDouyinDo
	Group(cols ...field.Expr) IAUserDouyinDo
	Having(conds ...gen.Condition) IAUserDouyinDo
	Limit(limit int) IAUserDouyinDo
	Offset(offset int) IAUserDouyinDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserDouyinDo
	Unscoped() IAUserDouyinDo
	Create(values ...*model.AUserDouyin) error
	CreateInBatches(values []*model.AUserDouyin, batchSize int) error
	Save(values ...*model.AUserDouyin) error
	First() (*model.AUserDouyin, error)
	Take() (*model.AUserDouyin, error)
	Last() (*model.AUserDouyin, error)
	Find() ([]*model.AUserDouyin, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUserDouyin, err error)
	FindInBatches(result *[]*model.AUserDouyin, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AUserDouyin) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAUserDouyinDo
	Assign(attrs ...field.AssignExpr) IAUserDouyinDo
	Joins(fields ...field.RelationField) IAUserDouyinDo
	Preload(fields ...field.RelationField) IAUserDouyinDo
	FirstOrInit() (*model.AUserDouyin, error)
	FirstOrCreate() (*model.AUserDouyin, error)
	FindByPage(offset int, limit int) (result []*model.AUserDouyin, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAUserDouyinDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aUserDouyinDo) Debug() IAUserDouyinDo {
	return a.withDO(a.DO.Debug())
}

func (a aUserDouyinDo) WithContext(ctx context.Context) IAUserDouyinDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aUserDouyinDo) ReadDB() IAUserDouyinDo {
	return a.Clauses(dbresolver.Read)
}

func (a aUserDouyinDo) WriteDB() IAUserDouyinDo {
	return a.Clauses(dbresolver.Write)
}

func (a aUserDouyinDo) Session(config *gorm.Session) IAUserDouyinDo {
	return a.withDO(a.DO.Session(config))
}

func (a aUserDouyinDo) Clauses(conds ...clause.Expression) IAUserDouyinDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aUserDouyinDo) Returning(value interface{}, columns ...string) IAUserDouyinDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aUserDouyinDo) Not(conds ...gen.Condition) IAUserDouyinDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aUserDouyinDo) Or(conds ...gen.Condition) IAUserDouyinDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aUserDouyinDo) Select(conds ...field.Expr) IAUserDouyinDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aUserDouyinDo) Where(conds ...gen.Condition) IAUserDouyinDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aUserDouyinDo) Order(conds ...field.Expr) IAUserDouyinDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aUserDouyinDo) Distinct(cols ...field.Expr) IAUserDouyinDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aUserDouyinDo) Omit(cols ...field.Expr) IAUserDouyinDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aUserDouyinDo) Join(table schema.Tabler, on ...field.Expr) IAUserDouyinDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aUserDouyinDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAUserDouyinDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aUserDouyinDo) RightJoin(table schema.Tabler, on ...field.Expr) IAUserDouyinDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aUserDouyinDo) Group(cols ...field.Expr) IAUserDouyinDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aUserDouyinDo) Having(conds ...gen.Condition) IAUserDouyinDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aUserDouyinDo) Limit(limit int) IAUserDouyinDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aUserDouyinDo) Offset(offset int) IAUserDouyinDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aUserDouyinDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAUserDouyinDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aUserDouyinDo) Unscoped() IAUserDouyinDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aUserDouyinDo) Create(values ...*model.AUserDouyin) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aUserDouyinDo) CreateInBatches(values []*model.AUserDouyin, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aUserDouyinDo) Save(values ...*model.AUserDouyin) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aUserDouyinDo) First() (*model.AUserDouyin, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserDouyin), nil
	}
}

func (a aUserDouyinDo) Take() (*model.AUserDouyin, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserDouyin), nil
	}
}

func (a aUserDouyinDo) Last() (*model.AUserDouyin, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserDouyin), nil
	}
}

func (a aUserDouyinDo) Find() ([]*model.AUserDouyin, error) {
	result, err := a.DO.Find()
	return result.([]*model.AUserDouyin), err
}

func (a aUserDouyinDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AUserDouyin, err error) {
	buf := make([]*model.AUserDouyin, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aUserDouyinDo) FindInBatches(result *[]*model.AUserDouyin, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aUserDouyinDo) Attrs(attrs ...field.AssignExpr) IAUserDouyinDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aUserDouyinDo) Assign(attrs ...field.AssignExpr) IAUserDouyinDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aUserDouyinDo) Joins(fields ...field.RelationField) IAUserDouyinDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aUserDouyinDo) Preload(fields ...field.RelationField) IAUserDouyinDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aUserDouyinDo) FirstOrInit() (*model.AUserDouyin, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserDouyin), nil
	}
}

func (a aUserDouyinDo) FirstOrCreate() (*model.AUserDouyin, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AUserDouyin), nil
	}
}

func (a aUserDouyinDo) FindByPage(offset int, limit int) (result []*model.AUserDouyin, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aUserDouyinDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aUserDouyinDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aUserDouyinDo) Delete(models ...*model.AUserDouyin) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aUserDouyinDo) withDO(do gen.Dao) *aUserDouyinDo {
	a.DO = *do.(*gen.DO)
	return a
}
