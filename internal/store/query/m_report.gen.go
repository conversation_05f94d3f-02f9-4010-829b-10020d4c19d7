// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMReport(db *gorm.DB, opts ...gen.DOOption) mReport {
	_mReport := mReport{}

	_mReport.mReportDo.UseDB(db, opts...)
	_mReport.mReportDo.UseModel(&model.MReport{})

	tableName := _mReport.mReportDo.TableName()
	_mReport.ALL = field.NewAsterisk(tableName)
	_mReport.ID = field.NewInt32(tableName, "id")
	_mReport.GameID = field.NewString(tableName, "game_id")
	_mReport.ReportedPlatformID = field.NewString(tableName, "reported_platform_id")
	_mReport.ReportedServerID = field.NewString(tableName, "reported_server_id")
	_mReport.ReportedRoleID = field.NewString(tableName, "reported_role_id")
	_mReport.ReportedAvatar = field.NewString(tableName, "reported_avatar")
	_mReport.ReportedNickname = field.NewString(tableName, "reported_nickname")
	_mReport.ReporterRoleID = field.NewString(tableName, "reporter_role_id")
	_mReport.ReporterServerID = field.NewString(tableName, "reporter_server_id")
	_mReport.ReporterAvatar = field.NewString(tableName, "reporter_avatar")
	_mReport.ReporterNickname = field.NewString(tableName, "reporter_nickname")
	_mReport.ReporterPlatformID = field.NewString(tableName, "reporter_platform_id")
	_mReport.ReportItem = field.NewString(tableName, "report_item")
	_mReport.ReportReason = field.NewString(tableName, "report_reason")
	_mReport.Status = field.NewInt32(tableName, "status")
	_mReport.ExtraParamA = field.NewString(tableName, "extra_param_a")
	_mReport.ExtraParamB = field.NewString(tableName, "extra_param_b")
	_mReport.SessionFrom = field.NewString(tableName, "session_from")
	_mReport.ReportTimeAt = field.NewInt64(tableName, "report_time_at")
	_mReport.CreatedAt = field.NewInt64(tableName, "created_at")
	_mReport.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mReport.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mReport.fillFieldMap()

	return _mReport
}

// mReport 举报记录表
type mReport struct {
	mReportDo

	ALL                field.Asterisk
	ID                 field.Int32  // 主键ID
	GameID             field.String // 游戏ID
	ReportedPlatformID field.String // 被举报者平台ID
	ReportedServerID   field.String
	ReportedRoleID     field.String // 被举报者角色ID
	ReportedAvatar     field.String
	ReportedNickname   field.String
	ReporterRoleID     field.String // 举报者角色ID
	ReporterServerID   field.String
	ReporterAvatar     field.String
	ReporterNickname   field.String // 举报者昵称
	ReporterPlatformID field.String
	ReportItem         field.String // 举报项 [1, 2,3,4]
	ReportReason       field.String
	Status             field.Int32 // 状态 0:未处理 1:已处理 2: 举报不成立
	ExtraParamA        field.String
	ExtraParamB        field.String // 额外参数
	SessionFrom        field.String // 透传数据
	ReportTimeAt       field.Int64  // 举报时间
	CreatedAt          field.Int64  // 创建时间
	UpdatedAt          field.Int64  // 更新时间
	IsDeleted          field.Bool   // 是否删除 0:否 1:是

	fieldMap map[string]field.Expr
}

func (m mReport) Table(newTableName string) *mReport {
	m.mReportDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mReport) As(alias string) *mReport {
	m.mReportDo.DO = *(m.mReportDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mReport) updateTableName(table string) *mReport {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.ReportedPlatformID = field.NewString(table, "reported_platform_id")
	m.ReportedServerID = field.NewString(table, "reported_server_id")
	m.ReportedRoleID = field.NewString(table, "reported_role_id")
	m.ReportedAvatar = field.NewString(table, "reported_avatar")
	m.ReportedNickname = field.NewString(table, "reported_nickname")
	m.ReporterRoleID = field.NewString(table, "reporter_role_id")
	m.ReporterServerID = field.NewString(table, "reporter_server_id")
	m.ReporterAvatar = field.NewString(table, "reporter_avatar")
	m.ReporterNickname = field.NewString(table, "reporter_nickname")
	m.ReporterPlatformID = field.NewString(table, "reporter_platform_id")
	m.ReportItem = field.NewString(table, "report_item")
	m.ReportReason = field.NewString(table, "report_reason")
	m.Status = field.NewInt32(table, "status")
	m.ExtraParamA = field.NewString(table, "extra_param_a")
	m.ExtraParamB = field.NewString(table, "extra_param_b")
	m.SessionFrom = field.NewString(table, "session_from")
	m.ReportTimeAt = field.NewInt64(table, "report_time_at")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mReport) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mReport) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 22)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["reported_platform_id"] = m.ReportedPlatformID
	m.fieldMap["reported_server_id"] = m.ReportedServerID
	m.fieldMap["reported_role_id"] = m.ReportedRoleID
	m.fieldMap["reported_avatar"] = m.ReportedAvatar
	m.fieldMap["reported_nickname"] = m.ReportedNickname
	m.fieldMap["reporter_role_id"] = m.ReporterRoleID
	m.fieldMap["reporter_server_id"] = m.ReporterServerID
	m.fieldMap["reporter_avatar"] = m.ReporterAvatar
	m.fieldMap["reporter_nickname"] = m.ReporterNickname
	m.fieldMap["reporter_platform_id"] = m.ReporterPlatformID
	m.fieldMap["report_item"] = m.ReportItem
	m.fieldMap["report_reason"] = m.ReportReason
	m.fieldMap["status"] = m.Status
	m.fieldMap["extra_param_a"] = m.ExtraParamA
	m.fieldMap["extra_param_b"] = m.ExtraParamB
	m.fieldMap["session_from"] = m.SessionFrom
	m.fieldMap["report_time_at"] = m.ReportTimeAt
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mReport) clone(db *gorm.DB) mReport {
	m.mReportDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mReport) replaceDB(db *gorm.DB) mReport {
	m.mReportDo.ReplaceDB(db)
	return m
}

type mReportDo struct{ gen.DO }

type IMReportDo interface {
	gen.SubQuery
	Debug() IMReportDo
	WithContext(ctx context.Context) IMReportDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMReportDo
	WriteDB() IMReportDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMReportDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMReportDo
	Not(conds ...gen.Condition) IMReportDo
	Or(conds ...gen.Condition) IMReportDo
	Select(conds ...field.Expr) IMReportDo
	Where(conds ...gen.Condition) IMReportDo
	Order(conds ...field.Expr) IMReportDo
	Distinct(cols ...field.Expr) IMReportDo
	Omit(cols ...field.Expr) IMReportDo
	Join(table schema.Tabler, on ...field.Expr) IMReportDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMReportDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMReportDo
	Group(cols ...field.Expr) IMReportDo
	Having(conds ...gen.Condition) IMReportDo
	Limit(limit int) IMReportDo
	Offset(offset int) IMReportDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMReportDo
	Unscoped() IMReportDo
	Create(values ...*model.MReport) error
	CreateInBatches(values []*model.MReport, batchSize int) error
	Save(values ...*model.MReport) error
	First() (*model.MReport, error)
	Take() (*model.MReport, error)
	Last() (*model.MReport, error)
	Find() ([]*model.MReport, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MReport, err error)
	FindInBatches(result *[]*model.MReport, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MReport) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMReportDo
	Assign(attrs ...field.AssignExpr) IMReportDo
	Joins(fields ...field.RelationField) IMReportDo
	Preload(fields ...field.RelationField) IMReportDo
	FirstOrInit() (*model.MReport, error)
	FirstOrCreate() (*model.MReport, error)
	FindByPage(offset int, limit int) (result []*model.MReport, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMReportDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mReportDo) Debug() IMReportDo {
	return m.withDO(m.DO.Debug())
}

func (m mReportDo) WithContext(ctx context.Context) IMReportDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mReportDo) ReadDB() IMReportDo {
	return m.Clauses(dbresolver.Read)
}

func (m mReportDo) WriteDB() IMReportDo {
	return m.Clauses(dbresolver.Write)
}

func (m mReportDo) Session(config *gorm.Session) IMReportDo {
	return m.withDO(m.DO.Session(config))
}

func (m mReportDo) Clauses(conds ...clause.Expression) IMReportDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mReportDo) Returning(value interface{}, columns ...string) IMReportDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mReportDo) Not(conds ...gen.Condition) IMReportDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mReportDo) Or(conds ...gen.Condition) IMReportDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mReportDo) Select(conds ...field.Expr) IMReportDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mReportDo) Where(conds ...gen.Condition) IMReportDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mReportDo) Order(conds ...field.Expr) IMReportDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mReportDo) Distinct(cols ...field.Expr) IMReportDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mReportDo) Omit(cols ...field.Expr) IMReportDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mReportDo) Join(table schema.Tabler, on ...field.Expr) IMReportDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mReportDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMReportDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mReportDo) RightJoin(table schema.Tabler, on ...field.Expr) IMReportDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mReportDo) Group(cols ...field.Expr) IMReportDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mReportDo) Having(conds ...gen.Condition) IMReportDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mReportDo) Limit(limit int) IMReportDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mReportDo) Offset(offset int) IMReportDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mReportDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMReportDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mReportDo) Unscoped() IMReportDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mReportDo) Create(values ...*model.MReport) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mReportDo) CreateInBatches(values []*model.MReport, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mReportDo) Save(values ...*model.MReport) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mReportDo) First() (*model.MReport, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReport), nil
	}
}

func (m mReportDo) Take() (*model.MReport, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReport), nil
	}
}

func (m mReportDo) Last() (*model.MReport, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReport), nil
	}
}

func (m mReportDo) Find() ([]*model.MReport, error) {
	result, err := m.DO.Find()
	return result.([]*model.MReport), err
}

func (m mReportDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MReport, err error) {
	buf := make([]*model.MReport, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mReportDo) FindInBatches(result *[]*model.MReport, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mReportDo) Attrs(attrs ...field.AssignExpr) IMReportDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mReportDo) Assign(attrs ...field.AssignExpr) IMReportDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mReportDo) Joins(fields ...field.RelationField) IMReportDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mReportDo) Preload(fields ...field.RelationField) IMReportDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mReportDo) FirstOrInit() (*model.MReport, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReport), nil
	}
}

func (m mReportDo) FirstOrCreate() (*model.MReport, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MReport), nil
	}
}

func (m mReportDo) FindByPage(offset int, limit int) (result []*model.MReport, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mReportDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mReportDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mReportDo) Delete(models ...*model.MReport) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mReportDo) withDO(do gen.Dao) *mReportDo {
	m.DO = *do.(*gen.DO)
	return m
}
