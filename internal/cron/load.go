package cron

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
)

// SwitchParam represents a single switch parameter
type SwitchParam struct {
	ID             int32  `json:"id"`
	CustomSwitchID int32  `json:"custom_switch_id"`
	ParentID       int32  `json:"parent_id"`
	Description    string `json:"description"`
	ParamType      int32  `json:"param_type"`
	ParamData      string `json:"param_data"`
	DefaultReturn  int32  `json:"default_return"`
	OtherParamData string `json:"other_param_data"`
	SortOrder      int32  `json:"sort_order"`
	CreatedAt      int64  `json:"created_at"`
	UpdatedAt      int64  `json:"updated_at"`
	IsDeleted      bool   `json:"is_deleted"`
}

// SwitchParamGroup represents a group of switch parameters
type SwitchParamGroup struct {
	GameID   string
	SwitchID string
	Params   map[int]SwitchParam
}

func LoadRedisSecret() {
	ctx := context.Background()
	games, err := service.SingletonUserService().GetAllGameList(ctx)
	if err != nil {
		log.Panicf("LoadRedisSecret GetAllGameList err: %v", err)
		return
	}
	// 获取gameList 中的secret, 并写入redis
	for _, game := range games {
		_, err := redis.Get(ctx, fmt.Sprintf(constants.SystemSecret, game.GameID))
		if err != nil && !errors.Is(err, redis.Nil) {
			log.Panicf("loading game secret redis.get err: %v", err)
			return
		} else if !errors.Is(err, redis.Nil) {
			continue
		}

		err = redis.Set(ctx, fmt.Sprintf(constants.SystemSecret, game.GameID), game.Secret, 0)
		if err != nil {
			log.Panicf("loading game secret redis.set err: %v", err)
			return
		}
	}
}

var (
	SensitiveWordsMap       sync.Map
	SensitiveWordsConfigMap sync.Map

	SwitchMap      sync.Map
	switchMapMutex sync.Mutex
	SwitchParamMap sync.Map
	SwitchCityMap  sync.Map

	// 敏感词订阅管理
	sensitiveSubscriptionCtx    context.Context
	sensitiveSubscriptionCancel context.CancelFunc
	sensitiveSubscriptionMutex  sync.Mutex

	// 敏感词操作互斥锁（按key分组）
	sensitiveWordMutexes sync.Map // key -> *sync.Mutex
)

func LoadMemorySensitiveWord() {
	logger.Logger.Info("LoadMemorySensitiveWord start")

	// 为数据加载使用带超时的context
	loadCtx, loadCancel := context.WithTimeout(context.Background(), 25*time.Second)
	defer loadCancel()

	// 为订阅操作创建独立的context（不带超时）
	sensitiveSubscriptionMutex.Lock()
	if sensitiveSubscriptionCancel != nil {
		// 如果已有订阅，先取消
		sensitiveSubscriptionCancel()
	}
	sensitiveSubscriptionCtx, sensitiveSubscriptionCancel = context.WithCancel(context.Background())
	sensitiveSubscriptionMutex.Unlock()

	// 获取所有游戏ID，直接构建具体的键
	games, err := service.SingletonUserService().GetAllGameList(loadCtx)
	if err != nil {
		logger.Logger.Errorf("LoadMemorySensitiveWord get game list err: %v", err)
		return
	}

	// 构建精确的键列表，替代使用模式匹配
	var setKeys []string
	for _, game := range games {
		sensitiveKey := fmt.Sprintf("%s:sensitive:info:%s", constants.SystemPrefix, game.GameID)
		// 检查键是否存在
		exists, err := redis.Exists(loadCtx, sensitiveKey)
		if err != nil {
			logger.Logger.Warnf("Check key exists err for game %s: %v", game.GameID, err)
			continue
		}
		if exists {
			setKeys = append(setKeys, sensitiveKey)
		}
	}

	logger.Logger.Infof("LoadMemorySensitiveWord: Found %d sensitive word keys", len(setKeys))

	if len(setKeys) == 0 {
		logger.Logger.Infof("LoadMemorySensitiveWord: No sensitive word keys found")
		// 初始化订阅，即使没有数据也需要订阅后续更新
		setupSensitiveWordSubscriptions()
		logger.Logger.Info("LoadMemorySensitiveWord end with empty data")
		return
	}

	for _, k := range setKeys {
		members, err := redis.SMembers(loadCtx, k)
		if err != nil {
			logger.Logger.Errorf("LoadMemorySensitiveWord err: %s", err.Error())
			return
		}
		logger.Logger.Infof("LoadMemorySensitiveWord: Processing key %s with %d members", k, len(members))

		// 加载到Sync.Map
		// 安全的字符串分割，添加边界检查
		keyParts := strings.SplitN(k, ":", 4)
		if len(keyParts) < 4 {
			logger.Logger.Errorf("LoadMemorySensitiveWord incorrect key format: %s", k)
			continue
		}
		gameKey := keyParts[3]

		for _, member := range members {
			split := strings.SplitN(member, ":", 2)
			if len(split) != 2 {
				logger.Logger.Errorf("LoadMemorySensitiveWord incorrect member format: %s", member)
				continue
			}
			// 使用等级作为key, 感词列表作为value
			level, word := split[0], split[1]
			combinedKey := fmt.Sprintf("%s:%s", gameKey, level)
			addAllSensitiveWords(combinedKey, word)
		}
	}

	// 直接获取固定键的配置数据
	// 注意：HGetAll 对于不存在的键会返回空 map，不会返回 redis.Nil 错误
	fieldMap, err := redis.HGetAll(loadCtx, constants.RedisSensitiveConfigAllKeys)
	if err != nil {
		// 只有真正的错误（网络、连接等）才会到这里，键不存在不会产生错误
		logger.Logger.Errorf("LoadMemorySensitiveWord get config err: %s", err.Error())
		return
	}

	if len(fieldMap) > 0 {
		logger.Logger.Infof("LoadMemorySensitiveWord: Processing config with %d fields", len(fieldMap))
		for k, field := range fieldMap {
			SensitiveWordsConfigMap.Store(k, field)
		}
	} else {
		logger.Logger.Infof("LoadMemorySensitiveWord: No sensitive word config data found (key may not exist or be empty)")
	}

	// 打印sensitiveWordsMap
	//SensitiveWordsMap.Range(func(key, value interface{}) bool {
	//	logger.Logger.Debugf("LoadMemorySensitiveWord all sensitive words: key: %v, value: %v\n", key, value)
	//	return true
	//})
	//SensitiveWordsConfigMap.Range(func(key, value interface{}) bool {
	//	logger.Logger.Debugf("SensitiveWordsConfigMap all sensitive words: key: %v, value: %v", key, value)
	//	return true
	//})

	// 设置订阅（使用独立的context，不会因为超时而断开）
	setupSensitiveWordSubscriptions()

	logger.Logger.Info("LoadMemorySensitiveWord end")
}

// setupSensitiveWordSubscriptions 设置敏感词订阅
func setupSensitiveWordSubscriptions() {
	subscribeKeys := []string{constants.RedisSensitivePublishAdd, constants.RedisSensitivePublishDel}
	logger.Logger.Infof("setupSensitiveWordSubscriptions: Subscribing to %d channels", len(subscribeKeys))

	for _, k := range subscribeKeys {
		isSubscribe, signal := redis.Subscribe(sensitiveSubscriptionCtx, k)
		if !isSubscribe {
			logger.Logger.Errorf("setupSensitiveWordSubscriptions: Failed to subscribe to channel %s, error: %v", k, signal)
			// 继续尝试订阅其他频道，而不是直接返回
			continue
		}
		logger.Logger.Infof("setupSensitiveWordSubscriptions: Successfully subscribed to channel %s", k)

		go func(channel string) {
			logger.Logger.Infof("setupSensitiveWordSubscriptions: Starting goroutine for channel %s", channel)
			defer logger.Logger.Infof("setupSensitiveWordSubscriptions: Goroutine for channel %s stopped", channel)

			for {
				select {
				case msg, ok := <-signal:
					if !ok {
						logger.Logger.Infof("setupSensitiveWordSubscriptions: Channel %s closed", channel)
						return
					}

					payloadSplit := strings.SplitN(msg.Payload, ":", 3)
					if len(payloadSplit) != 3 {
						logger.Logger.Errorf("setupSensitiveWordSubscriptions incorrect payload format: %s", msg.Payload)
						continue
					}
					gameID, level, word := payloadSplit[0], payloadSplit[1], payloadSplit[2]
					combinedKey := fmt.Sprintf("%s:%s", gameID, level)
					switch channel {
					case constants.RedisSensitivePublishAdd:
						// 添加敏感词
						addSensitiveWord(combinedKey, word)
						logger.Logger.Debugf("setupSensitiveWordSubscriptions Sensitive word added to cache. chan: %s, msg: %s end.", channel, msg.Payload)
					case constants.RedisSensitivePublishDel:
						// 删除敏感词
						delSensitiveWord(combinedKey, word)
						logger.Logger.Debugf("Sensitive word deleted from cache. chan: %s, msg: %s end.", channel, msg.Payload)
					}
				case <-sensitiveSubscriptionCtx.Done():
					logger.Logger.Infof("setupSensitiveWordSubscriptions: Context cancelled for channel %s", channel)
					return
				}
			}
		}(k)
	}

	subscribeConfigKeys := []string{constants.RedisSensitiveConfigPublishAdd}
	logger.Logger.Infof("setupSensitiveWordSubscriptions: Subscribing to %d config channels", len(subscribeConfigKeys))

	for _, k := range subscribeConfigKeys {
		isSubscribe, signal := redis.Subscribe(sensitiveSubscriptionCtx, k)
		if !isSubscribe {
			logger.Logger.Errorf("setupSensitiveWordSubscriptions: Failed to subscribe to config channel %s, error: %v", k, signal)
			// 继续尝试订阅其他频道，而不是直接返回
			continue
		}
		logger.Logger.Infof("setupSensitiveWordSubscriptions: Successfully subscribed to config channel %s", k)

		go func(channel string) {
			logger.Logger.Infof("setupSensitiveWordSubscriptions: Starting goroutine for config channel %s", channel)
			defer logger.Logger.Infof("setupSensitiveWordSubscriptions: Config goroutine for channel %s stopped", channel)

			for {
				select {
				case msg, ok := <-signal:
					if !ok {
						logger.Logger.Infof("setupSensitiveWordSubscriptions: Config channel %s closed", channel)
						return
					}

					payloadSplit := strings.SplitN(msg.Payload, ":", 2)
					if len(payloadSplit) != 2 {
						logger.Logger.Errorf("setupSensitiveWordSubscriptions config incorrect payload format: %s", msg.Payload)
						continue
					}
					gameID, ignoreCase := payloadSplit[0], payloadSplit[1]
					switch channel {
					case constants.RedisSensitiveConfigPublishAdd:
						SensitiveWordsConfigMap.Store(gameID, ignoreCase) // 直接覆盖即可
						logger.Logger.Debugf("setupSensitiveWordSubscriptions Config added to cache. chan: %s, gameID: %s, ignoreCase: %s", channel, gameID, ignoreCase)
					}
				case <-sensitiveSubscriptionCtx.Done():
					logger.Logger.Infof("setupSensitiveWordSubscriptions: Context cancelled for config channel %s", channel)
					return
				}
			}
		}(k)
	}
}

// addAllSensitiveWords 安全地添加敏感词（初始化时使用，不需要去重）
func addAllSensitiveWords(combinedKey, word string) {
	// 使用简单的Store操作，因为初始化时不会有并发冲突
	if value, ok := SensitiveWordsMap.Load(combinedKey); ok {
		// 如果存在，追加到对应的切片中
		if words, ok := value.([]string); ok {
			// 创建新的slice副本
			newWords := make([]string, len(words)+1)
			copy(newWords, words)
			newWords[len(words)] = word
			SensitiveWordsMap.Store(combinedKey, newWords)
		} else {
			logger.Logger.Errorf("addAllSensitiveWords value type is not []string: %v", value)
		}
	} else {
		// 如果不存在，新建切片并添加
		SensitiveWordsMap.Store(combinedKey, []string{word})
	}
}

// getSensitiveWordMutex 获取指定key的互斥锁
func getSensitiveWordMutex(key string) *sync.Mutex {
	if value, ok := sensitiveWordMutexes.Load(key); ok {
		return value.(*sync.Mutex)
	}

	mutex := &sync.Mutex{}
	actual, _ := sensitiveWordMutexes.LoadOrStore(key, mutex)
	return actual.(*sync.Mutex)
}

// addSensitiveWord 安全地添加敏感词（去重）
func addSensitiveWord(combinedKey, word string) {
	mutex := getSensitiveWordMutex(combinedKey)
	mutex.Lock()
	defer mutex.Unlock()

	if value, ok := SensitiveWordsMap.Load(combinedKey); ok {
		// 安全的类型断言
		words, ok := value.([]string)
		if !ok {
			logger.Logger.Errorf("addSensitiveWord value type is not []string: %v", value)
			return
		}

		// 检查是否已存在
		for _, v := range words {
			if v == word {
				// 已存在，无需添加
				return
			}
		}

		// 创建新的slice副本
		newWords := make([]string, len(words)+1)
		copy(newWords, words)
		newWords[len(words)] = word
		SensitiveWordsMap.Store(combinedKey, newWords)
	} else {
		// 如果不存在，创建新的slice
		SensitiveWordsMap.Store(combinedKey, []string{word})
	}
}

// delSensitiveWord 安全地删除敏感词
func delSensitiveWord(combinedKey, word string) {
	mutex := getSensitiveWordMutex(combinedKey)
	mutex.Lock()
	defer mutex.Unlock()

	value, ok := SensitiveWordsMap.Load(combinedKey)
	if !ok {
		// key不存在，无需删除
		return
	}

	// 安全的类型断言
	words, ok := value.([]string)
	if !ok {
		logger.Logger.Errorf("delSensitiveWord value type is not []string for key: %s", combinedKey)
		return
	}

	// 查找并移除敏感词
	newWords := make([]string, 0, len(words))
	found := false
	for _, w := range words {
		if w != word {
			newWords = append(newWords, w)
		} else {
			found = true
		}
	}

	if !found {
		// 没有找到要删除的词，无需操作
		return
	}

	if len(newWords) > 0 {
		SensitiveWordsMap.Store(combinedKey, newWords)
	} else {
		// 如果删除后为空，删除整个key
		SensitiveWordsMap.Delete(combinedKey)
	}
}

func GetSensitiveWordByGameID(gameID string) map[string][]string {
	// var allWords []string
	var allWordMap map[string][]string
	allLevel := [3]string{"1", "2", "3"}
	for _, level := range allLevel {
		combinedKey := fmt.Sprintf("%s:%s", gameID, level)
		if value, ok := SensitiveWordsMap.Load(combinedKey); ok {
			if words, ok := value.([]string); ok {
				if len(words) > 0 {
					if allWordMap == nil {
						allWordMap = make(map[string][]string, 0)
					}
					allWordMap[level] = words
				}
			} else {
				logger.Logger.Errorf("GetSensitiveWordByGameID value type is not []string for key: %s, possibly no value", combinedKey)
			}
		}
	}
	return allWordMap
}

func GetSensitiveWordLevelByGameID(gameID string) map[string]int32 {
	var allWordMap map[string]int32
	// 统一使用string格式，与其他函数保持一致
	allLevel := [3]string{"1", "2", "3"}
	for _, level := range allLevel {
		combinedKey := fmt.Sprintf("%s:%s", gameID, level)
		if value, ok := SensitiveWordsMap.Load(combinedKey); ok {
			// 安全的类型断言
			if words, ok := value.([]string); ok {
				if len(words) > 0 {
					if allWordMap == nil {
						allWordMap = make(map[string]int32)
					}
					// 将string level转换为int32
					levelInt, err := strconv.Atoi(level)
					if err != nil {
						logger.Logger.Errorf("GetSensitiveWordLevelByGameID invalid level format: %s", level)
						continue
					}
					for _, w := range words {
						allWordMap[w] = int32(levelInt)
					}
				}
			} else {
				logger.Logger.Errorf("GetSensitiveWordLevelByGameID value type is not []string for key: %s", combinedKey)
			}
		}
	}
	return allWordMap
}

func GetSensitiveWordConfigByGameID(gameID string) int32 {
	if v, ok := SensitiveWordsConfigMap.Load(gameID); ok {
		// 安全的类型断言
		if str, ok := v.(string); ok {
			if i, err := strconv.Atoi(str); err == nil {
				return int32(i)
			} else {
				logger.Logger.Errorf("GetSensitiveWordConfigByGameID invalid config value: %s", str)
			}
		} else {
			logger.Logger.Errorf("GetSensitiveWordConfigByGameID value type is not string: %v", v)
		}
	}
	return constants.SensitiveWordIgnoreCaseClose
}

func LoadMemorySwitch() {
	logger.Logger.Info("LoadMemorySwitch start")

	ctx := context.Background()
	// 添加超时控制
	ctx, cancel := context.WithTimeout(ctx, 25*time.Second)
	defer cancel()

	// 获取所有游戏ID，直接构建具体的键
	games, err := service.SingletonUserService().GetAllGameList(ctx)
	if err != nil {
		logger.Logger.Errorf("LoadMemorySwitch get game list err: %v", err)
		return
	}

	// 收集所有游戏ID
	gameIDs := make([]string, 0, len(games))
	for _, game := range games {
		gameIDs = append(gameIDs, game.GameID)
	}

	// 一次性查询所有游戏的开关
	switchQuery := store.QueryDB().MCustomSwitch
	allSwitches, err := switchQuery.WithContext(ctx).
		Where(switchQuery.GameID.In(gameIDs...)).
		Where(switchQuery.IsDeleted.Is(false)).
		Find()

	if err != nil {
		logger.Logger.Errorf("LoadMemorySwitch query switches for all games err: %v", err)
		return
	}

	// 构建精确的键列表
	var setKeys []string
	for _, sw := range allSwitches {
		switchKey := fmt.Sprintf("%s:switch:info:%s:%s", constants.SystemPrefix, sw.GameID, sw.SwitchID)
		// 检查键是否存在
		exists, err := redis.Redis().Exists(ctx, switchKey).Result()
		if err != nil {
			logger.Logger.Warnf("Check key exists err for game %s, switch %s: %v", sw.GameID, sw.SwitchID, err)
			continue
		}
		if exists > 0 {
			setKeys = append(setKeys, switchKey)
		}
	}

	logger.Logger.Infof("LoadMemorySwitch: Found %d switch keys", len(setKeys))
	if len(setKeys) == 0 {
		logger.Logger.Infof("LoadMemorySwitch: No switch keys found")
		return
	}
	for _, k := range setKeys {
		logger.Logger.Debugf("LoadMemorySwitch: Processing key: %s", k)
		hVal, err := redis.HGetAll(ctx, k)
		if err != nil {
			logger.Logger.Errorf("LoadMemorySwitch err: %s for key: %s", err.Error(), k)
			return
		}
		logger.Logger.Debugf("LoadMemorySwitch: HGetAll result for key %s: %+v (len: %d)", k, hVal, len(hVal))
		key := strings.SplitN(k, ":", 4)[3]
		addAllSwitch(key, hVal)

		// Load switch parameters
		gameID := hVal["game_id"]
		switchID := hVal["id"]
		if gameID != "" && switchID != "" {
			paramKey := fmt.Sprintf(constants.RedisSwitchParamHSetInfo, gameID, switchID)
			paramVal, err := redis.HGetAll(ctx, paramKey)
			if err != nil {
				logger.Logger.Errorf("LoadMemorySwitch param err: %s", err.Error())
				continue
			}

			// 添加空值检查的日志
			if len(paramVal) == 0 {
				logger.Logger.Debugf("No switch parameters found for gameID: %s, switchID: %s (key: %s)",
					gameID, switchID, paramKey)
				continue
			}

			switchParamGroup := SwitchParamGroup{
				GameID:   gameID,
				SwitchID: switchID,
				Params:   make(map[int]SwitchParam),
			}

			for paramID, paramJSON := range paramVal {
				var param SwitchParam
				if err := json.Unmarshal([]byte(paramJSON), &param); err != nil {
					logger.Logger.Errorf("Failed to unmarshal switch param: %v", err)
					continue
				}
				id, _ := strconv.Atoi(paramID)
				switchParamGroup.Params[id] = param
			}

			SwitchParamMap.Store(key, switchParamGroup)
		}
	}

	// Add logging for SwitchParamMap
	logger.Logger.Info("Logging SwitchParamMap contents:")
	SwitchParamMap.Range(func(key, value interface{}) bool {
		switchParamGroup, ok := value.(SwitchParamGroup)
		if !ok {
			logger.Logger.Errorf("Invalid type in SwitchParamMap for key: %v", key)
			return true
		}
		logger.Logger.Debugf("SwitchParamMap key: %v, value: %+v", key, switchParamGroup)
		return true
	})

	// SwitchMap
	//SwitchMap.Range(func(key, value interface{}) bool {
	//	logger.Logger.Debugf("LoadMemorySwitch all switch: key: %v, value: %v\n", key, value)
	//	return true
	//})

	subscribeKeys := []string{constants.RedisSwitchPublishAdd, constants.RedisSwitchPublishDel}
	for _, k := range subscribeKeys {
		isSubscribe, signal := redis.Subscribe(ctx, k)
		if !isSubscribe {
			logger.Logger.Errorf("LoadMemorySwitch Subscribe err :%v", signal)
			return
		}

		go func(channel string) {
			// 为goroutine创建新的context，避免使用已超时的context
			goroutineCtx := context.Background()

			for msg := range signal {
				payloadSplit := strings.SplitN(msg.Payload, ":", 2)
				if len(payloadSplit) != 2 {
					logger.Logger.Errorf("LoadMemorySwitch incorrect payload format: %s", msg.Payload)
					continue
				}
				gameID, switchID := payloadSplit[0], payloadSplit[1]
				combinedKey := fmt.Sprintf("%s:%s", gameID, switchID)
				completeCombinedKey := constants.RedisSwitchPrefix + fmt.Sprintf("info:%s:%s", gameID, switchID)

				// 添加调试日志
				logger.Logger.Debugf("LoadMemorySwitch: Attempting to get key: %s (gameID: %s, switchID: %s)",
					completeCombinedKey, gameID, switchID)

				// 先检查键是否存在
				exists, existsErr := redis.Exists(goroutineCtx, completeCombinedKey)
				if existsErr != nil {
					logger.Logger.Errorf("LoadMemorySwitch check exists err: %v for key: %s", existsErr, completeCombinedKey)
					continue
				}
				logger.Logger.Debugf("LoadMemorySwitch: Key exists check result: %t for key: %s", exists, completeCombinedKey)

				publishVal, err := redis.HGetAll(goroutineCtx, completeCombinedKey)
				if err != nil {
					logger.Logger.Errorf("LoadMemorySwitch publish err: %v for key: %s", err, completeCombinedKey)
					continue
				}

				// 添加获取结果的调试日志
				logger.Logger.Debugf("LoadMemorySwitch: HGetAll result for key %s: %+v (len: %d)",
					completeCombinedKey, publishVal, len(publishVal))

				switch channel {
				case constants.RedisSwitchPublishAdd:
					addAllSwitch(combinedKey, publishVal)
					// Load switch parameters
					parentSwitchID := publishVal["id"]
					paramKey := fmt.Sprintf(constants.RedisSwitchParamHSetInfo, gameID, parentSwitchID)
					paramVal, err := redis.HGetAll(goroutineCtx, paramKey)
					if err != nil {
						logger.Logger.Errorf("LoadMemorySwitch param err: %s", err.Error())
					} else {
						if len(paramVal) == 0 {
							logger.Logger.Debugf("subscribe No switch parameters found for gameID: %s, switchID: %s (key: %s)",
								gameID, switchID, paramKey)
							continue
						}

						// Use gameID:switchID as the key for SwitchParamMap
						switchParamKey := fmt.Sprintf("%s:%s", gameID, switchID)
						switchParamGroup := SwitchParamGroup{
							GameID:   gameID,
							SwitchID: switchID,
							Params:   make(map[int]SwitchParam),
						}
						for paramID, paramJSON := range paramVal {
							var param SwitchParam
							if err := json.Unmarshal([]byte(paramJSON), &param); err != nil {
								logger.Logger.Errorf("Failed to unmarshal switch param: %v", err)
								continue
							}
							id, _ := strconv.Atoi(paramID)
							switchParamGroup.Params[id] = param
						}
						SwitchParamMap.Store(switchParamKey, switchParamGroup)
					}
					logger.Logger.Debugf("LoadMemorySwitch Switch and params added to cache. chan: %s, msg: %s end.", channel, msg.Payload)
				case constants.RedisSwitchPublishDel:
					delSwitch(combinedKey)
					// Use gameID:switchID as the key for SwitchParamMap
					// parentSwitchID := publishVal["id"]
					switchParamKey := fmt.Sprintf("%s:%s", gameID, switchID)
					SwitchParamMap.Delete(switchParamKey)
					logger.Logger.Debugf("LoadMemorySwitch Switch and params deleted from cache. chan: %s, msg: %s end.", channel, msg.Payload)
				}
			}
		}(k)
	}

	logger.Logger.Info("LoadMemorySwitch end")
}

func addAllSwitch(combinedKey string, val map[string]string) {
	switchMapMutex.Lock()
	defer switchMapMutex.Unlock()
	SwitchMap.Store(combinedKey, val)
}

func delSwitch(combinedKey string) {
	switchMapMutex.Lock()
	defer switchMapMutex.Unlock()
	SwitchMap.Delete(combinedKey)
}

func GetSwitchByGameIDAndSwitchIDs(gameID string, switchIDs []string) []map[string]string {
	var allSwitch []map[string]string

	for _, s := range switchIDs {
		if v, ok := SwitchMap.Load(gameID + ":" + s); ok {
			allSwitch = append(allSwitch, v.(map[string]string))
		}
	}
	return allSwitch
}

func GetSwitchParamByGameIDAndSwitchID(gameID, switchID string) (SwitchParamGroup, bool) {
	key := fmt.Sprintf("%s:%s", gameID, switchID)
	value, ok := SwitchParamMap.Load(key)
	if !ok {
		return SwitchParamGroup{}, false
	}
	switchParamGroup, ok := value.(SwitchParamGroup)
	if !ok {
		return SwitchParamGroup{}, false
	}
	return switchParamGroup, true
}

// 加载城市列表
func LoadMemoryCity() {
	ctx := context.Background()
	// 从数据库读取
	code := store.QueryDB().MCityCode
	codeList, err := code.WithContext(ctx).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "LoadMemoryCity err: %v", err)
		return // 添加这行，在错误时提前返回
	}
	for _, v := range codeList {
		SwitchCityMap.Store(v.Name, v.Code)
	}
	// 可以添加日志，表示加载完成
	logger.Logger.InfofCtx(ctx, "LoadMemoryCity: Loaded %d cities", len(codeList))
}

// 获取城市列表
func GetCityCode(cityName string) (string, bool) {
	if v, ok := SwitchCityMap.Load(cityName); ok {
		if code, ok := v.(string); ok {
			return code, true
		}
		// 可以添加日志，记录类型断言失败的情况
		logger.Logger.Warnf("GetCityCode: Unexpected type for city %s", cityName)
	}
	return "", false
}

// StopSensitiveWordSubscriptions 停止敏感词订阅，用于优雅关闭
func StopSensitiveWordSubscriptions() {
	sensitiveSubscriptionMutex.Lock()
	defer sensitiveSubscriptionMutex.Unlock()

	if sensitiveSubscriptionCancel != nil {
		logger.Logger.Info("Stopping sensitive word subscriptions...")
		sensitiveSubscriptionCancel()
		sensitiveSubscriptionCancel = nil
		logger.Logger.Info("Sensitive word subscriptions stopped")
	}
}

// RestartSensitiveWordSubscriptions 重启敏感词订阅，用于自动重连
func RestartSensitiveWordSubscriptions() {
	logger.Logger.Info("Restarting sensitive word subscriptions...")

	// 先停止现有订阅
	StopSensitiveWordSubscriptions()

	// 等待一小段时间让资源清理
	time.Sleep(1 * time.Second)

	// 重新设置订阅
	sensitiveSubscriptionMutex.Lock()
	sensitiveSubscriptionCtx, sensitiveSubscriptionCancel = context.WithCancel(context.Background())
	sensitiveSubscriptionMutex.Unlock()

	setupSensitiveWordSubscriptions()
	logger.Logger.Info("Sensitive word subscriptions restarted")
}

// CheckSensitiveWordSubscriptionHealth 检查敏感词订阅健康状态
func CheckSensitiveWordSubscriptionHealth() bool {
	sensitiveSubscriptionMutex.Lock()
	defer sensitiveSubscriptionMutex.Unlock()

	if sensitiveSubscriptionCtx == nil {
		return false
	}

	select {
	case <-sensitiveSubscriptionCtx.Done():
		return false
	default:
		return true
	}
}
