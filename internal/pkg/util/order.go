package util

import (
	"fmt"
	"math/rand"
	"sync"
	"time"
)

var (
	// 用于保证ID生成的线程安全
	idMutex sync.Mutex
)

// GenerateWorkOrderID 生成工单ID
// 格式: Unix时间戳秒数 + 5位随机数
// 使用完整时间戳秒数和随机数组合，确保生成ID的唯一性
// 示例: 1705123456012345 (时间戳: 1705123456, 随机数: 12345)
func GenerateWorkOrderID() string {
	idMutex.Lock()
	defer idMutex.Unlock()

	// 获取Unix时间戳秒数
	timestamp := time.Now().Unix()

	// 生成5位随机数 (00001-99999)
	randomNum := rand.Intn(99999) + 1

	// 组合工单ID: 时间戳秒数 + 5位随机数
	return fmt.Sprintf("%d%05d", timestamp, randomNum)
}
