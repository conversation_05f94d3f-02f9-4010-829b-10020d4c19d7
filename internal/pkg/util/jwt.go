package util

import (
	"fmt"
	"strings"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"

	"github.com/golang-jwt/jwt/v4"
)

const (
	issuer       = "admin-console"
	secretMethod = "HS256"
)

type Claims struct {
	UserID   string `json:"user_id"`
	DeviceID string `header:"device_id"`
	AppID    string `header:"app_id"`
	GameID   string `header:"game_id"`
	OpenID   string `header:"open_id"`
	OS       string `header:"os"`
	// jwt.StandardClaims
	jwt.RegisteredClaims
}

// WorkorderClaims 工单系统专用的JWT Claims结构体
type WorkorderClaims struct {
	UserID   string `json:"user_id"`
	DeviceID string `json:"device_id"`
	AppID    string `json:"app_id"`
	GameID   string `json:"game_id"`
	OpenID   string `json:"open_id"`
	GameName string `json:"game_name"`
	// 移除 Source 和 DouyinOpenID 字段，这些信息将在创建工单时传递
	jwt.RegisteredClaims
}

// GenerateToken generate tokens used for auth
func GenerateToken(userID, DeviceID, AppID, GameID, OpenID, OS string) (string, error) {
	nowTime := time.Now()
	expireTime := nowTime.Add(constants.OneWeek)

	claims := Claims{
		userID,
		DeviceID,
		AppID,
		GameID,
		OpenID,
		OS,
		//jwt.StandardClaims{
		//	ExpiresAt: expireTime.Unix(),
		//	Issuer:    "admin-console",
		//},
		jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expireTime),
			Issuer:    issuer,
		},
	}

	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := tokenClaims.SignedString([]byte(config.GlobConfig.Server.JWTSecret))

	return token, err
}

func GenerateRefreshToken(userID, DeviceID, AppID, GameID, OpenID, OS string) (string, error) {
	nowTime := time.Now()
	expireTime := nowTime.Add(constants.TwoWeek)

	claims := Claims{
		userID,
		DeviceID,
		AppID,
		GameID,
		OpenID,
		OS,
		//jwt.StandardClaims{
		//	ExpiresAt: expireTime.Unix(),
		//	Issuer:    "admin-console",
		//},
		jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expireTime),
			Issuer:    issuer,
		},
	}

	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := tokenClaims.SignedString([]byte(config.GlobConfig.Server.JWTRefreshSecret))

	return token, err
}

// ParseToken parsing token
func ParseToken(token string) (*Claims, error) {
	token = strings.TrimPrefix(token, "Bearer ")

	tokenClaims, err := jwt.ParseWithClaims(token, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if method, ok := token.Header["alg"].(string); ok {
			if method != secretMethod {
				return nil, fmt.Errorf("unexpected signing method: %s", method)
			}
		}
		return []byte(config.GlobConfig.Server.JWTSecret), nil
	})

	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*Claims); ok && tokenClaims.Valid {
			return claims, nil
		}
	}

	return nil, err
}

func ParseRefreshToken(token string) (*Claims, error) {
	token = strings.TrimPrefix(token, "Bearer ")

	tokenClaims, err := jwt.ParseWithClaims(token, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if method, ok := token.Header["alg"].(string); ok {
			if method != secretMethod {
				return nil, fmt.Errorf("unexpected signing method: %s", method)
			}
		}
		return []byte(config.GlobConfig.Server.JWTRefreshSecret), nil
	})

	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*Claims); ok && tokenClaims.Valid {
			return claims, nil
		}
	}

	return nil, err
}

// GenerateWorkorderToken 生成工单系统专用的JWT token
// 注意：source 和 douyinOpenID 参数已移除，这些信息将在创建工单时传递
func GenerateWorkorderToken(userID, deviceID, appID, gameID, openID, gameName string) (string, error) {
	nowTime := time.Now()
	expireTime := nowTime.Add(constants.OneWeek)

	claims := WorkorderClaims{
		UserID:   userID,
		DeviceID: deviceID,
		AppID:    appID,
		GameID:   gameID,
		OpenID:   openID,
		GameName: gameName,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expireTime),
			Issuer:    issuer,
		},
	}

	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := tokenClaims.SignedString([]byte(config.GlobConfig.Server.JWTSecret))

	return token, err
}

// ParseWorkorderToken 解析工单系统专用的JWT token
func ParseWorkorderToken(token string) (*WorkorderClaims, error) {
	token = strings.TrimPrefix(token, "Bearer ")

	tokenClaims, err := jwt.ParseWithClaims(token, &WorkorderClaims{}, func(token *jwt.Token) (interface{}, error) {
		if method, ok := token.Header["alg"].(string); ok {
			if method != secretMethod {
				return nil, fmt.Errorf("unexpected signing method: %s", method)
			}
		}
		return []byte(config.GlobConfig.Server.JWTSecret), nil
	})

	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*WorkorderClaims); ok && tokenClaims.Valid {
			return claims, nil
		}
	}

	return nil, err
}
