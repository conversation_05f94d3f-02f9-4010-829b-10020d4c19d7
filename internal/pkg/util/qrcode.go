package util

import (
	"bytes"
	"encoding/base64"
	"image"
	"image/color"
	"image/draw"
	"image/png"
	"strings"

	"github.com/skip2/go-qrcode"
	"golang.org/x/image/font"
	"golang.org/x/image/font/basicfont"
	"golang.org/x/image/font/gofont/goregular"
	"golang.org/x/image/font/opentype"
	"golang.org/x/image/math/fixed"
)

const wechatPayBackgroundImage = `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`

// GenerateQRCode 生成二维码并返回base64编码
func GenerateQRCode(content string, amountText string) (string, error) {
	// 生成二维码
	qr, err := qrcode.New(content, qrcode.Medium)
	if err != nil {
		return "", err
	}

	// 设置二维码大小
	qr.DisableBorder = false
	qrImage := qr.Image(360) // 调整二维码大小以适应底图

	// 解码背景图片
	backgroundReader := base64.NewDecoder(base64.StdEncoding, strings.NewReader(wechatPayBackgroundImage))
	backgroundImage, err := png.Decode(backgroundReader)
	if err != nil {
		return "", err
	}

	// 创建一个新的图片，大小与背景图片相同
	bounds := backgroundImage.Bounds()
	combined := image.NewRGBA(bounds)

	// 绘制背景图片
	draw.Draw(combined, bounds, backgroundImage, image.Point{}, draw.Over)

	// 计算二维码在背景图片中的位置（居中）
	qrBounds := qrImage.Bounds()
	x := (bounds.Dx() - qrBounds.Dx()) / 2
	y := (bounds.Dy()-qrBounds.Dy())/2 + 20 // 向下偏移以为金额文字留出空间
	qrPoint := image.Point{X: x, Y: y}
	qrRect := image.Rectangle{Min: qrPoint, Max: image.Point{X: x + qrBounds.Dx(), Y: y + qrBounds.Dy()}}

	// 绘制二维码到背景图片上
	draw.Draw(combined, qrRect, qrImage, image.Point{}, draw.Over)

	// 添加金额文字
	if amountText != "" {

		fontBytes := goregular.TTF
		fnt, err := opentype.Parse(fontBytes)
		if err != nil {
			panic(err)
		}

		// Create a font face with the desired size
		const fontSize = 32
		face, err := opentype.NewFace(fnt, &opentype.FaceOptions{
			Size:    fontSize,
			DPI:     74,
			Hinting: font.HintingFull,
		})
		if err != nil {
			panic(err)
		}
		defer face.Close()

		// 使用基础字体
		fontFace := basicfont.Face7x13
		d := &font.Drawer{
			Dst:  combined,
			Src:  image.NewUniform(color.Black),
			Face: face,
			Dot:  fixed.P(10, 50),
		}

		// 计算文字宽度以实现居中
		textWidth := font.MeasureString(fontFace, amountText).Round()
		textX := (bounds.Dx() - textWidth - 20) / 2
		textY := y - 1 // 在二维码上方10像素处

		d.Dot = fixed.Point26_6{
			X: fixed.I(textX),
			Y: fixed.I(textY),
		}
		d.DrawString(amountText)
	}

	// 将合成后的图片编码为PNG
	var buf bytes.Buffer
	err = png.Encode(&buf, combined)
	if err != nil {
		return "", err
	}

	// 转换为base64
	return base64.StdEncoding.EncodeToString(buf.Bytes()), nil
}
