package util

import (
	"strconv"
	"testing"
	"time"
)

// TestGenerateWorkOrderID 测试工单ID生成功能
func TestGenerateWorkOrderID(t *testing.T) {
	// 生成一个工单ID
	id := GenerateWorkOrderID()

	// 测试ID不为空
	if id == "" {
		t.<PERSON>rror("生成的工单ID不能为空")
	}

	// 测试ID长度（Unix时间戳10位 + 5位随机数 = 15位）
	expectedLength := 15
	if len(id) != expectedLength {
		t.Errorf("工单ID长度应为%d位，实际为%d位，ID: %s", expectedLength, len(id), id)
	}

	// 测试ID是否为纯数字
	if _, err := strconv.ParseInt(id, 10, 64); err != nil {
		t.Errorf("工单ID应为纯数字，实际ID: %s, 错误: %v", id, err)
	}

	// 测试时间戳部分（前10位）
	timestampPart := id[:10]
	timestamp, err := strconv.ParseInt(timestampPart, 10, 64)
	if err != nil {
		t.<PERSON>rrorf("时间戳部分解析失败: %s, 错误: %v", timestampPart, err)
	}

	// 验证时间戳是否合理（应该接近当前时间）
	currentTime := time.Now().Unix()
	timeDiff := currentTime - timestamp
	if timeDiff < 0 || timeDiff > 5 { // 允许5秒的时间差
		t.Errorf("时间戳不合理，当前时间: %d, ID中时间戳: %d, 差值: %d秒", currentTime, timestamp, timeDiff)
	}

	// 测试序列号部分（后5位）
	sequencePart := id[10:]
	sequenceNum, err := strconv.Atoi(sequencePart)
	if err != nil {
		t.Errorf("序列号部分解析失败: %s, 错误: %v", sequencePart, err)
	}

	// 验证序列号范围（00000-99999）
	if sequenceNum < 0 || sequenceNum > 99999 {
		t.Errorf("序列号应在00000-99999范围内，实际: %d", sequenceNum)
	}
}

// TestGenerateWorkOrderIDUniqueness 测试工单ID的唯一性
func TestGenerateWorkOrderIDUniqueness(t *testing.T) {
	// 生成多个ID测试唯一性
	idCount := 1000
	ids := make(map[string]bool)
	duplicates := []string{}

	for i := 0; i < idCount; i++ {
		id := GenerateWorkOrderID()
		if ids[id] {
			duplicates = append(duplicates, id)
		}
		ids[id] = true

		// 添加微小延迟以确保时间戳可能发生变化
		if i%100 == 0 {
			time.Sleep(time.Microsecond)
		}
	}

	// 检查是否有重复ID
	if len(duplicates) > 0 {
		t.Errorf("发现重复的工单ID: %v", duplicates)
	}

	// 验证生成的ID数量
	if len(ids) != idCount {
		t.Errorf("期望生成%d个唯一ID，实际生成%d个", idCount, len(ids))
	}
}

// TestGenerateWorkOrderIDFormat 测试工单ID格式的一致性
func TestGenerateWorkOrderIDFormat(t *testing.T) {
	// 生成多个ID测试格式一致性
	for i := 0; i < 100; i++ {
		id := GenerateWorkOrderID()

		// 检查长度
		if len(id) != 15 {
			t.Errorf("第%d个ID长度不正确: %s (长度: %d)", i+1, id, len(id))
		}

		// 检查是否全为数字
		for j, char := range id {
			if char < '0' || char > '9' {
				t.Errorf("第%d个ID第%d位不是数字: %s", i+1, j+1, id)
			}
		}

		// 检查时间戳部分（前10位）
		timestampPart := id[:10]
		if len(timestampPart) != 10 {
			t.Errorf("时间戳部分长度应为10位，实际: %d, ID: %s", len(timestampPart), id)
		}

		// 检查序列号部分（后5位）
		sequencePart := id[10:]
		if len(sequencePart) != 5 {
			t.Errorf("序列号部分长度应为5位，实际: %d, ID: %s", len(sequencePart), id)
		}

		// 验证序列号部分是有效的5位数字（可以以0开头）
		if _, err := strconv.Atoi(sequencePart); err != nil {
			t.Errorf("序列号部分应为有效数字: %s, ID: %s, 错误: %v", sequencePart, id, err)
		}
	}
}

// TestGenerateWorkOrderIDConcurrency 测试并发安全性
func TestGenerateWorkOrderIDConcurrency(t *testing.T) {
	const goroutineCount = 100
	const idsPerGoroutine = 10

	idChan := make(chan string, goroutineCount*idsPerGoroutine)
	done := make(chan bool, goroutineCount)

	// 启动多个goroutine并发生成ID
	for i := 0; i < goroutineCount; i++ {
		go func() {
			for j := 0; j < idsPerGoroutine; j++ {
				id := GenerateWorkOrderID()
				idChan <- id
			}
			done <- true
		}()
	}

	// 等待所有goroutine完成
	for i := 0; i < goroutineCount; i++ {
		<-done
	}
	close(idChan)

	// 收集所有生成的ID
	ids := make(map[string]bool)
	duplicates := []string{}

	for id := range idChan {
		if ids[id] {
			duplicates = append(duplicates, id)
		}
		ids[id] = true

		// 验证ID格式
		if len(id) != 15 {
			t.Errorf("并发生成的ID长度不正确: %s", id)
		}
	}

	// 检查唯一性
	expectedCount := goroutineCount * idsPerGoroutine
	if len(ids) != expectedCount {
		t.Errorf("期望生成%d个唯一ID，实际生成%d个", expectedCount, len(ids))
	}

	if len(duplicates) > 0 {
		t.Errorf("并发测试中发现重复ID: %v", duplicates)
	}
}

// BenchmarkGenerateWorkOrderID 性能基准测试
func BenchmarkGenerateWorkOrderID(b *testing.B) {
	for i := 0; i < b.N; i++ {
		GenerateWorkOrderID()
	}
}
