package util

import (
	"crypto/sha1"
	"fmt"
	"sort"
	"strings"
	"time"
)

func GenerateSignature(ticket, url string) (string, string, string) {
	nonceStr := UUIDWithoutHyphens()
	timestamp := fmt.Sprint(time.Now().Unix())
	// 将参数按照 key=value 的格式准备好
	params := []string{
		"jsapi_ticket=" + ticket,
		"noncestr=" + nonceStr,
		"timestamp=" + timestamp,
		"url=" + url,
	}

	// 按照字典序排序
	sort.Strings(params)

	// 使用 & 符号连接排序后的参数列表
	rawString := strings.Join(params, "&")

	// 使用 SHA1 算法生成签名
	hasher := sha1.New()
	hasher.Write([]byte(rawString))
	signature := fmt.Sprintf("%x", hasher.Sum(nil))

	return signature, nonceStr, timestamp
}
