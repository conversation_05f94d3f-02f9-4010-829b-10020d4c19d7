package mail

import (
	"crypto/tls"
	"fmt"
	"mime"
	"net/smtp"
	"strings"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

// Sender 邮件发送器
type Sender struct {
	config config.SmtpConfig
}

// NewSender 创建邮件发送器
func NewSender(config config.SmtpConfig) *Sender {
	return &Sender{
		config: config,
	}
}

// encodeSubject 编码邮件主题以支持中文
func encodeSubject(subject string) string {
	return mime.QEncoding.Encode("UTF-8", subject)
}

// SendMail 发送邮件
func (s *Sender) SendMail(to []string, subject, body, contentType string) error {
	header := make(map[string]string)
	header["From"] = s.config.From
	header["To"] = strings.Join(to, ",")
	header["Subject"] = encodeSubject(subject)
	header["MIME-Version"] = "1.0"
	header["Content-Type"] = contentType + "; charset=UTF-8"
	header["Content-Transfer-Encoding"] = "8bit"

	message := ""
	for k, v := range header {
		message += fmt.Sprintf("%s: %s\r\n", k, v)
	}
	message += "\r\n" + body

	auth := smtp.PlainAuth("", s.config.Username, s.config.Password, s.config.Host)

	// 创建SMTP客户端
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)

	// 如果使用TLS加密连接
	if s.config.Port == 587 || s.config.Port == 465 {
		tlsConfig := &tls.Config{
			InsecureSkipVerify: true,
			ServerName:         s.config.Host,
		}

		// 对于465端口使用TLS直连
		if s.config.Port == 465 {
			conn, err := tls.Dial("tcp", addr, tlsConfig)
			if err != nil {
				logger.Logger.Errorf("无法建立TLS连接: %v", err)
				return err
			}

			client, err := smtp.NewClient(conn, s.config.Host)
			if err != nil {
				logger.Logger.Errorf("无法创建SMTP客户端: %v", err)
				return err
			}

			if err = client.Auth(auth); err != nil {
				logger.Logger.Errorf("认证失败: %v", err)
				return err
			}

			if err = client.Mail(s.config.Username); err != nil {
				logger.Logger.Errorf("设置发件人失败: %v", err)
				return err
			}

			for _, addr := range to {
				if err = client.Rcpt(addr); err != nil {
					logger.Logger.Errorf("添加收件人失败: %v", err)
					return err
				}
			}

			w, err := client.Data()
			if err != nil {
				logger.Logger.Errorf("获取数据写入器失败: %v", err)
				return err
			}

			_, err = w.Write([]byte(message))
			if err != nil {
				logger.Logger.Errorf("写入邮件内容失败: %v", err)
				return err
			}

			err = w.Close()
			if err != nil {
				logger.Logger.Errorf("关闭数据写入器失败: %v", err)
				return err
			}

			return client.Quit()
		}

		// 对于587端口使用STARTTLS
		client, err := smtp.Dial(addr)
		if err != nil {
			logger.Logger.Errorf("无法连接到SMTP服务器: %v", err)
			return err
		}

		if err = client.StartTLS(tlsConfig); err != nil {
			logger.Logger.Errorf("启动TLS失败: %v", err)
			return err
		}

		if err = client.Auth(auth); err != nil {
			logger.Logger.Errorf("认证失败: %v", err)
			return err
		}

		if err = client.Mail(s.config.Username); err != nil {
			logger.Logger.Errorf("设置发件人失败: %v", err)
			return err
		}

		for _, addr := range to {
			if err = client.Rcpt(addr); err != nil {
				logger.Logger.Errorf("添加收件人失败: %v", err)
				return err
			}
		}

		w, err := client.Data()
		if err != nil {
			logger.Logger.Errorf("获取数据写入器失败: %v", err)
			return err
		}

		_, err = w.Write([]byte(message))
		if err != nil {
			logger.Logger.Errorf("写入邮件内容失败: %v", err)
			return err
		}

		err = w.Close()
		if err != nil {
			logger.Logger.Errorf("关闭数据写入器失败: %v", err)
			return err
		}

		return client.Quit()
	}

	// 普通连接
	return smtp.SendMail(addr, auth, s.config.Username, to, []byte(message))
}

// SendHTMLMail 发送HTML格式的邮件
func (s *Sender) SendHTMLMail(to []string, subject, htmlBody string) error {
	return s.SendMail(to, subject, htmlBody, "text/html")
}

// SendTextMail 发送纯文本格式的邮件
func (s *Sender) SendTextMail(to []string, subject, textBody string) error {
	return s.SendMail(to, subject, textBody, "text/plain")
}
