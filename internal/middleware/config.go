package middleware

import "context"

// DouyinConfigProvider 抖音配置提供者接口
type DouyinConfigProvider interface {
	GetDouyinConfigByAppID(ctx context.Context, appID string) (*DouyinConfig, error)
}

// DouyinConfig 抖音配置
type DouyinConfig struct {
	AppSecret    string `json:"app_secret"`
	PaySecret    string `json:"pay_secret"`
	AccessToken  string `json:"access_token"`
	PayToken     string `json:"pay_token"`
	RtcAppID     string `json:"rtc_app_id"`
	RtcAppKey    string `json:"rtc_app_key"`
}

// 全局配置提供者
var douyinConfigProvider DouyinConfigProvider

// SetDouyinConfigProvider 设置抖音配置提供者
func SetDouyinConfigProvider(provider DouyinConfigProvider) {
	douyinConfigProvider = provider
}

// GetDouyinConfigProvider 获取抖音配置提供者
func GetDouyinConfigProvider() DouyinConfigProvider {
	return douyinConfigProvider
}