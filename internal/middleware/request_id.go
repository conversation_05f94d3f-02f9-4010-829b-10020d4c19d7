package middleware

import (
	"context"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"github.com/gin-gonic/gin"
)

// type contextKey string

// const TrackKey contextKey = "trace_id"

func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 添加追踪ID
		uuID := c.GetHeader(constants.HeaderXRequestID)
		if len(uuID) == 0 {
			uuID = util.UUID()
		}
		c.Request = c.Request.WithContext(context.WithValue(c.Request.Context(), constants.TrackKey, uuID))
		c.<PERSON><PERSON>(constants.HeaderXRequestID, uuID)
		c.Next()
	}
}
