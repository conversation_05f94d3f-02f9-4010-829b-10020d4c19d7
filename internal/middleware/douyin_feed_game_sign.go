package middleware

import (
	"crypto/md5"
	"encoding/base64"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/gin-gonic/gin"
)

func DouyinFeedGameSign() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestSignature := c.Get<PERSON>eader("x-signature")
		if requestSignature == "" {
			logger.Logger.WarnfCtx(c, "DouyinFeedGame missing x-signature header")
			respondDouyinFeedGameError(c, constants.DouyinFeedGameErrSignatureFailed, "check signature failed")
			return
		}

		queryParams := c.Request.URL.Query()
		params := make(map[string]string)
		for key, values := range queryParams {
			if len(values) > 0 {
				params[key] = values[0]
			}
		}

		requiredParams := []string{"nonce", "timestamp", "openid", "appid"}
		for _, param := range requiredParams {
			if _, exists := params[param]; !exists {
				logger.Logger.WarnfCtx(c, "DouyinFeedGame missing required param: %s", param)
				respondDouyinFeedGameError(c, constants.DouyinFeedGameErrInvalidParam, "invalid param")
				return
			}
		}

		timestampStr := params["timestamp"]
		timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
		if err != nil {
			logger.Logger.WarnfCtx(c, "DouyinFeedGame invalid timestamp: %s", timestampStr)
			respondDouyinFeedGameError(c, constants.DouyinFeedGameErrInvalidParam, "invalid param")
			return
		}

		now := time.Now().Unix()
		if now-timestamp > constants.DouyinFeedGameTimestampToleranceSeconds || timestamp-now > constants.DouyinFeedGameTimestampToleranceSeconds {
			logger.Logger.WarnfCtx(c, "DouyinFeedGame timestamp out of range: %d, now: %d", timestamp, now)
			respondDouyinFeedGameError(c, constants.DouyinFeedGameErrInvalidParam, "invalid param")
			return
		}

		secret := "kqm12589367554Yzv"
		calculatedSignature := calculateDouyinFeedGameSignature(params, "", secret)

		// secret := douyinConfig.AppSecret
		// if secret == "" {
		// 	logger.Logger.WarnfCtx(c, "DouyinFeedGame app_secret is empty for appID: %s", appID)
		// 	respondDouyinFeedGameError(c, constants.DouyinFeedGameErrInvalidParam, "invalid appid")
		// 	return
		// }

		if requestSignature != calculatedSignature {
			logger.Logger.WarnfCtx(c, "DouyinFeedGame signature verification failed. Expected: %s, Got: %s", calculatedSignature, requestSignature)
			respondDouyinFeedGameError(c, constants.DouyinFeedGameErrSignatureFailed, "check signature failed")
			return
		}

		c.Set("douyin_feed_game_params", params)
		c.Set("douyin_feed_game_secret", secret)

		logger.Logger.InfofCtx(c, "DouyinFeedGame signature verification passed for openid: %s, appid: %s", params["openid"], params["appid"])
		c.Next()
	}
}

func calculateDouyinFeedGameSignature(params map[string]string, bodyStr, secret string) string {
	keys := make([]string, 0, len(params))
	for key := range params {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	kvList := make([]string, 0, len(keys))
	for _, key := range keys {
		kvList = append(kvList, key+"="+params[key])
	}

	urlParams := strings.Join(kvList, "&")
	rawData := urlParams + bodyStr + secret
	md5Result := md5.Sum([]byte(rawData))
	return base64.StdEncoding.EncodeToString(md5Result[:])
}

func SetDouyinFeedGameResponseSignature(c *gin.Context, responseBody string) {
	paramsInterface, exists := c.Get("douyin_feed_game_params")
	if !exists {
		logger.Logger.WarnfCtx(c, "DouyinFeedGame params not found in context")
		return
	}

	secretInterface, exists := c.Get("douyin_feed_game_secret")
	if !exists {
		logger.Logger.WarnfCtx(c, "DouyinFeedGame secret not found in context")
		return
	}

	params, ok := paramsInterface.(map[string]string)
	if !ok {
		logger.Logger.WarnfCtx(c, "DouyinFeedGame invalid params type in context")
		return
	}

	secret, ok := secretInterface.(string)
	if !ok {
		logger.Logger.WarnfCtx(c, "DouyinFeedGame invalid secret type in context")
		return
	}

	responseSignature := calculateDouyinFeedGameSignature(params, responseBody, secret)
	c.Header("x-signature", responseSignature)
	c.Header("content-type", "application/json")
	logger.Logger.InfofCtx(c, "DouyinFeedGame response signature set: %s", responseSignature)
}

func respondDouyinFeedGameError(c *gin.Context, errNo int32, errMsg string) {
	response := map[string]any{
		"err_no":  errNo,
		"err_msg": errMsg,
		"data": map[string]any{
			"scenes": []any{},
		},
	}
	c.JSON(http.StatusOK, response)
	c.Abort()
}
