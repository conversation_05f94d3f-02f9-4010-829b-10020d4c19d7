package middleware

//func WechatSign() gin.HandlerFunc {
//	return func(c *gin.Context) {
//		timestamp := c.Request.Header.Get("Wechatpay-Timestamp")
//		nonce := c.Request.Header.Get("Wechatpay-Nonce")
//		signature := c.Request.Header.Get("Wechatpay-Signature")
//		serial := c.Request.Header.Get("Wechatpay-Serial")
//		// 构造被签名的字符串
//		bodyBytes, err := io.ReadAll(c.Request.Body)
//		if err != nil {
//			logger.Logger.Errorf("WechatSign Body reading error: %v", err)
//			c.Abort()
//			return
//		}
//		message := timestamp + "\n" + nonce + "\n" + string(bodyBytes) + "\n"
//
//		// 对消息进行SHA-256
//		hashed := sha256.Sum256([]byte(message))
//		// 从传入的apiCertificate中获取证书
//		block, _ := pem.Decode([]byte("")) // TODO 得到证书 apiCertificate
//		if block == nil {
//			c.Abort()
//			return
//		}
//		cert, err := x509.ParseCertificate(block.Bytes)
//		if err != nil {
//			c.Abort()
//			return
//		}
//
//		// 证书序列号要与HTTP头中的Wechatpay-Serial字段相匹配，如果不匹配，则返回错误
//		if strings.ToUpper(hex.EncodeToString(cert.SerialNumber.Bytes())) != strings.ToUpper(serial) {
//			// return errors.New("certificate serial number does not match Wechatpay-Serial")
//			logger.Logger.Errorf("Certificate serial number does not match Wechatpay-Serial")
//			c.Abort()
//			return
//		}
//		// 确保证书包含公钥
//		rsaPublicKey, ok := cert.PublicKey.(*rsa.PublicKey)
//		if !ok {
//			logger.Logger.Errorf("cert.PublicKey Certificate serial number")
//			c.Abort()
//			return
//			// return errors.New("certificate doesn't contain an RSA public key")
//		}
//		// 解码收到的签名
//		decodedSignature, err := base64.StdEncoding.DecodeString(signature)
//		if err != nil {
//			logger.Logger.Errorf("DecodeString error: %s", err.Error())
//			c.Abort()
//			return
//		}
//		// 使用SHA-256 with RSA算法验证签名
//		err = rsa.VerifyPKCS1v15(rsaPublicKey, crypto.SHA256, hashed[:], decodedSignature)
//		if err != nil {
//			logger.Logger.Errorf("VerifyPKCS1v15 failed to verify err: %s", err.Error())
//			c.Abort()
//			return
//		}
//
//		c.Next()
//	}
//}
