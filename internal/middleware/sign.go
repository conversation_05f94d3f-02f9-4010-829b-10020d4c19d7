package middleware

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/gin-gonic/gin"
)

type SignParam struct {
	GameID    string `json:"game_id"`
	Sign      string `json:"sign"`
	Timestamp string `json:"timestamp"`
	Secret    string `json:"secret"`
}

// Sign is api key middleware
func Sign() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := context.Background()

		handler := BaseHandler{}
		param := &SignParam{
			GameID:    c.Request.Header.Get(constants.HeaderGameID),
			Sign:      c.Request.Header.Get(constants.HeaderSign),
			Timestamp: c.Request.Header.Get(constants.HeaderTimestamp),
		}
		if len(param.GameID) == 0 || len(param.Sign) == 0 || len(param.Timestamp) == 0 {
			handler.AuthFailed(c, constants.ErrMissParam.Error())
			c.Abort()
			return
		}
		// 根据 game_id 从 redis 获取
		secretKey := fmt.Sprintf(constants.SystemSecret, param.GameID)
		secretVal, err := redis.Get(ctx, secretKey)
		if err != nil {
			handler.AuthFailed(c, constants.ErrMissParam.Error())
			c.Abort()
			return
		}
		param.Secret = secretVal

		if !verifyTimestamp(param.Timestamp, time.Hour.Seconds()) { // 如果时间超过1小时，则直接返回认证失败
			logger.Logger.Errorf("timestamp one hour timeout, request game id : %s", param.GameID)
			handler.AuthFailed(c, constants.ErrTimeoutByTimestamp.Error())
			c.Abort()
			return
		}
		//bodyBytes, err := io.ReadAll(c.Request.Body)
		bodyBytes, err := io.ReadAll(io.LimitReader(c.Request.Body, 1024)) // 限制字节为1024，防止恶意攻击
		if err != nil {
			logger.Logger.Errorf("read request body")
			handler.AuthFailed(c, constants.ErrAuthFailed.Error())
			c.Abort()
			return
		}
		c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
		if !verifySign(bodyBytes, param) {
			logger.Logger.Errorf("sign verify failed")
			handler.AuthFailed(c, constants.ErrAuthFailed.Error())
			c.Abort()
			return
		}
		c.Next()
	}
}

func verifyTimestamp(timestamp string, offset float64) bool {
	timestampInt, err := strconv.ParseInt(timestamp, 10, 64)
	if err != nil {
		logger.Logger.Errorf("verifyTimestamp err: %v", err)
		return false
	}
	// 对比当前时间和timestamp
	now := time.Now().Unix()
	//if now > (timestampInt + int64(offset)) {
	//	return false
	//}
	//return true
	return now <= (timestampInt + int64(offset))
}

func verifySign(content []byte, param *SignParam) bool {
	logger.Logger.Warnf("body content : %s", string(content))
	// 针对 content 做md5加密
	contentMd5 := util.EncodeMD5(string(content))
	info := []string{
		fmt.Sprintf("content=%s", contentMd5),
		fmt.Sprintf("game_id=%s", param.GameID),
		fmt.Sprintf("secret=%s", param.Secret),
		fmt.Sprintf("timestamp=%s", param.Timestamp),
	}
	// 升序排序
	sort.Strings(info)
	// 使用" "作为分隔符来连接所有字符串
	combined := strings.Join(info, "")
	logger.Logger.Debugf("verifySign: combined: %s, game_id: %s", combined, param.GameID)
	com := util.EncodeMD5(combined)
	logger.Logger.Debugf("verifySign: com: %s", com)
	if param.Sign != com {
		logger.Logger.Errorf("verifySign sign fail sign: %s, combined: %s", param.Sign, com)
		return false
	}
	return true
}

// GenSign 生成签名
func GenSign(gameID, secret, timestamp string, content []byte) string {
	contentMd5 := util.EncodeMD5(string(content))
	info := []string{
		fmt.Sprintf("content=%s", contentMd5),
		fmt.Sprintf("game_id=%s", gameID),
		fmt.Sprintf("secret=%s", secret),
		fmt.Sprintf("timestamp=%s", timestamp),
	}
	// 升序排序
	sort.Strings(info)
	// 使用" "作为分隔符来连接所有字符串
	combined := strings.Join(info, "")
	com := util.EncodeMD5(combined)
	logger.Logger.Infof("GenSign: combined:%s, com:%s", combined, com)
	return com
}
