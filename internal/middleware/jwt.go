package middleware

import (
	"errors"
	"fmt"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
)

// JWT is jwt middleware
func JWT() gin.HandlerFunc {
	return func(c *gin.Context) {
		handler := BaseHandler{}

		token := c.Request.Header.Get(constants.HeaderAuthKey)
		if token == "" {
			logger.Logger.Warn("JWT token is empty")
			handler.AuthFailed(c, constants.ErrAuthParam.Error())
			c.Abort()
		} else {
			claims, err := util.ParseToken(token)
			if err != nil {
				switch err.(*jwt.ValidationError).Errors {
				case jwt.ValidationErrorExpired:
					handler.AuthFailed(c, constants.ErrAuthCheckTokenTimeout.Error())
				default:
					logger.Logger.Warnf("JWT jwt validation err %s", err.Error())
					handler.AuthFailed(c, err.Error())
				}
				c.Abort()
				return
			}
			// 拆解后放入header， 后续解析保存的user_id, device_id, app_id, game_id
			c.Request.Header.Add(constants.HeaderUserID, claims.UserID)
			c.Request.Header.Add(constants.HeaderDeviceID, claims.DeviceID)
			c.Request.Header.Add(constants.HeaderAppID, claims.AppID)
			c.Request.Header.Add(constants.HeaderGameID, claims.GameID)
			c.Request.Header.Add(constants.HeaderOpenID, claims.OpenID)
			c.Request.Header.Add(constants.HeaderOS, claims.OS)
		}

		c.Next()
	}
}

func DecryptionRefreshToken() gin.HandlerFunc {
	return func(c *gin.Context) {
		handler := BaseHandler{}

		token := c.Request.Header.Get(constants.HeaderAuthRefreshKey)
		if token == "" {
			logger.Logger.Warn("DecryptionRefreshToken token is empty")
			handler.AuthFailed(c, constants.ErrAuthParam.Error())
			c.Abort()
		} else {
			blackListKey := fmt.Sprintf(constants.RedisTokenBlackList, token)
			blacklistToken, err := redis.Get(c, blackListKey)
			if err != nil && !errors.Is(err, redis.Nil) {
				handler.AuthFailed(c, constants.ErrAuthForbidden.Error())
				c.Abort()
				return
			}
			if blacklistToken != "" {
				handler.AuthFailed(c, constants.ErrAuthForbidden.Error())
				c.Abort()
				return
			}

			claims, err := util.ParseRefreshToken(token)
			if err != nil {
				switch err.(*jwt.ValidationError).Errors {
				case jwt.ValidationErrorExpired:
					handler.AuthFailed(c, constants.ErrAuthCheckTokenTimeout.Error())
				default:
					logger.Logger.Warnf("DecryptionRefreshToken jwt validation err %s", err.Error())
					handler.AuthFailed(c, err.Error())
				}
				c.Abort()
				return
			}
			// 拆解后放入header， 后续解析保存的user_id, device_id, app_id, game_id
			c.Request.Header.Add(constants.HeaderUserID, claims.UserID)
			c.Request.Header.Add(constants.HeaderDeviceID, claims.DeviceID)
			c.Request.Header.Add(constants.HeaderAppID, claims.AppID)
			c.Request.Header.Add(constants.HeaderGameID, claims.GameID)
			c.Request.Header.Add(constants.HeaderOpenID, claims.OpenID)
			c.Request.Header.Add(constants.HeaderOS, claims.OS)
			c.Request.Header.Add(constants.HeaderAuthRefreshKey, token)
		}

		c.Next()
	}
}

func QiyuJWT() gin.HandlerFunc {
	return func(c *gin.Context) {
		handler := BaseHandler{}

		token := c.Request.Header.Get(constants.HeaderAuthKey)
		if token == "" {
			logger.Logger.Warn("QiyuJWT token is empty")
			handler.AuthFailed(c, constants.ErrAuthParam.Error())
			c.Abort()
		} else {
			claims, err := util.ParseWorkorderToken(token)
			if err != nil {
				switch err.(*jwt.ValidationError).Errors {
				case jwt.ValidationErrorExpired:
					handler.AuthFailed(c, constants.ErrAuthCheckTokenTimeout.Error())
				default:
					logger.Logger.Warnf("JWT jwt validation err %s", err.Error())
					handler.AuthFailed(c, err.Error())
				}
				c.Abort()
				return
			}
			// 拆解后放入header， 后续解析保存的user_id, device_id, app_id, game_id
			c.Request.Header.Add(constants.HeaderDeviceID, claims.DeviceID)
			c.Request.Header.Add(constants.HeaderAppID, claims.AppID)
			c.Request.Header.Add(constants.HeaderUserID, claims.UserID)
			c.Request.Header.Add(constants.HeaderGameID, claims.GameID)
			c.Request.Header.Add(constants.HeaderOpenID, claims.OpenID)
			// 注意：Source 和 DouyinOpenID 字段已从JWT中移除，将在创建工单时从请求体获取
		}

		c.Next()
	}
}

func SwitchJWT() gin.HandlerFunc {
	return func(c *gin.Context) {
		handler := BaseHandler{}

		token := c.Request.Header.Get(constants.HeaderAuthKey)
		if token == "" {
			logger.Logger.InfofCtx(c, "SwitchJWT token is empty")
			// handler.AuthFailed(c, constants.ErrAuthParam.Error())
			// c.Abort()
			c.Next()
		} else {
			claims, err := util.ParseToken(token)
			if err != nil {
				switch err.(*jwt.ValidationError).Errors {
				case jwt.ValidationErrorExpired:
					handler.AuthFailed(c, constants.ErrAuthCheckTokenTimeout.Error())
				default:
					logger.Logger.Warnf("JWT jwt validation err %s", err.Error())
					handler.AuthFailed(c, err.Error())
				}
				c.Abort()
				return
			}
			// 拆解后放入header， 后续解析保存的user_id, device_id, app_id, game_id
			c.Request.Header.Add(constants.HeaderUserID, claims.UserID)
			c.Request.Header.Add(constants.HeaderDeviceID, claims.DeviceID)
			c.Request.Header.Add(constants.HeaderAppID, claims.AppID)
			c.Request.Header.Add(constants.HeaderGameID, claims.GameID)
			c.Request.Header.Add(constants.HeaderOpenID, claims.OpenID)
			c.Request.Header.Add(constants.HeaderOS, claims.OS)
		}

		c.Next()
	}
}
