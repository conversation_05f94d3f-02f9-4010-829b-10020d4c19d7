package https

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/go-resty/resty/v2"
)

var (
	_qqHttpOnce    sync.Once
	_qqHttpService *QQHttpService
)

type QQHttpService struct{}

func SingletonQQHttpService() *QQHttpService {
	_qqHttpOnce.Do(func() {
		_qqHttpService = &QQHttpService{}
	})
	return _qqHttpService
}

const (
	qqTokenURL = "/api/getToken"
	qqLoginURL = "/sns/jscode2session"
)

// GetAccessToken get QQ access token
func (s *QQHttpService) GetAccessToken(ctx context.Context, appID, appSecret string) (*bean.QQAccessToken, error) {
	resp, err := resty.
		New().
		SetTimeout(5 * time.Second).
		R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"grant_type": "client_credential",
			"appid":      appID,
			"secret":     appSecret,
		}).Get(config.GlobConfig.QQ.BaseURL + qqTokenURL)
	if err != nil {
		return nil, err
	}

	qqAccessToken := &bean.QQAccessToken{}
	if err := json.Unmarshal(resp.Body(), qqAccessToken); err != nil {
		return nil, err
	}
	return qqAccessToken, nil
}

func (s *QQHttpService) LoginQQ(ctx context.Context, code string, accessToken string, appID string, appSecret string) (*bean.QQLoginResp, error) {
	resp, err := resty.
		New().
		SetTimeout(5 * time.Second).
		R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"appid":      appID,
			"secret":     appSecret,
			"js_code":    code,
			"grant_type": "authorization_code",
		}).
		Get(config.GlobConfig.QQ.BaseURL + qqLoginURL)
	if err != nil {
		return nil, err
	}

	qqRes := &bean.QQLoginResp{}
	if err := json.Unmarshal(resp.Body(), &qqRes); err != nil {
		return nil, err
	}

	if qqRes.ErrCode != 0 {
		logger.Logger.Errorf("Service LoginQQ: 成功调用，但获取api结果失败，错误信息: %+v", qqRes)
		return nil, fmt.Errorf("success call, but get api error: %v+", qqRes)
	}

	return qqRes, nil
}
