package https

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/go-resty/resty/v2"
)

const (
	// 网易易盾验证码校验接口
	neteaseVerifyURL = "https://c.dun.163.com/api/v2/verify"
	// 腾讯云验证码校验接口
	tencentVerifyURL = "https://captcha.tencentcloudapi.com/"
)

// NeteaseVerifyResponse 网易易盾验证码校验响应
type NeteaseVerifyResponse struct {
	Result      bool   `json:"result"`      // 验证码是否通过，true：校验通过，false：校验不通过
	Error       int    `json:"error"`       // 错误码
	Msg         string `json:"msg"`         // 错误描述信息
	Phone       string `json:"phone"`       // 手机号（仅短信上行验证码类型返回）
	CaptchaType int    `json:"captchaType"` // 验证码类型
	Token       string `json:"token"`       // 验证码流程token
	SdkReduce   bool   `json:"sdkReduce"`   // 是否走降级策略
	ClientIp    string `json:"clientIp"`    // 用户IP
	ClientUa    string `json:"clientUa"`    // 用户UA
	ExtraData   string `json:"extraData"`   // 业务方传入的扩展字段
}

// VerifyNeteaseCaptcha 网易易盾验证码校验
// params: 请求参数，包含验证码相关信息
// return: 验证码校验结果和错误信息
func VerifyNeteaseCaptcha(ctx context.Context, params map[string]interface{}) (*NeteaseVerifyResponse, error) {
	logger.Logger.Infof("verify netease captcha request params: %+v", params)

	resp, err := resty.New().
		SetTimeout(5 * time.Second).
		R().
		SetContext(ctx).
		SetBody(params).
		Post(neteaseVerifyURL)
	if err != nil {
		logger.Logger.Errorf("verify netease captcha failed: %v", err)
		return nil, fmt.Errorf("verify netease captcha failed: %v", err)
	}

	var result NeteaseVerifyResponse
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		logger.Logger.Errorf("decode netease captcha response failed: %v", err)
		return nil, fmt.Errorf("decode netease captcha response failed: %v", err)
	}

	logger.Logger.Infof("verify netease captcha response: %+v", result)
	return &result, nil
}

// VerifyTencentCaptcha 腾讯云验证码校验
func VerifyTencentCaptcha(ctx context.Context, params map[string]string) (bool, error) {
	// 构建请求参数
	values := url.Values{}
	for k, v := range params {
		values.Set(k, v)
	}

	// 发送请求
	reqURL := fmt.Sprintf("%s?%s", tencentVerifyURL, values.Encode())
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, reqURL, nil)
	if err != nil {
		logger.Logger.Errorf("create tencent captcha request failed: %v", err)
		return false, fmt.Errorf("create tencent captcha request failed: %v", err)
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		logger.Logger.Errorf("verify tencent captcha failed: %v", err)
		return false, fmt.Errorf("verify tencent captcha failed: %v", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var result struct {
		Response struct {
			Error struct {
				Code    string `json:"Code"`
				Message string `json:"Message"`
			} `json:"Error"`
			CaptchaCode int    `json:"CaptchaCode"`
			CaptchaMsg  string `json:"CaptchaMsg"`
			RequestId   string `json:"RequestId"`
		} `json:"Response"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		logger.Logger.Errorf("decode tencent captcha response failed: %v", err)
		return false, fmt.Errorf("decode tencent captcha response failed: %v", err)
	}

	if result.Response.Error.Code != "" {
		logger.Logger.Errorf("verify tencent captcha failed: %s", result.Response.Error.Message)
		return false, fmt.Errorf("verify tencent captcha failed: %s", result.Response.Error.Message)
	}

	return result.Response.CaptchaCode == 1, nil
}
