package https

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// 初始化测试环境
func initTestEnvForWechat() {
	// 设置工作目录到项目根目录，确保能找到configs/目录
	wd, err := os.Getwd()
	if err != nil {
		panic("Failed to get working directory: " + err.Error())
	}

	// 如果当前在https目录，需要切换到项目根目录
	if filepath.Base(wd) == "https" {
		if err := os.Chdir("../.."); err != nil {
			panic("Failed to change directory to project root: " + err.Error())
		}
	}

	config.MustInit()
	logger.InitLogger(&config.GlobConfig.Logger)
}

// TestRefreshMiniprogramUrlLinkWithQuery_NoBase64Encoding 测试移除base64编码后的URL生成功能
func TestRefreshMiniprogramUrlLinkWithQuery_NoBase64Encoding(t *testing.T) {
	initTestEnvForWechat()

	// 创建模拟的微信API服务器
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求方法
		assert.Equal(t, "POST", r.Method)

		// 验证请求路径
		assert.Equal(t, "/wxa/generate_urllink", r.URL.Path)

		// 验证access_token参数
		assert.Equal(t, "test_access_token", r.URL.Query().Get("access_token"))

		// 验证Content-Type
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))

		// 读取请求体
		var requestBody map[string]interface{}
		err := json.NewDecoder(r.Body).Decode(&requestBody)
		require.NoError(t, err)

		// 验证基本参数
		assert.Equal(t, float64(1), requestBody["expire_type"])
		assert.Equal(t, float64(30), requestBody["expire_interval"])

		// 根据不同的测试用例验证query参数
		if query, exists := requestBody["query"]; exists {
			queryStr := query.(string)

			// 验证query参数没有被base64编码
			// 如果包含custom_data，应该是原始的JSON字符串，不是base64编码
			if strings.Contains(queryStr, "custom_data") {
				// 原始JSON应该包含花括号，base64编码后不会有花括号
				assert.Contains(t, queryStr, "{", "custom_data应该是原始JSON，不是base64编码")
				assert.Contains(t, queryStr, "}", "custom_data应该是原始JSON，不是base64编码")
			}

			// 如果包含player_name，应该是原始的中文字符，不是base64编码
			if strings.Contains(queryStr, "player_name") {
				// 检查是否包含中文字符（原始形式）
				// base64编码后不会包含中文字符
				assert.Contains(t, queryStr, "玩家", "player_name应该是原始中文，不是base64编码")
			}
		}

		// 返回成功响应
		response := map[string]interface{}{
			"errcode":  0,
			"errmsg":   "ok",
			"url_link": "https://wxaurl.cn/test_generated_link",
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer mockServer.Close()

	// 临时修改配置以使用模拟服务器
	originalBaseURL := config.GlobConfig.Minigame.BaseURL
	config.GlobConfig.Minigame.BaseURL = mockServer.URL
	defer func() {
		config.GlobConfig.Minigame.BaseURL = originalBaseURL
	}()

	service := SingletonWechatHttpService()
	ctx := context.Background()

	// 测试用例1: 包含custom_data的JSON字符串（原本会被base64编码）
	t.Run("测试custom_data原始JSON字符串", func(t *testing.T) {
		queryParams := `custom_data={"level":5,"score":1000}&game_id=test_game`

		urlLink, err := service.RefreshMiniprogramUrlLinkWithQuery(
			ctx,
			"test_access_token",
			"trial",
			queryParams,
		)

		require.NoError(t, err)
		assert.Equal(t, "https://wxaurl.cn/test_generated_link", urlLink)
	})

	// 测试用例2: 包含player_name的中文字符（原本会被base64编码）
	t.Run("测试player_name中文字符", func(t *testing.T) {
		queryParams := `player_name=测试玩家&game_id=test_game`

		urlLink, err := service.RefreshMiniprogramUrlLinkWithQuery(
			ctx,
			"test_access_token",
			"trial",
			queryParams,
		)

		require.NoError(t, err)
		assert.Equal(t, "https://wxaurl.cn/test_generated_link", urlLink)
	})

	// 测试用例3: 同时包含custom_data和player_name
	t.Run("测试同时包含custom_data和player_name", func(t *testing.T) {
		queryParams := `custom_data={"user":"测试用户","data":{"key":"value"}}&player_name=超级玩家&game_id=test_game`

		urlLink, err := service.RefreshMiniprogramUrlLinkWithQuery(
			ctx,
			"test_access_token",
			"trial",
			queryParams,
		)

		require.NoError(t, err)
		assert.Equal(t, "https://wxaurl.cn/test_generated_link", urlLink)
	})

	// 测试用例4: 只有envVersion，没有queryParams
	t.Run("测试只有envVersion", func(t *testing.T) {
		urlLink, err := service.RefreshMiniprogramUrlLinkWithQuery(
			ctx,
			"test_access_token",
			"trial",
			"",
		)

		require.NoError(t, err)
		assert.Equal(t, "https://wxaurl.cn/test_generated_link", urlLink)
	})

	// 测试用例5: 只有queryParams，没有envVersion
	t.Run("测试只有queryParams", func(t *testing.T) {
		queryParams := `game_id=test_game&level=1`

		urlLink, err := service.RefreshMiniprogramUrlLinkWithQuery(
			ctx,
			"test_access_token",
			"",
			queryParams,
		)

		require.NoError(t, err)
		assert.Equal(t, "https://wxaurl.cn/test_generated_link", urlLink)
	})

	// 测试用例6: 都为空
	t.Run("测试参数都为空", func(t *testing.T) {
		urlLink, err := service.RefreshMiniprogramUrlLinkWithQuery(
			ctx,
			"test_access_token",
			"",
			"",
		)

		require.NoError(t, err)
		assert.Equal(t, "https://wxaurl.cn/test_generated_link", urlLink)
	})
}

// TestRefreshMiniprogramUrlLinkWithQuery_ErrorHandling 测试错误处理
func TestRefreshMiniprogramUrlLinkWithQuery_ErrorHandling(t *testing.T) {
	initTestEnvForWechat()

	// 创建返回错误的模拟服务器
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := map[string]interface{}{
			"errcode": 40001,
			"errmsg":  "invalid access_token",
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer mockServer.Close()

	// 临时修改配置
	originalBaseURL := config.GlobConfig.Minigame.BaseURL
	config.GlobConfig.Minigame.BaseURL = mockServer.URL
	defer func() {
		config.GlobConfig.Minigame.BaseURL = originalBaseURL
	}()

	service := SingletonWechatHttpService()
	ctx := context.Background()

	// 测试API错误响应
	t.Run("测试API错误响应", func(t *testing.T) {
		_, err := service.RefreshMiniprogramUrlLinkWithQuery(
			ctx,
			"invalid_token",
			"trial",
			"test=value",
		)

		require.Error(t, err)
		assert.Contains(t, err.Error(), "40001")
		assert.Contains(t, err.Error(), "invalid access_token")
	})
}

// TestEscapeJSONString 测试JSON字符串转义功能
func TestEscapeJSONString(t *testing.T) {
	service := SingletonWechatHttpService()

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "包含双引号的JSON字符串",
			input:    `custom_data={"level":5,"score":1000}&game_id=test`,
			expected: `custom_data={\"level\":5,\"score\":1000}&game_id=test`,
		},
		{
			name:     "包含反斜杠的字符串",
			input:    `path=C:\Users\<USER>\\Users\\test&name=value`,
		},
		{
			name:     "包含双引号和反斜杠的复杂字符串",
			input:    `data={"path":"C:\temp","name":"test"}`,
			expected: `data={\"path\":\"C:\\temp\",\"name\":\"test\"}`,
		},
		{
			name:     "普通字符串不变",
			input:    `game_id=test&level=1`,
			expected: `game_id=test&level=1`,
		},
		{
			name:     "中文字符保持不变",
			input:    `player_name=测试玩家&game_id=游戏`,
			expected: `player_name=测试玩家&game_id=游戏`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.escapeJSONString(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}
