package https

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/go-resty/resty/v2"
)

type DouyinService interface {
	GetBalanceSign(p *bean.DouyinBalanceParam) string
	FetchDouyinChannelID(ctx context.Context, groupID string, userID int64) (string, error)
}

var (
	_douyinHttpOnce    sync.Once
	_douyinHttpService *DouyinHttpService
)

type DouyinHttpService struct{}

func SingletonDouyinHttpService() *DouyinHttpService {
	_douyinHttpOnce.Do(func() {
		_douyinHttpService = &DouyinHttpService{}
	})
	return _douyinHttpService
}

const (
	balanceURL = "/api/apps/game/wallet/get_balance"
	// GET https://developer.toutiao.com/api/apps/live/broadcast/generate_room_id
	generateRoomIDURL = "/api/apps/live/broadcast/generate_room_id"
)

func (s *DouyinHttpService) GetBalanceSign(p *bean.DouyinBalanceParam) string {
	pStr := fmt.Sprintf("access_token=%s&appid=%s&openid=%s&pf=%s&ts=%d&zone_id=%s", p.AccessToken, p.AppID, p.OpenID, p.PF, p.Ts, p.ZoneID)
	allStr := pStr + fmt.Sprintf("&org_loc=%s&method=POST", balanceURL)
	logger.Logger.Infof("GetPaySign allStr: %s", allStr)
	key := []byte(p.MpSig)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(allStr))
	return fmt.Sprintf("%x", h.Sum(nil))
}

// GetGameBalance 获取游戏币余额
func (s *DouyinHttpService) GetGameBalance(ctx context.Context, p *bean.DouyinBalanceParam) (*bean.DouyinBalance, error) {
	p.MpSig = s.GetBalanceSign(p) // 将支付密钥转换为支付Sign

	bodyByte, err := json.Marshal(p)
	if err != nil {
		return nil, err
	}
	resp, err := resty.
		New().
		// SetRetryCount(3).
		// SetRetryWaitTime(2*time.Second).
		SetTimeout(5*time.Second).
		R().
		SetContext(ctx).
		SetHeader("content-type", "application/json").
		SetBody(bodyByte).
		Post(config.GlobConfig.Douyin.ToutiaoURL + balanceURL)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("GetGameBalance StatusCode: %d", resp.StatusCode())
	}

	balance := &bean.DouyinBalance{}
	err = json.Unmarshal(resp.Body(), balance)
	if err != nil {
		return nil, err
	}
	if balance.ErrCode != 0 {
		return nil, fmt.Errorf("GetGameBalance ErrCode: %d, ErrMsg: %s", balance.ErrCode, balance.ErrMsg)
	}
	return balance, nil
}

// GET https://developer.toutiao.com/api/apps/live/broadcast/generate_room_id
func (s *DouyinHttpService) GenerateRoomID(ctx context.Context, accessToken string) (*bean.DouyinGenerateRoomIDResp, error) {
	resp, err := resty.
		New().
		SetTimeout(5*time.Second).
		R().
		SetContext(ctx).
		SetHeader("content-type", "application/json").
		SetQueryParam("access_token", accessToken).
		Get(config.GlobConfig.Douyin.ToutiaoURL + generateRoomIDURL)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("GenerateRoomID StatusCode: %d", resp.StatusCode())
	}

	douyinGenerateRoomIDResp := &bean.DouyinGenerateRoomIDResp{}
	err = json.Unmarshal(resp.Body(), douyinGenerateRoomIDResp)
	if err != nil {
		return nil, err
	}

	if douyinGenerateRoomIDResp.Code != 0 {
		return nil, fmt.Errorf("GenerateRoomID Code: %d, Msg: %s", douyinGenerateRoomIDResp.Code, douyinGenerateRoomIDResp.Msg)
	}
	return douyinGenerateRoomIDResp, nil
}
