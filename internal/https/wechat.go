package https

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/bizerrors"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/go-resty/resty/v2"
)

var (
	_wechatHttpOnce    sync.Once
	_wechatHttpService *WechatHttpService
)

type WechatHttpService struct{}

func SingletonWechatHttpService() *WechatHttpService {
	_wechatHttpOnce.Do(func() {
		_wechatHttpService = &WechatHttpService{}
	})
	return _wechatHttpService
}

const checkSessionKeyURL = "/wxa/checksession"               // https://api.weixin.qq.com/wxa/checksession
const refreshMiniprogramUrlLinkURL = "/wxa/generate_urllink" // https://api.weixin.qq.com/wxa/generate_urllink
const fetchWechatLogURL = "/wxaapi/userlog/userlog_search"
const getUserInfoURL = "/cgi-bin/user/info" // https://api.weixin.qq.com/cgi-bin/user/info

// CheckSessionKey check session key
func (s *WechatHttpService) CheckSessionKey(ctx context.Context, sessionKey, token, openid string) error {
	logger.Logger.Infof("CheckSessionKey req sessionKey: %s, token: %s, openid: %s", sessionKey, token, openid)

	signature, err := s.Signature(sessionKey, nil)
	if err != nil {
		return bizerrors.Wrap(err, "CheckSessionKey failed to generate signature")
	}

	resp, err := resty.New().
		SetTimeout(5*time.Second).
		R().
		SetContext(ctx).
		SetQueryParam("access_token", token).
		SetQueryParam("openid", openid).
		SetQueryParam("sig_method", constants.SecretSha256Type).
		SetQueryParam("signature", signature).
		Get(config.GlobConfig.Minigame.BaseURL + checkSessionKeyURL)
	if err != nil {
		return err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("CheckSessionKey err, https code: %d", resp.StatusCode())
		return fmt.Errorf("CheckSessionKey err, https code: %d", resp.StatusCode())
	}

	res := &bean.MinigameErr{}
	if err := json.Unmarshal(resp.Body(), res); err != nil {
		return err
	}
	if res.ErrCode != 0 {
		// logger.Logger.Errorf("CheckSessionKey err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
		return fmt.Errorf("CheckSessionKey err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
	}
	return nil
}

func (s *WechatHttpService) Signature(sessionKey string, params []byte) (string, error) {
	h := hmac.New(sha256.New, []byte(sessionKey))
	h.Write(params)
	return hex.EncodeToString(h.Sum(nil)), nil
}

// RefreshMiniprogramUrlLink 刷新小程序 URL Link
func (s *WechatHttpService) RefreshMiniprogramUrlLink(ctx context.Context, token string, envVersion string) (string, error) {
	// 构建请求体，设置30天的相对过期时间
	reqBody := map[string]interface{}{
		"expire_type":     1,  // 使用相对过期时间
		"expire_interval": 30, // 30天，使用最大有效期
	}

	// 如果提供了环境版本参数，则添加到请求体中
	if envVersion != "" {
		reqBody["env_version"] = envVersion
	}

	resp, err := resty.New().
		SetTimeout(5*time.Second).
		R().
		SetContext(ctx).
		SetQueryParam("access_token", token).
		SetBody(reqBody). // 添加请求体
		Post(config.GlobConfig.Minigame.BaseURL + refreshMiniprogramUrlLinkURL)
	if err != nil {
		return "", bizerrors.Wrap(err, "RefreshMiniprogramUrlLink request failed")
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("RefreshMiniprogramUrlLink err, https code: %d", resp.StatusCode())
		return "", fmt.Errorf("RefreshMiniprogramUrlLink err, https code: %d", resp.StatusCode())
	}

	// 新增响应结构体
	type urlLinkResponse struct {
		bean.MinigameErr
		URLLink string `json:"url_link"`
	}

	res := &urlLinkResponse{}
	if err := json.Unmarshal(resp.Body(), res); err != nil {
		return "", bizerrors.Wrap(err, "RefreshMiniprogramUrlLink failed to unmarshal response")
	}

	if res.ErrCode != 0 {
		logger.Logger.Errorf("RefreshMiniprogramUrlLink err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
		return "", fmt.Errorf("RefreshMiniprogramUrlLink err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
	}

	return res.URLLink, nil
}

// escapeJSONString 转义JSON字符串中的特殊字符
func (s *WechatHttpService) escapeJSONString(str string) string {
	// 转义双引号和反斜杠
	str = strings.ReplaceAll(str, `\`, `\\`)
	str = strings.ReplaceAll(str, `"`, `\"`)
	return str
}

// RefreshMiniprogramUrlLinkWithQuery 刷新小程序 URL Link 并传入query参数
func (s *WechatHttpService) RefreshMiniprogramUrlLinkWithQuery(ctx context.Context, token string, envVersion string, queryParams string) (string, error) {
	// 构建请求体JSON字符串，避免Go的JSON转义问题
	// 微信API不能正确处理Go JSON编码器对&字符的\u0026转义
	var reqBodyJSON string
	if queryParams != "" && envVersion != "" {
		reqBodyJSON = fmt.Sprintf(`{"expire_type":1,"expire_interval":30,"env_version":"%s","query":"%s"}`,
			envVersion, s.escapeJSONString(queryParams))
	} else if queryParams != "" {
		reqBodyJSON = fmt.Sprintf(`{"expire_type":1,"expire_interval":30,"query":"%s"}`, s.escapeJSONString(queryParams))
	} else if envVersion != "" {
		reqBodyJSON = fmt.Sprintf(`{"expire_type":1,"expire_interval":30,"env_version":"%s"}`, envVersion)
	} else {
		reqBodyJSON = `{"expire_type":1,"expire_interval":30}`
	}

	// 打印请求参数
	logger.Logger.InfofCtx(ctx, "RefreshMiniprogramUrlLinkWithQuery req params - token: %s, envVersion: %s, queryParams: %s, reqBodyJSON: %s", token, envVersion, queryParams, reqBodyJSON)

	resp, err := resty.New().
		SetTimeout(5*time.Second).
		R().
		SetContext(ctx).
		SetQueryParam("access_token", token).
		SetHeader("Content-Type", "application/json").
		SetBody(reqBodyJSON). // 使用JSON字符串避免转义问题
		Post(config.GlobConfig.Minigame.BaseURL + refreshMiniprogramUrlLinkURL)
	if err != nil {
		return "", bizerrors.Wrap(err, "RefreshMiniprogramUrlLinkWithQuery request failed")
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.WarnfCtx(ctx, "RefreshMiniprogramUrlLinkWithQuery err, https code: %d", resp.StatusCode())
		return "", fmt.Errorf("RefreshMiniprogramUrlLinkWithQuery err, https code: %d", resp.StatusCode())
	}

	// 新增响应结构体
	type urlLinkResponse struct {
		bean.MinigameErr
		URLLink string `json:"url_link"`
	}

	res := &urlLinkResponse{}
	if err := json.Unmarshal(resp.Body(), res); err != nil {
		return "", bizerrors.Wrap(err, "RefreshMiniprogramUrlLinkWithQuery failed to unmarshal response")
	}

	if res.ErrCode != 0 {
		logger.Logger.WarnfCtx(ctx, "RefreshMiniprogramUrlLinkWithQuery err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
		return "", fmt.Errorf("RefreshMiniprogramUrlLinkWithQuery err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
	}

	return res.URLLink, nil
}

// FetchWechatLogReq 获取微信的日志数据
type FetchWechatLogReq struct {
	Date      string `json:"date"`      // YYYYMMDD格式的日期，仅支持最近7天
	BeginTime int64  `json:"begintime"` // 开始时间，必须是 date 指定日期的时间
	EndTime   int64  `json:"endtime"`   // 结束时间，必须是 date 指定日期的时间
	Start     int    `json:"start"`     // 开始返回的数据下标，用作分页，默认为0
	Limit     int    `json:"limit"`     // 返回的数据条数，用作分页，默认为20
	TraceID   string `json:"traceId"`   // 小程序启动的唯一ID
	URL       string `json:"url"`       // 小程序页面路径，例如pages/index/index
	ID        string `json:"id"`        // 用户微信号或者OpenId
	FilterMsg string `json:"filterMsg"` // 开发者指定的 filterMsg 字段
	Level     int    `json:"level"`     // 日志等级：2(Info)、4(Warn)、8(Error)
}

// LogMessage 表示单条日志消息
type LogMessage struct {
	Time  int64    `json:"time"`  // 日志时间戳
	Msg   []string `json:"msg"`   // 日志内容
	Level int      `json:"level"` // 日志等级
}

// LogItem 表示一条完整的日志记录
type LogItem struct {
	Level          int          `json:"level"`          // 日志等级
	Platform       int          `json:"platform"`       // 平台类型
	LibraryVersion string       `json:"libraryVersion"` // 库版本
	ClientVersion  string       `json:"clientVersion"`  // 客户端版本
	ID             string       `json:"id"`             // 用户ID
	Timestamp      int64        `json:"timestamp"`      // 时间戳
	Msg            []LogMessage `json:"msg"`            // 日志消息列表
	URL            string       `json:"url"`            // 页面路径
	TraceID        string       `json:"traceid"`        // 追踪ID
	FilterMsg      string       `json:"filterMsg"`      // 过滤消息
}

// FetchWechatLogResp 获取微信日志的响应结构
type FetchWechatLogResp struct {
	bean.MinigameErr

	Data struct {
		List  []LogItem `json:"list"`  // 日志列表
		Total int       `json:"total"` // 总记录数
	} `json:"data"`
}

// FetchWechatLog 获取微信的日志数据
const (
	ErrCodeFrequencyLimit = 200010
)

func (s *WechatHttpService) FetchWechatLog(ctx context.Context, token string, req *FetchWechatLogReq) (*FetchWechatLogResp, error) {
	if req.Date == "" || req.BeginTime == 0 || req.EndTime == 0 {
		return nil, fmt.Errorf("date, begintime and endtime are required")
	}

	request := resty.New().
		SetTimeout(10*time.Second).
		R().
		SetContext(ctx).
		SetQueryParam("access_token", token).
		SetQueryParam("date", req.Date).
		SetQueryParam("begintime", fmt.Sprintf("%d", req.BeginTime)).
		SetQueryParam("endtime", fmt.Sprintf("%d", req.EndTime))

	// 仅在参数有值时才添加到请求中
	if req.Start != 0 {
		request.SetQueryParam("start", fmt.Sprintf("%d", req.Start))
	}
	if req.Limit != 0 {
		request.SetQueryParam("limit", fmt.Sprintf("%d", req.Limit))
	}
	if req.TraceID != "" {
		request.SetQueryParam("traceId", req.TraceID)
	}
	if req.URL != "" {
		request.SetQueryParam("url", req.URL)
	}
	if req.ID != "" {
		request.SetQueryParam("id", req.ID)
	}
	if req.FilterMsg != "" {
		request.SetQueryParam("filterMsg", req.FilterMsg)
	}
	if req.Level != 0 {
		request.SetQueryParam("level", fmt.Sprintf("%d", req.Level))
	}

	resp, err := request.Get(config.GlobConfig.Minigame.BaseURL + fetchWechatLogURL)
	if err != nil {
		return nil, bizerrors.Wrap(err, "FetchWechatLog request failed")
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.ErrorfCtx(ctx, "FetchWechatLog err, https code: %d", resp.StatusCode())
		return nil, fmt.Errorf("FetchWechatLog err, https code: %d", resp.StatusCode())
	}

	res := &FetchWechatLogResp{}
	if err := json.Unmarshal(resp.Body(), res); err != nil {
		return nil, bizerrors.Wrap(err, "FetchWechatLog failed to unmarshal response")
	}

	if res.ErrCode != 0 {
		logger.Logger.WarnfCtx(ctx, "FetchWechatLog err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
		if res.ErrCode == ErrCodeFrequencyLimit {
			return nil, nil
		} else {
			return nil, fmt.Errorf("FetchWechatLog err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
		}
	}

	return res, nil
}

// GetUserInfo gets user information from WeChat API
func (s *WechatHttpService) GetUserInfo(ctx context.Context, accessToken, openID string) (*bean.WechatUserInfo, error) {
	resp, err := resty.New().
		SetTimeout(5 * time.Second).
		R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"access_token": accessToken,
			"openid":       openID,
			"lang":         "zh_CN",
		}).
		Get(config.GlobConfig.Minigame.BaseURL + getUserInfoURL)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("GetUserInfo err, https code: %d", resp.StatusCode())
		return nil, fmt.Errorf("GetUserInfo err, https code: %d", resp.StatusCode())
	}

	res := &bean.WechatUserInfo{}
	if err := json.Unmarshal(resp.Body(), res); err != nil {
		return nil, err
	}

	if res.OpenID == "" {
		logger.Logger.Errorf("GetUserInfo err, response: %s", string(resp.Body()))
		return nil, fmt.Errorf("GetUserInfo failed, empty openid")
	}

	return res, nil
}
