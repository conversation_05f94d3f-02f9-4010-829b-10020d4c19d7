package constants

import (
	"errors"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/bizerrors"
)

var (
	// 原始错误
	ErrAuthCheckTokenTimeout = errors.New("token timeout")
	ErrAuthParam             = errors.New("validation parameter error")
	ErrAuthFailed            = errors.New("authentication failed")
	ErrTimeoutByTimestamp    = errors.New("timestamp timeout one hour")
	ErrMissParam             = errors.New("missing required parameters")
	// Token 禁止访问
	ErrAuthForbidden = errors.New("token forbidden")

	// 原始错误-游戏
	ErrGameIDIsEmpty   = errors.New("game_id can not be empty")
	ErrConfigIsEmpty   = errors.New("conf can not be empty")
	ErrDeviceIDIsEmpty = errors.New("device_id can not be empty")

	// 业务错误
	ErrGameIDNotExist          = bizerrors.NewBizError(100001, "游戏ID不存在")
	ErrPropertiesIsNil         = bizerrors.NewBizError(100002, "属性为空")
	ErrOrderNotFound           = bizerrors.NewBizError(100003, "订单不存在")
	ErrOrderGoodsInfo          = bizerrors.NewBizError(100004, "未找到商品信息")
	ErrOrderMoney              = bizerrors.NewBizError(100005, "商品金额错误")
	ErrWechatSignVerify        = bizerrors.NewBizError(100006, "微信签名验证失败")
	ErrSystemServiceIsBusy     = bizerrors.NewBizError(100007, "服务器繁忙，请稍后再试")
	ErrInitReportAppIsNil      = bizerrors.NewBizError(100008, "未提供 platform app id")
	ErrSubscribeMsgDataIsNil   = bizerrors.NewBizError(100009, "未提供 subscribe msg data")
	ErrRedemptionCodeNotExist  = bizerrors.NewBizError(100010, "兑换码已被使用")
	ErrRedemptionConfigNotOpen = bizerrors.NewBizError(100011, "兑换码配置未开启")
	ErrEventTypeIsNil          = bizerrors.NewBizError(100012, "未找到事件类型")
	ErrRedemptionCodeNotFound  = bizerrors.NewBizError(100013, "未找到此兑换码的信息，请检查是否不存在或删除")
	ErrRedemptionCodeExpire    = bizerrors.NewBizError(100014, "兑换码未到生效时间或已过期")
	ErrRedemptionFrequency     = bizerrors.NewBizError(100015, "兑换码已达到最大兑换次数")
	ErrOrderUserIDNotFound     = bizerrors.NewBizError(100016, "未找到用户信息")
	ErrOrderPayType            = bizerrors.NewBizError(100017, "订单支付类型错误，请检查商品支付类型")
	ErrDouyinPayNotSuccess     = bizerrors.NewBizError(100018, "抖音支付未成功, 请继续尝试调用")
	ErrDouyinMoneyAbnormal     = bizerrors.NewBizError(100019, "抖音支付金额异常")
	ErrDouyinMoneyRule         = bizerrors.NewBizError(100020, "抖音金额规则错误")
	ErrStorageUnKnow           = bizerrors.NewBizError(100021, "存储未知错误")
	ErrActivityIDIsNil         = bizerrors.NewBizError(100022, "活动ID为空")
	ErrActivityIDInvalid       = bizerrors.NewBizError(100023, "活动ID无效")
	ErrPushMsgTooManyUser      = bizerrors.NewBizError(100024, "推送消息超过限制100用户")
	ErrDouyinMoneyMinimum      = bizerrors.NewBizError(100025, "抖音金额不能低于100（单位: 分）")
	ErrUserIDIsValid           = bizerrors.NewBizError(100026, "用户ID无效")
	ErrPlatformTypeNotSupport  = bizerrors.NewBizError(100027, "平台类型不支持")

	ErrSessionKeyExpired      = bizerrors.NewBizError(100026, "用户登录状态已过期")
	ErrRoomAlreadyExist       = bizerrors.NewBizError(100027, "房间已存在")
	ErrDouyinChannelIDIsEmpty = bizerrors.NewBizError(100028, "抖音channel id 为空")
	ErrQiyuAuthCodeIsEmpty    = bizerrors.NewBizError(100029, "七鱼code过期或无效")
	ErrOrderNotExist          = bizerrors.NewBizError(100030, "订单不存在")
	ErrUnionIDMismatch        = bizerrors.NewBizError(100031, "用户身份验证失败")

	// 找不到用户
	ErrOrderExpired   = bizerrors.NewBizError(100032, "订单已失效，请重新从游戏下单")
	ErrUserIDNotFound = bizerrors.NewBizError(100033, "支付失败，请重试")

	// ErrTaskNameIsExist server code 针对于游戏服务器返回的错误
	ErrCheckSessionKey = bizerrors.NewBizError(200000, "服务器检测到用户登录状态已过期，请客户端尝试重新登录")
	ErrTaskNameIsExist = bizerrors.NewBizError(200001, "任务名称已存在")

	// 敏感词检测
	ErrSensitiveMessageVerificationFailed = bizerrors.NewBizError(200002, "敏感词检测失败, 重试3次后仍失败")
	ErrSubscribeCodeDuplicate             = bizerrors.NewBizError(200003, "重复的订阅号code")

	// 封号
	ErrUserBan               = bizerrors.NewBizError(200004, "用户已被封号")
	ErrSubscribeCodeSnapshot = bizerrors.NewBizError(200004, "订阅号code为快照模式")

	// ErrSubscribeCodeSnapshot = bizerrors.NewBizError(200004, "订阅号code为快照模式")
	ErrShareUserIDIsNil     = bizerrors.NewBizError(200005, "分享用户ID为空")
	ErrLoginConfigIsDisable = bizerrors.NewBizError(200006, "登录配置已禁用")

	// 工单系统
	ErrWorkorderAlreadyOngoing = bizerrors.NewBizError(200007, "工单已提交，请勿重复操作")

	// 举报系统
	ErrReportItemsNotFound       = bizerrors.NewBizError(200008, "举报项无效，请重新选择举报项")
	ErrInvalidReportItemType     = bizerrors.NewBizError(200009, "举报项数据类型不一致")
	ErrUnsupportedReportItemType = bizerrors.NewBizError(200010, "不支持的举报项数据类型")

	// 停服配置相关
	ErrRechargeDisabled = bizerrors.NewBizError(200011, "充值功能已关闭，暂时无法下单")

	// 延迟推送参数异常
	ErrDelayParamAbnormal = bizerrors.NewBizError(200012, "delay参数异常,请先确保任务名称存在")

	ErrConfigError = bizerrors.NewBizError(300001, "配置错误") // 不做具体提示，以免暴露参数
	// ErrCaptchaConfigNotFound
	ErrCaptchaConfigNotFound = bizerrors.NewBizError(300002, "验证码配置不存在")

	// H5管理后台错误
	ErrInvalidUsernameOrPassword = bizerrors.NewBizError(100102, "用户名或密码错误")

	// 该游戏已关闭新用户注册
	ErrGameRegistrationClosed = bizerrors.NewBizError(100103, "该游戏已关闭新用户注册")

	// 内容监控相关错误
	ErrInvalidSourceType    = bizerrors.NewBizError(100104, "无效的文本来源类型")
	ErrInvalidContentFormat = bizerrors.NewBizError(100105, "无效的内容格式")

	// 抖音工单认证相关错误
	ErrDouyinConfigNotFound = bizerrors.NewBizError(100106, "抖音客户端配置不存在")
	ErrDouyinInvalidCode    = bizerrors.NewBizError(100107, "抖音授权码无效或已过期")
	ErrDouyinAPIUnavailable = bizerrors.NewBizError(100108, "抖音API服务暂时不可用，请稍后重试")
)
