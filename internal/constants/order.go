package constants

import "time"

var PlatformTypeMap = map[int32]string{
	1: "ios",
	2: "android",
}

const (
	PlatformTypeIOS     = 1
	PlatformTypeAndroid = 2
)

// pay type
const (
	PayTypeAndroidWechatPay = 1
	PayTypeIOSWechatH5Pay   = 2
	PayTypeAndroidGooglePay = 3
	PayTypeIOSApplePay      = 4
	PayTypeAndroidDouyinPay = 5
	PayTypeIOSDouyinPay     = 6
)

// order
const (
	PaymentOrderCreate            = 1 // 创建订单
	PaymentOrderWait              = 2 // 待支付
	PaymentWechatPaySuccess       = 3 // 微信｜抖音回调成功
	PaymentProductShipmentSuccess = 4 // 产品发货成功
	PaymentProductShipmentFail    = 5 // 产品发货失败
)

// pay
const (
	PayChannelTypeIOS           = "h5"
	PayChannelTypeAndroid       = "mds"
	PayChannelTypeDouyinIOS     = "douyin_ios"
	PayChannelTypeDouyinAndroid = "douyin_android"

	PayTypeCurrencyCNY = "CNY"

	// cz
	PayTypeCZ = "cz"
	// 充值
	PayTypeRecharge = "充值"

	// 客服消息文案
	CustomerServiceTitle = "点击我进行充值"
	CustomerServiceDesc  = "充值后返回游戏查看"
)

// 回调产品方最大重试次数
const (
	CallProductShipmentMaxCount = 8
	CallProductShipmentMinute   = 1

	ProductShipmentListGetNumber = 5 // 一次性从redis队列取出的个数
)

const (
	IOSPayPagePath = "index?bkx_order_id="
	ThumbURL       = "https://platform-client.bkxgame.com/platform_pay/pay_ico.png"
)

// DouyinPriceLevels 抖音价格规则
var (
	// DouyinPriceLevels = []int32{1, 3, 6, 8, 12, 18, 25, 30, 40, 45, 50, 60, 68, 73, 78, 88, 98, 108, 118, 128, 148, 168, 188, 198, 328, 648, 998, 1288, 1998, 2998}
	DouyinPriceLevels = []int32{100, 300, 600, 800, 1200, 1800, 2500, 3000, 4000, 4500, 5000, 6000, 6800, 7300, 7800, 8800, 9800, 10800, 11800, 12800, 14800, 16800, 18800, 19800, 32800, 64800, 99800, 128800, 199800, 299800}
)

// RetryDelays 回调定时规则 将时间的重试机制改为  10s/30s/1m/2m/3m/4m/5m/6m/7m/8m/9m/10m/20m/30m/1h/2h - 总共4小时45m。
var RetryDelays = []time.Duration{
	10 * time.Second,
	30 * time.Second,
	1 * time.Minute,
	2 * time.Minute,
	3 * time.Minute,
	4 * time.Minute,
	5 * time.Minute,
	6 * time.Minute,
	7 * time.Minute,
	8 * time.Minute,
	9 * time.Minute,
	10 * time.Minute,
	20 * time.Minute,
	30 * time.Minute,
	1 * time.Hour,
	2 * time.Hour,
}
