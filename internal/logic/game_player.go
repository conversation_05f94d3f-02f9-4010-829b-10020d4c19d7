package logic

import (
	"context"
	"errors"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"gorm.io/gorm"
)

var (
	_gamePlayerOnce  sync.Once
	_gamePlayerLogic *GamePlayerLogic
)

type GamePlayerLogic struct{}

func SingletonGamePlayerLogic() *GamePlayerLogic {
	_gamePlayerOnce.Do(func() {
		_gamePlayerLogic = &GamePlayerLogic{}
	})
	return _gamePlayerLogic
}

// SaveDouyinGamePlayer 保存抖音游戏玩家数据
func (l *GamePlayerLogic) SaveDouyinGamePlayer(ctx context.Context, req *bean.SaveDouyinGamePlayerReq) error {
	logger.Logger.InfofCtx(ctx, "[游戏玩家] 保存抖音游戏玩家数据开始, UserID: %s, OpenID: %s, PlayerID: %s", req.UserID, req.OpenID, req.PlayerID)

	gamePlayerDouyin := store.QueryDB().AGamePlayerDouyin

	// 检查是否已存在相同的记录
	existingPlayer, err := gamePlayerDouyin.WithContext(ctx).
		Where(gamePlayerDouyin.OpenID.Eq(req.OpenID)).
		First()

	var playerModel *model.AGamePlayerDouyin
	var isUpdate bool

	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		// 记录不存在，创建新记录
		logger.Logger.DebugfCtx(ctx, "[游戏玩家] 创建新的抖音游戏玩家记录, UserID: %s, OpenID: %s", req.UserID, req.OpenID)

		playerModel = &model.AGamePlayerDouyin{
			UserID:              req.UserID,
			OpenID:              req.OpenID,
			RoleID:              req.RoleID,
			PlayerID:            req.PlayerID,
			PlayerName:          req.PlayerName,
			PlayerLevel:         req.PlayerLevel,
			RechargeTotalAmount: req.RechargeTotalAmount,
			CustomData:          req.CustomData,
			Zone:                req.Zone,
		}

		err = gamePlayerDouyin.WithContext(ctx).Create(playerModel)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[游戏玩家] 创建抖音游戏玩家记录失败: %v", err)
			return err
		}
		isUpdate = false
	} else if err != nil {
		logger.Logger.WarnfCtx(ctx, "[游戏玩家] 查询抖音游戏玩家记录失败: %v", err)
		return err
	} else {
		// 记录已存在，更新记录
		logger.Logger.DebugfCtx(ctx, "[游戏玩家] 更新现有抖音游戏玩家记录, ID: %d, UserID: %s, OpenID: %s", existingPlayer.ID, req.UserID, req.OpenID)

		updateFields := make(map[string]any)

		// 只更新非空字段
		if req.RoleID != "" {
			updateFields["role_id"] = req.RoleID
		}
		if req.PlayerID != "" {
			updateFields["player_id"] = req.PlayerID
		}
		if req.PlayerName != "" {
			updateFields["player_name"] = req.PlayerName
		}
		if req.PlayerLevel > 0 {
			updateFields["player_level"] = req.PlayerLevel
		}
		if req.RechargeTotalAmount >= 0 {
			updateFields["recharge_total_amount"] = req.RechargeTotalAmount
		}
		if req.CustomData != "" {
			updateFields["custom_data"] = req.CustomData
		}
		if req.Zone != "" {
			updateFields["zone"] = req.Zone
		}

		if len(updateFields) > 0 {
			_, err = gamePlayerDouyin.WithContext(ctx).
				Where(gamePlayerDouyin.ID.Eq(existingPlayer.ID)).
				Updates(updateFields)
			if err != nil {
				logger.Logger.WarnfCtx(ctx, "[游戏玩家] 更新抖音游戏玩家记录失败: %v", err)
				return err
			}
		}

		playerModel = existingPlayer
		isUpdate = true
	}

	operation := "创建"
	if isUpdate {
		operation = "更新"
	}

	logger.Logger.InfofCtx(ctx, "[游戏玩家] %s抖音游戏玩家数据成功, ID: %d, UserID: %s, OpenID: %s, PlayerID: %s",
		operation, playerModel.ID, req.UserID, req.OpenID, req.PlayerID)

	return nil
}
