package logic

import (
	"context"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
)

type ContentLogic struct {
	contentService *service.ContentService
}

func SingletonContentLogic() *ContentLogic {
	return &ContentLogic{
		contentService: service.SingletonContentService(),
	}
}

// ReportContent 上报监控内容
func (l *ContentLogic) ReportContent(ctx context.Context, req *bean.ReportContentReq) (*bean.ReportContentResp, error) {
	return l.contentService.ReportContent(ctx, req)
}
