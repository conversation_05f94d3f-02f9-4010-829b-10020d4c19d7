package logic

import (
	"testing"
)

func TestGenerateDouyinJSSignature(t *testing.T) {
	logic := &WorkorderLogic{}

	// Test with example data from Douyin documentation
	// Based on: https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/sdk/web-app/js/signature
	testCases := []struct {
		name        string
		nonceStr    string
		jsapiTicket string
		timestamp   string
		url         string
		expected    string
	}{
		{
			name:        "Douyin documentation example",
			nonceStr:    "Wm3WZYTPz0wzccnW",
			jsapiTicket: "sM4AOVdWfPE4DxkXGEs8VMCPGGVi4C3VM0P37wVUCFvkVAy_90u5h9nbSlYy3-Sl-HhTdfl2fzFy1AOcHKP7qg",
			timestamp:   "1414587457",
			url:         "https://open.douyin.com?params=value",
			expected:    "af3b1c5a146c609fbe429de8ba6163a0", // Expected result from Douyin docs
		},
		{
			name:        "Simple test case",
			nonceStr:    "test123",
			jsapiTicket: "test_ticket",
			timestamp:   "1234567890",
			url:         "https://example.com",
			expected:    "", // We'll calculate this to verify algorithm
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := logic.generateDouyinJSSignature(tc.nonceStr, tc.jsapiTicket, tc.timestamp, tc.url)

			if tc.expected != "" && result != tc.expected {
				t.Errorf("Expected signature %s, got %s", tc.expected, result)
			}

			// Verify that the signature is not empty and has the correct format (32 character MD5)
			if len(result) != 32 {
				t.Errorf("Expected signature length 32, got %d", len(result))
			}

			t.Logf("Test case: %s", tc.name)
			t.Logf("Input: nonce_str=%s, jsapi_ticket=%s, timestamp=%s, url=%s", tc.nonceStr, tc.jsapiTicket, tc.timestamp, tc.url)
			t.Logf("Output signature: %s", result)
		})
	}
}

func TestDouyinJSSignatureParameterSorting(t *testing.T) {
	logic := &WorkorderLogic{}

	// Test that parameters are sorted correctly according to ASCII order
	result1 := logic.generateDouyinJSSignature("abc", "ticket", "123", "url")
	result2 := logic.generateDouyinJSSignature("abc", "ticket", "123", "url")

	// Same inputs should produce same results
	if result1 != result2 {
		t.Errorf("Same inputs should produce same signature, got %s and %s", result1, result2)
	}

	// Different order of providing the same parameters should still produce the same result
	// since the method sorts them internally
	if len(result1) != 32 {
		t.Errorf("Signature should be 32 characters (MD5), got %d", len(result1))
	}
}
