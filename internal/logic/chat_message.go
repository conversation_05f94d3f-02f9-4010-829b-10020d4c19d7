package logic

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_chatMessageOnce  sync.Once
	_chatMessageLogic *ChatMessageLogic
)

// ChatMessageLogic 聊天消息逻辑
type ChatMessageLogic struct {
	chatMessageService *service.ChatMessageService
	qiyuService        *service.QiyuService
	userService        *service.UserService
	dataReportService  *service.DataReportService
}

// SingletonChatMessageLogic 获取聊天消息逻辑单例
func SingletonChatMessageLogic() *ChatMessageLogic {
	_chatMessageOnce.Do(func() {
		_chatMessageLogic = &ChatMessageLogic{
			chatMessageService: service.SingletonChatMessageService(),
			qiyuService:        service.SingletonQiyuService(),
			userService:        service.SingletonUserService(),
			dataReportService:  service.SingletonDataReportService(),
		}
	})
	return _chatMessageLogic
}

// GetMessageHistory 获取消息历史
func (l *ChatMessageLogic) GetMessageHistory(ctx context.Context, req *bean.WorkorderHistoryReq) (*bean.WorkorderHistoryResp, error) {
	logger.Logger.InfofCtx(ctx, "[聊天系统] 开始获取消息历史, GameID: %s, UserID: %s, AppID(UnionID): %s", req.GameID, req.UserID, req.AppID)

	// 如果AppID(UnionID)不为空，尝试查找用户绑定信息（GameID是必传参数）
	if req.AppID != "" {
		logger.Logger.InfofCtx(ctx, "[聊天系统] UnionID存在，开始查找用户信息, UnionID: %s, GameID: %s", req.AppID, req.GameID)

		// 尝试通过UnionID和GameID获取userID
		foundUserID, err := l.userService.GetUserIDByUnionIDSafe(ctx, req.AppID, req.GameID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[聊天系统] 通过UnionID查找UserID失败: %v, UnionID: %s, GameID: %s", err, req.AppID, req.GameID)
			// 继续使用请求中的UserID，不中断流程
		} else if foundUserID != "" {
			// 找到了UserID
			logger.Logger.InfofCtx(ctx, "[聊天系统] 查找用户成功, UserID: %s", foundUserID)

			// 更新请求中的UserID
			req.UserID = foundUserID

			// 尝试获取用户的小游戏OpenID
			minigameInfo, err := l.userService.GetMinigameModel(ctx, foundUserID)
			if err != nil {
				logger.Logger.WarnfCtx(ctx, "[聊天系统] 获取小游戏信息失败: %v, UserID: %s", err, foundUserID)
				// 继续使用请求中的OpenID，不中断流程
			} else if minigameInfo != nil {
				// 更新请求中的OpenID
				req.OpenID = minigameInfo.OpenID
				logger.Logger.InfofCtx(ctx, "[聊天系统] 获取小游戏OpenID成功: %s", minigameInfo.OpenID)
			} else {
				logger.Logger.WarnfCtx(ctx, "[聊天系统] 未找到小游戏信息, UserID: %s", foundUserID)
			}
		} else {
			logger.Logger.WarnfCtx(ctx, "[聊天系统] 未找到用户, UnionID: %s, GameID: %s", req.AppID, req.GameID)
		}
	}

	// 调用service层获取消息历史
	resp, err := l.chatMessageService.GetMessageHistory(ctx, req)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[聊天系统] 获取消息历史失败: %v", err)
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "[聊天系统] 获取消息历史成功, UserID: %s, 消息数量: %d", req.UserID, len(resp.Messages))
	return resp, nil
}

// SaveFeedback 保存反馈
func (l *ChatMessageLogic) SaveFeedback(ctx context.Context, req *bean.WorkorderFeedbackReq) error {
	logger.Logger.InfofCtx(ctx, "[聊天系统] 开始保存反馈, GameID: %s, UserID: %s, FeedbackType: %d", req.GameID, req.UserID, req.FeedbackType)

	// 原有的保存反馈逻辑
	err := l.chatMessageService.SaveFeedback(ctx, req)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[聊天系统] 保存反馈失败: %v", err)
		return err
	}

	// 获取消息详情，以获取 content
	chatMessage, err := l.getChatMessageByID(ctx, req.MessageID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[聊天系统] 获取消息详情失败，无法进行数数打点: %v", err)
		return nil // 不应因为打点失败而影响主流程
	}

	// 进行数数打点
	isUseful := "no"
	if req.FeedbackType == 1 {
		isUseful = "yes"
	}

	// 准备打点数据
	properties := make(map[string]interface{})
	properties["game_id"] = chatMessage.GameID
	properties["question"] = chatMessage.Content
	properties["answer"] = chatMessage.Content // 假设这是回答内容，可能需要调整
	properties["is_useful"] = isUseful

	// 准备 AccountID，如果 UserID 为空则使用 UUID 生成一个
	accountID := chatMessage.UserID
	if accountID == "" {
		accountID = util.UUID() // 生成一个随机 UUID 作为 AccountID
		logger.Logger.InfofCtx(ctx, "[聊天系统] UserID 为空，已生成随机 UUID 作为 AccountID: %s", accountID)
	}

	// 上报数据
	reportReq := &bean.UploadReport{
		UUID:       util.UUID(),
		AccountID:  accountID, // 对应数数平台的 Account_ID
		AppID:      config.GlobConfig.Thinkdata.ThinkdataAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     chatMessage.GameID,
		EventName:  "server_answer_useful",
		EventType:  constants.ThinkingdataTrack,
		Properties: properties,
	}

	err = l.dataReportService.UploadReport(ctx, reportReq)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[聊天系统] 数数打点失败: %v", err)
		// 不应因为打点失败而影响主流程
	} else {
		logger.Logger.InfofCtx(ctx, "[聊天系统] 数数打点成功，事件: server_answer_useful, UserID: %s, GameID: %s", chatMessage.UserID, chatMessage.GameID)
	}

	logger.Logger.InfofCtx(ctx, "[聊天系统] 保存反馈成功")
	return nil
}

// getChatMessageByID 根据消息ID获取消息详情
func (l *ChatMessageLogic) getChatMessageByID(ctx context.Context, messageID int32) (*model.MChatMessage, error) {
	q := store.QueryDB().MChatMessage
	message, err := q.WithContext(ctx).
		Where(q.ID.Eq(messageID)).
		First()

	if err != nil {
		return nil, fmt.Errorf("查询消息详情失败: %w", err)
	}

	return message, nil
}

// SubmitChatMessage 提交聊天消息
func (l *ChatMessageLogic) SubmitChatMessage(ctx context.Context, req *bean.ChatMessageSubmitReq) (*bean.ChatMessageSubmitResp, error) {
	logger.Logger.InfofCtx(ctx, "[聊天系统] 开始提交聊天消息, GameID: %s, UserID: %s, AppID(UnionID): %s, Content: %s", req.GameID, req.UserID, req.AppID, req.Content)

	// 如果AppID(UnionID)不为空，尝试查找用户绑定信息（GameID是必传参数）
	if req.AppID != "" {
		logger.Logger.InfofCtx(ctx, "[聊天系统] UnionID存在，开始查找用户信息, UnionID: %s, GameID: %s", req.AppID, req.GameID)

		// 尝试通过UnionID和GameID获取userID
		foundUserID, err := l.userService.GetUserIDByUnionIDSafe(ctx, req.AppID, req.GameID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[聊天系统] 通过UnionID查找UserID失败: %v, UnionID: %s, GameID: %s", err, req.AppID, req.GameID)
			// 继续使用请求中的UserID，不中断流程
		} else if foundUserID != "" {
			// 找到了UserID
			logger.Logger.InfofCtx(ctx, "[聊天系统] 查找用户成功, UserID: %s", foundUserID)

			// 更新请求中的UserID
			req.UserID = foundUserID

			// 尝试获取用户的小游戏OpenID
			minigameInfo, err := l.userService.GetMinigameModel(ctx, foundUserID)
			if err != nil {
				logger.Logger.WarnfCtx(ctx, "[聊天系统] 获取小游戏信息失败: %v, UserID: %s", err, foundUserID)
				// 继续使用请求中的OpenID，不中断流程
			} else if minigameInfo != nil {
				// 更新请求中的OpenID
				req.OpenID = minigameInfo.OpenID
				logger.Logger.InfofCtx(ctx, "[聊天系统] 获取小游戏OpenID成功: %s", minigameInfo.OpenID)
			} else {
				logger.Logger.WarnfCtx(ctx, "[聊天系统] 未找到小游戏信息, UserID: %s", foundUserID)
			}
		} else {
			logger.Logger.WarnfCtx(ctx, "[聊天系统] 未找到用户, UnionID: %s, GameID: %s", req.AppID, req.GameID)
		}
	}

	// 调用服务层提交聊天消息
	resp, err := l.chatMessageService.SubmitChatMessage(ctx, req)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[聊天系统] 提交聊天消息失败: %v", err)
		return nil, err
	}

	// 上报库问题事件
	if err := l.dataReportService.ReportLibraryQuestionAnswer(ctx, req.UserID, req.OpenID, req.GameID, req.Content, resp.ReplyContent); err != nil {
		logger.Logger.WarnfCtx(ctx, "[聊天系统] library_question_answer埋点上报失败: %v", err)
		// 不影响主流程
	} else {
		logger.Logger.InfofCtx(ctx, "[聊天系统] library_question_answer埋点上报成功, UserID: %s, GameID: %s", req.UserID, req.GameID)
	}

	logger.Logger.InfofCtx(ctx, "[聊天系统] 提交聊天消息成功, UserID: %s, 回复内容: %s", req.UserID, resp.ReplyContent)
	return resp, nil
}
