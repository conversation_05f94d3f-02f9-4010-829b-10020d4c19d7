package logic

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"strconv"
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_gravityEngineOnce  sync.Once
	_gravityEngineLogic *GravityEngineLogic
)

type GravityEngineLogic struct {
	minigameService      *service.MinigameService
	gravityEngineService *service.GravityEngineService
	userService          *service.UserService
	douyinService        *service.DouyinService
}

func SingletonGravityEngineLogic() *GravityEngineLogic {
	_gravityEngineOnce.Do(func() {
		_gravityEngineLogic = &GravityEngineLogic{
			minigameService:      service.SingletonMinigameService(),
			gravityEngineService: service.SingletonGravityEngineService(),
			userService:          service.SingletonUserService(),
			douyinService:        service.SingletonDouyinService(),
		}
	})
	return _gravityEngineLogic
}

// WechatTokenInfo 微信token信息
type WechatTokenInfo struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int32  `json:"expires_in"`
}

// VerifyGravityRequestAndGetToken 验证引力引擎请求签名并获取微信Token信息
func (l *GravityEngineLogic) VerifyGravityRequestAndGetToken(ctx context.Context, req *bean.RefreshWechatTokenReq) (*WechatTokenInfo, error) {
	// 验证参数是否为空
	if req.GameID == "" {
		logger.Logger.WarnfCtx(ctx, "Invalid parameters: GameID: %s, GravitySign: %s, GravityTimestamp: %d", req.GameID, req.GravitySign, req.GravityTimestamp)
		return nil, errors.New("invalid parameters")
	}
	// 获取引力引擎AccessToken
	gravityAccessToken, _, err := l.userService.GetBindGravityAppID(ctx, req.GameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "GetBindGravityAppID failed for game %s: %v", req.GameID, err)
		return nil, errors.New("failed to get gravity config")
	}
	if gravityAccessToken == "" {
		logger.Logger.WarnfCtx(ctx, "GravityAccessToken is empty for game %s", req.GameID)
		return nil, errors.New("gravity access token not configured")
	}

	// 获取小游戏AppID
	var appID string
	var accessToken string
	var expiresIn int32

	// 根据平台类型获取不同的配置
	if req.PlatformType == "douyin_minigame" {
		// 抖音小游戏：从 a_config_douyin 表获取配置
		douyinConfig, err := l.douyinService.GetDouyinConf(ctx, req.GameID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "GetDouyinConf failed for game %s: %v", req.GameID, err)
			return nil, err
		}
		appID = douyinConfig.AppID
		accessToken = douyinConfig.AccessToken
		expiresIn = douyinConfig.ExpiresIn
	} else {
		// 默认：微信小游戏，从原有配置表获取
		config, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "GetMinigameConfig failed for game %s: %v", req.GameID, err)
			return nil, err
		}
		appID = config.AppID
		accessToken = config.AccessToken
		expiresIn = config.ExpiresIn
	}

	// 验证签名
	timestampStr := strconv.FormatInt(req.GravityTimestamp, 10)
	strToSign := gravityAccessToken + timestampStr + appID
	hash := md5.Sum([]byte(strToSign))
	calculatedSign := strings.ToLower(hex.EncodeToString(hash[:]))

	if calculatedSign != req.GravitySign {
		logger.Logger.WarnfCtx(ctx, "Invalid signature for game %s. Expected: %s, Got: %s, StringToSign: %s", req.GameID, calculatedSign, req.GravitySign, strToSign)
		return nil, errors.New("invalid signature")
	}

	return &WechatTokenInfo{
		AccessToken: accessToken,
		ExpiresIn:   expiresIn,
	}, nil
}
