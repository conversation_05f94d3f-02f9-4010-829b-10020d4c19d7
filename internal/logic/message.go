package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/task"
	"github.com/hibiken/asynq"
	"gorm.io/gorm"
)

var (
	_messageOnce  sync.Once
	_messageLogic *MessageLogic
)

type MessageLogic struct {
	userService          *service.UserService
	minigameService      *service.MinigameService
	messageService       *service.MessageService
	douyinService        *service.DouyinService
	wechatMessageService *service.WechatMessageService
	douyinMessageService *service.DouyinMessageService
}

func SingletonMessageLogic() *MessageLogic {
	_messageOnce.Do(func() {
		_messageLogic = &MessageLogic{
			userService:          service.SingletonUserService(),
			minigameService:      service.SingletonMinigameService(),
			messageService:       service.SingletonMessageService(),
			douyinService:        service.SingletonDouyinService(),
			wechatMessageService: service.SingletonWechatMessageService(),
			douyinMessageService: service.SingletonDouyinMessageService(),
		}
	})
	return _messageLogic
}

// SubMessageNotify 订阅消息
//func (l *MessageLogic) SubMessageNotify(ctx context.Context, req *bean.SubMessageNotifyReq) (*bean.SubMessageNotifyRes, error) {
//	//  删除取消消息任务
//	if req.PushType == constants.MsgSubscribeDelayedPushType && req.Delay == constants.MsgSubscribeCancelDelay {
//		msg, err := l.messageService.GetSubscribeMessageByTaskName(ctx, req.TaskName)
//		if err != nil {
//			return nil, err
//		}
//		err = sdasdasd.Cancel(ctx, msg.TaskID)
//		if err != nil {
//			return nil, err
//		}
//		err = l.messageService.UpdateMessageNotifyStatus(ctx,
//			msg.ID, constants.WechatSubscribeMsgCancelDelayStatus, "")
//		if err != nil {
//			return nil, err
//		}
//		return &bean.SubMessageNotifyRes{
//			SuccessCount: 1,
//		}, nil
//	}
//
//	// 注意: 批量用户必须是同一个小游戏下的用户id
//	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
//	if err != nil {
//		return nil, err
//	}
//
//	var successCount int32
//	for _, userID := range req.UserIDs {
//		info, err := l.userService.GetMinigameModel(ctx, userID)
//		if err != nil {
//			return nil, err
//		}
//		id, err := l.messageService.SubMessageNotify(ctx, userID, req)
//		if err != nil {
//			return nil, err
//		}
//
//		taskReq := sdasdasd.SubscribeMessageTaskReq{
//			ID:          id,
//			AccessToken: conf.AccessToken,
//			OpenID:      info.OpenID,
//			TemplateID:  req.TemplateID,
//			Page:        req.Page,
//			Data:        req.Data,
//		}
//		taskReqByte, err := json.Marshal(taskReq)
//		if err != nil {
//			return nil, err
//		}
//		// 判断PushType
//		var taskID string
//		if req.PushType == constants.MsgSubscribePushType {
//			taskID, err = sdasdasd.Submit(asynq.NewTask(sdasdasd.TypeSubscribeMessage, taskReqByte))
//			if err != nil {
//				logger.Logger.Errorf("SubMessageNotify sdasdasd.Submit err: %s", err.Error())
//				return nil, err
//			}
//		} else if req.PushType == constants.MsgSubscribeDelayedPushType {
//			taskID, err = sdasdasd.SubmitByDelay(asynq.NewTask(sdasdasd.TypeSubscribeMessage, taskReqByte),
//				time.Duration(req.Delay)*time.Second)
//			if err != nil {
//				logger.Logger.Errorf("SubMessageNotify sdasdasd.SubmitByDelay err: %s", err.Error())
//				return nil, err
//			}
//		}
//		// 更新taskID
//		err = l.messageService.UpdateMessageNotifyTaskID(ctx, id, taskID)
//		if err != nil {
//			return nil, err
//		}
//		successCount++
//	}
//	return &bean.SubMessageNotifyRes{
//		SuccessCount: successCount,
//	}, nil
//}

// SubMessageNotify 微信订阅消息
func (l *MessageLogic) SubMessageNotify(ctx context.Context, req *bean.SubMessageNotifyReq) (*bean.SubMessageNotifyRes, error) {
	logger.Logger.InfofCtx(ctx, "SubMessageNotify req: %+v", req)
	// Handle message cancellation
	if req.PushType == constants.MsgSubscribeDelayedPushType && req.Delay == constants.MsgSubscribeCancelDelay {
		return l.cancelSubscribeMessage(ctx, req)
	}

	return l.handleBatchUsers(ctx, req)
}

// DouyinSubMessageNotify 抖音订阅消息
func (l *MessageLogic) DouyinSubMessageNotify(ctx context.Context, req *bean.DouyinSubMessageNotifyReq) (*bean.DouyinSubMessageNotifyRes, error) {
	conf, err := l.douyinService.GetDouyinConf(ctx, req.GameID)
	if err != nil {
		return nil, err
	}
	// 根据userID 获取openID
	if len(req.UserIDs) == 0 {
		return nil, fmt.Errorf("DouyinSubMessageNotify userIDs is empty, gameID: %s", req.GameID)
	}
	// 保存到sub message
	if err := l.messageService.BatchSubMessageNotify(ctx, req.UserIDs,
		&bean.SubMessageNotifyReq{
			GameID:       req.GameID,
			PlatformType: req.PlatformType,
			TaskName:     req.TaskName,
			TemplateID:   req.TemplateID,
			Data:         req.Data,
			Page:         req.Page,
			PushType:     constants.MsgSubscribePushType,
		},
	); err != nil {
		return nil, err
	}
	openIDMap, err := l.userService.GetOpenIDMapByUserIDs(ctx, req.UserIDs)
	if err != nil {
		return nil, err
	}

	var (
		successCount   int32
		successUserIDs []string
		failUserIDs    []string
	)
	for openID := range openIDMap {
		_, err := l.douyinMessageService.FetchSubscribeMessage(ctx, &bean.DouyinSubMessageReq{
			AccessToken: conf.AccessToken,
			AppID:       conf.AppID,
			OpenID:      openID,
			TplID:       req.TemplateID,
			Data:        req.Data,
			Page:        req.Page,
		})
		if err != nil {
			failUserIDs = append(failUserIDs, openIDMap[openID])
			logger.Logger.Errorf("FetchSubscribeMessage err: %s, gameID: %s, userID: %s", err.Error(), req.GameID, openIDMap[openID])
			continue
		}
		successUserIDs = append(successUserIDs, openIDMap[openID])
		successCount++
	}
	if len(successUserIDs) > 0 {
		if err := l.messageService.UpdatesSubMessageNotifyStatus(ctx, successUserIDs, constants.WechatSubscribeMsgSuccessStatus); err != nil {
			return nil, err
		}
	}
	if len(failUserIDs) > 0 {
		if err := l.messageService.UpdatesSubMessageNotifyStatus(ctx, failUserIDs, constants.WechatSubscribeMsgFailStatus); err != nil {
			return nil, err
		}
	}
	return &bean.DouyinSubMessageNotifyRes{SuccessCount: successCount}, nil
}

// cancelSubscribeMessage handles the specific logic of message cancellation.
func (l *MessageLogic) cancelSubscribeMessage(ctx context.Context, req *bean.SubMessageNotifyReq) (*bean.SubMessageNotifyRes, error) {
	// 参数基本验证
	if req.TaskName == "" {
		logger.Logger.WarnfCtx(ctx, "取消订阅消息失败，TaskName为空, GameID: %s", req.GameID)
		return nil, constants.ErrDelayParamAbnormal
	}

	msg, err := l.messageService.GetSubscribeMessageByTaskName(ctx, req.TaskName)
	if err != nil {
		// 针对 delay 参数异常的友好错误处理
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.WarnfCtx(ctx, "取消订阅消息失败，任务名称不存在, GameID: %s, TaskName: %s", req.GameID, req.TaskName)
			return nil, constants.ErrDelayParamAbnormal
		}
		// 其他数据库错误直接返回
		return nil, err
	}

	if err := task.Cancel(ctx, msg.TaskID); err != nil {
		return nil, err
	}
	if err := l.messageService.UpdateMessageNotifyStatus(ctx, msg.ID, constants.WechatSubscribeMsgCancelDelayStatus, ""); err != nil {
		return nil, err
	}
	return &bean.SubMessageNotifyRes{SuccessCount: 1}, nil
}

// handleBatchUsers goes through the user IDs and manages the notification sdasdasd.
func (l *MessageLogic) handleBatchUsers(ctx context.Context, req *bean.SubMessageNotifyReq) (*bean.SubMessageNotifyRes, error) {
	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return nil, err
	}

	var successCount int32
	for _, userID := range req.UserIDs {
		if err := l.processSingleUser(ctx, conf, userID, req); err != nil {
			return nil, err
		}
		successCount++
	}
	return &bean.SubMessageNotifyRes{SuccessCount: successCount}, nil
}

// processSingleUser handles the notification logic for a single user.
func (l *MessageLogic) processSingleUser(ctx context.Context, conf *model.AConfigMinigame, userID string, req *bean.SubMessageNotifyReq) error {
	info, err := l.userService.GetMinigameModel(ctx, userID)
	if err != nil {
		return err
	}

	isExist, err := l.messageService.IsTaskNameExist(ctx, req.GameID, req.TaskName)
	if err != nil {
		return err
	}
	if isExist {
		return constants.ErrTaskNameIsExist
	}

	id, err := l.messageService.SubMessageNotify(ctx, userID, req)
	if err != nil {
		return err
	}

	taskReq := bean.SubscribeMessageTaskReq{
		ID:          id,
		AccessToken: conf.AccessToken,
		OpenID:      info.OpenID,
		TemplateID:  req.TemplateID,
		Page:        req.Page,
		Data:        req.Data,
	}
	if err := l.submitTask(ctx, req, taskReq); err != nil {
		return err
	}

	return nil
}

// submitTask submits the notification sdasdasd based on the PushType.
func (l *MessageLogic) submitTask(ctx context.Context, req *bean.SubMessageNotifyReq, taskReq bean.SubscribeMessageTaskReq) error {
	taskReqByte, err := json.Marshal(taskReq)
	if err != nil {
		return err
	}
	var taskID string
	switch req.PushType {
	case constants.MsgSubscribePushType:
		taskID, err = task.Submit(asynq.NewTask(task.TypeSubscribeMessage, taskReqByte))
	case constants.MsgSubscribeDelayedPushType:
		taskID, err = task.SubmitByDelay(asynq.NewTask(task.TypeSubscribeMessage, taskReqByte), time.Duration(req.Delay)*time.Second)
	}
	if err != nil {
		logger.Logger.Errorf("Task submission error: %s", err.Error())
		return err
	}
	return l.messageService.UpdateMessageNotifyTaskID(ctx, taskReq.ID, taskID)
}
