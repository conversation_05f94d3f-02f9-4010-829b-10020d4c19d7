package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/task"
	"github.com/hibiken/asynq"
	"github.com/jinzhu/copier"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

// 测试函数
func TestOrderService_WechatPayCallback(t *testing.T) {
	Init()

	ctx := context.Background()
	order, err2 := SingletonOrderLogic().orderService.GetOrderDetail(ctx, "8a5bac21fdbd4785a64950cda592721d")
	if err2 != nil {
		return
	}

	// 查询game_id
	game, err := SingletonUserLogic().userService.GetGameInfo(ctx, order.GameID)
	if err != nil {
		logger.Logger.Errorf("Error getting game")
		return
	}
	// 判断是否是首充
	isFirstPay := SingletonOrderLogic().orderService.IsFirstPay(ctx, order.UserID)
	// 上报引力数据
	goods, err := SingletonOrderLogic().orderService.GetGoodsDetail(ctx, order.GoodsID)
	if err != nil {
		logger.Logger.Errorf("Error getting good")
		return
	}
	minigameUser, err := SingletonUserLogic().userService.GetMinigameModel(ctx, order.UserID)
	if err != nil {
		// return nil, err
		return
	}
	clientID := minigameUser.OpenID

	properties := make(map[string]interface{})
	properties["$is_first_pay"] = isFirstPay
	properties["$order_id"] = order.OrderID
	properties["$pay_amount"] = 1
	properties["$pay_method"] = constants.PayChannelTypeIOS
	properties["$pay_reason"] = goods.GoodsName
	properties["$pay_type"] = constants.PayTypeCurrencyCNY
	err = service.SingletonGravityEngineService().ReportPayEvent(ctx, game.GravityAccessToken, clientID, properties)
	if err != nil {
		logger.Logger.Errorf("Error report pay event: %v", err)
		return
	}
}

func TestOrderLogic_WechatMidasCallback(t *testing.T) {
	//type WechatMidasCallbackReq struct {
	//	// query
	//	Signature    string `form:"signature"`
	//	Timestamp    string `form:"timestamp"`
	//	Nonce        string `form:"nonce"`
	//	EncryptType  string `form:"encrypt_type"`
	//	MsgSignature string `form:"msg_signature"`
	//
	//	// body
	//	Encrypt    string `json:"Encrypt"`
	//	ToUserName string `json:"ToUserName"`
	//
	//	GameID string `form:"game_id"`
	//}
	//{"Encrypt":"ubGHN6mrwlEkl7ImZ4nuzwxraDsq36i/h8sgjcMKqV2L83oAiChnW83k8CU8RGrE+2sTCrESH0BwzdtjHO2bX/TyTB/PSPg2GQ7OtVH6ArC/Y5HE0208VAY0NKz2GXTtEu6iy5UxfrDcrAnEFzGRefIo+1dGRMFSPdtdX7BaedmrmMEOlH/tUADsm755+RNosrH157DNu8iuaemkK3DksczDEP3qv+jqnBS/tFTDk92RyMrbof+YkEb9qfHw6huIhcQYyNG4/6cNCw+6H9VnDbiBH27ExlLllsOi1mGr5chCwwZVmXhsSNbj+yIPu7jMFYo3L3yGvBVg1ID4lvlJ/gY+cDq1kRoATJZYrIdVW7mHtLSc+nlWRk5gXtdmgZH5WnJryahh0Ahr9lm/BjYv76NuKjn4l7TbJTccjooBdiV02g9AJC4kbEoJqJf/8oUAg0SMnY0Ws6TNExtAw104bgCe6eCVZn7QvgLcaC79/oG1bJuDmgmMA2c2+0bvdOqSVrgNdLYKJFD079f5IWgIvDZsOEhWeUZ5ITR0FVoP+jK5MRTETKMOUpojYnu4zBFTOYepj1zbdUzW4yoaCuekHP0vqVHfxWPC1QzJ4xo5Vqvj9zgViM1hZfK1ymzy+RjZZmM6VlLfFg598LYGa88xxYPhBK7bYGN0jCv0/k3UJ6wEzd4nkXn3TmT0wbZ7/IKTV4gUP/rloyZgLT4vMqoNvFu0KGkJEeMigWRc2/+0JHD+Pdo3zvPYolvvOq6VlqmkMnjWmyeoffxZej6bTPjtsEX7lavxxntncxs0zhYBDpLzxZ4jz03wIDc/fedA1HtP/gkw0FbJF6wPyU/lsGLko+Ho0fi9kM/aAZ04rHlEF3GoYX11fIq2eu0VClmfHMkWzIIs6VRbmFFYx8YjGtew7Qmm6T9DpxGtprokzS174BhuqdCBxjzoX7auazogYBzK2+oj6SQHRHNzFW1wL8TPIA==","ToUserName":"gh_e251cea8d4b1"}
	Init()
	callback, err := SingletonOrderLogic().WechatMidasCallback(context.Background(), &bean.WechatMidasCallbackReq{
		Signature:    "70d0c7f44ed8a87c0ed1cbd8d6fa17be3ee6e8e8",
		Timestamp:    "1717481908",
		Nonce:        "1337776360",
		EncryptType:  "aes",
		MsgSignature: "a320bf609c7e165a57e6e0a1c6e91aff10246409",
		Encrypt:      "l/xHSRsMYKXUEdNXeaXY+qi/cdLQp7QKkS9ANlYr5tE+meJFy4apJheiuHJUD7thYYQaSMwTRaavFXYIQaHKSyuYQDPYXSM4nY5fHnJDEW57lOyhtvMz4lTa4C/n/Et8ZS70xubqpA12tnj0E45HIf/oUOpyzFM9/AmFNp0HhhR2Q+1DkARC0oTn8pyMDdAEJNBg0oHlRL/54vWPiGyF9FvWa0cr8I7fquhwOMGCUkinrtX7fMdqwSouP20g+1n3uTXlQOwppzq8wekYN1KzhgupcSxZjCYj/lL8ybn9x565Ufq19BPw99sElMOnyvnGsJBv5ud2Dalw3B3d/hsp3x1pVU09S/HgxdOq61va0+CSP9VgYf0ZAa+mGDtTWmcFIXFNLT5WUpQrPYQWi/G178P0goITlq+eNRHnVXK2bXMK/z7xpCwu0bmc7w8Eh4ey97XQmn9NkK9nKHXrDdQKS3qDoQEq1I/eOiujb/yr7ewz5IBOhs13wni3ZS148TsxGo/2uIArqU7BD6v9MgzWZNpCPjq3KclTFpUSB2fFp6YJ7+OPY1OnWAG6Nmhgg4iPOiA97yYAvnBliuyo4pj4uIDvnlAy1Yi/kn7u7a5O+f5Dpd2d2l2Sbh8OG/cTiQN6IAW4X7kQhNkPTp631fjUDCGxd41Jsukl9OKBa2UJzx6KhMB2fn9Q0eH4q9wqetBRwELAvsM8nrWbpX1TIijMLBUEquX1z8SWOy+otYRYnT5IMk1vAMGGcIMC0amf2SXJjSaRoQj944//rq64GSJFsYcsVOMgpq8GvEJjMw4gu46o6L6HNI/kZ8uPla/OKJTOGS/Zw5z5Td1f6M0lB8cR+KU2PnSFKBIB4l2kazS1qlRdN8uDARBC3gH78nFICqO8PCPHvBSYf2me1Y7fkmik6S3EYSYA/tHOZqKRYS/8wd9npvXKpKkVIDySMgqpif1Ev9O2rgs27Rl3d4CzFTInQQ==",
		ToUserName:   "gh_e251cea8d4b1",
		GameID:       "ppx",
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(callback)
}

// WechatCustomerCallback
func TestOrderLogic_WechatCustomerCallback(t *testing.T) {
	Init()

	callback, err := SingletonOrderLogic().WechatCustomerCallback(context.Background(), &bean.WechatCustomerCallbackReq{
		MsgType: "text",
		// PagePath:    "index?bkx_order_id=123456",
		Content:      "666我在测试",
		SessionFrom:  "index.html?platformReplyType=111",
		ToUserName:   "gh_f69e440e6dd1",
		FromUserName: "oWlEB7YGRwllpQGStkqjR1JapvpI",
		GameID:       "mygame",
	})
	if err != nil {
		return
	}

	fmt.Println(callback)
}

// DouyinPayCallback
func TestOrderLogic_DouyinPayCallback(t *testing.T) {
	Init()

	callback, err := SingletonOrderLogic().DouyinPayCallback(context.Background(), &bean.DouyinPayCallbackReq{
		Signature: "704bf9fb00bf86253b4b0f7c43502e9af6dd4ac0",
		Timestamp: "1721121199",
		Msg:       "{\"appid\":\"tt59cd2f70c6fb7dfe02\",\"cp_orderno\":\"d673ded38e384c36a8e474fcab9af094\",\"cp_extra\":\"\",\"amount_cent\":100,\"amount_coin\":10,\"order_no_channel\":\"7392154539031548968\"}",
		Nonce:     "5995",
		EchoStr:   "YLTjqyh",
		GameID:    "test_douyin",
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(callback)
}

func TestOrderService_SubmitTask(t *testing.T) {
	Init()

	productOrder := new(bean.ProductShipmentOrder)
	err := copier.Copy(productOrder, model.AOrder{
		ID:                      0,
		UserID:                  "c32aba2d-ff86-4087-a5cb-0f2f391ec8a3",
		OrderID:                 "a71ac27da197446d9b545907e6e79f80",
		GameID:                  "ghsnk",
		GoodsID:                 "Pay_Diamond_1",
		PayType:                 2,
		Money:                   1,
		CurrencyPrice:           1,
		PlatformType:            1,
		Status:                  3,
		Extra:                   "{\"orderId\":\"975639643817283584_2\",\"rid\":\"17_2\"}",
		PayerOpenID:             "",
		GameCurrency:            0,
		PrepayID:                "wx2316103688855114e706b47adcdbb80001",
		ThirdPartyTransactionID: "",
		TransactionsInfo:        "{\"appId\":\"wxc2f465aa5321842a\",\"timeStamp\":\"1721722237\",\"nonceStr\":\"jK34YjCedk1A3mr5WDBsvCeJAojcexdo\",\"package\":\"prepay_id=wx2316103688855114e706b47adcdbb80001\",\"signType\":\"RSA\",\"paySign\":\"PCy68Jq2XL+In9UCnzp6l+muojM3mTbuVO0urO2ZdvD3XBSR7IwOi09bt1+NS1QakchkMKsaPsjZG14POjhiKByrIB6H5zity4k+nkCNcf7PBIzVwPe0xoDy6ahECX5w3BUXBb9rnmbAZxW+wSC9Qm79/3HmU/03XS+C6vmPYHmleJPROU2wb6TXcpYRqQcmKvUbr9BmwEnNuKoeURWsLHsIqfLJ97xIeE022ZqGzxCJd0CsIK0FOOv4y/BC2cV78PyfkNlSbX+/Yg0ygU9FveybV7LKt+1aDYXr3siRpWPDx2C3i2G8X0jIRKdkCiH194VU+wSgYbbuKQpl2XdhFQ==\",\"prepay_id\":\"wx2316103688855114e706b47adcdbb80001\"}",
		CallbackOriginData:      "{\"mchid\":\"**********\",\"appid\":\"wxc2f465aa5321842a\",\"out_trade_no\":\"a71ac27da197446d9b545907e6e79f80\",\"transaction_id\":\"4200002348202407232679829933\",\"trade_type\":\"JSAPI\",\"trade_state\":\"SUCCESS\",\"trade_state_desc\":\"支付成功\",\"bank_type\":\"OTHERS\",\"attach\":\"\",\"success_time\":\"2024-07-23T16:10:40+08:00\",\"payer\":{\"openid\":\"oqpjh6lifMXf63hfVydKYi5Mjb-E\"},\"amount\":{\"total\":1,\"payer_total\":1,\"currency\":\"CNY\",\"payer_currency\":\"CNY\"}}",
		SaveAmt:                 0,
		CreatedAt:               *************,
		UpdatedAt:               *************,
	})
	if err != nil {
		t.Error(err)
		return
	}

	orderReq := &bean.OrderReq{
		Attempt:     0,
		Order:       productOrder,
		CallbackURL: "https://wwww.baidu.com",
	}
	orderByte, err := json.Marshal(orderReq)
	if err != nil {
		t.Error(err)
		return
	}
	_, err = task.Submit(asynq.NewTask(task.TypeProductShipmentOrder, orderByte))
	if err != nil {
		t.Error(err)
		return
	}
}

func TestOrderLogicWechatCustomerCallback(t *testing.T) {
	os.Setenv("BKX_CONFIG_PATH", "../../configs")
	Init()

	ctx := context.Background()
	//svcCtx := svc.NewServiceContext(nil)
	logic := SingletonOrderLogic() //NewOrderLogic(svcCtx)

	// 测试用例 ：iOS H5支付
	// req := &bean.WechatCustomerCallbackReq{
	// 	GameID:       "mygame",
	// 	MsgType:      "event",
	// 	PagePath:     "",
	// 	FromUserName: "oWlEB7YGRwllpQGStkqjR1JapvpI",
	// 	ToUserName:   "gh_1a5e5bdbe55c",
	// 	CreateTime:   1725002202,
	// 	Content:      "游戏卡顿",
	// 	MsgID:        24696095214143403,
	// 	SessionFrom:  "",
	// 	PicUrl:       "",
	// 	MediaID:      "",
	// 	Title:        "我要充值68.00元",
	// 	AppID:        "wx03b0a11bdfcb33d7",
	// 	ThumbUrl:     "",
	// 	ThumbMediaID: "XJQQ9U77r70ZSrD8U4i6BCm2NQN53AnVS4PukQgz-rE1jpybEayRXNCQ_Ta0Fkbp",
	// 	Event:        "user_enter_tempsession",
	// }
	// resp, err := logic.WechatCustomerCallback(ctx, req)
	// if err != nil {
	// 	t.Error(err)
	// 	return
	// }
	// fmt.Println(resp)
	// 测试用例 ：iOS H5支付
	req := &bean.WechatCustomerCallbackReq{
		GameID:       "mygame",
		MsgType:      "event",
		PagePath:     "",
		FromUserName: "oWlEB7YGRwllpQGStkqjR1JapvpI",
		ToUserName:   "gh_1a5e5bdbe55c",
		CreateTime:   1725002202,
		Content:      "转发到工单卡片",
		MsgID:        24696095214143403,
		SessionFrom:  `{"player_id":"123","player_level":20,"custom_data":{"roleId":"aaa"}}`,
		PicUrl:       "",
		MediaID:      "",
		Title:        "我要充值68.00元",
		AppID:        "wx03b0a11bdfcb33d7",
		ThumbUrl:     "",
		ThumbMediaID: "XJQQ9U77r70ZSrD8U4i6BCm2NQN53AnVS4PukQgz-rE1jpybEayRXNCQ_Ta0Fkbp",
		Event:        "user_enter_tempsession",
	}
	resp, err := logic.WechatCustomerCallback(ctx, req)
	if err != nil {
		t.Error(err)
		return
	}
	fmt.Println(resp)
}

// 测试添加参数
func TestOrderLogic_handleLinkAddParams(t *testing.T) {
	Init()

	logic := SingletonOrderLogic()

	link := "<h1><a href=\"https://wxaurl.cn/HjR0cIS2arg?cq=\">点击这里</a></h1><br/>"
	params := map[string]string{
		"game_id":   "12345",
		"game_name": "SuperAdventure",
	}
	newLink := logic.handleLinkAddParams(link, params)
	fmt.Println(newLink)
}
