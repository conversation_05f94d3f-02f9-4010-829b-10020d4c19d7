package logic

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"gorm.io/gorm"
)

var (
	_captchaOnce  sync.Once
	_captchaLogic *CaptchaLogic
)

type CaptchaLogic struct {
	captchaService *service.CaptchaService
}

func SingletonCaptchaLogic() *CaptchaLogic {
	_captchaOnce.Do(func() {
		_captchaLogic = &CaptchaLogic{
			captchaService: service.SingletonCaptchaService(),
		}
	})
	return _captchaLogic
}

// GetCaptchaConfig 获取验证码配置
func (l *CaptchaLogic) GetCaptchaConfig(ctx context.Context, req *bean.GetCaptchaConfigReq) (*bean.GetCaptchaConfigResp, error) {
	logger.Logger.InfofCtx(ctx, "GetCaptchaConfig req: %+v", req)
	// 从数据库中获取游戏配置以获取secret
	game := store.QueryDB().MGame
	gameInfo, err := game.WithContext(ctx).Where(
		game.GameID.Eq(req.GameID),
		game.IsDeleted.Is(false),
	).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, constants.ErrGameIDNotExist
	} else if err != nil {
		return nil, err
	}

	// 验证签名
	if !l.verifySign(req.ExtraData, req.GameID, gameInfo.Secret, req.Timestamp, req.Sign) {
		logger.Logger.WarnfCtx(ctx, "verifySign failed, game_id: %s, extra_data: %s, timestamp: %d, sign: %s", req.GameID, req.ExtraData, req.Timestamp, req.Sign)

		return &bean.GetCaptchaConfigResp{}, nil
	}

	// 从数据库中获取验证码配置
	captchaConfig := store.QueryDB().MCaptchaConfig
	config, err := captchaConfig.WithContext(ctx).Where(
		captchaConfig.GameID.Eq(req.GameID),
		captchaConfig.IsDeleted.Is(false),
	).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		// return nil, err
		return nil, constants.ErrCaptchaConfigNotFound
	} else if err != nil {
		return nil, err
	}

	// 生成签名
	sign := l.generateCaptchaConfigSign(req.ExtraData, req.GameID, gameInfo.Secret, req.Timestamp)

	resp := &bean.GetCaptchaConfigResp{
		GameID:   config.GameID,
		Provider: config.Provider,
		// Sign:     sign,
		// Timestamp: now,
		ExtraDataMd5: sign, // 加密后的extra_data
	}
	// 根据提供商类型设置不同的验证码ID
	switch config.Provider {
	case 1: // 腾讯云
		captchaAppID := strconv.Itoa(int(config.CaptchaAppID))
		resp.CaptchaID = captchaAppID
	case 2: // 网易易盾
		resp.CaptchaID = config.CaptchaID
	default:
		return nil, errors.New("invalid provider")
	}
	resp.Valid = true

	// 上报验证码配置获取信息
	if err := service.SingletonDataReportService().ReportCaptchaConfig(
		ctx,
		req.AppID,
		req.GameID,
		req.UserID,
		req.DeviceID,
		req.ExtraData,
		req.Timestamp,
		resp.Valid,
		config.Provider,
		resp.CaptchaID,
	); err != nil {
		logger.Logger.WarnfCtx(ctx, "ReportCaptchaConfig failed: %v", err)
	}

	return resp, nil
}

// VerifyCaptcha 验证码校验
func (l *CaptchaLogic) VerifyCaptcha(ctx context.Context, req *bean.VerifyCaptchaReq) (*bean.VerifyCaptchaResp, error) {
	logger.Logger.InfofCtx(ctx, "VerifyCaptcha req: %+v", req)
	now := time.Now().Unix()

	// 从数据库中获取游戏配置以获取secret
	game := store.QueryDB().MGame
	gameInfo, err := game.WithContext(ctx).Where(
		game.GameID.Eq(req.GameID),
		game.IsDeleted.Is(false),
	).First()
	if err != nil {
		// 上报验证码校验失败结果
		if reportErr := service.SingletonDataReportService().ReportCaptchaResult(
			ctx,
			req.AppID,
			req.GameID,
			req.UserID,
			req.DeviceID,
			req.Ticket,
			req.ExtraData,
			now,
			false,
		); reportErr != nil {
			logger.Logger.WarnfCtx(ctx, "ReportCaptchaResult failed: %v", reportErr)
		}
		return nil, err
	}

	var (
		valid    bool
		provider int32
	)

	// 智能识别验证码提供商：基于ticket前缀识别
	if strings.HasPrefix(req.Ticket, constants.CaptchaNeteaseTicketPrefix) {
		// 网易易盾验证码，直接识别，跳过数据库配置查询
		provider = constants.CaptchaProviderNetease
		logger.Logger.InfofCtx(ctx, "VerifyCaptcha detected Netease captcha by ticket prefix, game_id: %s", req.GameID)
	} else if strings.HasPrefix(req.Ticket, constants.CaptchaTencentTicketPrefix) {
		provider = constants.CaptchaProviderTencent
		logger.Logger.InfofCtx(ctx, "VerifyCaptcha detected Tencent captcha by ticket prefix, game_id: %s", req.GameID)
	} else {
		// 其他情况，使用数据库配置查询
		captchaConfig := store.QueryDB().MCaptchaConfig
		config, err := captchaConfig.WithContext(ctx).Where(
			captchaConfig.GameID.Eq(req.GameID),
			captchaConfig.IsDeleted.Is(false),
		).First()
		if err != nil {
			// 上报验证码校验失败结果
			if reportErr := service.SingletonDataReportService().ReportCaptchaResult(
				ctx,
				req.AppID,
				req.GameID,
				req.UserID,
				req.DeviceID,
				req.Ticket,
				req.ExtraData,
				now,
				false,
			); reportErr != nil {
				logger.Logger.WarnfCtx(ctx, "ReportCaptchaResult failed: %v", reportErr)
			}
			return nil, err
		}
		provider = config.Provider
		logger.Logger.InfofCtx(ctx, "VerifyCaptcha using database config provider: %d, game_id: %s", provider, req.GameID)
	}

	// 根据提供商类型调用对应的验证方法
	switch provider {
	case constants.CaptchaProviderTencent: // 腾讯云
		valid, err = l.captchaService.VerifyTencentCaptcha(ctx, req.GameID, req.Ticket, req.IP)
	case constants.CaptchaProviderNetease: // 网易易盾
		valid, err = l.captchaService.VerifyNeteaseCaptcha(ctx, req.GameID, req.Ticket, req.ExtraData, req.Timestamp)
	default:
		// 上报验证码校验失败结果
		if reportErr := service.SingletonDataReportService().ReportCaptchaResult(
			ctx,
			req.AppID,
			req.GameID,
			req.UserID,
			req.DeviceID,
			req.Ticket,
			req.ExtraData,
			now,
			false,
		); reportErr != nil {
			logger.Logger.WarnfCtx(ctx, "ReportCaptchaResult failed: %v", reportErr)
		}
		return nil, bean.ErrInvalidCaptchaProvider
	}

	if err != nil {
		// 上报验证码校验失败结果
		if reportErr := service.SingletonDataReportService().ReportCaptchaResult(
			ctx,
			req.AppID,
			req.GameID,
			req.UserID,
			req.DeviceID,
			req.Ticket,
			req.ExtraData,
			now,
			false,
		); reportErr != nil {
			logger.Logger.WarnfCtx(ctx, "ReportCaptchaResult failed: %v", reportErr)
		}
		return nil, err
	}

	// 只有验证通过时才校验时间戳
	if valid {
		// 检查时间戳是否在5分钟内
		if abs := time.Now().Unix() - req.Timestamp; abs > 300 { // 临时30秒测试
			logger.Logger.WarnfCtx(ctx, "VerifyCaptcha timestamp out of range 5 minute, game_id: %s", req.GameID)
			// 上报验证码校验失败结果
			if reportErr := service.SingletonDataReportService().ReportCaptchaResult(
				ctx,
				req.AppID,
				req.GameID,
				req.UserID,
				req.DeviceID,
				req.Ticket,
				req.ExtraData,
				now,
				false,
			); reportErr != nil {
				logger.Logger.WarnfCtx(ctx, "ReportCaptchaResult failed: %v", reportErr)
			}
			return &bean.VerifyCaptchaResp{}, nil
		}
	}

	// 生成加密sign
	sign := l.generateSign(ctx, req.ExtraData, req.GameID, gameInfo.Secret, now, valid)

	// 上报验证码校验结果
	if err := service.SingletonDataReportService().ReportCaptchaResult(
		ctx,
		req.AppID,
		req.GameID,
		req.UserID,
		req.DeviceID,
		req.Ticket,
		req.ExtraData,
		now,
		valid,
	); err != nil {
		logger.Logger.WarnfCtx(ctx, "ReportCaptchaResult failed: %v", err)
	}

	logger.Logger.InfofCtx(ctx, "VerifyCaptcha resp: %+v", &bean.VerifyCaptchaResp{
		Valid:     valid,
		GameID:    req.GameID,
		Sign:      sign,
		Timestamp: now,
	})

	return &bean.VerifyCaptchaResp{
		Valid:     valid,
		GameID:    req.GameID,
		Sign:      sign,
		Timestamp: now,
	}, nil
}

// verifySign 验证签名
func (l *CaptchaLogic) verifySign(extra_data, game_id, secret string, timestamp int64, sign string) bool {
	if extra_data == "" || game_id == "" || secret == "" || timestamp == 0 || sign == "" {
		return false
	}

	// 按ASCII顺序拼接参数
	params := []string{
		fmt.Sprintf("extra_data=%s", extra_data),
		fmt.Sprintf("game_id=%s", game_id),
		fmt.Sprintf("secret=%s", secret),
		fmt.Sprintf("timestamp=%d", timestamp),
	}

	// 用&连接参数
	paramsStr := strings.Join(params, "")

	// 计算MD5
	hash := md5.New()
	hash.Write([]byte(paramsStr))
	expectedSign := hex.EncodeToString(hash.Sum(nil))

	// 比对签名
	return expectedSign == sign
}

// generateCaptchaConfigSign 生成验证码配置的签名
func (l *CaptchaLogic) generateCaptchaConfigSign(extra_data, game_id, secret string, timestamp int64) string {
	if extra_data == "" || game_id == "" || secret == "" {
		return ""
	}

	// 按ASCII顺序拼接参数
	signStr := fmt.Sprintf("%s%s%s%d", extra_data, game_id, secret, timestamp)
	h := md5.New()
	h.Write([]byte(signStr))
	return hex.EncodeToString(h.Sum(nil))
}

// generateSign 生成签名
func (l *CaptchaLogic) generateSign(ctx context.Context, extra_data, game_id, secret string, timestamp int64, valid bool) string {
	// 按ASCII顺序拼接参数
	params := []string{
		fmt.Sprintf("extra_data=%s", extra_data),
		fmt.Sprintf("game_id=%s", game_id),
		fmt.Sprintf("secret=%s", secret),
		fmt.Sprintf("timestamp=%d", timestamp),
		fmt.Sprintf("valid=%t", valid),
	}

	// 拼接参数
	paramsStr := strings.Join(params, "")

	logger.Logger.InfofCtx(ctx, "generateSign paramsStr: %s", paramsStr)

	// 计算MD5
	hash := md5.New()
	hash.Write([]byte(paramsStr))
	return hex.EncodeToString(hash.Sum(nil))
}
