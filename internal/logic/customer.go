package logic

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/task"
	"github.com/hibiken/asynq"
)

var (
	_customerOnce  sync.Once
	_customerLogic *CustomerLogic
)

type CustomerLogic struct {
	userService     *service.UserService
	customerService *service.CustomerService
	messageService  *service.MessageService
	minigameService *service.MinigameService
}

func SingletonCustomerLogic() *CustomerLogic {
	_customerOnce.Do(func() {
		_customerLogic = &CustomerLogic{
			userService:     service.SingletonUserService(),
			customerService: service.SingletonCustomerService(),
			messageService:  service.SingletonMessageService(),
			minigameService: service.SingletonMinigameService(),
		}
	})
	return _customerLogic
}

// PushMinigameMessage 推送小游戏消息到微信
func (l *CustomerLogic) PushMinigameMessage(ctx context.Context, req *bean.PushMinigameMessageReq) (*bean.PushMinigameMessageResp, error) {
	if len(req.UserIDs) > constants.PushMessageMaxUser {
		return nil, constants.ErrPushMsgTooManyUser
	}

	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return nil, err
	}

	var successCount int32
	for _, userID := range req.UserIDs {
		if err = l.processSingleUser(ctx, conf, userID, req); err != nil {
			return nil, err
		}
		successCount++
	}
	return &bean.PushMinigameMessageResp{SuccessCount: successCount}, nil
}

func (l *CustomerLogic) processSingleUser(ctx context.Context, conf *model.AConfigMinigame, userID string, req *bean.PushMinigameMessageReq) error {
	info, err := l.userService.GetMinigameModel(ctx, userID)
	if err != nil {
		return err
	}
	if req.ImgUrl != "" {
		mediaID, err := l.customerService.FetchMediaID(ctx, conf.AccessToken, req.GameID, req.ImgUrl)
		if err != nil {
			return err
		}
		req.ImgUrl = mediaID
	}

	data, err := l.formatReplyForWechat(req)
	if err != nil {
		return err
	}
	taskReq := bean.CustomerMessageTaskReq{
		AccessToken: conf.AccessToken,
		OpenID:      info.OpenID,
		MsgType:     req.MsgType,
		Data:        data,
	}
	if err := l.submitTask(ctx, req, taskReq); err != nil {
		return err
	}
	return nil
}

func (l *CustomerLogic) formatReplyForWechat(req *bean.PushMinigameMessageReq) (map[string]any, error) {
	switch req.MsgType {
	case constants.WechatMsgTypeText:
		return map[string]any{
			constants.WechatMsgTypeText: map[string]string{"content": req.Content},
		}, nil
	case constants.WechatMsgTypeImage:
		return map[string]any{
			constants.WechatMsgTypeImage: map[string]string{"media_id": req.ImgUrl},
		}, nil
	case constants.WechatMsgTypeMiniprogram:
		return map[string]any{
			constants.WechatMsgTypeMiniprogram: map[string]string{
				"title":          req.Title,
				"pagepath":       req.PagePath,
				"thumb_media_id": req.ImgUrl,
			},
		}, nil
	case constants.WechatMsgTypeLink:
		return map[string]any{
			constants.WechatMsgTypeLink: map[string]string{
				"title":       req.Title,
				"description": req.LinkDesc,
				"url":         req.LinkUrl,
				"thumb_url":   req.LinkThumbURL,
			},
		}, nil
	default:
		return nil, errors.New("equipmentData message type error")
	}
}

func (l *CustomerLogic) submitTask(_ context.Context, req *bean.PushMinigameMessageReq, taskReq bean.CustomerMessageTaskReq) error {
	taskReqByte, err := json.Marshal(taskReq)
	if err != nil {
		return err
	}
	switch req.PushType {
	case constants.MsgSubscribePushType:
		_, err = task.Submit(asynq.NewTask(task.TypeCustomerMessage, taskReqByte))
	case constants.MsgSubscribeDelayedPushType:
		_, err = task.SubmitByDelay(asynq.NewTask(task.TypeCustomerMessage, taskReqByte), time.Duration(req.Delay)*time.Second)
	}
	if err != nil {
		logger.Logger.Errorf("CustomerLogic Task submission error: %s", err.Error())
		return err
	}
	return nil
}

// DouyinCustomerServiceCallback 处理抖音客服推送回调
func (l *CustomerLogic) DouyinCustomerServiceCallback(ctx context.Context, req *bean.DouyinCustomerServiceCallbackReq) (*bean.DouyinCustomerServiceCallbackResp, error) {
	logger.Logger.InfofCtx(ctx, "[DouyinCustomerServiceCallback] 收到抖音客服推送回调，GameID: %s, UserID: %s, Event: %s, MsgType: %s",
		req.GameID, req.UserID, req.Event, req.MsgType)

	// 根据事件类型和消息类型处理不同的逻辑
	switch req.Event {
	case constants.DouyinEventUserEnterTempsession:
		return l.handleDouyinUserEnterSession(ctx, req)
	case constants.DouyinEventKfCreateSession:
		return l.handleDouyinCreateSession(ctx, req)
	case constants.DouyinEventKfCloseSession:
		return l.handleDouyinCloseSession(ctx, req)
	default:
		// 处理普通消息
		return l.handleDouyinMessage(ctx, req)
	}
}

// handleDouyinUserEnterSession 处理用户进入客服会话
func (l *CustomerLogic) handleDouyinUserEnterSession(ctx context.Context, req *bean.DouyinCustomerServiceCallbackReq) (*bean.DouyinCustomerServiceCallbackResp, error) {
	logger.Logger.InfofCtx(ctx, "[handleDouyinUserEnterSession] 用户进入抖音客服会话，GameID: %s, UserID: %s", req.GameID, req.UserID)

	// 可以在这里记录用户进入客服会话的日志或触发欢迎消息
	// TODO: 实现具体的业务逻辑

	return &bean.DouyinCustomerServiceCallbackResp{
		Success: true,
	}, nil
}

// handleDouyinCreateSession 处理创建客服会话
func (l *CustomerLogic) handleDouyinCreateSession(ctx context.Context, req *bean.DouyinCustomerServiceCallbackReq) (*bean.DouyinCustomerServiceCallbackResp, error) {
	logger.Logger.InfofCtx(ctx, "[handleDouyinCreateSession] 创建抖音客服会话，GameID: %s, UserID: %s", req.GameID, req.UserID)

	// TODO: 实现具体的业务逻辑

	return &bean.DouyinCustomerServiceCallbackResp{
		Success: true,
	}, nil
}

// handleDouyinCloseSession 处理关闭客服会话
func (l *CustomerLogic) handleDouyinCloseSession(ctx context.Context, req *bean.DouyinCustomerServiceCallbackReq) (*bean.DouyinCustomerServiceCallbackResp, error) {
	logger.Logger.InfofCtx(ctx, "[handleDouyinCloseSession] 关闭抖音客服会话，GameID: %s, UserID: %s", req.GameID, req.UserID)

	// TODO: 实现具体的业务逻辑

	return &bean.DouyinCustomerServiceCallbackResp{
		Success: true,
	}, nil
}

// handleDouyinMessage 处理抖音客服消息
func (l *CustomerLogic) handleDouyinMessage(ctx context.Context, req *bean.DouyinCustomerServiceCallbackReq) (*bean.DouyinCustomerServiceCallbackResp, error) {
	logger.Logger.InfofCtx(ctx, "[handleDouyinMessage] 处理抖音客服消息，GameID: %s, UserID: %s, MsgType: %s, Content: %s",
		req.GameID, req.UserID, req.MsgType, req.Content)

	// 根据消息类型处理不同的逻辑
	switch req.MsgType {
	case constants.DouyinMsgTypeText:
		return l.handleDouyinTextMessage(ctx, req)
	case constants.DouyinMsgTypeImage:
		return l.handleDouyinImageMessage(ctx, req)
	case constants.DouyinMsgTypeMiniprogram:
		return l.handleDouyinMiniprogramMessage(ctx, req)
	default:
		logger.Logger.WarnfCtx(ctx, "[handleDouyinMessage] 未支持的消息类型: %s", req.MsgType)
	}

	return &bean.DouyinCustomerServiceCallbackResp{
		Success: true,
	}, nil
}

// handleDouyinTextMessage 处理抖音文本消息
func (l *CustomerLogic) handleDouyinTextMessage(ctx context.Context, req *bean.DouyinCustomerServiceCallbackReq) (*bean.DouyinCustomerServiceCallbackResp, error) {
	gameID := req.GameID
	if gameID == "" {
		gameID = req.AppID
	}

	// 解析文本消息内容，根据抖音文档，文本消息的content是JSON格式：{"text":"1","action":{}}
	var textContent string
	if req.Content != "" {
		var contentObj map[string]any
		if err := json.Unmarshal([]byte(req.Content), &contentObj); err == nil {
			if text, ok := contentObj["text"].(string); ok {
				textContent = text
			} else {
				textContent = req.Content // 如果解析失败，使用原始内容
			}
		} else {
			textContent = req.Content // 如果不是JSON格式，使用原始内容
		}
	}

	logger.Logger.InfofCtx(ctx, "[handleDouyinTextMessage] 收到抖音文本消息，GameID: %s, OpenID: %s, Content: %s, TextContent: %s, ConversationID: %d",
		gameID, req.OpenID, req.Content, textContent, req.ConversationID)

	// 参数验证
	if gameID == "" || req.OpenID == "" || textContent == "" {
		logger.Logger.WarnfCtx(ctx, "[handleDouyinTextMessage] 参数不完整，GameID: %s, OpenID: %s, TextContent: %s",
			gameID, req.OpenID, textContent)
		errCode := int32(100002) // 使用抖音官方错误码
		return &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  "invalid parameters",
		}, nil
	}

	// 查询客服消息配置，匹配自动回复
	messages, err := l.customerService.GetCustomerServiceMessages(ctx, gameID, constants.PlatformTypeDouyin, constants.DouyinSceneSend)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[handleDouyinTextMessage] 查询客服消息配置失败: %v", err)
		errCode := int32(100002)
		return &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  "internal error",
		}, err
	}

	// 尝试匹配自动回复
	for _, msg := range messages {
		if l.matchTextMessage(textContent, msg.AcceptText) {
			logger.Logger.InfofCtx(ctx, "[handleDouyinTextMessage] 找到匹配的自动回复配置，ID: %d, ReplyType: %d",
				msg.ID, msg.ReplyType)

			// 发送自动回复
			if err := l.sendDouyinAutoReply(ctx, req, msg); err != nil {
				logger.Logger.ErrorfCtx(ctx, "[handleDouyinTextMessage] 发送自动回复失败: %v", err)
			}
			break
		}
	}

	return &bean.DouyinCustomerServiceCallbackResp{
		Success: true,
	}, nil
}

// handleDouyinImageMessage 处理抖音图片消息
func (l *CustomerLogic) handleDouyinImageMessage(ctx context.Context, req *bean.DouyinCustomerServiceCallbackReq) (*bean.DouyinCustomerServiceCallbackResp, error) {
	logger.Logger.InfofCtx(ctx, "[handleDouyinImageMessage] 收到抖音图片消息，GameID: %s, UserID: %s, PicURL: %s",
		req.GameID, req.UserID, req.Content)

	// 查询图片消息的自动回复配置
	messages, err := l.customerService.GetCustomerServiceMessages(ctx, req.GameID, constants.PlatformTypeDouyin, constants.DouyinSceneSend)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[handleDouyinImageMessage] 查询客服消息配置失败: %v", err)
		errCode := int32(100002)
		return &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  "internal error",
		}, err
	}

	// 对于图片消息，可以配置通用的自动回复
	for _, msg := range messages {
		// 检查是否有针对图片消息的配置（acceptText为空、包含"image"关键词或包含"any"）
		if msg.AcceptText == "" || msg.AcceptText == "[]" ||
			l.matchTextMessage("image", msg.AcceptText) ||
			l.matchTextMessage("any", msg.AcceptText) {
			logger.Logger.InfofCtx(ctx, "[handleDouyinImageMessage] 找到匹配的自动回复配置，ID: %d, ReplyType: %d",
				msg.ID, msg.ReplyType)

			// 发送自动回复
			if err := l.sendDouyinAutoReply(ctx, req, msg); err != nil {
				logger.Logger.ErrorfCtx(ctx, "[handleDouyinImageMessage] 发送自动回复失败: %v", err)
			}
			break
		}
	}

	return &bean.DouyinCustomerServiceCallbackResp{
		Success: true,
	}, nil
}

// handleDouyinMiniprogramMessage 处理抖音小程序消息
func (l *CustomerLogic) handleDouyinMiniprogramMessage(ctx context.Context, req *bean.DouyinCustomerServiceCallbackReq) (*bean.DouyinCustomerServiceCallbackResp, error) {
	logger.Logger.InfofCtx(ctx, "[handleDouyinMiniprogramMessage] 收到抖音小程序消息，GameID: %s, UserID: %s, Body: %+v",
		req.GameID, req.UserID, req.Body)

	// 查询小程序消息的自动回复配置
	messages, err := l.customerService.GetCustomerServiceMessages(ctx, req.GameID, constants.PlatformTypeDouyin, constants.DouyinSceneSendCard)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[handleDouyinMiniprogramMessage] 查询客服消息配置失败: %v", err)
		errCode := int32(100002)
		return &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  "internal error",
		}, err
	}

	// 对于小程序消息，可以配置通用的自动回复
	for _, msg := range messages {
		// 检查是否有针对小程序消息的配置（acceptText为空、包含"miniprogram"关键词或包含"any"）
		if l.matchTextMessage("any", msg.AcceptText) { // msg.AcceptText == "" || msg.AcceptText == "[]" ||
			logger.Logger.InfofCtx(ctx, "[handleDouyinMiniprogramMessage] 找到匹配的自动回复配置，ID: %d, ReplyType: %d",
				msg.ID, msg.ReplyType)

			// 发送自动回复
			if err := l.sendDouyinAutoReply(ctx, req, msg); err != nil {
				logger.Logger.ErrorfCtx(ctx, "[handleDouyinMiniprogramMessage] 发送自动回复失败: %v", err)
			}
			break
		}
	}

	return &bean.DouyinCustomerServiceCallbackResp{
		Success: true,
	}, nil
}

// matchTextMessage 匹配文本消息
func (l *CustomerLogic) matchTextMessage(content, acceptText string) bool {
	var acceptTexts []string
	if err := json.Unmarshal([]byte(acceptText), &acceptTexts); err != nil {
		return false
	}

	// 检查是否包含 "any"，如果包含则匹配任何消息
	if slices.Contains(acceptTexts, "any") {
		return true
	}

	return slices.Contains(acceptTexts, content)
}

// sendDouyinAutoReply 发送抖音自动回复
func (l *CustomerLogic) sendDouyinAutoReply(ctx context.Context, req *bean.DouyinCustomerServiceCallbackReq, msg *model.MCustomerServiceMessage) error {
	gameID := req.GameID
	if gameID == "" {
		gameID = req.AppID
	}

	logger.Logger.InfofCtx(ctx, "[sendDouyinAutoReply] 发送抖音自动回复，GameID: %s, OpenID: %s, ReplyType: %d",
		gameID, req.OpenID, msg.ReplyType)

	// 获取抖音配置
	douyinService := service.SingletonDouyinService()
	douyinConf, err := douyinService.GetDouyinConf(ctx, gameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[sendDouyinAutoReply] 获取抖音配置失败: %v", err)
		return err
	}

	douyinCustomerService := service.SingletonDouyinCustomerService()

	switch msg.ReplyType {
	case 1: // 文本回复
		return l.sendDouyinTextReply(ctx, req, msg, douyinConf.AccessToken, douyinCustomerService)
	case 2: // 图片回复
		return l.sendDouyinImageReply(ctx, req, msg, douyinConf.AccessToken, douyinCustomerService)
	case 3: // 透传给服务器
		return l.sendDouyinServerCallback(ctx, req, msg)
	case 5: // 链接回复
		return l.sendDouyinLinkReply(ctx, req, msg, douyinConf.AccessToken, douyinCustomerService)
	case 7: // 微信URL链接回复
		return l.sendDouyinWechatUrlLinkReply(ctx, req, msg, douyinCustomerService)
	default:
		logger.Logger.WarnfCtx(ctx, "[sendDouyinAutoReply] 不支持的回复类型: %d", msg.ReplyType)
		return nil
	}
}

// PushDouyinCustomerServiceMessage 推送抖音客服消息
// func (l *CustomerLogic) PushDouyinCustomerServiceMessage(ctx context.Context, req *bean.DouyinPushCustomerServiceMessageReq) (*bean.DouyinPushCustomerServiceMessageResp, error) {
// 	if len(req.UserIDs) > constants.PushMessageMaxUser {
// 		return nil, constants.ErrPushMsgTooManyUser
// 	}

// 	logger.Logger.InfofCtx(ctx, "[PushDouyinCustomerServiceMessage] 推送抖音客服消息，GameID: %s, UserCount: %d, MsgType: %s",
// 		req.GameID, len(req.UserIDs), req.MsgType)

// 	// 1. 获取抖音配置
// 	douyinService := service.SingletonDouyinService()
// 	douyinConf, err := douyinService.GetDouyinConf(ctx, req.GameID)
// 	if err != nil {
// 		logger.Logger.ErrorfCtx(ctx, "[PushDouyinCustomerServiceMessage] 获取抖音配置失败: %v", err)
// 		return nil, err
// 	}

// 	// 2. 格式化消息内容
// 	data, err := l.formatDouyinReplyMessage(req)
// 	if err != nil {
// 		logger.Logger.ErrorfCtx(ctx, "[PushDouyinCustomerServiceMessage] 格式化消息失败: %v", err)
// 		return nil, err
// 	}

// 	var successCount int32
// 	douyinCustomerService := service.SingletonDouyinCustomerService()

// 	// 3. 处理每个用户的消息推送
// 	for _, userID := range req.UserIDs {
// 		if err := l.processSingleDouyinUser(ctx, douyinConf, userID, req, data, douyinCustomerService); err != nil {
// 			logger.Logger.ErrorfCtx(ctx, "[PushDouyinCustomerServiceMessage] 处理用户 %s 失败: %v", userID, err)
// 			continue
// 		}
// 		successCount++
// 	}

// 	return &bean.DouyinPushCustomerServiceMessageResp{
// 		SuccessCount: successCount,
// 	}, nil
// }

// sendDouyinTextReply 发送抖音文本回复
func (l *CustomerLogic) sendDouyinTextReply(ctx context.Context, req *bean.DouyinCustomerServiceCallbackReq, msg *model.MCustomerServiceMessage, accessToken string, douyinCustomerService *service.DouyinCustomerService) error {
	logger.Logger.InfofCtx(ctx, "[sendDouyinTextReply] 发送抖音文本回复，Content: %s", msg.ReplyContent)

	// 构建文本回复请求
	replyReq := douyinCustomerService.BuildTextReplyRequest(
		req.AppID, // TODO 待调试
		req.ConversationID,
		req.MsgID,
		msg.ReplyContent,
		req.AppID,
	)

	return douyinCustomerService.SendTextMessage(ctx, accessToken, replyReq)
}

// sendDouyinImageReply 发送抖音图片回复
func (l *CustomerLogic) sendDouyinImageReply(ctx context.Context, _ *bean.DouyinCustomerServiceCallbackReq, msg *model.MCustomerServiceMessage, _ string, _ *service.DouyinCustomerService) error {
	logger.Logger.InfofCtx(ctx, "[sendDouyinImageReply] 发送抖音图片回复，PicURL: %s", msg.PicURL)

	// 抖音图片回复需要通过专门的图片上传API实现
	// 这里暂时返回成功，实际实现需要调用图片上传和发送API
	logger.Logger.WarnfCtx(ctx, "[sendDouyinImageReply] 图片回复功能暂未实现")
	return nil
}

// sendDouyinLinkReply 发送抖音链接回复
func (l *CustomerLogic) sendDouyinLinkReply(ctx context.Context, req *bean.DouyinCustomerServiceCallbackReq, msg *model.MCustomerServiceMessage, accessToken string, douyinCustomerService *service.DouyinCustomerService) error {
	logger.Logger.InfofCtx(ctx, "[sendDouyinLinkReply] 发送抖音链接回复，Title: %s, Link: %s", msg.Title, msg.Link)

	// 构建链接回复请求
	replyReq := douyinCustomerService.BuildLinkReplyRequest(
		ctx,
		req.AppID,
		req.ConversationID,
		req.MsgID,
		msg.Title,
		// "",
		msg.Link,
		msg.PicURL,
		req.OpenID,
		req.AppID,
	)

	return douyinCustomerService.SendLinkMessage(ctx, accessToken, replyReq)
}

// sendDouyinServerCallback 透传给服务器处理
func (l *CustomerLogic) sendDouyinServerCallback(ctx context.Context, req *bean.DouyinCustomerServiceCallbackReq, msg *model.MCustomerServiceMessage) error {
	logger.Logger.InfofCtx(ctx, "[sendDouyinServerCallback] 透传抖音消息给服务器处理")

	// 构建回调请求体，兼容新旧字段
	callbackReq := &bean.CallbackMsgRequest{
		GameID:      req.GameID,
		UserID:      req.UserID,
		OpenID:      req.OpenID,
		SessionFrom: req.SessionFrom,
		MsgType:     req.MsgType,
		Content:     req.Content,
		Event:       req.Event,
		Body:        req.Body,
	}

	// 如果是新格式的请求，使用新字段
	if req.AppID != "" {
		callbackReq.GameID = req.AppID
	}
	if req.ConversationID != 0 {
		callbackReq.SessionFrom = fmt.Sprintf("%d", req.ConversationID)
	}

	// 调用服务器回调
	return l.customerService.CallbackMsgToServer(ctx, callbackReq.GameID, msg.Link, callbackReq)
}

// sendDouyinWechatUrlLinkReply 发送抖音微信URL链接回复
func (l *CustomerLogic) sendDouyinWechatUrlLinkReply(ctx context.Context, req *bean.DouyinCustomerServiceCallbackReq, msg *model.MCustomerServiceMessage, douyinCustomerService *service.DouyinCustomerService) error {
	logger.Logger.InfofCtx(ctx, "[sendDouyinWechatUrlLinkReply] 发送抖音微信URL链接回复，TargetGameID: %s", msg.ReplyContent)

	// 1. 获取目标游戏的配置
	targetGameID := msg.ReplyContent
	if targetGameID == "" {
		return fmt.Errorf("reply_content中的目标游戏ID不能为空")
	}

	// 2. 构建query参数，包含基础参数和玩家参数
	gameID := req.GameID
	if gameID == "" {
		gameID = req.AppID
	}

	// 构建基础参数
	baseParams := []string{
		"game_id=" + targetGameID, // 使用目标游戏ID
		"source=douyin",           // 添加抖音来源参数
	}

	var queryParams string
	if req.OpenID != "" {
		// 尝试获取玩家参数
		playerReq := struct {
			OpenID string `json:"open_id"`
		}{
			OpenID: req.OpenID,
		}

		playerParams, err := douyinCustomerService.GetPlayerLinkByOpenID(ctx, playerReq)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[sendDouyinWechatUrlLinkReply] 获取玩家信息失败，使用基础参数，OpenID: %s, 错误: %v", req.OpenID, err)
			// 使用基础参数
			queryParams = strings.Join(baseParams, "&")
		} else {
			logger.Logger.InfofCtx(ctx, "[sendDouyinWechatUrlLinkReply] 成功获取玩家信息，OpenID: %s, 参数: %s", req.OpenID, playerParams)
			// 合并基础参数和玩家参数，避免重复的source参数
			allParams := []string{
				"game_id=" + targetGameID,
			}
			// 玩家参数已经包含source=douyin，直接追加
			queryParams = strings.Join(allParams, "&") + "&" + playerParams
		}
	} else {
		logger.Logger.DebugfCtx(ctx, "[sendDouyinWechatUrlLinkReply] OpenID为空，使用基础参数")
		queryParams = strings.Join(baseParams, "&")
	}

	// 打印queryParams
	logger.Logger.InfofCtx(ctx, "[sendDouyinWechatUrlLinkReply] 生成的query参数: %s", queryParams)

	// 3. 构建固定URL链接，将参数追加到固定URL
	baseURL := config.GlobConfig.CustomerService.BaseURL
	var urlLink string
	if queryParams != "" {
		urlLink = baseURL + "?" + queryParams
	} else {
		urlLink = baseURL
	}

	logger.Logger.InfofCtx(ctx, "[sendDouyinWechatUrlLinkReply] 成功构建固定URL链接: %s", urlLink)

	// 4. 构建抖音链接回复请求，将微信URL链接作为内容发送
	// gameID 已在上面定义，这里直接使用

	// 生成消息ID（转换为int64）
	msgIDStr := douyinCustomerService.GenerateUniqueMessageID()
	msgID, _ := strconv.ParseInt(msgIDStr, 10, 64)
	if msgID == 0 {
		msgID = time.Now().UnixNano() / 1000000 // 使用毫秒时间戳作为备用
	}

	replyReq := douyinCustomerService.BuildLinkReplyRequest(
		ctx,
		req.AppID, //
		req.ConversationID,
		msgID,
		"点我联系客服",
		// "",
		urlLink,
		"",         // 缩略图URL为空
		req.OpenID, // 添加OpenID参数
		req.AppID,
	)

	// 5. 获取抖音配置并发送链接消息
	douyinService := service.SingletonDouyinService()
	douyinConf, err := douyinService.GetDouyinConf(ctx, gameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[sendDouyinWechatUrlLinkReply] 获取抖音配置失败: %v", err)
		return err
	}

	return douyinCustomerService.SendLinkMessage(ctx, douyinConf.AccessToken, replyReq)
}

// VerifyDouyinSignature 验证抖音回调签名
func (l *CustomerLogic) VerifyDouyinSignature(ctx context.Context, gameID string, headerMap map[string]string, bodyStr string, requestSignature string) (bool, error) {
	// 获取抖音配置中的token
	douyinService := service.SingletonDouyinService()
	douyinConf, err := douyinService.GetDouyinConf(ctx, gameID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[VerifyDouyinSignature] 获取抖音配置失败: %v", err)
		return false, err
	}

	// 使用专门的客服回调验证Token
	token := douyinConf.CustomerServiceToken
	if token == "" {
		// 如果CustomerServiceToken为空，尝试使用PayToken
		token = douyinConf.PayToken
		if token == "" {
			logger.Logger.WarnfCtx(ctx, "[VerifyDouyinSignature] 客服回调验证Token未配置")
			return false, fmt.Errorf("customer service token not configured")
		}
		logger.Logger.WarnfCtx(ctx, "[VerifyDouyinSignature] CustomerServiceToken未配置，使用PayToken进行签名验证")
	}

	// 生成签名
	signature := generateDouyinSignature(ctx, headerMap, bodyStr, token)

	logger.Logger.DebugfCtx(ctx, "[VerifyDouyinSignature] 生成的签名: %s, 请求签名: %s", signature, requestSignature)

	// 比较签名
	return signature == requestSignature, nil
}

// DouyinGiftDeliveryCallback 抖音游戏站礼包推送处理
func (l *CustomerLogic) DouyinGiftDeliveryCallback(ctx context.Context, req *bean.DouyinGiftDeliveryReq) (*bean.DouyinCustomerServiceCallbackResp, error) {
	logger.Logger.InfofCtx(ctx, "[DouyinGiftDeliveryCallback] 处理抖音游戏站礼包推送，gameID: %s, giftCode: %s, openID: %s",
		req.GameID, req.GiftCode, req.OpenID)

	// 1. 验证请求参数
	if err := l.validateGiftDeliveryRequest(req); err != nil {
		logger.Logger.WarnfCtx(ctx, "[DouyinGiftDeliveryCallback] 礼包推送参数验证失败: %v", err)
		errCode := int32(100001)
		return &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  err.Error(),
		}, nil
	}

	// 2. 检查礼包码是否已处理（幂等性检查）
	if isDuplicate, err := l.checkGiftCodeDuplicate(ctx, req.GiftCode); err != nil {
		logger.Logger.ErrorfCtx(ctx, "[DouyinGiftDeliveryCallback] 检查礼包码重复性失败: %v", err)
		errCode := int32(100002)
		return &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  "internal error",
		}, nil
	} else if isDuplicate {
		logger.Logger.InfofCtx(ctx, "[DouyinGiftDeliveryCallback] 礼包码已处理，跳过: %s", req.GiftCode)
		return &bean.DouyinCustomerServiceCallbackResp{
			Success: true,
		}, nil
	}

	// 3. 标记礼包码已处理
	if err := l.markGiftCodeProcessed(ctx, req.GiftCode); err != nil {
		logger.Logger.ErrorfCtx(ctx, "[DouyinGiftDeliveryCallback] 标记礼包码失败: %v", err)
		errCode := int32(100002)
		return &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  "internal error",
		}, nil
	}

	// 4. 获取游戏配置中的回调地址
	userService := service.SingletonUserService()
	game, err := userService.GetGameInfo(ctx, req.GameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[DouyinGiftDeliveryCallback] 获取游戏配置失败: %v", err)
		errCode := int32(100002)
		return &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  "game not found",
		}, nil
	}

	callbackURL := game.CustomerServiceDouyinCallback
	if callbackURL == "" {
		logger.Logger.WarnfCtx(ctx, "[DouyinGiftDeliveryCallback] 游戏未配置抖音客服回调地址，gameID: %s", req.GameID)
		return &bean.DouyinCustomerServiceCallbackResp{
			Success: true,
		}, nil
	}

	// 5. 提交异步任务进行回调
	if err := l.submitGiftDeliveryTask(ctx, callbackURL, req); err != nil {
		logger.Logger.ErrorfCtx(ctx, "[DouyinGiftDeliveryCallback] 提交礼包推送任务失败: %v", err)
		errCode := int32(100002)
		return &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  "internal error",
		}, nil
	}

	logger.Logger.InfofCtx(ctx, "[DouyinGiftDeliveryCallback] 抖音游戏站礼包推送处理完成，gameID: %s, giftCode: %s",
		req.GameID, req.GiftCode)

	return &bean.DouyinCustomerServiceCallbackResp{
		Success: true,
	}, nil
}

// generateDouyinSignature 生成抖音数据签名
func generateDouyinSignature(_ context.Context, headerMap map[string]string, bodyStr string, token string) string {
	// 1、对 headerMap 中的 key 按字典序从小到大排序
	keyList := make([]string, 0, len(headerMap))
	for key := range headerMap {
		keyList = append(keyList, key)
	}
	sort.Slice(keyList, func(i, j int) bool {
		return keyList[i] < keyList[j]
	})

	// 2、将 key-value 按顺序拼接成字符串，格式为 key1=value1&key2=value2&...
	kvList := make([]string, 0, len(keyList))
	for _, key := range keyList {
		kvList = append(kvList, key+"="+headerMap[key])
	}
	urlParams := strings.Join(kvList, "&")

	// 3、直接拼接(无需连接符)：urlParams + bodyStr + token
	rawData := urlParams + bodyStr + token

	// 4、计算MD5
	md5Result := md5.Sum([]byte(rawData))

	// 5、Base64编码
	signatureStr := base64.StdEncoding.EncodeToString(md5Result[:])

	return signatureStr
}

// validateGiftDeliveryRequest 验证礼包推送请求参数
func (l *CustomerLogic) validateGiftDeliveryRequest(req *bean.DouyinGiftDeliveryReq) error {
	if req.GiftCode == "" {
		return fmt.Errorf("gift_code不能为空")
	}
	if req.OpenID == "" {
		return fmt.Errorf("open_id不能为空")
	}
	if len(req.PropList) == 0 {
		return fmt.Errorf("prop_list不能为空")
	}
	if req.EnvType == "" {
		return fmt.Errorf("env_type不能为空")
	}
	if req.EnvType != "development" && req.EnvType != "production" {
		return fmt.Errorf("env_type必须是development或production")
	}
	for _, prop := range req.PropList {
		if prop.PropID == "" {
			return fmt.Errorf("道具prop_id不能为空")
		}
		if prop.Name == "" {
			return fmt.Errorf("道具name不能为空")
		}
		if prop.Count <= 0 {
			return fmt.Errorf("道具count必须大于0")
		}
	}
	return nil
}

// checkGiftCodeDuplicate 检查礼包码是否已处理（幂等性检查）
func (l *CustomerLogic) checkGiftCodeDuplicate(ctx context.Context, giftCode string) (bool, error) {
	// 使用Redis检查礼包码是否已处理，保留24小时
	key := fmt.Sprintf("douyin_gift_code:%s", giftCode)
	exists, err := redis.Redis().Exists(ctx, key).Result()
	if err != nil {
		return false, err
	}
	return exists > 0, nil
}

// markGiftCodeProcessed 标记礼包码已处理
func (l *CustomerLogic) markGiftCodeProcessed(ctx context.Context, giftCode string) error {
	// 设置Redis标记，24小时过期
	key := fmt.Sprintf("douyin_gift_code:%s", giftCode)
	return redis.Redis().Set(ctx, key, "1", 24*time.Hour).Err()
}

// submitGiftDeliveryTask 提交礼包推送异步任务
func (l *CustomerLogic) submitGiftDeliveryTask(ctx context.Context, callbackURL string, req *bean.DouyinGiftDeliveryReq) error {
	// 在提交任务前就进行 open_id 到 user_id 的转换
	userService := service.SingletonUserService()
	userID, err := userService.GetUserIDByDouyinOpenIDSafe(ctx, req.OpenID)
	if err != nil {
		return fmt.Errorf("通过抖音OpenID获取UserID失败: %w", err)
	}

	if userID == "" {
		return fmt.Errorf("未找到与openID关联的用户")
	}

	// 创建用于回调的数据结构，使用转换后的 user_id
	callbackReq := &bean.DouyinGiftDeliveryCallbackReq{
		GiftID:   req.GiftID,
		GiftCode: req.GiftCode,
		UserID:   userID, // 使用转换后的 user_id
		PropList: req.PropList,
		EnvType:  req.EnvType,
		SendTime: req.SendTime,
		GameID:   req.GameID,
	}

	taskReq := &bean.DouyinGiftDeliveryTaskReq{
		CallbackURL:  callbackURL,
		GameID:       req.GameID,
		CallbackData: callbackReq, // 用于回调的转换后数据
		Attempt:      0,
	}

	taskData, err := json.Marshal(taskReq)
	if err != nil {
		return fmt.Errorf("序列化任务数据失败: %w", err)
	}

	// 使用现有的任务队列机制提交任务
	_, err = task.Submit(asynq.NewTask(task.TypeDouyinGiftDelivery, taskData))
	if err != nil {
		return fmt.Errorf("提交异步任务失败: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[submitGiftDeliveryTask] 礼包推送任务提交成功，gameID: %s, giftCode: %s, openID: %s, userID: %s",
		req.GameID, req.GiftCode, req.OpenID, userID)

	return nil
}
