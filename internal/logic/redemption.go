package logic

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
)

var (
	_redemptionOnce  sync.Once
	_redemptionLogic *RedemptionLogic
)

type RedemptionLogic struct {
	redemptionService *service.RedemptionService
}

func SingletonRedemptionLogic() *RedemptionLogic {
	_redemptionOnce.Do(func() {
		_redemptionLogic = &RedemptionLogic{
			redemptionService: service.SingletonRedemptionService(),
		}
	})
	return _redemptionLogic
}

// GetRedemptionCode 获取兑换码
func (r *RedemptionLogic) GetRedemptionCode(ctx context.Context, req *bean.GetRedemptionCodeReq) (*bean.GetRedemptionCodeResp, error) {
	return r.redemptionService.GetRedemptionCode(ctx, req)
}

func (r *RedemptionLogic) RedemptionCodeCallback(ctx context.Context, req *bean.RedemptionCodeCallbackReq) (*bean.GetRedemptionCodeResp, error) {
	// 检测是否是通兑码
	codeType := constants.RedemptionCodeNormalType
	if isSlogan := r.redemptionService.IsSloganCode(ctx, req); isSlogan {
		codeType = constants.RedemptionCodeSloganType
	}

	return r.redemptionService.RedemptionCodeCallback(ctx, req, codeType)
}
