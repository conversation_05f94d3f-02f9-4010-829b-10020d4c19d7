package logic

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_rtcOnce  sync.Once
	_rtcLogic *RTCLogic
)

type RTCLogic struct {
	rtcService    *service.RTCService
	douyinService *service.DouyinService
}

func SingletonRTCLogic() *RTCLogic {
	_rtcOnce.Do(func() {
		_rtcLogic = &RTCLogic{
			rtcService:    service.SingletonRTCService(),
			douyinService: service.SingletonDouyinService(),
		}
	})
	return _rtcLogic
}

// CreateRoomID
func (l *RTCLogic) CreateRoomID(ctx context.Context, req *bean.CreateRoomIDReq) (*bean.CreateRoomIDResp, error) {
	logger.Logger.InfofCtx(ctx, "CreateRoomID req: %+v", req)

	var accessToken string
	if req.PlatformType == constants.PlatformTypeDouyin {
		conf, err := l.douyinService.GetDouyinConf(ctx, req.GameID)
		if err != nil {
			return nil, err
		}
		accessToken = conf.AccessToken
	}
	return l.rtcService.CreateRoomID(ctx, req.GameID, accessToken, req.PlatformType)
}

// GetRoomID
func (l *RTCLogic) GetRoomID(ctx context.Context, req *bean.GetRoomIDReq) (*bean.GetRoomIDResp, error) {
	return l.rtcService.GetRoomID(ctx, req.GameID, req.PlatformType)
}
