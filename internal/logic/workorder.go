package logic

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"sort"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_workorderOnce  sync.Once
	_workorderLogic *WorkorderLogic
)

type WorkorderLogic struct {
	workorderService *service.WorkorderService
	uploadService    *service.UploadService
	minigameService  *service.MinigameService
	userService      *service.UserService
	douyinService    *service.DouyinService
}

func SingletonWorkorderLogic() *WorkorderLogic {
	_workorderOnce.Do(func() {
		_workorderLogic = &WorkorderLogic{
			workorderService: service.SingletonWorkorderService(),
			uploadService:    service.SingletonUploadService(),
			userService:      service.SingletonUserService(),
			minigameService:  service.SingletonMinigameService(),
			douyinService:    service.SingletonDouyinService(),
		}
	})
	return _workorderLogic
}

// CreateSupportTicket 创建工单
func (l *WorkorderLogic) CreateSupportTicket(ctx context.Context, req *bean.WorkorderCreateSupportTicketReq) error {
	// 设置默认来源为微信
	var source string
	if req.Header.GameID == constants.WorkorderSourceDouyin {
		source = constants.WorkorderSourceDouyin
	} else {
		source = constants.WorkorderSourceWechat
	}

	logger.Logger.InfofCtx(ctx, "[工单系统] 开始创建工单, 游戏ID: %s, Header游戏ID: %s, 用户ID: %s, AppID(UnionID): %s, 来源: %s", req.GameID, req.Header.GameID, req.UserID, req.AppID, source)

	// 根据来源处理用户识别逻辑
	if source == constants.WorkorderSourceDouyin {
		// 抖音用户：通过DouyinOpenID查找用户信息
		logger.Logger.InfofCtx(ctx, "[工单系统] 处理抖音用户工单创建, DouyinOpenID: %s", req.OpenID)

		// 通过抖音OpenID查找对应的UserID
		foundUserID, err := l.userService.GetUserIDByDouyinOpenIDSafe(ctx, req.OpenID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[工单系统] 通过抖音OpenID查找UserID失败: %v, DouyinOpenID: %s", err, req.OpenID)
			// 如果查找失败，使用抖音OpenID作为fallback
			req.UserID = req.OpenID
		} else if foundUserID != "" {
			// 找到了UserID，使用查找到的UserID
			logger.Logger.InfofCtx(ctx, "[工单系统] 通过抖音OpenID查找到UserID: %s, DouyinOpenID: %s", foundUserID, req.OpenID)
			req.UserID = foundUserID
		} else {
			// 未找到用户，使用抖音OpenID作为fallback
			logger.Logger.WarnfCtx(ctx, "[工单系统] 未找到与抖音OpenID关联的用户，使用OpenID作为fallback: %s", req.OpenID)
			req.UserID = req.OpenID
		}
	} else {
		// 微信用户：如果AppID(UnionID)不为空，尝试查找用户绑定信息（GameID是必传参数）
		if req.AppID != "" {
			logger.Logger.InfofCtx(ctx, "[工单系统] UnionID存在，开始查找用户信息, UnionID: %s, GameID: %s", req.AppID, req.GameID)

			// 尝试通过UnionID和GameID获取userID
			foundUserID, err := l.userService.GetUserIDByUnionIDSafe(ctx, req.AppID, req.GameID)
			if err != nil {
				logger.Logger.WarnfCtx(ctx, "[工单系统] 通过UnionID查找UserID失败: %v, UnionID: %s, GameID: %s", err, req.AppID, req.GameID)
				// 继续使用请求中的UserID，不中断流程
			} else if foundUserID != "" {
				// 找到了UserID
				logger.Logger.InfofCtx(ctx, "[工单系统] 查找用户成功, UserID: %s", foundUserID)

				// 更新请求中的UserID
				req.UserID = foundUserID

				// 尝试获取用户的小游戏OpenID
				minigameInfo, err := l.userService.GetMinigameModel(ctx, foundUserID)
				if err != nil {
					logger.Logger.WarnfCtx(ctx, "[工单系统] 获取小游戏信息失败: %v, UserID: %s", err, foundUserID)
					// 继续使用请求中的OpenID，不中断流程
				} else if minigameInfo != nil {
					// 更新请求中的OpenID
					req.OpenID = minigameInfo.OpenID
					logger.Logger.InfofCtx(ctx, "[工单系统] 获取小游戏OpenID成功: %s", minigameInfo.OpenID)
				} else {
					logger.Logger.WarnfCtx(ctx, "[工单系统] 未找到小游戏信息, UserID: %s", foundUserID)
				}
			} else {
				logger.Logger.WarnfCtx(ctx, "[工单系统] 未找到用户, UnionID: %s, GameID: %s", req.AppID, req.GameID)
			}
		}
	}

	// 构建请求参数
	checkReq := &bean.GetOngoingWorkorderStatusReq{
		GameID: req.GameID,
	}
	// Header字段赋值
	checkReq.UserID = req.UserID
	checkReq.DeviceID = req.DeviceID

	// 检查是否已有进行中的工单
	ongoingStatus, err := l.workorderService.GetOngoingWorkorderStatus(ctx, checkReq)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[工单系统] 校验进行中工单失败: %v", err)
		return fmt.Errorf("校验进行中工单失败: %w", err)
	}

	// 如果已有进行中工单，则不允许再创建
	if ongoingStatus != nil && ongoingStatus.HasOngoing {
		logger.Logger.WarnfCtx(ctx, "[工单系统] 用户已有进行中工单, 游戏ID: %s, 用户ID: %s", req.GameID, req.UserID)
		return constants.ErrWorkorderAlreadyOngoing
	}

	// 调用service层创建工单
	_, err = l.workorderService.CreateSupportTicket(ctx, req)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[工单系统] 创建工单失败: %v", err)
		return err
	}
	logger.Logger.InfofCtx(ctx, "[工单系统] 创建工单成功, 游戏ID: %s, 用户ID: %s", req.GameID, req.UserID)
	return nil
}

// GetTicketLogs 获取工单操作日志
func (l *WorkorderLogic) GetTicketLogs(ctx context.Context, req *bean.WorkorderGetTicketLogsReq) (*bean.WorkorderGetTicketLogsResp, error) {
	logger.Logger.InfofCtx(ctx, "[工单系统] 开始获取工单操作日志, 工单ID: %s", req.OrderID)
	logs, err := l.workorderService.GetTicketLogs(ctx, req)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[工单系统] 获取工单操作日志失败: %v", err)
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "[工单系统] 获取工单操作日志成功, 工单ID: %s, 日志数量: %d", req.OrderID, len(logs))
	return &bean.WorkorderGetTicketLogsResp{
		Data: logs,
	}, nil
}

// GetSupportTickets 获取工单列表
func (l *WorkorderLogic) GetSupportTickets(ctx context.Context, req *bean.WorkorderGetSupportTicketsReq) (*bean.WorkorderSupportTicketsData, error) {
	logger.Logger.InfofCtx(ctx, "[工单系统] 开始获取工单列表, 状态: %d", req.Status)
	data, err := l.workorderService.GetSupportTickets(ctx, req)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[工单系统] 获取工单列表失败: %v", err)
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "[工单系统] 获取工单列表成功, 总数: %d", data.Total)
	return data, nil
}

// GetTicketDetail 获取工单详情
func (l *WorkorderLogic) GetTicketDetail(ctx context.Context, req *bean.WorkorderGetTicketDetailReq) (*bean.WorkorderTicketDetailData, error) {
	logger.Logger.InfofCtx(ctx, "[工单系统] 开始获取工单详情, 工单ID: %s", req.OrderID)
	data, err := l.workorderService.GetTicketDetail(ctx, req)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[工单系统] 获取工单详情失败: %v", err)
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "[工单系统] 获取工单详情成功, 工单ID: %s", req.OrderID)
	return data, nil
}

// UploadFile 上传文件
func (l *WorkorderLogic) UploadFile(ctx context.Context, file *multipart.FileHeader) (string, error) {
	logger.Logger.InfofCtx(ctx, "[工单系统] 开始上传文件, 文件名: %s, 大小: %d bytes", file.Filename, file.Size)
	// 使用UploadService上传文件
	fileURL, err := l.uploadService.UploadFile(ctx, file)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[工单系统] 上传文件失败: %v", err)
		return "", err
	}
	logger.Logger.InfofCtx(ctx, "[工单系统] 上传文件成功, 文件URL: %s", fileURL)
	return fileURL, nil
}

// ReplyTicket 回复工单
func (l *WorkorderLogic) ReplyTicket(ctx context.Context, req *bean.WorkorderReplyTicketReq) error {
	logger.Logger.InfofCtx(ctx, "[工单系统] 开始回复工单, 工单ID: %s, GameID: %s, UserID: %s, AppID(UnionID): %s", req.OrderID, req.GameID, req.UserID, req.AppID)

	// 如果AppID(UnionID)不为空，尝试查找用户绑定信息（GameID是必传参数）
	if req.AppID != "" {
		logger.Logger.InfofCtx(ctx, "[工单系统] UnionID存在，开始查找用户信息, UnionID: %s, GameID: %s", req.AppID, req.GameID)

		// 尝试通过UnionID和GameID获取userID
		foundUserID, err := l.userService.GetUserIDByUnionIDSafe(ctx, req.AppID, req.GameID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[工单系统] 通过UnionID查找UserID失败: %v, UnionID: %s, GameID: %s", err, req.AppID, req.GameID)
			// 继续使用请求中的UserID，不中断流程
		} else if foundUserID != "" {
			// 找到了UserID
			logger.Logger.InfofCtx(ctx, "[工单系统] 查找用户成功, UserID: %s", foundUserID)

			// 更新请求中的UserID
			req.UserID = foundUserID

			// 尝试获取用户的小游戏OpenID
			minigameInfo, err := l.userService.GetMinigameModel(ctx, foundUserID)
			if err != nil {
				logger.Logger.WarnfCtx(ctx, "[工单系统] 获取小游戏信息失败: %v, UserID: %s", err, foundUserID)
				// 继续使用请求中的OpenID，不中断流程
			} else if minigameInfo != nil {
				// 更新请求中的OpenID
				req.OpenID = minigameInfo.OpenID
				logger.Logger.InfofCtx(ctx, "[工单系统] 获取小游戏OpenID成功: %s", minigameInfo.OpenID)
			} else {
				logger.Logger.WarnfCtx(ctx, "[工单系统] 未找到小游戏信息, UserID: %s", foundUserID)
			}
		} else {
			logger.Logger.WarnfCtx(ctx, "[工单系统] 未找到用户, UnionID: %s, GameID: %s", req.AppID, req.GameID)
		}
	}

	// 调用service层回复工单
	err := l.workorderService.ReplyTicket(ctx, req)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[工单系统] 回复工单失败: %v", err)
		return err
	}
	logger.Logger.InfofCtx(ctx, "[工单系统] 回复工单成功, 工单ID: %s, UserID: %s", req.OrderID, req.UserID)
	return nil
}

// GetTicketTemplate 获取工单模板
// func (l *WorkorderLogic) GetTicketTemplate(ctx context.Context, req *bean.WorkorderGetTicketTemplateReq) (*bean.WorkorderGetTicketTemplateResp, error) {
// 	// 构建查询条件
// 	query := store.QueryDB().MWorkorderTemplateField
// 	conditions := []gen.Condition{query.IsDeleted.Is(false)}
// 	if req.GameID != "" {
// 		conditions = append(conditions, query.GameID.Eq(req.GameID))
// 	}

// 	// 查询模板字段列表
// 	fields, err := query.WithContext(ctx).Where(conditions...).Order(query.SortOrder).Find()
// 	if err != nil {
// 		return nil, errors.Wrap(err, "查询工单模板字段失败")
// 	}

// 	// 转换为响应结构
// 	resp := &bean.WorkorderGetTicketTemplateResp{
// 		Fields: make([]*bean.WorkorderTemplateField, 0, len(fields)),
// 	}

// 	for _, field := range fields {
// 		resp.Fields = append(resp.Fields, &bean.WorkorderTemplateField{
// 			FieldKey:    field.FieldKey,
// 			DisplayName: field.DisplayName,
// 			FieldType:   field.FieldType,
// 			IsVisible:   field.IsVisible,
// 			Required:    field.Required,
// 			SortOrder:   field.SortOrder,
// 		})
// 	}

// 	return resp, nil
// }

// GetGameList 获取客服中心游戏列表
func (l *WorkorderLogic) GetGameList(ctx context.Context) (*bean.WorkorderGameListResp, error) {
	logger.Logger.InfofCtx(ctx, "[工单系统] 开始获取客服中心游戏列表")
	resp, err := l.workorderService.GetGameList(ctx)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[工单系统] 获取客服中心游戏列表失败: %v", err)
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "[工单系统] 获取客服中心游戏列表成功, 游戏数量: %d", len(resp.List))
	return resp, nil
}

// GetWelcomeContent 获取客服中心欢迎语
func (l *WorkorderLogic) GetWelcomeContent(ctx context.Context, req *bean.WorkorderWelcomeReq) (*bean.WorkorderWelcomeResp, error) {
	gameID := ""
	if req.Header.GameID != "" {
		gameID = req.Header.GameID
	} else if req.GameID != "" {
		gameID = req.GameID
	}

	logger.Logger.InfofCtx(ctx, "[工单系统] 开始获取客服中心欢迎语, GameID: %s", gameID)
	resp, err := l.workorderService.GetWelcomeContent(ctx, gameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[工单系统] 获取客服中心欢迎语失败: %v", err)
		return nil, err
	}
	// logger.Logger.InfofCtx(ctx, "[工单系统] 获取客服中心欢迎语成功, 问题数量: %d", len(resp.QuestionList))
	return resp, nil
}

// GetBusySwitch 获取繁忙提示开关状态
func (l *WorkorderLogic) GetBusySwitch(ctx context.Context, req *bean.WorkorderBusySwitchReq) (*bean.WorkorderBusySwitchResp, error) {
	logger.Logger.InfofCtx(ctx, "[工单系统] 开始获取繁忙提示开关状态")
	resp, err := l.workorderService.GetBusySwitchGlobal(ctx)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[工单系统] 获取繁忙提示开关状态失败: %v", err)
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "[工单系统] 获取繁忙提示开关状态成功, BusySwitch: %v", resp.BusySwitch)
	return resp, nil
}

// GetOngoingWorkorderStatus 获取当前用户是否有正在进行中的工单状态
func (l *WorkorderLogic) GetOngoingWorkorderStatus(ctx context.Context, req *bean.GetOngoingWorkorderStatusReq) (*bean.GetOngoingWorkorderStatusResp, error) {
	logger.Logger.InfofCtx(ctx, "[工单系统] 开始获取用户进行中工单状态, UserID: %s, DeviceID: %s, AppID(UnionID): %s, GameID: %s", req.UserID, req.DeviceID, req.AppID, req.GameID)

	// 如果AppID(UnionID)不为空，尝试查找用户绑定信息（GameID是必传参数）
	if req.AppID != "" {
		logger.Logger.InfofCtx(ctx, "[工单系统] UnionID存在，开始查找用户信息, UnionID: %s, GameID: %s", req.AppID, req.GameID)

		// 尝试通过UnionID和GameID获取userID
		foundUserID, err := l.userService.GetUserIDByUnionIDSafe(ctx, req.AppID, req.GameID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[工单系统] 通过UnionID查找UserID失败: %v, UnionID: %s, GameID: %s", err, req.AppID, req.GameID)
			// 继续使用请求中的UserID，不中断流程
		} else if foundUserID != "" {
			// 找到了UserID
			logger.Logger.InfofCtx(ctx, "[工单系统] 查找用户成功, UserID: %s", foundUserID)

			// 更新请求中的UserID
			req.UserID = foundUserID

			// 尝试获取用户的小游戏OpenID
			minigameInfo, err := l.userService.GetMinigameModel(ctx, foundUserID)
			if err != nil {
				logger.Logger.WarnfCtx(ctx, "[工单系统] 获取小游戏信息失败: %v, UserID: %s", err, foundUserID)
				// 继续使用请求中的OpenID，不中断流程
			} else if minigameInfo != nil {
				// 更新请求中的OpenID
				req.OpenID = minigameInfo.OpenID
				logger.Logger.InfofCtx(ctx, "[工单系统] 获取小游戏OpenID成功: %s", minigameInfo.OpenID)
			} else {
				logger.Logger.WarnfCtx(ctx, "[工单系统] 未找到小游戏信息, UserID: %s", foundUserID)
			}
		} else {
			logger.Logger.WarnfCtx(ctx, "[工单系统] 未找到用户, UnionID: %s, GameID: %s", req.AppID, req.GameID)
		}
	}

	// 调用service层获取工单状态
	resp, err := l.workorderService.GetOngoingWorkorderStatus(ctx, req)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[工单系统] 获取用户进行中工单状态失败: %v", err)
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "[工单系统] 获取用户进行中工单状态成功, UserID: %s, HasOngoing: %v", req.UserID, resp.HasOngoing)
	return resp, nil
}

// QiyuAuthLogin 工单系统登录
// 注意：Source 和 DouyinOpenID 验证逻辑已移至工单创建时进行
func (l *WorkorderLogic) AuthLogin(ctx context.Context, req *bean.QiyuAuthLoginReq) (*bean.QiyuAuthLoginResp, error) {
	logger.Logger.InfofCtx(ctx, "[工单系统] 开始处理登录请求, 游戏ID: %s, Code: %s", req.GameID, req.Code)

	miniprogrampage, err := l.workorderService.GetQiyuMiniGameSession(ctx)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[工单系统] 获取小程序配置失败: %v", err)
		return nil, fmt.Errorf("get mini game session: %w", err)
	}

	// 获取小程序的openID（都使用微信认证，因为都在微信小程序中）
	logger.Logger.InfofCtx(ctx, "[工单系统] 开始获取小程序会话, AppID: %s", miniprogrampage.AppID)
	miniGameSession, err := l.minigameService.GetCode2Session(ctx, miniprogrampage.AppID, miniprogrampage.AppSecret, req.Code)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[工单系统] 获取小程序会话失败: %v", err)
		return nil, fmt.Errorf("get code2session: %w", err)
	}
	sessionJson, _ := json.Marshal(miniGameSession)
	logger.Logger.InfofCtx(ctx, "[工单系统] 成功获取小程序会话, session: %s", string(sessionJson))
	var gameName string
	var userID string
	var minigameOpenID string

	// 简化用户识别逻辑：统一使用微信小程序的逻辑
	// 注意：抖音用户的特殊处理逻辑已移至工单创建时进行
	if miniGameSession.UnionID != "" && req.GameID != "" {
		info, err := l.userService.GetGameInfo(ctx, req.GameID)
		if err != nil {
			return nil, fmt.Errorf("get game info: %w", err)
		}
		gameName = info.Name

		userID, err = l.userService.GetUserIDByUnionIDSafe(ctx, miniGameSession.UnionID, req.GameID)
		if err != nil {
			return nil, fmt.Errorf("get user id by union id: %w", err)
		}
		// 如果查不到用户，直接跳过后续逻辑
		if userID == "" {
			logger.Logger.WarnfCtx(ctx, "[工单系统] 未找到用户, UnionID: %s, GameID: %s", miniGameSession.UnionID, req.GameID)
			// 跳过后续逻辑，直接返回空字符串
			goto GENERATE_TOKEN
		}
		minigameInfo, err := l.userService.GetMinigameModel(ctx, userID)
		if err != nil {
			return nil, fmt.Errorf("get minigame info: %w", err)
		}
		if minigameInfo == nil {
			return nil, fmt.Errorf("minigame info not found")
		}
		minigameOpenID = minigameInfo.OpenID
	}

GENERATE_TOKEN:
	logger.Logger.InfofCtx(ctx, "[工单系统] 开始生成登录令牌, UserID: %s, GameID: %s", userID, req.GameID)
	resp, err := l.workorderService.AuthLogin(ctx, userID, minigameOpenID, miniGameSession.OpenID, req.GameID, gameName, miniGameSession.UnionID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[工单系统] 生成登录令牌失败: %v", err)
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "[工单系统] 登录成功, UserID: %s, GameID: %s", userID, req.GameID)
	return resp, nil
}

// ManualTriggerWorkorderStats 手动触发工单统计邮件发送
func (l *WorkorderLogic) ManualTriggerWorkorderStats(ctx context.Context) (*bean.ManualTriggerWorkorderStatsResp, error) {
	logger.Logger.InfofCtx(ctx, "[ManualTriggerWorkorderStats] 开始手动触发工单统计邮件发送")

	// 创建带有手动触发标记的上下文
	ctx = context.WithValue(ctx, "manual_trigger", true)

	// 获取工单统计服务
	workorderStatsService := service.SingletonWorkorderStatsService()

	// 记录开始时间
	startTime := time.Now()

	// 执行邮件生成和发送
	err := workorderStatsService.GenerateAndSendStatsMail(ctx)

	// 计算执行时间
	duration := time.Since(startTime)

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[ManualTriggerWorkorderStats] 手动触发工单统计邮件任务失败，耗时: %v, 错误: %v", duration, err)
		return nil, err
	}

	logger.Logger.InfofCtx(ctx, "[ManualTriggerWorkorderStats] 手动触发工单统计邮件任务完成，耗时: %v", duration)

	return &bean.ManualTriggerWorkorderStatsResp{
		Success:  true,
		Message:  "工单统计邮件已成功触发",
		Duration: duration.String(),
	}, nil
}

// DouyinAuthLogin 抖音工单认证登录
func (l *WorkorderLogic) DouyinAuthLogin(ctx context.Context, req *bean.DouyinAuthLoginReq) (*bean.DouyinAuthLoginResp, error) {
	// 日志脱敏: 仅打印授权码前缀
	safeCode := req.Code
	if len(safeCode) > 8 {
		safeCode = safeCode[:8]
	}
	logger.Logger.InfofCtx(ctx, "[抖音工单认证] 开始处理抖音授权登录, 授权码前缀: %s", safeCode)

	// 1. 获取抖音客户端配置
	config, err := l.douyinService.GetDouyinClientConfig(ctx)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[抖音工单认证] 获取抖音客户端配置失败: %v", err)
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "[抖音工单认证] 成功获取抖音客户端配置, AppID: %s", config.AppID)

	// 2. 使用授权码换取访问令牌
	tokenResp, err := l.douyinService.ExchangeCodeForAccessToken(ctx, req.Code, config.AppID, config.AppSecret)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[抖音工单认证] 交换访问令牌失败: %v", err)
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "[抖音工单认证] 成功获取访问令牌, OpenID: %s", tokenResp.Data.OpenID)

	// 3. 获取用户公开信息（包含union_id）
	userPublicInfo, err := l.douyinService.GetUserPublicInfo(ctx, tokenResp.Data.AccessToken, tokenResp.Data.OpenID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[抖音工单认证] 获取用户公开信息失败: %v", err)
		// 获取公开信息失败不阻断流程，使用空的userInfo
		userPublicInfo = nil
	} else {
		logger.Logger.InfofCtx(ctx, "[抖音工单认证] 成功获取用户公开信息, UnionID: %s, Nickname: %s", userPublicInfo.UnionID, userPublicInfo.NickName)
	}

	// 4. 存储认证信息到AUserDouyinWeb表（新表结构需要 client_key 参数，这里使用 config.AppID 作为 client_key）
	if err := l.userService.SaveDouyinWebAuthInfo(ctx, config.AppID, tokenResp, userPublicInfo); err != nil {
		logger.Logger.ErrorfCtx(ctx, "[抖音工单认证] 存储认证信息失败: %v", err)
		return nil, fmt.Errorf("存储认证信息失败: %w", err)
	}
	logger.Logger.InfofCtx(ctx, "[抖音工单认证] 成功存储认证信息到AUserDouyinWeb表")

	// 5. 通过union_id查找已有用户（简化逻辑：如果没有unionID，则不进行用户查找）
	var userID string
	var douyinUserInfo *model.AUserDouyin
	var unionID string

	if userPublicInfo != nil && userPublicInfo.UnionID != "" {
		unionID = userPublicInfo.UnionID
		douyinUserInfo, err = l.userService.GetUserInfoByDouyinUnionID(ctx, unionID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[抖音工单认证] 通过UnionID查找用户失败: %v, UnionID: %s", err, unionID)
			// 查找失败不阻断流程
		} else if douyinUserInfo != nil {
			userID = douyinUserInfo.UserID
			logger.Logger.InfofCtx(ctx, "[抖音工单认证] 通过UnionID找到已有用户, UserID: %s, UnionID: %s", userID, unionID)
		} else {
			logger.Logger.InfofCtx(ctx, "[抖音工单认证] 未找到与UnionID关联的用户, UnionID: %s", unionID)
		}
	} else {
		logger.Logger.InfofCtx(ctx, "[抖音工单认证] 未获取到UnionID，将作为新用户处理")
	}

	// 避免空指针: 仅在存在记录时读取 open_id
	var openIDFromTable string
	if douyinUserInfo != nil {
		openIDFromTable = douyinUserInfo.OpenID
	}

	authResp, err := l.workorderService.AuthLogin(
		ctx,
		userID,                // 用户ID（新用户可能为空）
		openIDFromTable,       // OpenID参数（来自a_user_douyin表的open_id，如无则为空）
		tokenResp.Data.OpenID, // Web OpenID（deviceID位置传入）
		"douyin",              // GameID（工单系统中可能不需要特定游戏ID）
		"",                    // 预留字段
		unionID,               // UnionID参数
	)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[抖音工单认证] 生成JWT令牌失败: %v", err)
		return nil, fmt.Errorf("生成JWT令牌失败: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[抖音工单认证] 认证成功, UserID: %s, OpenID: %s, UnionID: %s", userID, tokenResp.Data.OpenID, unionID)

	return &bean.DouyinAuthLoginResp{
		Token: authResp.Token,
	}, nil
}

// DouyinAuthSign 抖音JS授权签名
func (l *WorkorderLogic) DouyinAuthSign(ctx context.Context, req *bean.DouyinAuthSignReq) (*bean.DouyinAuthSignResp, error) {
	logger.Logger.InfofCtx(ctx, "[抖音JS授权签名] 开始生成签名, URL: %s, nonce_str: %s, timestamp: %s", req.URL, req.NonceStr, req.Timestamp)

	// 1. 获取抖音客户端配置
	config, err := l.douyinService.GetDouyinClientConfig(ctx)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[抖音JS授权签名] 获取抖音客户端配置失败: %v", err)
		return nil, fmt.Errorf("获取抖音客户端配置失败: %w", err)
	}

	// 校验 ticket 是否可用
	if strings.TrimSpace(config.Ticket) == "" {
		logger.Logger.WarnfCtx(ctx, "[抖音JS授权签名] ticket 为空，请稍后重试或检查定时刷新任务")
		return nil, constants.ErrConfigError
	}

	// 规范化 URL，剔除 # 及其后内容
	sanitizedURL := req.URL
	if idx := strings.Index(sanitizedURL, "#"); idx >= 0 {
		sanitizedURL = sanitizedURL[:idx]
	}

	// 2. 生成签名（按照抖音官方文档规范）
	signature := l.generateDouyinJSSignature(req.NonceStr, config.Ticket, req.Timestamp, sanitizedURL)

	logger.Logger.InfofCtx(ctx, "[抖音JS授权签名] 签名生成成功, signature: %s", signature)

	return &bean.DouyinAuthSignResp{
		Signature: signature,
	}, nil
}

// generateDouyinJSSignature 生成抖音JS SDK签名
// 按照抖音官方文档规范：https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/sdk/web-app/js/signature
func (l *WorkorderLogic) generateDouyinJSSignature(nonceStr, jsapiTicket, timestamp, url string) string {
	// 参与签名的字段：nonce_str, jsapi_ticket, timestamp, url
	params := map[string]string{
		"nonce_str":    nonceStr,
		"jsapi_ticket": jsapiTicket,
		"timestamp":    timestamp,
		"url":          url,
	}

	// 1. 对参数按字段名的ASCII码从小到大排序（字典序）
	var keys []string
	for key := range params {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	// 2. 使用URL键值对格式拼接成字符串
	var parts []string
	for _, key := range keys {
		parts = append(parts, key+"="+params[key])
	}
	rawString := strings.Join(parts, "&")

	// 3. 对拼接字符串进行MD5签名
	md5Hash := md5.Sum([]byte(rawString))
	signature := fmt.Sprintf("%x", md5Hash)

	return signature
}
