package logic

import (
	"context"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	voipLogicOnce sync.Once
	voipLogic     *VoipLogic
)

type VoipLogic struct {
	voipService        *service.VoipService
	minigameService    *service.MinigameService
	userService        *service.UserService
	douyinService      *service.DouyinService
	douyinTokenService *service.DouyinTokenService
}

func SingletonVoipLogic() *VoipLogic {
	voipLogicOnce.Do(func() {
		voipLogic = &VoipLogic{
			voipService:        service.SingletonVoipService(),
			minigameService:    service.SingletonMinigameService(),
			userService:        service.SingletonUserService(),
			douyinService:      service.SingletonDouyinService(),
			douyinTokenService: service.SingletonDouyinTokenService(),
		}
	})
	return voipLogic
}

func (l *VoipLogic) JoinRoom(ctx context.Context, req *bean.JoinRoomReq) (*bean.JoinRoomResp, error) {
	logger.Logger.Infof("JoinRoom start, req: %+v", req)
	handler, ok := l.getPlatformHandler(req.PlatformType)
	if !ok {
		logger.Logger.Errorf("Platform type not supported: %s", req.PlatformType)
		return nil, constants.ErrPlatformTypeNotSupport
	}

	resp, err := handler(ctx, req)
	if err != nil {
		logger.Logger.Errorf("Handler error: %v", err)
		return nil, err
	}

	// exist, err := l.voipService.IsVoipRoomExist(ctx, req.GameID, resp.GroupID, req.UserID)
	// if err != nil {
	// 	logger.Logger.Errorf("IsVoipRoomExist error: %v", err)
	// 	return nil, err
	// }
	// if exist {
	// 	logger.Logger.Errorf("Room already exists for gameID: %s, groupID: %s, userID: %s", req.GameID, resp.GroupID, req.UserID)
	// 	return nil, constants.ErrRoomAlreadyExist
	// }

	if err := l.voipService.JoinRoom(ctx, req.GameID, resp.GroupID, req.PlatformType, req.UserID); err != nil {
		logger.Logger.Errorf("JoinRoom error: %v", err)
		return nil, err
	}

	logger.Logger.Infof("JoinRoom success, resp: %+v", resp)
	return resp, nil
}

func (l *VoipLogic) getPlatformHandler(platformType string) (func(context.Context, *bean.JoinRoomReq) (*bean.JoinRoomResp, error), bool) {
	handlers := map[string]func(context.Context, *bean.JoinRoomReq) (*bean.JoinRoomResp, error){
		constants.PlatformTypeMinigame: l.handleMinigame,
		constants.PlatformTypeDouyin:   l.handleDouyin,
	}
	handler, ok := handlers[platformType]
	return handler, ok
}

func (l *VoipLogic) handleMinigame(ctx context.Context, req *bean.JoinRoomReq) (*bean.JoinRoomResp, error) {
	logger.Logger.Infof("handleMinigame start, req: %+v", req)
	groupID, err := l.voipService.CreateGroupID(ctx, req.GroupID)
	if err != nil {
		logger.Logger.Errorf("CreateGroupID error: %v", err)
		return nil, err
	}

	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		logger.Logger.Errorf("VoipLogic GetMinigameConfig error: %v", err)
		return nil, err
	}

	user, err := l.userService.GetMinigameModel(ctx, req.UserID)
	if err != nil {
		logger.Logger.Errorf("VoipLogic GetMinigameModel error: %v", err)
		return nil, err
	}

	nonceStr, timestamp := l.voipService.GenerateNonceStrAndTimeStamp()
	signature, err := l.voipService.GenerateWechatRoomSignature(conf.AppID, groupID, nonceStr, timestamp, user.SessionKey)
	if err != nil {
		logger.Logger.Errorf("GenerateWechatRoomSignature error: %v", err)
		return nil, err
	}

	resp := &bean.JoinRoomResp{
		GroupID: groupID,
		UserID:  req.UserID,
		WechatVoipSign: &bean.WechatVoipSign{
			Signature: signature,
			NonceStr:  nonceStr,
			Timestamp: timestamp,
		},
	}
	logger.Logger.Infof("handleMinigame success, resp: %+v", resp)
	return resp, nil
}

func (l *VoipLogic) handleDouyin(ctx context.Context, req *bean.JoinRoomReq) (*bean.JoinRoomResp, error) {
	logger.Logger.Infof("handleDouyin start, req: %+v", req)
	// user, err := l.userService.GetUserDouyinModel(ctx, req.UserID)
	// if err != nil {
	// 	logger.Logger.Errorf("VoipLogic GetUserDouyinModel error: %v", err)
	// 	return nil, err
	// }

	conf, err := l.douyinService.GetDouyinConf(ctx, req.GameID)
	if err != nil {
		logger.Logger.Errorf("VoipLogic GetDouyinConf error: %v", err)
		return nil, err
	}

	if req.GroupID == "" {
		req.GroupID, err = l.voipService.FetchDouyinChannelID(ctx, conf.AccessToken)
		if err != nil {
			logger.Logger.Errorf("FetchDouyinChannelID error: %v", err)
			return nil, err
		}
	}
	tokenObj := l.douyinTokenService.New(conf.RtcAppID, conf.RtcAppKey, req.GroupID, req.UserID)
	tokenObj.ExpireTime(time.Now().Add(time.Hour * 24))
	tokenObj.AddPrivilege(constants.PrivSubscribeStream, time.Time{})
	tokenObj.AddPrivilege(constants.PrivPublishStream, time.Now().Add(time.Minute))
	token, err := tokenObj.Serialize()
	if err != nil {
		logger.Logger.Errorf("Token serialization error: %v", err)
		return nil, err
	}

	resp := &bean.JoinRoomResp{
		GroupID:         req.GroupID,
		DouyinVoipToken: token,
		UserID:          req.UserID,
	}
	logger.Logger.Infof("handleDouyin success, resp: %+v", resp)
	return resp, nil
}
