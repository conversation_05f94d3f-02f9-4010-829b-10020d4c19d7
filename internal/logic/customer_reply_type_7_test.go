package logic

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestMatchTextMessageWithAny 测试 matchTextMessage 方法对 "any" 的支持
func TestMatchTextMessageWithAny(t *testing.T) {
	logic := &CustomerLogic{}

	tests := []struct {
		name       string
		content    string
		acceptText string
		expected   bool
	}{
		{
			name:       "匹配any关键词",
			content:    "任何文本内容",
			acceptText: `["any"]`,
			expected:   true,
		},
		{
			name:       "匹配any关键词（混合配置）",
			content:    "任何文本内容",
			acceptText: `["hello", "any", "world"]`,
			expected:   true,
		},
		{
			name:       "匹配具体文本",
			content:    "hello",
			acceptText: `["hello", "world"]`,
			expected:   true,
		},
		{
			name:       "不匹配具体文本",
			content:    "test",
			acceptText: `["hello", "world"]`,
			expected:   false,
		},
		{
			name:       "空配置不匹配",
			content:    "test",
			acceptText: `[]`,
			expected:   false,
		},
		{
			name:       "无效JSON格式",
			content:    "test",
			acceptText: `invalid json`,
			expected:   false,
		},
		{
			name:       "图片消息匹配any",
			content:    "image",
			acceptText: `["any"]`,
			expected:   true,
		},
		{
			name:       "小程序消息匹配any",
			content:    "miniprogram",
			acceptText: `["any"]`,
			expected:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := logic.matchTextMessage(tt.content, tt.acceptText)
			assert.Equal(t, tt.expected, result, "测试用例: %s", tt.name)
		})
	}
}

// TestReplyType7Configuration 测试 reply_type = 7 的配置场景
func TestReplyType7Configuration(t *testing.T) {
	// 这个测试验证配置的正确性，不涉及实际的API调用
	
	// 模拟数据库配置
	testConfigs := []struct {
		name         string
		gameID       string
		platformType string
		scenes       int
		acceptText   string
		replyType    int
		replyContent string
		description  string
	}{
		{
			name:         "reply_type=7的标准配置",
			gameID:       "test_game_001",
			platformType: "douyin_minigame",
			scenes:       2, // 用户发消息场景
			acceptText:   `["any"]`,
			replyType:    7,
			replyContent: "target_game_002", // 目标游戏ID
			description:  "匹配任何消息类型，生成微信URL链接",
		},
		{
			name:         "reply_type=7的混合配置",
			gameID:       "test_game_001",
			platformType: "douyin_minigame",
			scenes:       2,
			acceptText:   `["hello", "any", "help"]`,
			replyType:    7,
			replyContent: "target_game_003",
			description:  "匹配特定文本或任何消息类型",
		},
	}

	for _, config := range testConfigs {
		t.Run(config.name, func(t *testing.T) {
			// 验证配置的有效性
			assert.NotEmpty(t, config.gameID, "游戏ID不能为空")
			assert.Equal(t, "douyin_minigame", config.platformType, "平台类型必须是douyin_minigame")
			assert.Equal(t, 2, config.scenes, "场景必须是2（用户发消息）")
			assert.Equal(t, 7, config.replyType, "回复类型必须是7")
			assert.NotEmpty(t, config.replyContent, "目标游戏ID不能为空")
			
			// 验证acceptText格式
			logic := &CustomerLogic{}
			isValidAny := logic.matchTextMessage("any", config.acceptText)
			assert.True(t, isValidAny, "配置应该能匹配'any'关键词")
			
			t.Logf("配置验证通过: %s", config.description)
		})
	}
}

// TestWechatUrlLinkGeneration 测试微信URL链接生成的逻辑
func TestWechatUrlLinkGeneration(t *testing.T) {
	// 这个测试验证URL链接生成的参数和逻辑
	
	testCases := []struct {
		name           string
		targetGameID   string
		expectedError  bool
		errorMessage   string
	}{
		{
			name:          "有效的目标游戏ID",
			targetGameID:  "valid_game_id",
			expectedError: false,
		},
		{
			name:          "空的目标游戏ID",
			targetGameID:  "",
			expectedError: true,
			errorMessage:  "reply_content中的目标游戏ID不能为空",
		},
		{
			name:          "包含特殊字符的游戏ID",
			targetGameID:  "game_id_with_special_chars_123",
			expectedError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 验证目标游戏ID的有效性
			if tc.expectedError {
				assert.Empty(t, tc.targetGameID, "应该是空的目标游戏ID")
			} else {
				assert.NotEmpty(t, tc.targetGameID, "目标游戏ID不应该为空")
			}
		})
	}
}

// TestDouyinMessageTypeHandling 测试不同消息类型的处理
func TestDouyinMessageTypeHandling(t *testing.T) {
	logic := &CustomerLogic{}
	
	messageTypes := []struct {
		msgType     string
		content     string
		acceptText  string
		shouldMatch bool
	}{
		{
			msgType:     "text",
			content:     "用户发送的文本消息",
			acceptText:  `["any"]`,
			shouldMatch: true,
		},
		{
			msgType:     "image",
			content:     "image", // 图片消息的标识
			acceptText:  `["any"]`,
			shouldMatch: true,
		},
		{
			msgType:     "miniprogrampage",
			content:     "miniprogram", // 小程序消息的标识
			acceptText:  `["any"]`,
			shouldMatch: true,
		},
		{
			msgType:     "text",
			content:     "特定文本",
			acceptText:  `["特定文本"]`,
			shouldMatch: true,
		},
		{
			msgType:     "text",
			content:     "不匹配的文本",
			acceptText:  `["特定文本"]`,
			shouldMatch: false,
		},
	}

	for _, mt := range messageTypes {
		t.Run("消息类型_"+mt.msgType+"_"+mt.content, func(t *testing.T) {
			result := logic.matchTextMessage(mt.content, mt.acceptText)
			assert.Equal(t, mt.shouldMatch, result, 
				"消息类型: %s, 内容: %s, 配置: %s", mt.msgType, mt.content, mt.acceptText)
		})
	}
}

// TestReplyTypeSwitch 测试回复类型的分支逻辑
func TestReplyTypeSwitch(t *testing.T) {
	// 验证所有支持的回复类型
	supportedReplyTypes := []struct {
		replyType   int
		description string
	}{
		{1, "文本回复"},
		{2, "图片回复"},
		{3, "透传给服务器"},
		{5, "链接回复"},
		{7, "微信URL链接回复"}, // 新增的类型
	}

	for _, rt := range supportedReplyTypes {
		t.Run(rt.description, func(t *testing.T) {
			assert.True(t, rt.replyType > 0, "回复类型必须大于0")
			assert.NotEmpty(t, rt.description, "描述不能为空")
			
			// 验证新增的类型7
			if rt.replyType == 7 {
				assert.Equal(t, "微信URL链接回复", rt.description, "类型7应该是微信URL链接回复")
			}
		})
	}
}
