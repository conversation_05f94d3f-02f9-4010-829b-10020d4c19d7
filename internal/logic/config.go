package logic

import (
	"context"
	"encoding/json"
	"sort"
	"strconv"
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/ip"

	"git.panlonggame.com/bkxplatform/admin-console/internal/cron"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
)

var (
	_configOnce  sync.Once
	_configLogic *ConfigLogic
)

type ConfigLogic struct {
	configService *service.ConfigService
	userService   *service.UserService
}

func SingletonConfigLogic() *ConfigLogic {
	_configOnce.Do(func() {
		_configLogic = &ConfigLogic{
			configService: service.SingletonConfigService(),
			userService:   service.SingletonUserService(),
		}
	})
	return _configLogic
}

// GetGameConfig returns the configuration
func (l *ConfigLogic) GetGameConfig(ctx context.Context, gameID string) (*bean.GetGameConfigRes, error) {
	return l.configService.GetGameConfig(ctx, gameID)
}

// GetSwitches get custom switches
func (l *ConfigLogic) GetSwitches(_ context.Context, req *bean.GetSwitchesReq) (*bean.GetSwitchesResp, error) {
	switchValues := cron.GetSwitchByGameIDAndSwitchIDs(req.GameID, req.SwitchesIDs)

	resp := &bean.GetSwitchesResp{Switches: make([]interface{}, len(req.SwitchesIDs))}

	for i, switchID := range req.SwitchesIDs {
		matched := false
		for _, sw := range switchValues {
			if switchID == sw["switch_id"] {
				matched = true
				if sw["status"] != "1" ||
					(sw["effective_time_start"] != "0" && sw["effective_time_end"] != "0") ||
					sw["applicable_platforms"] == "" ||
					(sw["applicable_platforms"] != "" && !l.exactPlatformMatch(sw["applicable_platforms"], req.PlatformType)) {
					resp.Switches[i] = constants.SwitchNotFound
				} else {
					if sw["versions"] == "" || l.containsVersion(sw["versions"], req.Version) {
						if intValue, err := strconv.Atoi(sw["default_return"]); err == nil {
							resp.Switches[i] = intValue
						} else {
							resp.Switches[i] = constants.SwitchNotFound
						}
					} else {
						resp.Switches[i] = constants.SwitchNotFound
					}
				}
				break
			}
		}
		if !matched {
			resp.Switches[i] = constants.SwitchNotFound
		}
	}
	return resp, nil
}

func (l *ConfigLogic) containsVersion(versions, targetVersion string) bool {
	for _, v := range strings.Split(versions, ",") {
		if strings.TrimSpace(v) == targetVersion {
			return true
		}
	}
	return false
}

// GetSwitchesV2 获取自定义开关
func (l *ConfigLogic) GetSwitchesV2(ctx context.Context, req *bean.GetSwitchesV2Req) (*bean.GetSwitchesResp, error) {
	if req.UserID != "" {
		userInfo, err := l.userService.GetUserInfo(ctx, req.UserID)
		if err != nil {
			return nil, err
		}
		req.Channel = userInfo.Channel
	}

	switchValues := cron.GetSwitchByGameIDAndSwitchIDs(req.RequestGameID, req.SwitchesIDs)

	// 根据IP获取城市，并获取code
	city := ip.GetRegionCityByIP(req.IP)
	cityCode := constants.CityCodeUnknown // 默认使用未知代码

	if city != "" {
		if code, ok := cron.GetCityCode(city); ok {
			cityCode = code
		}
		// 如果无法获取城市代码，保持使用未知代码
	}
	req.IPRegionID = cityCode

	resp := &bean.GetSwitchesResp{Switches: make([]interface{}, len(req.SwitchesIDs))}

	for i, switchID := range req.SwitchesIDs {
		matched := false
		for _, sw := range switchValues {
			if switchID == sw["switch_id"] {
				matched = true
				if sw["status"] != "1" ||
					(sw["effective_time_start"] != "0" && sw["effective_time_end"] != "0") ||
					sw["applicable_platforms"] == "" ||
					(sw["applicable_platforms"] != "" && !l.exactPlatformMatch(sw["applicable_platforms"], req.PlatformType)) {
					resp.Switches[i] = constants.SwitchNotFound
				} else {
					if sw["versions"] == "" || l.containsVersion(sw["versions"], req.Version) {
						if intValue, err := strconv.Atoi(sw["default_return"]); err == nil {
							switchParamGroup, ok := cron.GetSwitchParamByGameIDAndSwitchID(req.RequestGameID, switchID)
							if !ok {
								resp.Switches[i] = intValue
							} else {
								// Pass the intValue as the default return value
								resp.Switches[i] = l.deepSearchParam(switchParamGroup, req, int32(intValue))
							}
						} else {
							resp.Switches[i] = constants.SwitchNotFound
						}
					} else {
						resp.Switches[i] = constants.SwitchNotFound
					}
				}
				break
			}
		}
		if !matched {
			resp.Switches[i] = constants.SwitchNotFound
		}
	}
	return resp, nil
}

func (l *ConfigLogic) deepSearchParam(paramGroup cron.SwitchParamGroup, req *bean.GetSwitchesV2Req, defaultReturn int32) interface{} {
	// Create a map to pass to searchParamTree
	paramMap := make(map[int32][]cron.SwitchParam)
	for _, param := range paramGroup.Params {
		paramMap[param.ParentID] = append(paramMap[param.ParentID], param)
	}

	// Sort params by SortOrder
	for parentID, params := range paramMap {
		sort.Slice(params, func(i, j int) bool {
			return params[i].SortOrder < params[j].SortOrder
		})
		paramMap[parentID] = params
	}

	return l.searchParamTree(paramMap[0], paramMap, req, defaultReturn)
}

func (l *ConfigLogic) searchParamTree(params []cron.SwitchParam, paramMap map[int32][]cron.SwitchParam, req *bean.GetSwitchesV2Req, defaultReturn int32) int32 {
	var lastMatchedReturn int32 = defaultReturn

	for _, p := range params {
		if l.matchParam(p, req) {
			lastMatchedReturn = p.DefaultReturn
			if children, exists := paramMap[p.ID]; exists {
				childResult := l.searchParamTree(children, paramMap, req, p.DefaultReturn)
				if childResult != constants.SwitchNotFound {
					return childResult
				}
			} else {
				// 如果没有子规则且当前规则匹配，直接返回结果
				return lastMatchedReturn
			}
			// Continue searching other items at the same level
		}
	}

	// If a match was found at this level but no deeper matches, return the last matched DefaultReturn
	return lastMatchedReturn
}

func (l *ConfigLogic) matchParam(p cron.SwitchParam, req *bean.GetSwitchesV2Req) bool {
	switch p.ParamType {
	case constants.SwitchParamTypePlatformType:
		paramData := l.ParamToSlice(p.ParamData)
		return l.verifySliceInStr(paramData, req.PlatformType)
	case constants.SwitchParamTypeVersion:
		paramData := l.ParamToSlice(p.ParamData)
		return l.verifySliceInStr(paramData, req.Version)
	case constants.SwitchParamTypeNickname:
		paramData := l.ParamToSlice(p.ParamData)
		return l.verifySliceInStr(paramData, req.NickName)
	case constants.SwitchParamTypeIP:
		paramData := l.ParamToSlice(p.ParamData)
		return l.verifySliceInStr(paramData, req.IP)
	case constants.SwitchParamTypeIPRegionID:
		paramData := l.ParamToSlice(p.ParamData)
		return l.verifySliceInStr(paramData, req.IPRegionID)
	case constants.SwitchParamTypeUniqueID:
		paramData := l.ParamToSlice(p.ParamData)
		return l.verifySliceInStr(paramData, req.UserID)
	case constants.SwitchParamTypeChannel:
		paramData := l.ParamToSlice(p.ParamData)
		return l.verifySliceInStr(paramData, req.Channel)
	case constants.SwitchParamTypeScene:
		paramData := l.ParamToSlice(p.ParamData)
		otherParamData := l.ParamToSlice(p.OtherParamData)
		for _, data := range otherParamData {
			if data != "" {
				paramData = append(paramData, data)
			}
		}
		return l.verifySliceInStr(paramData, req.Scene)
	case constants.SwitchParamTypeCustom:
		paramData := make(map[string]interface{})
		err := json.Unmarshal([]byte(p.ParamData), &paramData)
		if err != nil {
			return false
		}

		// 获取规则参数
		key, ok := paramData["key"].(string)
		if !ok {
			return false
		}
		value := paramData["value"]
		condition, ok := paramData["condition"].(string)
		if !ok {
			return false
		}
		preCondition, _ := paramData["pre_condition"].(string)

		// 遍历req.Params数组，查找所有匹配的key
		for _, param := range req.Params {
			reqValue, ok := param[key]
			if !ok {
				continue
			}

			valueType, _ := param["value_type"].(string)

			// 应用前置条件
			if preCondition == "modulo_operation" {
				reqValueFloat, ok := toFloat64(reqValue)
				if !ok {
					continue
				}
				preValue := paramData["pre_value"]
				preValueFloat, ok := toFloat64(preValue)
				if !ok {
					continue
				}
				reqValue = int(reqValueFloat) % int(preValueFloat)
			}

			// 根据条件进行比较
			match := false
			switch condition {
			case "equals":
				if valueType == "number" {
					reqFloat, reqOk := toFloat64(reqValue)
					valueFloat, valueOk := toFloat64(value)
					if reqOk && valueOk {
						match = (reqFloat == valueFloat)
					}
				}
			case "less_than":
				if valueType == "number" {
					reqFloat, reqOk := toFloat64(reqValue)
					valueFloat, valueOk := toFloat64(value)
					if reqOk && valueOk {
						match = (reqFloat < valueFloat)
					}
				}
			case "greater_than":
				if valueType == "number" {
					reqFloat, reqOk := toFloat64(reqValue)
					valueFloat, valueOk := toFloat64(value)
					if reqOk && valueOk {
						match = (reqFloat > valueFloat)
					}
				}
			case "less_than_or_equal_to":
				if valueType == "number" {
					reqFloat, reqOk := toFloat64(reqValue)
					valueFloat, valueOk := toFloat64(value)
					if reqOk && valueOk {
						match = (reqFloat <= valueFloat)
					}
				}
			case "greater_than_or_equal_to":
				if valueType == "number" {
					reqFloat, reqOk := toFloat64(reqValue)
					valueFloat, valueOk := toFloat64(value)
					if reqOk && valueOk {
						match = (reqFloat >= valueFloat)
					}
				}
			case "not_equal_to":
				if valueType == "number" {
					reqFloat, reqOk := toFloat64(reqValue)
					valueFloat, valueOk := toFloat64(value)
					if reqOk && valueOk {
						match = (reqFloat != valueFloat)
					}
				}
			case "string_equals":
				if valueType == "string" {
					reqStr, reqOk := toString(reqValue)
					valueStr, valueOk := toString(value)
					match = (reqOk && valueOk && reqStr == valueStr)
				}
			case "string_not_equal_to":
				if valueType == "string" {
					reqStr, reqOk := toString(reqValue)
					valueStr, valueOk := toString(value)
					match = (reqOk && valueOk && reqStr != valueStr)
				}
			case "number_contains":
				if valueType == "number" {
					reqNum, reqOk := toFloat64(reqValue)
					if !reqOk {
						return false
					}

					var valueSlice []float64
					switch v := value.(type) {
					case string:
						// 处理 JSON 字符串
						if err := json.Unmarshal([]byte(v), &valueSlice); err != nil {
							return false
						}
					case []interface{}, []float64:
						// 如果已经是数组类型，直接转换
						jsonBytes, err := json.Marshal(value)
						if err != nil {
							return false
						}
						if err := json.Unmarshal(jsonBytes, &valueSlice); err != nil {
							return false
						}
					default:
						return false
					}

					for _, v := range valueSlice {
						if reqNum == v {
							match = true
							break
						}
					}
				}
			case "string_contains":
				if valueType == "string" {
					reqStr, reqOk := toString(reqValue)
					if !reqOk {
						return false
					}

					var valueSlice []string
					switch v := value.(type) {
					case string:
						// 处理 JSON 字符串
						if err := json.Unmarshal([]byte(v), &valueSlice); err != nil {
							return false
						}
					case []interface{}, []string:
						// 如果已经是数组类型，直接转换
						jsonBytes, err := json.Marshal(value)
						if err != nil {
							return false
						}
						if err := json.Unmarshal(jsonBytes, &valueSlice); err != nil {
							return false
						}
					default:
						return false
					}
					for _, v := range valueSlice {
						if reqStr == v {
							match = true
							break
						}
					}
				}
			default:
				continue
			}

			// 如果有一个值匹配，则整体匹配
			if match {
				return true
			}
		}
		return false

	default:
		return false
	}
}

func (l *ConfigLogic) ParamToSlice(paramData string) []string {
	paramSlice := make([]string, 0)
	err := json.Unmarshal([]byte(paramData), &paramSlice)
	if err != nil {
		return nil
	}

	return paramSlice
}

func (l *ConfigLogic) verifySliceInStr(paramData []string, value string) bool {
	for _, v := range paramData {
		if v == value {
			return true
		}
	}
	return false
}

func (l *ConfigLogic) matchCustomParam(paramData []string, reqParams map[string]interface{}) bool {
	paramDataMap := make(map[string]interface{})
	for _, item := range paramData {
		parts := strings.SplitN(item, ":", 2)
		if len(parts) == 2 {
			paramDataMap[parts[0]] = parts[1]
		}
	}

	for key, value := range paramDataMap {
		if reqValue, ok := reqParams[key]; !ok || reqValue != value {
			return false
		}
	}
	return true
}

func toFloat64(v interface{}) (float64, bool) {
	switch v := v.(type) {
	case float64:
		return v, true
	case float32:
		return float64(v), true
	case int:
		return float64(v), true
	case int64:
		return float64(v), true
	case string:
		f, err := strconv.ParseFloat(v, 64)
		if err != nil {
			return 0, false
		}
		return f, true
	default:
		return 0, false
	}
}

func toString(v interface{}) (string, bool) {
	switch v := v.(type) {
	case string:
		return v, true
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64), true
	case float32:
		return strconv.FormatFloat(float64(v), 'f', -1, 32), true
	case int:
		return strconv.Itoa(v), true
	case int64:
		return strconv.FormatInt(v, 10), true
	default:
		return "", false
	}
}

// Add this new helper function
func (l *ConfigLogic) exactPlatformMatch(platforms, targetPlatform string) bool {
	platformList := strings.Split(platforms, ",")
	targetPlatform = strings.TrimSpace(targetPlatform)
	for _, p := range platformList {
		p = strings.TrimSpace(p)
		// 使用完全相等比较
		if p == targetPlatform {
			return true
		}
	}
	return false
}

// GetAd 获取广告位配置
func (l *ConfigLogic) GetAd(ctx context.Context, req *bean.GetAdReq) (*bean.GetAdResp, error) {
	return l.configService.GetAd(ctx, req)
}
