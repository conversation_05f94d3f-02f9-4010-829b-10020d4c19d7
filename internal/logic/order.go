package logic

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/https"
	"golang.org/x/net/html"
	"gorm.io/gorm"

	"github.com/jinzhu/copier"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/bizerrors"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/task"
	"github.com/hibiken/asynq"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_orderOnce  sync.Once
	_orderLogic *OrderLogic
)

type OrderLogic struct {
	orderService             *service.OrderService
	userService              *service.UserService
	subscribeService         *service.SubscribeService
	minigameService          *service.MinigameService
	wechatPayService         *service.WechatPayService
	gravityEngineService     *service.GravityEngineService
	douyinService            *service.DouyinService
	customerService          *service.CustomerService
	dataReportService        *service.DataReportService
	httpsWechatService       *https.WechatHttpService
	qiyuService              *service.QiyuService
	stopServiceConfigService *service.StopServiceConfigService
}

func SingletonOrderLogic() *OrderLogic {
	_orderOnce.Do(func() {
		_orderLogic = &OrderLogic{
			orderService:             service.SingletonOrderService(),
			userService:              service.SingletonUserService(),
			subscribeService:         service.SingletonSubscribeService(),
			minigameService:          service.SingletonMinigameService(),
			wechatPayService:         service.SingletonWechatPayService(),
			gravityEngineService:     service.SingletonGravityEngineService(),
			douyinService:            service.SingletonDouyinService(),
			customerService:          service.SingletonCustomerService(),
			dataReportService:        service.SingletonDataReportService(),
			httpsWechatService:       https.SingletonWechatHttpService(),
			qiyuService:              service.SingletonQiyuService(),
			stopServiceConfigService: service.SingletonStopServiceConfigService(),
		}
	})
	return _orderLogic
}

// CreateOrder 创建订单
func (l *OrderLogic) CreateOrder(ctx context.Context, req *bean.CreateOrderReq) (*bean.Order, error) {
	logger.Logger.InfofCtx(ctx, "[CreateOrder] 开始创建订单, gameID: %s, goodsID: %s, userID: %s, money: %d",
		req.GameID, req.GoodsID, req.UserID, req.Money)

	// 获取游戏信息
	game, err := l.userService.GetGameInfo(ctx, req.GameID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[CreateOrder] GetGameInfo failed, gameID: %s, err: %v", req.GameID, err)
		return nil, err
	}

	// 检查充值是否启用
	rechargeEnabled, err := l.checkRechargeEnabledForAllPlatforms(ctx, req.GameID, game.PlatformType)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[CreateOrder] 检查充值开关失败, gameID: %s, platformType: %s, err: %v", req.GameID, game.PlatformType, err)
		return nil, err
	}
	if !rechargeEnabled {
		logger.Logger.WarnfCtx(ctx, "[CreateOrder] 充值功能已关闭, gameID: %s, platformType: %s", req.GameID, game.PlatformType)
		return nil, constants.ErrRechargeDisabled
	}

	// 校验 goods id 金额
	money, err := l.orderService.GetVerifyMoney(ctx, req.GameID, req.GoodsID, req.Money)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[CreateOrder] GetVerifyMoney failed, gameID: %s, goodsID: %s, money: %d, err: %v", req.GameID, req.GoodsID, req.Money, err)
		return nil, err
	}

	// 记录商品验证成功和金额调整情况
	logger.Logger.DebugfCtx(ctx, "[CreateOrder] 商品验证成功, gameID: %s, goodsID: %s, originalMoney: %d, verifiedMoney: %d",
		req.GameID, req.GoodsID, req.Money, money)
	req.Money = money

	resp, err := l.orderService.CreateOrder(ctx, req)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[CreateOrder] CreateOrder failed, gameID: %s, goodsID: %s, userID: %s, money: %d, err: %v",
			req.GameID, req.GoodsID, req.UserID, req.Money, err)
		return nil, err
	}

	err = l.dataReportService.ReportCreateOrder(ctx, req.UserID, "", req.GameID, game.PlatformAppID, resp.OrderID, req.GoodsID, req.Money)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "dataReportService ReportCreateOrder gameid: %s, userid: %s, platformappid: %s, err: %s",
			req.GameID, req.UserID, game.PlatformAppID, err.Error())
		// return nil, err 仅输出错误，不返回error，以防止影响正常下单
	}

	// 记录订单创建成功
	logger.Logger.InfofCtx(ctx, "[CreateOrder] 订单创建成功, orderID: %s, gameID: %s, goodsID: %s, userID: %s, money: %d",
		resp.OrderID, req.GameID, req.GoodsID, req.UserID, req.Money)
	return resp, nil
}

// GetOrderTotal 获取用户在特定游戏中的充值总金额
func (l *OrderLogic) GetOrderTotal(ctx context.Context, req *bean.GetOrderTotalReq) (*bean.GetOrderTotalResp, error) {
	totalAmount, err := l.orderService.GetTotalAmountByUserIDAndGameID(ctx, req.UserID, req.GameID)
	if err != nil {
		return nil, err
	}
	return &bean.GetOrderTotalResp{TotalAmount: totalAmount}, nil
}

// GetOrderDetailSign 米大师安卓
func (l *OrderLogic) GetOrderDetailSign(ctx context.Context, req *bean.GetOrderDetailSignReq) (*bean.GetOrderDetailRes, error) {
	// 函数入口日志记录
	logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 开始处理米大师安卓订单详情请求, order_id: %s, user_id: %s, game_id: %s, platform_type: %s",
		req.OrderID, req.UserID, req.GameID, req.PlatformType)

	if req.PlatformType == "" {
		req.PlatformType = constants.PlatformTypeMinigame
	}

	// 获取订单详情
	orderDetail, err := l.orderService.GetOrderDetail(ctx, req.OrderID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 获取订单详情失败, order_id: %s, error: %v", req.OrderID, err)
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 获取订单详情成功, order_id: %s, user_id: %s, game_id: %s, goods_id: %s, money: %d, status: %d",
		orderDetail.OrderID, orderDetail.UserID, orderDetail.GameID, orderDetail.GoodsID, orderDetail.Money, orderDetail.Status)

	resp := &bean.GetOrderDetailRes{
		OrderDetail: orderDetail,
	}

	// 数据上报
	err = l.dataReportService.ReportPullOrder(ctx, req.UserID, req.DeviceID, req.GameID, req.AppID, orderDetail.OrderID, orderDetail.GoodsID, orderDetail.Money, constants.PlatformTypeMap[orderDetail.PlatformType])
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 数据上报失败, order_id: %s, user_id: %s, game_id: %s, error: %v",
			req.OrderID, req.UserID, req.GameID, err)
		// return nil, err
	} else {
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 数据上报成功, order_id: %s", req.OrderID)
	}

	// 根据平台类型处理不同的业务逻辑
	switch req.PlatformType {
	case constants.PlatformTypeMinigame:
		// 校验传入的类型
		if !util.InSlice(orderDetail.GoodsPayTypes, constants.PayTypeAndroidWechatPay) {
			logger.Logger.WarnfCtx(ctx, "[GetOrderDetailSign] 订单支付类型校验失败, order_id: %s, goods_pay_types: %v, expected: %d",
				req.OrderID, orderDetail.GoodsPayTypes, constants.PayTypeAndroidWechatPay)
			return nil, constants.ErrOrderPayType
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 订单支付类型校验通过, order_id: %s", req.OrderID)

		//  更新pay_type = 1 安卓米大师
		update := map[string]interface{}{
			"pay_type":      constants.PayTypeAndroidWechatPay,
			"platform_type": constants.PlatformTypeAndroid,
		}
		if err := l.orderService.UpdateOrderStatus(ctx, orderDetail.OrderID, update); err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 更新订单状态失败, order_id: %s, error: %v", req.OrderID, err)
			return nil, err
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 更新订单状态成功, order_id: %s", req.OrderID)

		// 获取微信支付的 AppKey
		conf, err := l.minigameService.GetMinigameConfig(ctx, orderDetail.GameID)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 获取小游戏配置失败, order_id: %s, game_id: %s, error: %v",
				req.OrderID, orderDetail.GameID, err)
			return nil, err
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 获取小游戏配置成功, order_id: %s, game_id: %s, pay_offer_id: %s",
			req.OrderID, orderDetail.GameID, conf.PayOfferID)

		// 获取订单签名数据
		signData, err := l.wechatPayService.GetOrderSignData(ctx, orderDetail, conf.PayOfferID)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 获取订单签名数据失败, order_id: %s, error: %v", req.OrderID, err)
			return nil, err
		}

		// 序列化签名数据
		signByte, err := json.Marshal(signData)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 序列化签名数据失败, order_id: %s, error: %v", req.OrderID, err)
			return nil, err
		}

		// 生成支付签名
		paySig, err := l.wechatPayService.PaySignature(conf.PayAppKey, signByte)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 生成支付签名失败, order_id: %s, error: %v", req.OrderID, err)
			return nil, err
		}

		// 获取微信支付的 sessionKey
		userInfo, err := l.userService.GetMinigameModel(ctx, orderDetail.UserID)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 获取用户小游戏信息失败, order_id: %s, user_id: %s, error: %v",
				req.OrderID, orderDetail.UserID, err)
			return nil, err
		}
		if userInfo == nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 用户小游戏信息为空, order_id: %s, user_id: %s", req.OrderID, orderDetail.UserID)
			return nil, constants.ErrOrderUserIDNotFound
		}

		// 生成用户签名
		signature, err := l.wechatPayService.Signature(userInfo.SessionKey, signByte)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 生成用户签名失败, order_id: %s, error: %v", req.OrderID, err)
			return nil, err
		}

		resp.SignData = signData
		resp.PaySig = paySig
		resp.Signature = signature
	case constants.PlatformTypeDouyin:
		// 校验传入的类型
		if !util.InSlice(orderDetail.GoodsPayTypes, constants.PayTypeAndroidDouyinPay) {
			logger.Logger.WarnfCtx(ctx, "[GetOrderDetailSign] 抖音订单支付类型校验失败, order_id: %s, goods_pay_types: %v, expected: %d",
				req.OrderID, orderDetail.GoodsPayTypes, constants.PayTypeAndroidDouyinPay)
			return nil, constants.ErrOrderPayType
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 抖音订单支付类型校验通过, order_id: %s", req.OrderID)

		// 获取商品详情以检查是否为小额钻石支付
		goodsInfo, err := l.orderService.GetGoodsDetailByGameIDAndGoodsID(ctx, orderDetail.GameID, orderDetail.GoodsID)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 获取商品详情失败, order_id: %s, goods_id: %s, error: %v",
				req.OrderID, orderDetail.GoodsID, err)
			return nil, err
		}

		var update map[string]interface{}
		var gameCurrency int32 // 声明在外层作用域，供后续使用

		if goodsInfo.IsSmallDiamond {
			// 小额钻石支付逻辑
			logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 检测到小额钻石支付, order_id: %s, money: %d", req.OrderID, orderDetail.Money)

			update = map[string]interface{}{
				"pay_type":      constants.PayTypeAndroidDouyinPay,
				"platform_type": constants.PlatformTypeAndroid,
			}
		} else {
			// 传统抖音虚拟支付逻辑
			var err error
			gameCurrency, err = l.orderService.GetDouyinGameCurrency(orderDetail.Money)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 计算抖音游戏币数量失败, order_id: %s, money: %d, error: %v",
					req.OrderID, orderDetail.Money, err)
				return nil, err
			}
			logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 计算抖音游戏币数量成功, order_id: %s, money: %d, game_currency: %d",
				req.OrderID, orderDetail.Money, gameCurrency)

			update = map[string]interface{}{
				"pay_type":      constants.PayTypeAndroidDouyinPay,
				"game_currency": gameCurrency,
				"platform_type": constants.PlatformTypeAndroid,
			}
		}
		if err := l.orderService.UpdateOrderStatus(ctx, orderDetail.OrderID, update); err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 更新抖音订单状态失败, order_id: %s, error: %v", req.OrderID, err)
			return nil, err
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 更新抖音订单状态成功, order_id: %s", req.OrderID)

		// 查询玩家的游戏币余额并写入订单，支付完成后回调对比，确定支付是否成功
		conf, err := l.douyinService.GetDouyinConf(ctx, orderDetail.GameID)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 获取抖音配置失败, order_id: %s, game_id: %s, error: %v",
				req.OrderID, orderDetail.GameID, err)
			return nil, err
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 获取抖音配置成功, order_id: %s, game_id: %s", req.OrderID, orderDetail.GameID)

		// 获取抖音用户信息
		user, err := l.userService.GetUserDouyinModel(ctx, orderDetail.UserID)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 获取抖音用户信息失败, order_id: %s, user_id: %s, error: %v",
				req.OrderID, orderDetail.UserID, err)
			return nil, err
		}

		// 构建抖音余额查询参数
		p := &bean.DouyinBalanceParam{
			OpenID:      user.OpenID,
			AppID:       conf.AppID,
			Ts:          time.Now().Unix(),
			ZoneID:      "1",
			PF:          "android",
			AccessToken: conf.AccessToken,
			MpSig:       conf.PaySecret,
		}
		balance, err := l.douyinService.GetGameBalance(ctx, p)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 查询抖音游戏余额失败, order_id: %s, open_id: %s, error: %v",
				req.OrderID, user.OpenID, err)
			return nil, err
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 查询抖音游戏余额成功, order_id: %s, open_id: %s, save_amt: %d",
			req.OrderID, user.OpenID, balance.SaveAmt)

		// 更新订单余额
		if err := l.orderService.UpdateOrderBalance(ctx, orderDetail.OrderID, balance.SaveAmt); err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 更新订单余额失败, order_id: %s, save_amt: %d, error: %v",
				req.OrderID, balance.SaveAmt, err)
			return nil, err
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 更新订单余额成功, order_id: %s", req.OrderID)

		// 根据是否为小额钻石支付获取不同的支付参数
		var douyinOrder *bean.DouyinOrder
		if goodsInfo.IsSmallDiamond {
			// 小额钻石支付参数
			douyinOrder = l.douyinService.GetSmallDiamondPayParam(ctx, orderDetail.OrderID, orderDetail.Money)
			logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 获取小额钻石支付参数, order_id: %s, order_amount: %d",
				req.OrderID, orderDetail.Money)
		} else {
			// 传统抖音虚拟支付参数 - 使用之前计算的 gameCurrency 值
			douyinOrder = l.douyinService.GetGamePayParam(ctx, orderDetail.OrderID, gameCurrency)
			logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 获取传统抖音支付参数, order_id: %s, game_currency: %d",
				req.OrderID, gameCurrency)
		}

		if douyinOrder == nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailSign] 获取抖音支付参数失败, order_id: %s", req.OrderID)
			return nil, fmt.Errorf("获取抖音支付参数失败，订单ID为空")
		}
		resp.DouyinOrder = douyinOrder

	default:
		logger.Logger.WarnfCtx(ctx, "[GetOrderDetailSign] 未知的平台类型, order_id: %s, platform_type: %s", req.OrderID, req.PlatformType)
	}

	resp.OrderDetail.PlatformType = constants.PlatformTypeAndroid

	// 函数出口日志记录
	logger.Logger.InfofCtx(ctx, "[GetOrderDetailSign] 米大师安卓订单详情处理完成, order_id: %s, user_id: %s, game_id: %s, platform_type: %d",
		req.OrderID, orderDetail.UserID, orderDetail.GameID, resp.OrderDetail.PlatformType)

	return resp, nil
}

// GetOrderDetailCustomer
func (l *OrderLogic) GetOrderDetailCustomer(ctx context.Context, req *bean.GetOrderDetailCustomerReq) (*bean.GetOrderDetailCustomerRes, error) {
	// 函数入口日志记录
	logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 开始处理客服订单详情请求, order_id: %s, user_id: %s, game_id: %s, platform_type: %s",
		req.OrderID, req.UserID, req.GameID, req.PlatformType)

	if req.PlatformType == "" {
		req.PlatformType = constants.PlatformTypeMinigame
	}

	// 获取订单详情
	orderDetail, err := l.orderService.GetOrderDetail(ctx, req.OrderID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailCustomer] 获取订单详情失败, order_id: %s, error: %v", req.OrderID, err)
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 获取订单详情成功, order_id: %s, user_id: %s, game_id: %s, goods_id: %s, money: %d, status: %d",
		orderDetail.OrderID, orderDetail.UserID, orderDetail.GameID, orderDetail.GoodsID, orderDetail.Money, orderDetail.Status)

	res := &bean.GetOrderDetailCustomerRes{OrderDetail: orderDetail}

	// 数据上报
	err = l.dataReportService.ReportPullOrder(ctx, req.UserID, req.DeviceID, req.GameID, req.AppID, orderDetail.OrderID, orderDetail.GoodsID, orderDetail.Money, constants.PlatformTypeMap[orderDetail.PlatformType])
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailCustomer] 数据上报失败, order_id: %s, user_id: %s, game_id: %s, error: %v",
			req.OrderID, req.UserID, req.GameID, err)
		// return nil, err
	} else {
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 数据上报成功, order_id: %s", req.OrderID)
	}

	// 根据平台类型处理不同的业务逻辑
	switch req.PlatformType {
	case constants.PlatformTypeMinigame:
		// 校验传入的类型
		if !util.InSlice(orderDetail.GoodsPayTypes, constants.PayTypeIOSWechatH5Pay) {
			logger.Logger.WarnfCtx(ctx, "[GetOrderDetailCustomer] 订单支付类型校验失败, order_id: %s, goods_pay_types: %v, expected: %d",
				req.OrderID, orderDetail.GoodsPayTypes, constants.PayTypeIOSWechatH5Pay)
			return nil, constants.ErrOrderPayType
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 订单支付类型校验通过, order_id: %s", req.OrderID)

		//  更新pay_type = 1 安卓米大师
		update := map[string]interface{}{
			"pay_type":      constants.PayTypeIOSWechatH5Pay,
			"platform_type": constants.PlatformTypeIOS,
		}
		if err := l.orderService.UpdateOrderStatus(ctx, orderDetail.OrderID, update); err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailCustomer] 更新订单状态失败, order_id: %s, error: %v", req.OrderID, err)
			return nil, err
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 更新订单状态成功, order_id: %s", req.OrderID)

		// 获取小游戏配置
		conf, err := l.minigameService.GetMinigameConfig(ctx, orderDetail.GameID)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailCustomer] 获取小游戏配置失败, order_id: %s, game_id: %s, error: %v",
				req.OrderID, orderDetail.GameID, err)
			return nil, err
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 获取小游戏配置成功, order_id: %s, game_id: %s", req.OrderID, orderDetail.GameID)

		// 如果配置的图片为空，则使用默认图片
		if conf.CsPaymentBigPic == "" {
			conf.CsPaymentBigPic = constants.ThumbURL
		}

		customerServiceData := &bean.CustomerServiceData{
			SendMessageTitle: fmt.Sprintf("我要充值%.2f元", float64(orderDetail.Money)/100.0),
			SendMessagePath:  fmt.Sprintf("index?bkx_order_id=%s", orderDetail.OrderID),
			SendMessageImg:   conf.CsPaymentBigPic,
			SessionFrom:      fmt.Sprintf("{\"is_bkx_ios_pay\":true,\"bkx_order_id\":\"%s\"}", orderDetail.OrderID),
		}
		res.CustomerServiceData = customerServiceData

	case constants.PlatformTypeDouyin:
		// 校验传入的类型
		if !util.InSlice(orderDetail.GoodsPayTypes, constants.PayTypeIOSDouyinPay) {
			logger.Logger.WarnfCtx(ctx, "[GetOrderDetailCustomer] 抖音订单支付类型校验失败, order_id: %s, goods_pay_types: %v, expected: %d",
				req.OrderID, orderDetail.GoodsPayTypes, constants.PayTypeIOSDouyinPay)
			return nil, constants.ErrOrderPayType
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 抖音订单支付类型校验通过, order_id: %s", req.OrderID)

		// 获取商品详情以检查是否为小额钻石支付
		goodsInfo, err := l.orderService.GetGoodsDetailByGameIDAndGoodsID(ctx, orderDetail.GameID, orderDetail.GoodsID)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailCustomer] 获取商品详情失败, order_id: %s, goods_id: %s, error: %v",
				req.OrderID, orderDetail.GoodsID, err)
			return nil, err
		}

		var update map[string]interface{}
		var gameCurrency int32 // 声明在外层作用域，供后续使用

		if goodsInfo.IsSmallDiamond {
			// 小额钻石支付逻辑
			logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 检测到小额钻石支付, order_id: %s, money: %d", req.OrderID, orderDetail.Money)

			update = map[string]interface{}{
				"pay_type":      constants.PayTypeIOSDouyinPay,
				"platform_type": constants.PlatformTypeIOS,
			}
		} else {
			// 传统抖音虚拟支付逻辑
			var err error
			gameCurrency, err = l.orderService.GetDouyinGameCurrency(orderDetail.Money)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailCustomer] 计算抖音游戏币数量失败, order_id: %s, money: %d, error: %v",
					req.OrderID, orderDetail.Money, err)
				return nil, err
			}
			logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 计算抖音游戏币数量成功, order_id: %s, money: %d, game_currency: %d",
				req.OrderID, orderDetail.Money, gameCurrency)

			update = map[string]interface{}{
				"pay_type":      constants.PayTypeIOSDouyinPay,
				"game_currency": gameCurrency,
				"platform_type": constants.PlatformTypeIOS,
			}
		}
		if err := l.orderService.UpdateOrderStatus(ctx, orderDetail.OrderID, update); err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailCustomer] 更新抖音订单状态失败, order_id: %s, error: %v", req.OrderID, err)
			return nil, err
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 更新抖音订单状态成功, order_id: %s", req.OrderID)

		// 获取抖音配置
		conf, err := l.douyinService.GetDouyinConf(ctx, orderDetail.GameID)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailCustomer] 获取抖音配置失败, order_id: %s, game_id: %s, error: %v",
				req.OrderID, orderDetail.GameID, err)
			return nil, err
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 获取抖音配置成功, order_id: %s, game_id: %s", req.OrderID, orderDetail.GameID)

		// 获取抖音用户信息
		user, err := l.userService.GetUserDouyinModel(ctx, orderDetail.UserID)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailCustomer] 获取抖音用户信息失败, order_id: %s, user_id: %s, error: %v",
				req.OrderID, orderDetail.UserID, err)
			return nil, err
		}

		// 构建抖音余额查询参数
		p := &bean.DouyinBalanceParam{
			OpenID:      user.OpenID,
			AppID:       conf.AppID,
			Ts:          time.Now().Unix(),
			ZoneID:      "1",
			PF:          "ios",
			AccessToken: conf.AccessToken,
			MpSig:       conf.PaySecret,
		}
		balance, err := l.douyinService.GetGameBalance(ctx, p)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailCustomer] 查询抖音游戏余额失败, order_id: %s, open_id: %s, error: %v",
				req.OrderID, user.OpenID, err)
			return nil, err
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 查询抖音游戏余额成功, order_id: %s, open_id: %s, save_amt: %d",
			req.OrderID, user.OpenID, balance.SaveAmt)

		// 更新订单余额
		if err := l.orderService.UpdateOrderBalance(ctx, orderDetail.OrderID, balance.SaveAmt); err != nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailCustomer] 更新订单余额失败, order_id: %s, save_amt: %d, error: %v",
				req.OrderID, balance.SaveAmt, err)
			return nil, err
		}
		logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 更新订单余额成功, order_id: %s", req.OrderID)

		// 根据是否为小额钻石支付获取不同的支付参数（iOS客服场景专用）
		var douyinOrder *bean.DouyinOrder
		if goodsInfo.IsSmallDiamond {
			// iOS小额钻石支付参数
			douyinOrder = l.douyinService.GetIOSSmallDiamondPayParam(ctx, orderDetail.OrderID, orderDetail.Money)
			logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 获取iOS小额钻石支付参数, order_id: %s, order_amount: %d",
				req.OrderID, orderDetail.Money)
		} else {
			// iOS传统抖音虚拟支付参数 - 使用之前计算的 gameCurrency 值
			douyinOrder = l.douyinService.GetIOSGamePayParam(ctx, orderDetail.OrderID, gameCurrency)
			logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 获取iOS传统抖音支付参数, order_id: %s, game_currency: %d",
				req.OrderID, gameCurrency)
		}

		if douyinOrder == nil {
			logger.Logger.ErrorfCtx(ctx, "[GetOrderDetailCustomer] 获取抖音支付参数失败, order_id: %s", req.OrderID)
			return nil, fmt.Errorf("获取抖音支付参数失败，订单ID为空")
		}
		res.DouyinOrder = douyinOrder

	default:
		logger.Logger.WarnfCtx(ctx, "[GetOrderDetailCustomer] 未知的平台类型, order_id: %s, platform_type: %s", req.OrderID, req.PlatformType)
	}

	res.OrderDetail.PlatformType = constants.PlatformTypeIOS

	// 函数出口日志记录
	logger.Logger.InfofCtx(ctx, "[GetOrderDetailCustomer] 客服订单详情处理完成, order_id: %s, user_id: %s, game_id: %s, platform_type: %d",
		req.OrderID, orderDetail.UserID, orderDetail.GameID, res.OrderDetail.PlatformType)

	return res, nil
}

// WechatPayCallback 微信支付
func (l *OrderLogic) WechatPayCallback(ctx context.Context, r *http.Request) (*bean.WechatPayCallbackRes, error) {
	order, isRepeatedCallback, err := l.wechatPayService.WechatPayCallback(ctx, r)
	if err != nil {
		logger.Logger.Errorf("[OrderLogic] WechatPayCallback WechatPayCallback err: %s", err.Error())
		return nil, err
	}

	// 如果是重复回调或订单，直接返回成功响应
	if isRepeatedCallback {
		logger.Logger.Infof("[OrderLogic] WechatPayCallback order %s already processed (repeated: %v), returning success directly", order.OrderID, isRepeatedCallback)
		return &bean.WechatPayCallbackRes{
			Code:    "SUCCESS",
			Message: "",
		}, nil
	}

	// 检查是否已经完成上报和发货处理
	reportKey := fmt.Sprintf("wechat:pay:reported:%s", order.OrderID)
	reported, err := redis.Redis().Get(ctx, reportKey).Result()
	if err != nil && err != redis.Nil {
		logger.Logger.Errorf("[OrderLogic] WechatPayCallback check report status err: %s", err.Error())
	}

	game, err := l.userService.GetGameInfo(ctx, order.GameID)
	if err != nil {
		return nil, err
	}

	if reported == "" {
		// 查找open_id作为client_id
		minigameUser, err := l.userService.GetMinigameModel(ctx, order.UserID)
		if err != nil {
			return nil, err
		}
		clientID := minigameUser.OpenID
		goods, err := l.orderService.GetGoodsDetail(ctx, order.GoodsID)
		if err != nil {
			return nil, err
		}
		isFirstPay := l.orderService.IsFirstPay(ctx, order.UserID)

		properties := make(map[string]interface{})
		properties["$is_first_pay"] = isFirstPay
		properties["$order_id"] = order.OrderID
		properties["$pay_amount"] = order.CurrencyPrice
		properties["$pay_method"] = constants.PayChannelTypeIOS
		properties["$pay_reason"] = goods.GoodsName
		properties["$pay_type"] = constants.PayTypeCurrencyCNY
		properties["$os"] = "ios"

		// Create a separate context with a longer timeout for long operations
		longCtx, cancel := context.WithTimeout(ctx, 30*time.Second) // Adjust the timeout as needed
		defer cancel()

		if game.GravityIsEnabled == 1 {
			err = l.gravityEngineService.ReportPayEvent(longCtx, game.GravityAccessToken, clientID, properties)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "[OrderLogic] WechatPayCallback reported error: %s", err.Error())
			}
		}

		err = l.dataReportService.ReportPaySuccess(longCtx, order.UserID, "", order.GameID, game.PlatformAppID, order.OrderID, order.Money, order.CurrencyPrice, order.Status, order.PayType)
		if err != nil {
			logger.Logger.Errorf("[OrderLogic] WechatPayCallback reported error: %s", err.Error())
		}

		// Use the original context for Redis operations
		err = redis.Redis().SetEX(ctx, reportKey, "1", 24*time.Hour).Err()
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[OrderLogic] WechatPayCallback set report status err: %s", err.Error())
		}
	} else {
		logger.Logger.Infof("[OrderLogic] WechatPayCallback order %s already reported", order.OrderID)
	}

	productOrder := &bean.ProductShipmentOrder{}
	err = copier.Copy(productOrder, order)
	if err != nil {
		return nil, err
	}

	orderReq := &bean.OrderReq{
		Attempt:       0,
		Order:         productOrder,
		PlatformAppID: game.PlatformAppID,
		CallbackURL:   l.getCallbackURL(order.ShipmentCallback, game.PayCallback),
	}

	orderByte, err := json.Marshal(orderReq)
	if err != nil {
		return nil, err
	}
	_, err = task.Submit(asynq.NewTask(task.TypeProductShipmentOrder, orderByte))
	if err != nil {
		return nil, err
	}

	return &bean.WechatPayCallbackRes{
		Code:    "SUCCESS",
		Message: "",
	}, nil
}

// WechatMidasSignCallback 微信米大师回调
func (l *OrderLogic) WechatMidasSignCallback(ctx context.Context, req *bean.WechatMidasSignCallbackReq) (string, error) {
	logger.Logger.DebugfCtx(ctx, "[WechatMidasSignCallback] 收到微信米大师签名回调, gameID: %s, timestamp: %s", req.GameID, req.Timestamp)
	if req.GameID == "" {
		return "", constants.ErrGameIDIsEmpty
	}

	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return "", err
	}
	if conf == nil {
		logger.Logger.WarnfCtx(ctx, "[WechatMidasSignCallback] 小游戏配置为空, gameID: %s", req.GameID)
		return "", constants.ErrConfigIsEmpty
	}
	// 认证会话 TOKEN
	tmpStrs := []string{conf.MessageToken, req.Timestamp, req.Nonce}
	sort.Strings(tmpStrs)
	// 拼接字符串
	tmpStr := strings.Join(tmpStrs, "")
	h := sha1.New()
	if _, err := io.WriteString(h, tmpStr); err != nil {
		return "", err
	}
	hashedStr := fmt.Sprintf("%x", h.Sum(nil))
	// 比较signature, 如果请求来自微信，返回 echostr
	if hashedStr != req.Signature {
		logger.Logger.WarnfCtx(ctx, "[WechatMidasSignCallback] 签名验证失败, gameID: %s", req.GameID)
		return "", errors.New("signature mismatch")
	}
	logger.Logger.DebugfCtx(ctx, "[WechatMidasSignCallback] 微信米大师签名回调成功, gameID: %s", req.GameID)
	return req.Echostr, nil
}

func generateSignature(token, timestamp, nonce, encryptMsg string) string {
	sigElements := []string{token, timestamp, nonce, encryptMsg}
	sort.Strings(sigElements)
	sigRaw := strings.Join(sigElements, "")
	hasher := sha1.New()
	io.WriteString(hasher, sigRaw)
	return fmt.Sprintf("%x", hasher.Sum(nil))
}

func (l *OrderLogic) VerifySignature(token string, timestamp, nonce, encryptMsg, msgSign string) bool {
	genSig := generateSignature(token, timestamp, nonce, encryptMsg)
	return genSig == msgSign
}

// WechatMidasCallback 微信米大师回调
func (l *OrderLogic) WechatMidasCallback(ctx context.Context, req *bean.WechatMidasCallbackReq) (*bean.MidasResp, error) {
	logger.Logger.InfofCtx(ctx, "OrderLogic WechatMidasCallback req: %v", req)
	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return nil, err
	}

	// 校验数据的合法性
	if !l.VerifySignature(conf.MessageToken, req.Timestamp, req.Nonce, req.Encrypt, req.MsgSignature) {
		return nil, errors.New("OrderLogic WechatMidasCallback signature mismatch")
	}

	// 开始解密
	decryptMsg, err := l.decryptMsg(conf.EncodingAesKey, req.Encrypt, conf.AppID)
	if err != nil {
		return nil, fmt.Errorf("OrderLogic WechatMidasCallback decryptMsg failed: %v", err)
	}
	logger.Logger.InfofCtx(ctx, "OrderLogic WechatMidasCallback decryptMsg: %s", decryptMsg)

	// 处理Payload获取GoodsInfo
	msg := &bean.WechatMidas{}
	err = json.Unmarshal([]byte(decryptMsg), msg)
	if err != nil {
		return nil, err
	}
	if msg.MiniGame.Payload == "" {
		return nil, errors.New("OrderLogic WechatMidasCallback payload can not be empty")
	}
	payload := &bean.WechatMidasMiniGamePayload{}
	err = json.Unmarshal([]byte(msg.MiniGame.Payload), payload)
	if err != nil {
		return nil, err
	}

	// 测试
	if payload.OutTradeNo == "example_out_trade_no" {
		return &bean.MidasResp{
			ErrCode: 0,
			ErrMsg:  "Success",
		}, nil
	}

	order := store.QueryDB().AOrder
	detail, err := order.WithContext(ctx).Where(order.OrderID.Eq(payload.OutTradeNo)).First()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "WechatPayService WechatPayCallback callback find order err: %s", err.Error())
		return nil, err
	}

	if detail.Status == constants.PaymentWechatPaySuccess || detail.Status == constants.PaymentProductShipmentSuccess {
		logger.Logger.InfofCtx(ctx, "WechatPayService WechatPayCallback 订单 %s 已处于支付成功状态，可能是重复回调", payload.OutTradeNo)
		return &bean.MidasResp{
			ErrCode: 0,
			ErrMsg:  "Success",
		}, nil
	}

	// 校验 goods id 金额
	if payload.GoodsInfo.OrigPrice != detail.Money {
		return nil, fmt.Errorf("OrderLogic WechatMidasCallback 支付金额异常, 订单金额: %d, 实际支付金额: %d", detail.Money, payload.GoodsInfo.OrigPrice)
	}

	_, err = order.WithContext(ctx).Where(order.OrderID.Eq(payload.OutTradeNo)).UpdateSimple(
		order.Status.Value(constants.PaymentWechatPaySuccess),
		order.CurrencyPrice.Value(payload.GoodsInfo.ActualPrice),
		order.CallbackOriginData.Value(decryptMsg),
	)
	if err != nil {
		return nil, err
	}
	// 更新查询最新
	detail, err = order.WithContext(ctx).Where(order.OrderID.Eq(payload.OutTradeNo)).First()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "WechatPayService WechatPayCallback callback find new data order err: %s", err.Error())
		return nil, err
	}

	// 检查是否已经上报过
	reportKey := fmt.Sprintf("wechat:midas:reported:%s", payload.OutTradeNo)
	reported, err := redis.Redis().Get(ctx, reportKey).Result()
	if err != nil && err != redis.Nil {
		logger.Logger.ErrorfCtx(ctx, "[OrderLogic] WechatMidasCallback check report status err: %s", err.Error())
	}

	// 提前获取game信息，确保后续代码可以使用
	game, err := l.userService.GetGameInfo(ctx, conf.GameID)
	if err != nil {
		return nil, err
	}

	if reported == "" {
		// 查找open_id作为client_id
		minigameUser, err := l.userService.GetMinigameModel(ctx, detail.UserID)
		if err != nil {
			return nil, err
		}
		clientID := minigameUser.OpenID
		goods, err := l.orderService.GetGoodsDetail(ctx, detail.GoodsID)
		if err != nil {
			return nil, err
		}
		isFirstPay := l.orderService.IsFirstPay(ctx, detail.UserID)

		properties := make(map[string]interface{})
		properties["$is_first_pay"] = isFirstPay
		properties["$order_id"] = detail.OrderID
		properties["$pay_amount"] = detail.CurrencyPrice
		properties["$pay_method"] = constants.PayChannelTypeAndroid
		properties["$pay_reason"] = goods.GoodsName
		properties["$pay_type"] = constants.PayTypeCurrencyCNY
		properties["$os"] = "android"

		// Create a separate context with a longer timeout for long operations
		longCtx, cancel := context.WithTimeout(ctx, 30*time.Second) // Adjust the timeout as needed
		defer cancel()

		if game.GravityIsEnabled == 1 {
			err = l.gravityEngineService.ReportPayEvent(longCtx, game.GravityAccessToken, clientID, properties)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "[OrderLogic] WechatMidasCallback reported error: %s", err.Error())
			}
		}

		err = l.dataReportService.ReportPaySuccess(longCtx, detail.UserID, "", detail.GameID, game.PlatformAppID, detail.OrderID, detail.Money, detail.CurrencyPrice, detail.Status, detail.PayType)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[OrderLogic] WechatMidasCallback reported error: %s", err.Error())
		}

		// Use the original context for Redis operations
		err = redis.Redis().SetEX(ctx, reportKey, "1", 24*time.Hour).Err()
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[OrderLogic] WechatMidasCallback set report status err: %s", err.Error())
		}
	} else {
		logger.Logger.InfofCtx(ctx, "[OrderLogic] WechatMidasCallback order %s already reported", payload.OutTradeNo)
	}

	productOrder := &bean.ProductShipmentOrder{}
	err = copier.Copy(productOrder, detail)
	if err != nil {
		return nil, err
	}

	orderReq := &bean.OrderReq{
		Attempt:       0,
		Order:         productOrder,
		PlatformAppID: game.PlatformAppID,
		CallbackURL:   l.getCallbackURL(detail.ShipmentCallback, game.PayCallback),
	}

	orderByte, err := json.Marshal(orderReq)
	if err != nil {
		return nil, err
	}
	_, err = task.Submit(asynq.NewTask(task.TypeProductShipmentOrder, orderByte))
	if err != nil {
		return nil, err
	}

	return &bean.MidasResp{
		ErrCode: 0,
		ErrMsg:  "Success",
	}, nil
}

// getCallbackURL 获取回调地址（如自定义发货回调地址不为空，则使用自定义发货回调地址，否则使用默认发货回调地址）
func (l *OrderLogic) getCallbackURL(shipmentCallback, defaultCallback string) string {
	if shipmentCallback != "" {
		return shipmentCallback
	}
	return defaultCallback
}

// decryptMsg 解密回调数据
func (l *OrderLogic) decryptMsg(encodingAESKey, encryptMsg, appID string) (string, error) {
	// base64 解码
	aesKey, err := base64.StdEncoding.DecodeString(encodingAESKey + "=")
	if err != nil {
		return "", fmt.Errorf("OrderLogic decryptMsg decode encodingAESKey failed: %v", err)
	}

	cipherText, err := base64.StdEncoding.DecodeString(encryptMsg)
	if err != nil {
		return "", fmt.Errorf("OrderLogic decryptMsg decode encryptMsg failed: %v", err)
	}

	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return "", fmt.Errorf("OrderLogic decryptMsg new cipher failed: %v", err)
	}

	blockSize := block.BlockSize()
	if len(cipherText) < blockSize {
		return "", errors.New("OrderLogic decryptMsg ciphertext too short")
	}
	iv := cipherText[:blockSize]
	cipherText = cipherText[blockSize:]

	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(cipherText, cipherText)

	// 去除填充
	cipherText = cipherText[:len(cipherText)-(len(cipherText)%aes.BlockSize)]
	cipherText, err = pkcs7UnPadding(cipherText)
	if err != nil {
		return "", fmt.Errorf("OrderLogic decryptMsg pkcs7UnPadding failed: %v", err)
	}
	// 去除前4位的数据填充
	cipherText = cipherText[4:]

	appIDLength := 18
	fromAppID := cipherText[len(cipherText)-appIDLength:]
	if string(fromAppID) != appID {
		return "", errors.New("OrderLogic decryptMsg appID mismatch")
	}

	jsonData := cipherText[:len(cipherText)-appIDLength]
	return string(jsonData), nil
}

// pkcs7UnPadding 去除 PKCS#7 填充
func pkcs7UnPadding(data []byte) ([]byte, error) {
	length := len(data)
	if length == 0 {
		return nil, errors.New("OrderLogic pkcs7UnPadding data is empty")
	}

	// 判断填充是否合法
	padding := int(data[length-1])
	if padding < 1 || padding > length {
		return nil, errors.New("OrderLogic pkcs7UnPadding invalid padding")
	}

	// 检查填充字节是否都相同
	for i := length - padding; i < length-1; i++ {
		if data[i] != byte(padding) {
			return nil, errors.New("OrderLogic pkcs7UnPadding invalid padding")
		}
	}

	return data[:(length - padding)], nil
}

// WechatH5PayCallback
//func (l *OrderLogic) WechatH5PayCallback(ctx context.Context, req *bean.WechatPayCallback) (*bean.WechatPayCallbackRes, error) {
//	resp, err := l.wechatPayService.WechatPayCallback(ctx, req)
//	if err != nil {
//		return nil, err
//	}
//	return resp, nil
//}

// WechatCustomerSignCallback 会话
func (l *OrderLogic) WechatCustomerSignCallback(ctx context.Context, req *bean.WechatCustomerSignCallbackReq) (string, error) {
	if req.GameID == "" {
		return "", constants.ErrGameIDIsEmpty
	}

	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return "", err
	}
	if conf == nil {
		return "", constants.ErrConfigIsEmpty
	}
	// 认证会话 TOKEN
	tmpStrs := []string{conf.MessageToken, req.Timestamp, req.Nonce}
	sort.Strings(tmpStrs)
	// 拼接字符串
	tmpStr := strings.Join(tmpStrs, "")
	h := sha1.New()
	if _, err := io.WriteString(h, tmpStr); err != nil {
		return "", err
	}
	hashedStr := fmt.Sprintf("%x", h.Sum(nil))
	// 比较signature, 如果请求来自微信，返回 echostr
	if hashedStr != req.Signature {
		return "", errors.New("signature mismatch")
	}
	return req.Echostr, nil
}

// WechatCustomerCallback 微信客服回调
//func (l *OrderLogic) WechatCustomerCallback(ctx context.Context, req *bean.WechatCustomerCallbackReq) (string, error) {
//	logger.Logger.Debugf("WechatCustomerCallback req: %+v", req)
//
//	if req.GameID == "" {
//		return "", constants.ErrGameIDIsEmpty
//	}
//	if req.MsgType != "miniprogrampage" {
//		logger.Logger.Infof("WechatCustomerCallback not support msg type: %s", req.MsgType)
//		return "", nil
//	}
//
//	// 解析PagePath值为index?bkx_order_id=xxxxx
//	params, err := url.ParseQuery(strings.Split(req.PagePath, "?")[1])
//	if err != nil {
//		logger.Logger.Errorf("WechatCustomerCallback parse url failed: %v", err)
//		return "", err
//	}
//	orderID := params.Get("bkx_order_id")
//	content := fmt.Sprintf("%s?order_id=%s", config.GlobConfig.WechatPay.CustomerPayURL, orderID)
//	logger.Logger.Infof("WechatCustomerCallback call back url is :%s", content)
//
//	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
//	if err != nil {
//		return "", err
//	}
//	if conf == nil {
//		return "", constants.ErrConfigIsEmpty
//	}
//	err = l.minigameService.SendCustomerService(ctx, conf.AccessToken, req.FromUserName, content)
//	if err != nil {
//		return "", err
//	}
//	return "", nil
//}

// WechatCustomerCallback 微信客服回调
func (l *OrderLogic) WechatCustomerCallback(ctx context.Context, req *bean.WechatCustomerCallbackReq) (string, error) {
	logger.Logger.InfofCtx(ctx, "WechatCustomerCallback START - MsgType: %s, Event: %s, FromUserName: %s", req.MsgType, req.Event, req.FromUserName)
	logger.Logger.DebugfCtx(ctx, "WechatCustomerCallback req: %+v", req)

	if req.GameID == "" {
		return "", constants.ErrGameIDIsEmpty
	}
	// 校验游戏id是否存在
	info, err := l.userService.GetGameInfo(ctx, req.GameID)
	if err != nil {
		return "", err
	}
	if info == nil {
		return "", constants.ErrConfigIsEmpty
	}
	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return "", err
	}
	if conf == nil {
		return "", constants.ErrConfigIsEmpty
	}

	if req.MsgType == constants.WechatMsgActionEvent && req.Event == constants.WechatEventMinigameDeliverGoods {
		return l.handleDeliverGoods(ctx, req, conf)
	}

	if req.MsgType == constants.WechatMsgTypeMiniprogram && strings.Contains(req.PagePath, constants.IOSPayPagePath) { // 严格认定此条件为iOS支付
		return l.handleIOSPayment(ctx, req, conf)
	}

	if req.MsgType == constants.WechatMsgTypeText && (req.Content == constants.PayTypeCZ || req.Content == constants.PayTypeRecharge) { // 严格认定此条件为iOS支付
		order, err := l.orderService.GetLastWaitPayOrderByOpenID(ctx, req.FromUserName)
		if err != nil {
			return "", err
		}
		if order == nil {
			return "", nil
		}
		// index?bkx_order_id=869f76c49ef145e7adaea67aafdce628
		req.PagePath = fmt.Sprintf("%s%s", constants.IOSPayPagePath, order.OrderID)
		return l.handleIOSPayment(ctx, req, conf)
	}

	if req.MsgType == constants.WechatMsgActionEvent && req.Event == constants.WechatEventKfCreateSession {
		logger.Logger.InfofCtx(ctx, "WechatCustomerCallback kf_create_session, user nam: %s", req.FromUserName)
		return "", nil
	}

	if req.MsgType == constants.WechatMsgActionEvent && req.Event == constants.WechatEventKfCloseSession {
		logger.Logger.InfofCtx(ctx, "WechatCustomerCallback kf_close_session, user nam: %s", req.FromUserName)
		return "", nil
	}
	// user_authorization_revoke
	if req.MsgType == constants.WechatMsgActionEvent && req.Event == constants.WechatEventUserAuthorizationRevoke {
		logger.Logger.InfofCtx(ctx, "WechatCustomerCallback user authorization revoke, user name: %s", req.FromUserName)
		return "", nil
	}
	// subscribe_msg_popup_event
	if req.MsgType == constants.WechatMsgActionEvent && req.Event == constants.WechatEventSubscribeMsgPopup {
		logger.Logger.InfofCtx(ctx, "WechatCustomerCallback BLOCKED subscribe_msg_popup_event, user name: %s", req.FromUserName)
		return "", nil
	}

	// subscribe_msg_sent_event
	if req.MsgType == constants.WechatMsgActionEvent && req.Event == constants.WechatEventSubscribeMsgSent {
		logger.Logger.InfofCtx(ctx, "WechatCustomerCallback BLOCKED subscribe_msg_sent_event, user name: %s", req.FromUserName)
		return "", nil
	}

	if req.MsgType == constants.WechatMsgActionEvent && req.Event == constants.WechatEventUserEnterTempsession {
		logger.Logger.InfofCtx(ctx, "WechatCustomerCallback user enter tempsession, user name: %s", req.FromUserName)

		if req.SessionFrom != "" && strings.TrimSpace(req.SessionFrom) != "" {
			userSessionFrom := &bean.UserSessionFrom{}
			err := json.Unmarshal([]byte(req.SessionFrom), userSessionFrom)
			if err != nil {
				logger.Logger.WarnfCtx(ctx, "WechatCustomerCallback user enter tempsession unmarshal session_from failed: %v", err)
				return "", nil
			}

			if userSessionFrom.IsBkxIOSPay {
				// 获取 userSessionFrom.BkxOrderID中的值，之后像handleIOSPayment函数那样发送消息给用户
				if userSessionFrom.BkxOrderID != "" {
					content := fmt.Sprintf("%s?order_id=%s", config.GlobConfig.WechatPay.CustomerPayURL, userSessionFrom.BkxOrderID)
					logger.Logger.InfofCtx(ctx, "WechatCustomerCallback user enter tempsession iOS pay callback url: %s", content)

					// 如果配置的图片为空，则使用默认图片
					if conf.CsPaymentSmallPic == "" {
						conf.CsPaymentSmallPic = constants.ThumbURL
					}
					if err := l.minigameService.SendCustomerService(ctx, conf.AccessToken, req.FromUserName, content, conf.CsPaymentSmallPic); err != nil {
						logger.Logger.ErrorfCtx(ctx, "WechatCustomerCallback user enter tempsession failed to send customer service message: %v", err)
					}
				}
			}

			if userSessionFrom.PlayerID != "" {
				// 存储到数据库
				err = l.userService.UpdateUserSessionFrom(ctx, req.FromUserName, userSessionFrom)
				if err != nil {
					return "", err
				}
				// 数数埋点
				if err := l.dataReportService.ReportUserEnterTempsession(ctx, req.FromUserName, info.GameID, userSessionFrom); err != nil {
					logger.Logger.ErrorfCtx(ctx, "WechatCustomerCallback ReportUserEnterTempsession failed: %v", err)
					// 打点异常不return，继续执行
				}
			}
		}
		return "", nil
	}

	// 组装发送的数据是否匹配后台管理系统
	messages, err := l.customerService.GetCustomerServiceMsg(ctx, req.GameID, constants.PlatformTypeMinigame)
	if err != nil {
		return "", err
	}

	var (
		msgType = ""
		content = make(map[string]interface{})
	)
	for _, m := range messages {
		if (req.MsgType == constants.WechatMsgActionEvent || req.MsgType == constants.WechatMsgTypeMiniprogram) &&
			(m.Scenes == constants.WechatSceneEnterInto || m.Scenes == constants.WechatSceneSendCard) { // 用户进入客服消息、用户发送小程序卡片
			// req.PagePath 匹配参数值 platformReplyType = "index?platformReplyType=xxx"
			logger.Logger.Infof("WechatCustomerCallback insert event or minigame scenes = 1 or 3: %+v", req)

			originPath, platformReplyType := l.getPlatformReplyType(req.PagePath)
			if platformReplyType != m.MatchParam { // 匹配path的值
				logger.Logger.Infof("WechatCustomerCallback MatchParam 未匹配到%s%s", req.GameID, req.FromUserName)
				return "", nil
			}
			// 匹配完成后, 将 originPath 还原给 req.PagePath
			req.PagePath = originPath
			logger.Logger.Infof("WechatCustomerCallback origin req.PagePath: %s", req.PagePath)

			msgType, content, err = l.handleReplyForWechat(ctx, m.ReplyType, conf.AccessToken, info.CustomerServiceCallback, req, m)
			if err != nil {
				return "", err
			}
			if msgType == "" && content == nil { // 透传给服务器，无需继续执行
				return "", nil
			}
		} else if (req.MsgType == constants.WechatMsgTypeText || req.MsgType == constants.WechatMsgTypeImage) &&
			m.Scenes == constants.WechatSceneSend {

			msgType, content, err = l.handleTextOrImageMessage(ctx, m, req, conf, info.CustomerServiceCallback)
			if err != nil {
				return "", err
			}
			if msgType != "" && content != nil {
				break
			}
		}
	}
	if msgType == "" || content == nil {
		msgType, content, err = l.handleMissedMessage(ctx, req, messages, conf.AccessToken, info.CustomerServiceCallback)
		if err != nil || (msgType == "" && content == nil) {
			//  其他未配置 ， 则走七鱼机器人回复
			logger.Logger.Infof("WechatCustomerCallback handleMissedMessage no message, use qiyu robot reply")
			replyContent, err := l.handleQiyuRobotChatReply(ctx, req.Content, req.FromUserName, info)
			if err != nil {
				return "", err
			}
			if replyContent == "" {
				logger.Logger.InfofCtx(ctx, "WechatCustomerCallback handleQiyuRobotChatReply replyContent is empty")
				return "", nil
			}
			// 如果包含*，则查询filter表，之后过滤对应的文案
			contentList, err := l.FilteredQuestions(ctx, req.GameID)
			if err != nil {
				return "", err
			}
			replyContent = l.filterContent(replyContent, contentList)

			logger.Logger.InfofCtx(ctx, "WechatCustomerCallback handleQiyuRobotChatReply origin reply: %s", replyContent)
			// 当发现包含https时，进行参数拼接
			if strings.Contains(replyContent, constants.HttpPrefix) {
				// 读取session_from
				userSessionFrom, err := l.userService.GetUserSessionFrom(ctx, req.FromUserName)
				if err != nil {
					return "", err
				}
				if userSessionFrom == nil {
					paramsMap := map[string]string{
						"game_id":   info.GameID,
						"game_name": encodeToBase64(info.Name),
					}
					replyContent = l.handleLinkAddParams(replyContent, paramsMap)
				} else {
					// 进入客服转发
					paramsMap, err := l.buildParamsMapFromSessionFrom(userSessionFrom, info)
					if err != nil {
						return "", err
					}
					replyContent = l.handleLinkAddParams(replyContent, paramsMap)
				}
			}
			replyContent = l.convertHTMLToWechatText(replyContent)
			logger.Logger.InfofCtx(ctx, "WechatCustomerCallback last reply handleQiyuRobotReply reply: %s", replyContent)
			msgType, content, err = l.handleTextReply(replyContent)
			if err != nil {
				return "", err
			}
		}
	}
	if err := l.minigameService.SendWechatCustomerService(ctx, conf.AccessToken, req.FromUserName, msgType, content); err != nil {
		logger.Logger.ErrorfCtx(ctx, "WechatCustomerCallback SendWechatCustomerService failed: %v", err)
		return "", err
	}
	return "", nil
}

// handleQiyuRobotReply 处理七鱼机器人回复
func (l *OrderLogic) handleQiyuRobotReply(ctx context.Context, fromUserName string, info *model.MGame, conf *model.AConfigMinigame) error {
	headContent, list, err := l.handleQiyuRobotCreateReply(ctx, fromUserName, info)
	if err != nil {
		return err
	}
	logger.Logger.InfofCtx(ctx, "WechatCustomerCallback user enter handleQiyuRobotReply reply: %s, list: %+v", headContent, list)

	msgType, content, err := l.handleMenuReply(headContent, list)
	if err != nil {
		return err
	}
	if err := l.minigameService.SendWechatCustomerService(ctx, conf.AccessToken, fromUserName, msgType, content); err != nil {
		return err
	}
	return nil
}

// handleLinkAddParams
func (l *OrderLogic) handleLinkAddParams(link string, params map[string]string) string {
	// link 是由a标签组成的，帮我拼接后续参数，示例 game_id=12345&game_name=xxxx
	// <a href="https://www.baidu.com?game_id=12345&game_name=xxxx">
	startIndex := strings.Index(link, "href=\"")
	if startIndex == -1 {
		return link
	}
	startIndex += 6 // length of 'href="'

	endIndex := strings.Index(link[startIndex:], "\"")
	if endIndex == -1 {
		return link
	}
	endIndex += startIndex

	href := link[startIndex:endIndex]

	// 查找 "cq=" 的位置
	cqIndex := strings.Index(href, "cq=")
	if cqIndex == -1 {
		// 如果没有找到 "cq="，直接返回原始链接
		return link
	}

	// 分割 URL
	baseURL := href[:cqIndex+3] // 包含 "cq="
	existingParams := href[cqIndex+3:]

	// 编码 cq= 之后的所有内容
	encodedExisting := url.QueryEscape(existingParams)

	// 编码新参数
	var newParams []string
	for k, v := range params {
		newParams = append(newParams, url.QueryEscape(fmt.Sprintf("%s=%s", k, v)))
	}

	// 组合所有参数
	var allParams string
	if encodedExisting != "" {
		allParams = encodedExisting
		if len(newParams) > 0 {
			allParams += url.QueryEscape("&") + strings.Join(newParams, url.QueryEscape("&"))
		}
	} else {
		allParams = strings.Join(newParams, url.QueryEscape("&"))
	}

	// 组合新的 href
	newHref := baseURL + allParams

	// 构建新的链接
	newLink := link[:startIndex] + newHref + link[endIndex:]
	return newLink
}

func (l *OrderLogic) convertHTMLToWechatText(htmlText string) string {
	tokenizer := html.NewTokenizer(strings.NewReader(htmlText))
	var result strings.Builder
	var boldText strings.Builder
	var inBold bool
	var currentLink string

	for {
		tokenType := tokenizer.Next()
		switch tokenType {
		case html.ErrorToken:
			return strings.TrimSpace(result.String())
		case html.StartTagToken, html.SelfClosingTagToken:
			l.handleStartTag(tokenizer, &result, &inBold, &boldText, &currentLink)
		case html.TextToken:
			l.handleTextToken(tokenizer, &result, &boldText, inBold, currentLink)
		case html.EndTagToken:
			l.handleEndTag(tokenizer, &result, &inBold, &boldText)
		}
	}
}

func (l *OrderLogic) handleStartTag(tokenizer *html.Tokenizer, result *strings.Builder, inBold *bool, boldText *strings.Builder, currentLink *string) {
	token := tokenizer.Token()
	switch token.Data {
	case "p", "div":
		// 只在非空结果后添加换行符
		if result.Len() > 0 {
			result.WriteString("\n")
		}
	case "br":
		result.WriteString("\n")
	case "a":
		for _, attr := range token.Attr {
			if attr.Key == "href" {
				*currentLink = attr.Val
				break
			}
		}
	case "b", "strong":
		*inBold = true
		boldText.Reset()
	}
}

func (l *OrderLogic) handleTextToken(tokenizer *html.Tokenizer, result *strings.Builder, boldText *strings.Builder, inBold bool, currentLink string) {
	text := strings.TrimSpace(string(tokenizer.Text()))
	if text == "" {
		return
	}

	if inBold {
		boldText.WriteString(text)
	} else if currentLink != "" {
		l.handleLink(result, currentLink, text)
	} else {
		result.WriteString(text)
	}
	result.WriteString(" ")
}

func (l *OrderLogic) handleLink(result *strings.Builder, link, text string) {
	if strings.HasPrefix(link, "weixin://") {
		result.WriteString(link)
	} else if strings.HasPrefix(link, "http://") || strings.HasPrefix(link, "https://") {
		result.WriteString(fmt.Sprintf("<a href=\"%s\">%s</a>", link, text))
	} else {
		result.WriteString(text)
	}
}

func (l *OrderLogic) handleEndTag(tokenizer *html.Tokenizer, result *strings.Builder, inBold *bool, boldText *strings.Builder) {
	token := tokenizer.Token()
	switch token.Data {
	case "p", "div":
		// 移除这里的换行符
	case "b", "strong":
		*inBold = false
		boldTextStr := boldText.String()
		if boldTextStr != "" {
			result.WriteString(fmt.Sprintf("<a href=\"weixin://bizmsgmenu?msgmenucontent=%s&msgmenuid=%s\">%s</a>",
				url.QueryEscape(boldTextStr),
				url.QueryEscape(boldTextStr),
				boldTextStr))
			// 如果是"联系客服"，则在之后添加一个额外的换行符
			if boldTextStr == "联系客服" {
				result.WriteString("\n")
			}
		}
	}
}

func (l *OrderLogic) getPlatformReplyType(pagePath string) (string, string) {
	parts := strings.SplitN(pagePath, "?platformReplyType=", 2)
	if len(parts) < 2 {
		return "", ""
	}
	return parts[0], parts[1]
}

// handleQiyuRobotReply
func (l *OrderLogic) handleQiyuRobotCreateReply(ctx context.Context, fromUserName string, gameConf *model.MGame) (string, []map[string]string, error) {
	sessionResp, err := l.qiyuService.CreateSession(ctx, gameConf.QiyuAppKey, gameConf.QiyuAppSecret, &bean.CreateSessionReq{
		UID: fromUserName,
		// WelcomeTemplateID:  0,
		// FromType:           constants.DefaultFromType,
		// QuestionTemplateID: constants.DefaultQuestionTemplateID,
	})
	if err != nil {
		return "", nil, err
	}
	welcomeMsg := sessionResp.Data.Welcome.MsgBody.Content
	questionList := make([]map[string]string, 0)

	// 通过gameConf查询filter，如有值，则过滤问题列表
	contentList, err := l.FilteredQuestions(ctx, gameConf.GameID)
	if err != nil {
		return "", nil, err
	}

	for _, question := range sessionResp.Data.CommonQuestions.QuestionList {
		shouldAdd := true

		// 检查问题是否在过滤列表中
		for _, filter := range contentList {
			if filter == question.Question {
				shouldAdd = false
				break
			}
		}

		// 如果问题不在过滤列表中，则添加到结果中
		if shouldAdd {
			questionList = append(questionList, map[string]string{
				"id":      fmt.Sprint(question.ID),
				"content": question.Question,
			})
		}
	}

	return welcomeMsg, questionList, nil
}

func (l *OrderLogic) handleQiyuRobotChatReply(ctx context.Context, content, fromUserName string, gameConf *model.MGame) (string, error) {
	sessionResp, err := l.qiyuService.SessionChat(ctx, gameConf.QiyuAppKey, gameConf.QiyuAppSecret, &bean.SessionChatReq{
		UID: fromUserName,
		// FromType: constants.DefaultFromType,
		Msg: bean.CommonMsg{MsgType: constants.WechatMsgTypeText, MsgBody: bean.MsgBody{Content: content}},
	})
	if err != nil {
		return "", err
	}
	if len(sessionResp.Data.AnswerList) == 0 {
		return "", nil
	}
	return sessionResp.Data.AnswerList[0].AnsContent.Answer.MsgBody.Content, nil
}

func (l *OrderLogic) parseURL(pagePath string) (url.Values, error) {
	parts := strings.Split(pagePath, "?")
	if len(parts) < 2 {
		return nil, fmt.Errorf("invalid pagePath format")
	}
	return url.ParseQuery(parts[1])
}

func (l *OrderLogic) handleTextOrImageMessage(ctx context.Context,
	m *model.MCustomerServiceMessage, req *bean.WechatCustomerCallbackReq, conf *model.AConfigMinigame, callback string,
) (string, map[string]interface{}, error) {
	var text []string
	err := json.Unmarshal([]byte(m.AcceptText), &text)
	if err != nil {
		logger.Logger.Errorf("parse url failed: %v", err)
		return "", nil, err
	}
	for _, t := range text {
		regexContent := regexp.QuoteMeta(req.Content)
		matched, err := regexp.MatchString(t, regexContent)
		if err != nil {
			return "", nil, fmt.Errorf("regex match error: %v", err)
		}
		if matched {
			return l.handleReplyForWechat(ctx, m.ReplyType, conf.AccessToken, callback, req, m)
		}
	}
	return "", nil, nil
}

func (l *OrderLogic) handleMissedMessage(ctx context.Context,
	req *bean.WechatCustomerCallbackReq, messages []*model.MCustomerServiceMessage, token, callback string,
) (string, map[string]interface{}, error) {
	for _, m := range messages {
		if (req.MsgType == constants.WechatMsgTypeText || req.MsgType == constants.WechatMsgTypeImage) &&
			m.Scenes == constants.WechatSceneMiss {
			return l.handleReplyForWechat(ctx, m.ReplyType, token, callback, req, m)
		}
	}
	return "", nil, nil
}

// handleDeliverGoods 处理游戏礼包回调
func (l *OrderLogic) handleDeliverGoods(ctx context.Context, req *bean.WechatCustomerCallbackReq, _ *model.AConfigMinigame) (string, error) {
	gameInfo, err := l.userService.GetGameInfo(ctx, req.GameID)
	if err != nil {
		return "", nil
	}
	return l.handleServerTransferGameGift(ctx, req, gameInfo.CustomerServiceCallback)
}

// handleIOSPayment 处理iOS H5支付
func (l *OrderLogic) handleIOSPayment(ctx context.Context, req *bean.WechatCustomerCallbackReq, conf *model.AConfigMinigame) (string, error) {
	params, err := url.ParseQuery(strings.Split(req.PagePath, "?")[1])
	if err != nil {
		return "", fmt.Errorf("failed to parse url: %w", err)
	}
	orderID := params.Get("bkx_order_id")
	content := fmt.Sprintf("%s?order_id=%s", config.GlobConfig.WechatPay.CustomerPayURL, orderID)
	logger.Logger.Infof("WechatCustomerCallback call back url is :%s", content)

	// 如果配置的图片为空，则使用默认图片
	if conf.CsPaymentSmallPic == "" {
		conf.CsPaymentSmallPic = constants.ThumbURL
	}
	if err := l.minigameService.SendCustomerService(ctx, conf.AccessToken, req.FromUserName, content, conf.CsPaymentSmallPic); err != nil {
		return "", fmt.Errorf("failed to send customer service message: %w", err)
	}

	return "", nil
}

func (l *OrderLogic) handleReplyForWechat(ctx context.Context, replyType int32, accessToken, customerServiceCallback string, req *bean.WechatCustomerCallbackReq, m *model.MCustomerServiceMessage) (string, map[string]interface{}, error) {
	logger.Logger.Infof("formatReplyForWechat replyType: %d", replyType)
	logger.Logger.Infof("formatReplyForWechat customerServiceCallback: %s", customerServiceCallback)
	// logger.Logger.Infof("formatReplyForWechat accessToken: %s", accessToken) // 不要记录敏感信息
	logger.Logger.Infof("formatReplyForWechat req: %+v", req)
	logger.Logger.Infof("formatReplyForWechat m: %+v", m)

	switch replyType {
	case 1: // 文本
		return l.handleTextReply(m.ReplyContent)
	case 2: // 图片
		return l.handleImageReply(ctx, accessToken, req.GameID, m.PicURL)
	case 3: // 透传服务器，由服务器处理
		return l.handleServerTransfer(ctx, req, customerServiceCallback)
	case 5: // 链接
		return l.handleLinkReply(m.Title, m.Description, m.Link, m.PicURL)
	}
	logger.Logger.Infof("formatReplyForWechat no replyType: %d", replyType)
	return "", nil, nil
}

// handleMiniProgramCard
// func (l *OrderLogic) handleMiniProgramCard(_ string) (string, map[string]interface{}, error) {
// 	return constants.WechatMsgTypeMiniprogram, map[string]interface{}{
// 		constants.WechatMsgTypeMiniprogram: map[string]interface{}{
// 			"title":          "客服工单系统",
// 			"app_id":         "wxa31620f1980fcd3b",
// 			"page_path":      "pages/index/index",
// 			"thumb_media_id": "k5uzI-pAke_oItIH4C6naUJJYF1w_mz934qtVVoTceVnHrR35iX3D8x3a2roUObT",
// 		},
// 	}, nil
// }

func (l *OrderLogic) handleTextReply(replyContent string) (string, map[string]interface{}, error) {
	return constants.WechatMsgTypeText, map[string]interface{}{
		constants.WechatMsgTypeText: map[string]interface{}{
			"content": replyContent,
		},
	}, nil
}

// handleImageReply
func (l *OrderLogic) handleImageReply(ctx context.Context, accessToken, gameID string, picURL string) (string, map[string]interface{}, error) {
	mediaID, err := l.customerService.FetchMediaID(ctx, accessToken, gameID, picURL)
	if err != nil {
		return "", nil, err
	}
	return constants.WechatMsgTypeImage, map[string]interface{}{
		constants.WechatMsgTypeImage: map[string]interface{}{
			"media_id": mediaID,
		},
	}, nil
}

// handleServerTransfer
func (l *OrderLogic) handleServerTransfer(ctx context.Context, req *bean.WechatCustomerCallbackReq, customerServiceCallback string,
) (string, map[string]interface{}, error) {
	sendReq := &bean.CallbackMsgRequest{
		GameID:      req.GameID,
		UserID:      req.FromUserName,
		OpenID:      req.FromUserName,
		SessionFrom: req.SessionFrom,
		MsgType:     req.MsgType,
		Event:       req.Event,
		// Content:     req.Content,
	}
	// { "title": "title", //标题 "app_id": "appid", //小程序appid "page_path": "page path", 小程序页面路径 "thumb_url":
	// "thumb url", //封面图片的临时cdn链接 "thumb_media_id": "thumb media id" //封面图片的临时素材id }
	switch req.MsgType {
	case constants.WechatMsgTypeText:
		sendReq.Content = req.Content
	case constants.WechatMsgTypeImage:
		sendReq.Content = req.PicUrl
	case constants.WechatMsgActionEvent:
		sendReq.Content = req.Event
	case constants.WechatMsgTypeMiniprogram:
		sendReq.Body = map[string]interface{}{
			"title":          req.Title,
			"app_id":         req.AppID,
			"page_path":      req.PagePath,
			"thumb_url":      req.ThumbUrl,
			"thumb_media_id": req.ThumbMediaID,
		}
	}
	// 触发回调的情况下，将信息发送给游戏服务器
	err := l.customerService.CallbackMsgToServer(ctx, req.GameID, customerServiceCallback, sendReq)
	if err != nil {
		return "", nil, err
	}
	return "", nil, nil
}

// handleServerTransferGameGift
func (l *OrderLogic) handleServerTransferGameGift(ctx context.Context, req *bean.WechatCustomerCallbackReq, customerServiceCallback string,
) (string, error) {
	sendReq := &bean.CallbackMsgRequest{
		GameID:      req.GameID,
		UserID:      req.FromUserName,
		OpenID:      req.FromUserName,
		SessionFrom: req.SessionFrom,
		MsgType:     req.MsgType,
		Event:       req.Event,
	}
	sendBody, err := json.Marshal(req.MiniGame)
	if err != nil {
		return "", err
	}
	sendReq.Body = sendBody
	logger.Logger.Infof("handleServerTransferGameGift sendReq: %+v, customerServiceCallback: %s", sendReq, customerServiceCallback)
	// 触发回调的情况下，将信息发送给游戏服务器
	err = l.customerService.CallbackMsgToServer(ctx, req.GameID, customerServiceCallback, sendReq)
	if err != nil {
		return "", err
	}
	return "{\"ErrCode\":0,\"ErrMsg\":\"Success\"}", nil // TODO 回包，待确认是否正确
}

// handleLinkReply
func (l *OrderLogic) handleLinkReply(title, description, url, picURL string) (string, map[string]interface{}, error) {
	return constants.WechatMsgTypeLink, map[string]interface{}{
		constants.WechatMsgTypeLink: map[string]interface{}{
			"title":       title,
			"description": description,
			"url":         url,
			"thumb_url":   picURL,
		},
	}, nil
}

// handleMenuReply
func (l *OrderLogic) handleMenuReply(headContent string, list []map[string]string) (string, map[string]interface{}, error) {
	// 构建菜单消息
	menuText := headContent + "\n\n\n"
	for _, item := range list {
		// 使用微信支持的链接格式创建可点击的蓝色文本，点击后自动填充到输入框
		menuText += fmt.Sprintf("<a href=\"weixin://bizmsgmenu?msgmenucontent=%s&msgmenuid=%s\">%s</a>\n",
			url.QueryEscape(item["content"]),
			item["id"],
			item["content"])
	}
	menuText += "\n您可以点击上方蓝色选项，或直接描述您想要反馈的问题~\n\n如果您在咨询过程中想回到主菜单，可以输入【*】返回主菜单哦~"

	return constants.WechatMsgTypeText, map[string]interface{}{
		constants.WechatMsgTypeText: map[string]interface{}{
			"content": menuText,
		},
	}, nil
}

// GetSubscribeConfig 获取订阅配置
func (l *OrderLogic) GetSubscribeConfig(ctx context.Context, req *bean.GetSubscribeConfigReq) (*bean.ConfigSign, error) {
	ticket, err := l.subscribeService.GetSubscribeConfig(ctx)
	if err != nil {
		logger.Logger.Errorf("[OrderLogic] GetOrderDetailH5Pay getting subscribe err: %v", err)
		return nil, err
	}
	signature, nonceStr, timestamp := util.GenerateSignature(ticket.Ticket, req.URL)
	return &bean.ConfigSign{
		AppID:     ticket.AppID,
		Signature: signature,
		NonceStr:  nonceStr,
		TimeStamp: timestamp,
	}, nil
}

// GetOrderDetailH5Pay H5支付
func (l *OrderLogic) GetOrderDetailH5Pay(ctx context.Context, req *bean.GetOrderDetailH5PayReq) (*bean.GetOrderDetailH5PayRes, error) {
	logger.Logger.InfofCtx(ctx, "GetOrderDetailH5Pay req: %+v", req)

	// 检测req.OrderID的合法性，规则为必须是当年用户user_id的最新的订单，否则返回错误 ErrOrderExpired
	orderDetail, err := l.orderService.GetOrderDetail(ctx, req.OrderID)
	if err != nil {
		return nil, err
	}

	if isLatest, err := l.orderService.VerifyOrderLatest(ctx, orderDetail.UserID, orderDetail.GoodsID, req.OrderID); err != nil || !isLatest {
		return nil, constants.ErrOrderExpired
	}
	logger.Logger.InfofCtx(ctx, "GetOrderDetailH5Pay orderDetail: %+v", orderDetail)

	if err := l.verifyUnionID(ctx, orderDetail.GameID, orderDetail.UserID, req.UserID); err != nil {
		return nil, err
	}

	// 如果存在预定单，则直接下发订单配置信息
	if orderDetail.PrepayID != "" {
		payData := &bean.JSAPIWechatPayData{}
		err = json.Unmarshal([]byte(orderDetail.TransactionsInfo), payData)
		if err != nil {
			return nil, err
		}
		return &bean.GetOrderDetailH5PayRes{
			OrderDetail:   orderDetail,
			WechatPayData: payData,
		}, nil
	}

	goodsInfo, err := l.orderService.GetGoodsDetail(ctx, orderDetail.GoodsID)
	if err != nil {
		return nil, err
	}
	//minigameConf, err := l.minigameService.GetMinigameConfig(ctx, orderDetail.GameID)
	//if err != nil {
	//	return nil, err
	//}
	//companyConf, err := l.minigameService.GetCompanyConfig(ctx, orderDetail.GameID)
	companyConf, err := l.minigameService.GetCompanyConfig(ctx, "") // 获取系统唯一商户号，暂不指定game id
	if err != nil {
		return nil, err
	}
	ticket, err := l.subscribeService.GetSubscribeConfig(ctx)
	if err != nil {
		logger.Logger.Errorf("[OrderLogic] GetOrderDetailH5Pay getting subscribe err: %v", err)
		return nil, err
	}
	// 调用 JSAPI 生成预定单
	jsApiReq := &bean.PayTransactionsJSAPI{ // Attach:      "",
		AppID:       ticket.AppID, // 使用公众号的app_id
		MchID:       companyConf.WechatPayMchID,
		Description: goodsInfo.Description,
		OutTradeNo:  orderDetail.OrderID,
		// NotifyUrl:   minigameConf.PayCallback,
		NotifyUrl:   config.GlobConfig.WechatPay.H5PayCallbackURL, // 使用服务器回调地址
		AmountTotal: orderDetail.Money,
		PayerOpenID: req.OpenID, // 使用公众号的open_id
	}
	logger.Logger.Infof("GetOrderDetailH5Pay jsApiReq: %+v", jsApiReq)

	transactionsInfo, err := l.wechatPayService.PayTransactionsJSAPI(ctx, jsApiReq)
	if err != nil {
		logger.Logger.Errorf("[OrderLogic] GetOrderDetailH5Pay PayTransactionsJSAPI err: %v", err)
		return nil, err
	}
	transactionsInfoStr, err := json.Marshal(transactionsInfo)
	if err != nil {
		return nil, err
	}
	updateOrder := map[string]interface{}{
		"prepay_id":         transactionsInfo.PrepayID,
		"transactions_info": transactionsInfoStr,
		"status":            constants.PaymentOrderWait,
	}
	err = l.orderService.UpdateOrderStatus(ctx, orderDetail.OrderID, updateOrder)
	if err != nil {
		return nil, err
	}
	orderDetail.Status = constants.PaymentOrderWait
	// 构建
	return &bean.GetOrderDetailH5PayRes{
		OrderDetail:   orderDetail,
		WechatPayData: transactionsInfo,
	}, nil
}

// DouyinPaySignCallback 微信支付回调
func (l *OrderLogic) DouyinPaySignCallback(ctx context.Context, req *bean.DouyinPaySignCallbackReq) (string, error) {
	logger.Logger.Debugf("DouyinPaySignCallback req: %+v", req)
	conf, err := l.douyinService.GetDouyinConf(ctx, req.GameID)
	if err != nil {
		return "", err
	}
	if conf == nil || conf.PayToken == "" {
		return "", constants.ErrConfigIsEmpty
	}

	if ok := l.douyinService.VerifySign(conf.PayToken, req.Timestamp, req.Nonce, req.Msg, req.Signature); !ok {
		return "", errors.New("DouyinPaySignCallback signature mismatch")
	}
	return req.EchoStr, nil
}

// DouyinPayCallback 抖音支付回调处理
func (l *OrderLogic) DouyinPayCallback(ctx context.Context, req *bean.DouyinPayCallbackReq) (map[string]string, error) {
	logger.Logger.InfofCtx(ctx, "[DouyinPayCallback] 开始处理抖音支付回调, game_id: %s", req.GameID)

	// 解析订单信息
	var info bean.DouyinOrderSuccessPayInfo
	if err := json.Unmarshal([]byte(req.Msg), &info); err != nil {
		logger.Logger.ErrorfCtx(ctx, "[DouyinPayCallback] 解析订单信息失败: %v", err)
		return nil, bizerrors.Wrap(err, "unmarshal order info")
	}

	logger.Logger.InfofCtx(ctx, "[DouyinPayCallback] 解析订单信息成功, order_id: %s, amount_cent: %d", info.CpOrderNo, info.AmountCent)

	// 1. 添加分布式锁保护，防止同一订单并发处理
	lockKey := fmt.Sprintf("douyin_pay_callback:%s", info.CpOrderNo)
	if isLock := redis.Lock(ctx, lockKey, 600*time.Second); !isLock {
		logger.Logger.WarnfCtx(ctx, "[DouyinPayCallback] 获取分布式锁失败，可能存在并发处理, order_id: %s", info.CpOrderNo)
		return nil, constants.ErrSystemServiceIsBusy
	}
	defer func() {
		redis.UnLock(ctx, lockKey)
		logger.Logger.InfofCtx(ctx, "[DouyinPayCallback] 释放分布式锁, order_id: %s", info.CpOrderNo)
	}()

	// 2. 获取订单详情并检查状态，防止重复回调
	order, err := l.orderService.GetOrderDetail(ctx, info.CpOrderNo)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[DouyinPayCallback] 获取订单详情失败, order_id: %s, error: %v", info.CpOrderNo, err)
		return nil, bizerrors.Wrap(err, "get order detail")
	}

	// 3. 重复回调检查：如果订单已处于支付成功状态，直接返回成功
	if order.Status == constants.PaymentWechatPaySuccess || order.Status == constants.PaymentProductShipmentSuccess {
		logger.Logger.InfofCtx(ctx, "[DouyinPayCallback] 订单已处于支付成功状态，可能是重复回调, order_id: %s, current_status: %d", info.CpOrderNo, order.Status)
		return map[string]string{"status": "success"}, nil
	}

	logger.Logger.InfofCtx(ctx, "[DouyinPayCallback] 订单状态检查通过, order_id: %s, current_status: %d", info.CpOrderNo, order.Status)

	// 4. 验证签名信息
	if err := l.validateDouyinSignature(ctx, req.GameID, req); err != nil {
		logger.Logger.ErrorfCtx(ctx, "[DouyinPayCallback] 签名验证失败, game_id: %s, error: %v", req.GameID, err)
		return nil, bizerrors.Wrap(err, "validate signature")
	}

	// 5. 验证支付金额
	if info.AmountCent != order.Money {
		logger.Logger.ErrorfCtx(ctx, "[DouyinPayCallback] 支付金额不匹配, order_id: %s, callback_amount: %d, order_amount: %d",
			info.CpOrderNo, info.AmountCent, order.Money)
		return nil, errors.New("DouyinPayCallback 支付金额不匹配")
	}

	// 增加金额合理性检查
	if info.AmountCent <= 0 {
		logger.Logger.ErrorfCtx(ctx, "[DouyinPayCallback] 支付金额异常, order_id: %s, amount: %d", info.CpOrderNo, info.AmountCent)
		return nil, errors.New("DouyinPayCallback 支付金额必须大于0")
	}

	// 6. 获取商品详情以检查是否为小额钻石支付
	goodsInfo, err := l.orderService.GetGoodsDetailByGameIDAndGoodsID(ctx, order.GameID, order.GoodsID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[DouyinPayCallback] 获取商品详情失败, order_id: %s, goods_id: %s, error: %v",
			info.CpOrderNo, order.GoodsID, err)
		return nil, bizerrors.Wrap(err, "get goods detail")
	}

	// 验证游戏币数量是否合理（防止异常大额）- 小额钻石支付不做验证
	if !goodsInfo.IsSmallDiamond {
		if info.AmountCoin <= 0 || info.AmountCoin > 1000000 {
			logger.Logger.ErrorfCtx(ctx, "[DouyinPayCallback] 游戏币数量异常, order_id: %s, coin: %d", info.CpOrderNo, info.AmountCoin)
			return nil, errors.New("DouyinPayCallback 游戏币数量异常")
		}
	} else {
		logger.Logger.InfofCtx(ctx, "[DouyinPayCallback] 小额钻石支付跳过游戏币验证, order_id: %s, coin: %d", info.CpOrderNo, info.AmountCoin)
	}

	// 7. 获取用户抖音信息
	user, err := l.userService.GetUserDouyinModel(ctx, order.UserID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[DouyinPayCallback] 获取用户抖音信息失败, user_id: %s, error: %v", order.UserID, err)
		return nil, bizerrors.Wrap(err, "get user douyin model")
	}

	// 8. 更新订单状态（带状态检查的原子更新）
	if err := l.douyinService.CallbackSuccessUpdate(ctx, user.OpenID, info.CpOrderNo, info.AmountCent, info.OrderNoChannel, req.Msg); err != nil {
		logger.Logger.ErrorfCtx(ctx, "[DouyinPayCallback] 更新订单状态失败, order_id: %s, error: %v", info.CpOrderNo, err)
		return nil, bizerrors.Wrap(err, "callback success update")
	}

	logger.Logger.InfofCtx(ctx, "[DouyinPayCallback] 订单状态更新成功, order_id: %s", info.CpOrderNo)

	// 9. 获取游戏信息用于数据上报
	game, err := l.userService.GetGameInfo(ctx, order.GameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[DouyinPayCallback] 获取游戏信息失败, game_id: %s, error: %v", order.GameID, err)
		// 游戏信息获取失败是严重错误，应该返回
		return nil, bizerrors.Wrap(err, "get game info")
	}

	// 10. 检查是否已经完成上报
	reportKey := fmt.Sprintf("douyin:pay:reported:%s", info.CpOrderNo)
	reported, err := redis.Redis().Get(ctx, reportKey).Result()
	if err != nil && err != redis.Nil {
		logger.Logger.WarnfCtx(ctx, "[DouyinPayCallback] 检查上报状态失败, order_id: %s, error: %v", info.CpOrderNo, err)
		// Redis 错误不应阻断流程，但需要记录
	}

	if reported == "" {
		// 获取商品信息
		goods, err := l.orderService.GetGoodsDetail(ctx, order.GoodsID)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[DouyinPayCallback] 获取商品信息失败, goods_id: %s, error: %v", order.GoodsID, err)
			// 商品信息获取失败不应阻断整个支付流程，但需要记录
		}

		// 构建商品名称，如果获取失败使用默认值
		goodsName := "游戏商品"
		if goods != nil && goods.GoodsName != "" {
			goodsName = goods.GoodsName
		}

		// 检查是否是首次充值
		isFirstPay := l.orderService.IsFirstPay(ctx, order.UserID)

		// 构建上报属性
		properties := make(map[string]interface{})
		properties["$is_first_pay"] = isFirstPay
		properties["$order_id"] = info.CpOrderNo
		properties["$pay_amount"] = info.AmountCent // 使用回调中的实际支付金额

		// 根据平台类型设置支付渠道
		var payChannel string
		var os string
		if order.PlatformType == constants.PlatformTypeIOS {
			payChannel = constants.PayChannelTypeDouyinIOS
			os = "ios"
		} else {
			payChannel = constants.PayChannelTypeDouyinAndroid
			os = "android"
		}

		properties["$pay_method"] = payChannel
		properties["$pay_reason"] = goodsName
		properties["$pay_type"] = constants.PayTypeCurrencyCNY
		properties["$os"] = os

		// 创建长时间超时的上下文
		longCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
		defer cancel()

		// 上报到引力引擎
		if game.GravityIsEnabled == 1 {
			err = l.gravityEngineService.ReportPayEvent(longCtx, game.GravityAccessToken, user.OpenID, properties)
			if err != nil {
				logger.Logger.WarnfCtx(ctx, "[DouyinPayCallback] 引力引擎上报失败, order_id: %s, error: %v", info.CpOrderNo, err)
			}
		}

		// 数据上报服务
		err = l.dataReportService.ReportPaySuccess(longCtx, order.UserID, "", order.GameID, game.PlatformAppID, info.CpOrderNo, order.Money, info.AmountCent, order.Status, order.PayType)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[DouyinPayCallback] 数据上报失败, order_id: %s, error: %v", info.CpOrderNo, err)
		}

		// 标记已上报
		err = redis.Redis().SetEX(ctx, reportKey, "1", 24*time.Hour).Err()
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[DouyinPayCallback] 设置上报标记失败, order_id: %s, error: %v", info.CpOrderNo, err)
		}
	} else {
		logger.Logger.InfofCtx(ctx, "[DouyinPayCallback] 订单已完成上报, order_id: %s", info.CpOrderNo)
	}

	isSmallDiamond := goodsInfo.IsSmallDiamond
	logger.Logger.InfofCtx(ctx, "[DouyinPayCallback] 商品信息检查完成, order_id: %s, goods_id: %s, is_small_diamond: %v",
		info.CpOrderNo, order.GoodsID, isSmallDiamond)

	// 11. 提交异步支付任务（同步方式，确保任务成功提交）
	// 不再传递可能过期的 access token，而是传递 game_id，让任务处理时重新获取最新的 token
	orderPlatform := constants.PlatformTypeMap[order.PlatformType]
	if err := l.submitPayTaskWithDeduplication(order.GameID, user.OpenID, info.CpOrderNo, orderPlatform, order.SaveAmt, info.AmountCoin, isSmallDiamond); err != nil {
		logger.Logger.ErrorfCtx(ctx, "[DouyinPayCallback] 提交支付任务失败, order_id: %s, error: %v", info.CpOrderNo, err)
		// 任务提交失败是严重错误，但订单已支付成功，记录错误并返回成功
		// 可以通过监控告警来处理这种情况
		logger.Logger.ErrorWithFiled(map[string]interface{}{
			"order_id": info.CpOrderNo,
			"game_id":  order.GameID,
			"user_id":  order.UserID,
			"error":    err.Error(),
			"extra":    "抖音支付任务提交失败，订单已支付成功但后续处理可能失败",
		}, "抖音支付任务提交异常")
	} else {
		logger.Logger.InfofCtx(ctx, "[DouyinPayCallback] 支付任务提交成功, order_id: %s", info.CpOrderNo)
	}

	logger.Logger.InfofCtx(ctx, "[DouyinPayCallback] 抖音支付回调处理完成, order_id: %s", info.CpOrderNo)
	return map[string]string{"status": "success"}, nil
}

// submitPayTaskWithDeduplication 支付任务提交（简化版本）
func (l *OrderLogic) submitPayTaskWithDeduplication(gameID, openID, orderID, orderPlatform string, saveAmt, deductAmt int32, isSmallDiamond bool) error {
	logger.Logger.InfofCtx(context.Background(), "[submitPayTaskWithDeduplication] 开始提交支付任务, order_id: %s, game_id: %s, is_small_diamond: %v", orderID, gameID, isSmallDiamond)

	// 构造支付任务请求，现在传递 GameID 而不是 AccessToken
	payReq := &bean.DouyinPayTaskReq{
		GameID:         gameID, // 传递 GameID，让任务处理时重新获取最新的配置
		OpenID:         openID,
		OrderID:        orderID,
		OrderPlatform:  orderPlatform,
		SaveAmt:        saveAmt,
		DeductAmt:      deductAmt,
		IsSmallDiamond: isSmallDiamond, // 传递小额钻石支付标识
	}

	reqByte, err := json.Marshal(payReq)
	if err != nil {
		logger.Logger.ErrorfCtx(context.Background(), "[submitPayTaskWithDeduplication] 序列化任务请求失败, order_id: %s, error: %v", orderID, err)
		return bizerrors.Wrap(err, "marshal pay task request")
	}

	// 提交异步任务
	taskID, err := task.Submit(asynq.NewTask(task.TypeDouyinPayCallback, reqByte))
	if err != nil {
		logger.Logger.ErrorfCtx(context.Background(), "[submitPayTaskWithDeduplication] 提交异步任务失败, order_id: %s, error: %v", orderID, err)
		return bizerrors.Wrap(err, "submit pay task")
	}

	logger.Logger.InfofCtx(context.Background(), "[submitPayTaskWithDeduplication] 支付任务提交成功, order_id: %s, task_id: %s", orderID, taskID)
	return nil
}

func (l *OrderLogic) validateDouyinSignature(ctx context.Context, gameID string, req *bean.DouyinPayCallbackReq) error {
	conf, err := l.douyinService.GetDouyinConf(ctx, gameID)
	if err != nil {
		return constants.ErrConfigIsEmpty
	}
	if conf == nil || conf.PayToken == "" {
		return constants.ErrConfigIsEmpty
	}
	if !l.douyinService.VerifySign(conf.PayToken, req.Timestamp, req.Nonce, req.Msg, req.Signature) {
		return errors.New("DouyinPayCallback signature mismatch")
	}
	return nil
}

// GetOrderPayURL 获取用户支付链接(ios)
func (l *OrderLogic) GetOrderPayURL(ctx context.Context, req *bean.GetOrderPayURLReq) (*bean.GetOrderPayURLResp, error) {
	logger.Logger.Debugf("GetOrderPayURL req: %+v", req)

	_, err := l.orderService.CheckOrderExist(ctx, req.OrderID)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, constants.ErrOrderNotExist
	} else if err != nil {
		return nil, err
	}

	return &bean.GetOrderPayURLResp{
		PayURL: fmt.Sprintf("%s?order_id=%s", config.GlobConfig.WechatPay.CustomerPayURL, req.OrderID),
	}, nil
}

// GetOrderPayQRCode 获取订单支付二维码
func (l *OrderLogic) GetOrderPayQRCode(ctx context.Context, req *bean.GetOrderPayQRCodeReq) (*bean.GetOrderPayQRCodeResp, error) {
	logger.Logger.Debugf("GetOrderPayQRCode req: %+v", req)

	// 获取订单详情
	orderDetail, err := l.orderService.GetOrderDetail(ctx, req.OrderID)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, constants.ErrOrderNotExist
	} else if err != nil {
		return nil, err
	}

	// 生成支付URL
	payURL := fmt.Sprintf("%s?order_id=%s", config.GlobConfig.WechatPay.CustomerPayURL, req.OrderID)

	// 将金额从分转换为元
	amountYuan := float64(orderDetail.Money) / 100.0
	amountText := fmt.Sprintf("¥%.0f", amountYuan)

	// 生成二维码
	qrCode, err := util.GenerateQRCode(payURL, amountText)
	if err != nil {
		logger.Logger.Errorf("GenerateQRCode error: %v", err)
		return nil, err
	}

	return &bean.GetOrderPayQRCodeResp{
		QRCodeBase64: qrCode,
		OrderDetail:  orderDetail,
	}, nil
}

// FilteredQuestions 返回应从机器人响应中过滤的问题列表
func (l *OrderLogic) FilteredQuestions(ctx context.Context, gameID string) ([]string, error) {
	contentList, err := l.qiyuService.GetRobotFilter(ctx, gameID)
	if err != nil {
		return nil, err
	}
	return contentList, nil
}

func (l *OrderLogic) filterContent(replyContent string, contentList []string) string {
	for _, content := range contentList {
		// 构建要移除的 HTML 标签
		tagToRemove := fmt.Sprintf("<p><b>%s</b></p>", content)
		// 从 replyContent 中移除该标签
		replyContent = strings.ReplaceAll(replyContent, tagToRemove, "")
	}
	return replyContent
}

func encodeToBase64(value string) string {
	return base64.StdEncoding.EncodeToString([]byte(value))
}

// buildParamsMapFromSessionFrom 从session_from构建参数映射
func (l *OrderLogic) buildParamsMapFromSessionFrom(userSessionFrom *model.AGamePlayer, gameInfo *model.MGame) (map[string]string, error) {
	// 构建参数映射
	result := map[string]string{
		"game_id":   gameInfo.GameID,
		"game_name": encodeToBase64(gameInfo.Name),
	}

	if userSessionFrom == nil {
		return result, nil
	}

	// 添加必需参数
	result["player_id"] = encodeToBase64(userSessionFrom.PlayerID)
	result["player_name"] = encodeToBase64(userSessionFrom.PlayerName)

	// 添加可选参数（如果存在）
	optionalParams := map[string]string{
		"player_level":          strconv.Itoa(int(userSessionFrom.PlayerLevel)),
		"recharge_total_amount": strconv.Itoa(int(userSessionFrom.RechargeTotalAmount)),
		"zone":                  userSessionFrom.Zone,
		"custom_data":           userSessionFrom.CustomData,
	}

	for key, value := range optionalParams {
		if value != "" {
			result[key] = encodeToBase64(value)
		}
	}

	return result, nil
}

// GetOrderDetailH5 获取H5订单详情
func (l *OrderLogic) GetOrderDetailH5(ctx context.Context, req *bean.GetOrderDetailH5PayReq) (*bean.GetOrderDetailH5Res, error) {
	logger.Logger.InfofCtx(ctx, "GetOrderDetailH5 req: %+v", req)

	orderDetail, err := l.orderService.GetOrderDetail(ctx, req.OrderID)
	if err != nil {
		return nil, err
	}

	return &bean.GetOrderDetailH5Res{
		OrderDetail: orderDetail,
	}, nil
}

// verifyUnionID 验证小游戏用户和H5用户的UnionID是否匹配
func (l *OrderLogic) verifyUnionID(ctx context.Context, gameID, userID, h5UserID string) error {
	// 获取小游戏配置
	minigameConf, err := l.minigameService.GetMinigameConfig(ctx, gameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "获取小游戏配置失败, gameID: %s, err: %v", gameID, err)
		return fmt.Errorf("failed to get minigame config: %w", err)
	}

	// 未开启UnionID验证则直接返回
	if !minigameConf.IsVerifyUnionID {
		return nil
	}

	// 获取小游戏用户信息
	minigameUser, err := l.userService.GetMinigameModel(ctx, userID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "获取小游戏用户信息失败, userID: %s, err: %v", userID, err)
		return fmt.Errorf("failed to get minigame user: %w", err)
	}
	if minigameUser == nil {
		logger.Logger.ErrorfCtx(ctx, "小游戏用户不存在, userID: %s", userID)
		return constants.ErrUserIDNotFound
	}
	// 如果小游戏用户没有unionID，则跳过验证
	if minigameUser.UnionID == "" {
		logger.Logger.WarnfCtx(ctx, "小游戏用户无unionID, userID: %s, 跳过验证", userID)
		return nil
	}

	// 获取H5用户的unionID
	subscribeInfo, err := l.userService.GetSubscribeUnionIDByUserID(ctx, h5UserID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "获取H5用户unionID失败, h5UserID: %s, err: %v", h5UserID, err)
		return fmt.Errorf("failed to get H5 user unionID: %w", err)
	}

	// 通过subscribeInfo.unionID反查minigameUser.user_id
	verifyUserID, err := l.userService.GetUserIDByUnionID(ctx, subscribeInfo.UnionID, gameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "通过unionID反查UserID失败, unionID: %s, err: %v", subscribeInfo.UnionID, err)
		return fmt.Errorf("failed to get UserID by unionID: %w", err)
	}

	// 验证userID是否匹配
	if userID != verifyUserID {
		logger.Logger.WarnfCtx(ctx, "小游戏用户ID不匹配, expected: %s, got: %s", h5UserID, verifyUserID)
		return constants.ErrUserIDNotFound
	}

	// 验证unionID是否匹配
	if minigameUser.UnionID != subscribeInfo.UnionID {
		logger.Logger.ErrorfCtx(ctx, "用户UnionID不匹配, minigameUnionID: %s, h5UnionID: %s",
			minigameUser.UnionID, subscribeInfo.UnionID)
		return constants.ErrUserIDNotFound
	}

	return nil
}

// TestOrder 测试订单
func (l *OrderLogic) TestOrder(ctx context.Context, req *bean.TestOrderReq) (*bean.Order, error) {
	return l.orderService.TestOrder(ctx, req)
}

// TestCallbackShipment 测试产品发货回调
func (l *OrderLogic) TestCallbackShipment(ctx context.Context, req *bean.ProductShipmentOrder) (*bean.ProductShipmentRes, error) {
	logger.Logger.Infof("TestCallbackShipment received request: %+v", req)

	// 1. 构造响应对象
	response := &bean.ProductShipmentRes{
		OrderID: req.OrderID,
		Code:    0, // 0 表示成功
		Msg:     "发货成功",
	}

	// 获取商品名称（如果可能）
	goodsName := "未知商品"
	if req.GoodsID != "" && req.GameID != "" {
		goodsInfo, goodsErr := l.orderService.GetGoodsDetailByGameIDAndGoodsID(ctx, req.GameID, req.GoodsID)
		if goodsErr == nil && goodsInfo != nil {
			goodsName = goodsInfo.GoodsName
		}
	}

	// 2. 发送更结构化的通知到飞书机器人
	message := fmt.Sprintf(
		"收到产品发货回调通知:\n"+
			"订单ID: %s\n"+
			"支付状态: %s\n"+
			"游戏ID: %s\n"+
			"用户ID: %s\n"+
			"物品ID: %s\n"+
			"物品名称: %s\n"+
			"金额（分）: %d\n"+
			"平台类型: %s\n"+
			"额外信息: %s",
		req.OrderID,
		"发货成功",
		req.GameID,
		req.UserID,
		req.GoodsID,
		goodsName,
		req.Money,
		func() string {
			// 内联获取平台类型文本
			switch req.PlatformType {
			case constants.PlatformTypeIOS:
				return "iOS"
			case constants.PlatformTypeAndroid:
				return "安卓"
			default:
				return fmt.Sprintf("平台类型: %d", req.PlatformType)
			}
		}(),
		req.Extra,
	)

	if err := l.orderService.NotifyFeishu(ctx, message); err != nil {
		// 只记录错误，不影响正常流程
		logger.Logger.WarnfCtx(ctx, "Failed to send Feishu notification: %v", err)
	}

	// 3. 返回响应
	return response, nil
}

// checkRechargeEnabledForAllPlatforms 检查多平台是否都允许充值
// 如果 platformType 包含多个平台（逗号分割），需要所有平台都允许充值才返回 true
func (l *OrderLogic) checkRechargeEnabledForAllPlatforms(ctx context.Context, gameID, platformType string) (bool, error) {
	// 处理多平台逗号分割的情况
	platforms := l.parsePlatformTypes(platformType)

	// 检查每个平台的充值状态
	for _, platform := range platforms {
		enabled, err := l.stopServiceConfigService.CheckRechargeEnabled(ctx, gameID, platform)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "[checkRechargeEnabledForAllPlatforms] 检查平台充值状态失败, gameID: %s, platform: %s, err: %v", gameID, platform, err)
			return false, err
		}

		// 如果任意一个平台不允许充值，则整体不允许充值
		if !enabled {
			logger.Logger.InfofCtx(ctx, "[checkRechargeEnabledForAllPlatforms] 平台充值已关闭, gameID: %s, platform: %s", gameID, platform)
			return false, nil
		}
	}

	logger.Logger.InfofCtx(ctx, "[checkRechargeEnabledForAllPlatforms] 所有平台充值均已开启, gameID: %s, platforms: %v", gameID, platforms)
	return true, nil
}

// parsePlatformTypes 解析平台类型字符串，支持单平台和多平台逗号分割
func (l *OrderLogic) parsePlatformTypes(platformType string) []string {
	// 去除空格并按逗号分割
	platformType = strings.TrimSpace(platformType)
	if platformType == "" {
		return []string{}
	}

	platforms := strings.Split(platformType, ",")
	// 去除每个平台名称的空格
	for i, platform := range platforms {
		platforms[i] = strings.TrimSpace(platform)
	}

	// 过滤空字符串
	var result []string
	for _, platform := range platforms {
		if platform != "" {
			result = append(result, platform)
		}
	}

	return result
}
