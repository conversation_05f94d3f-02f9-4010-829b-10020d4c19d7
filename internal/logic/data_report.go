package logic

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/ip"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_dataReportOnce  sync.Once
	_dataReportLogic *DataReportLogic
)

type DataReportLogic struct {
	dataReportService *service.DataReportService
}

func SingletonDataReportLogic() *DataReportLogic {
	_dataReportOnce.Do(func() {
		_dataReportLogic = &DataReportLogic{
			dataReportService: service.SingletonDataReportService(),
		}
	})
	return _dataReportLogic
}

// InitReport 初始化
func (l *DataReportLogic) InitReport(ctx context.Context, req *bean.InitReportReq) error {
	if req.IP != constants.EmptyStr {
		req.IPRegionID = ip.GetRegionByIP(req.IP)
	}
	if err := l.dataReportService.InitReport(ctx, req); err != nil {
		return err
	}
	if err := l.dataReportService.SystemDataReport(
		constants.EmptyStr,
		req.PlatformDeviceID,
		req.IP,
		req.IPRegionID,
		req.PlatformGameID,
		req.EventName,
		req.EventType,
		req.UUID,
		req.Properties); err != nil {
		return err
	}
	return nil
}

// BatchInitReport 批量数据
func (l *DataReportLogic) BatchInitReport(ctx context.Context, req *bean.BatchInitReportReq) error {
	if req.IP != constants.EmptyStr {
		req.IPRegionID = ip.GetRegionByIP(req.IP)
	}
	errChan := make(chan error, len(req.InitReports))
	wg := sync.WaitGroup{}

	wg.Add(len(req.InitReports))

	for _, r := range req.InitReports {
		if r.Properties == nil {
			r.Properties = make(map[string]interface{})
		}
		if r.Time != "" {
			r.Properties["#time"] = r.Time
		}
		report := &bean.InitReportReq{
			PlatformAppID:    req.PlatformAppID,
			PlatformGameID:   req.PlatformGameID,
			PlatformDeviceID: req.PlatformDeviceID,
			IP:               req.IP,
			IPRegionID:       req.IPRegionID,
			EventName:        r.EventName,
			UUID:             r.UUID,
			EventType:        r.EventType,
			Properties:       r.Properties,
		}

		go func(report *bean.InitReportReq) {
			defer wg.Done()
			err := l.dataReportService.InitReport(ctx, report)
			if err != nil {
				errChan <- err
			}
		}(report)
	}

	wg.Wait()
	close(errChan)

	// Return the first error encountered
	for err := range errChan {
		if err != nil {
			return err
		}
	}
	return nil
}

// UploadReport 上传数据打点上报
func (l *DataReportLogic) UploadReport(ctx context.Context, req *bean.UploadReportReq) error {
	errChan := make(chan error, len(req.Logs))
	wg := sync.WaitGroup{}

	wg.Add(len(req.Logs))

	for _, r := range req.Logs {
		go func(report *bean.UploadReport) {
			defer wg.Done()
			err := l.dataReportService.UploadReport(ctx, report)
			if err != nil {
				errChan <- err
			}
		}(r)
	}

	wg.Wait()
	close(errChan)

	// Return the first error encountered
	for err := range errChan {
		if err != nil {
			return err
		}
	}
	return nil
}

// DataReport 获取时间
func (l *DataReportLogic) DataReport(ctx context.Context, req *bean.DataReportReq) error {
	if req.UUID == "" || req.Properties == nil || req.EventType == "" {
		logger.Logger.WarnfCtx(ctx, "DataReportLogic DataReport validation failed: UUID=%s, Properties=%v, EventType=%s, EventName=%s",
			req.UUID, req.Properties, req.EventType, req.EventName)
		return constants.ErrConfigError
	}

	if req.IP != constants.EmptyStr {
		req.IPRegionID = ip.GetRegionByIP(req.IP)
	}
	if err := l.dataReportService.DataReport(ctx, req); err != nil {
		return err
	}
	if err := l.dataReportService.SystemDataReport(req.UserID,
		req.DeviceID,
		req.IP,
		req.IPRegionID,
		req.GameID,
		req.EventName,
		req.EventType,
		req.UUID,
		req.Properties); err != nil {
		return err
	}

	// 执行额外数据上报（异步，不影响主流程）
	l.dataReportService.AdditionalDataReport(ctx, req)

	return nil
}

// BatchDataReport 批量数据上报
func (l *DataReportLogic) BatchDataReport(ctx context.Context, req *bean.BatchDataReportReq) error {
	if req.IP != constants.EmptyStr {
		req.IPRegionID = ip.GetRegionByIP(req.IP)
	}
	errChan := make(chan error, len(req.DataReports))
	wg := sync.WaitGroup{}

	wg.Add(len(req.DataReports))

	h := middleware.Header{
		UserID:   req.UserID,
		DeviceID: req.DeviceID,
		AppID:    req.AppID,
		GameID:   req.GameID,
	}
	for _, r := range req.DataReports {
		if r.Properties == nil {
			r.Properties = make(map[string]interface{})
		}
		if r.Time != "" {
			r.Properties["#time"] = r.Time
		}
		report := &bean.DataReportReq{
			Header:     h,
			RoleID:     r.RoleID,
			IP:         req.IP,
			IPRegionID: req.IPRegionID,
			EventName:  r.EventName,
			UUID:       r.UUID,
			EventType:  r.EventType,
			Properties: r.Properties,
		}

		go func(report *bean.DataReportReq) {
			defer wg.Done()
			err := l.dataReportService.DataReport(ctx, report)
			if err != nil {
				errChan <- err
			}
		}(report)
	}

	wg.Wait()
	close(errChan)

	// Return the first error encountered
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	// 执行额外数据上报（异步，不影响主流程）
	l.dataReportService.AdditionalBatchDataReport(ctx, req)

	return nil
}
