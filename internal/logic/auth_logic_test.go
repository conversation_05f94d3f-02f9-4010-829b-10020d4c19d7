//go:build unit

package logic

import (
	"context"
	"errors"
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
)

// 定义接口，方便 mock
type H5AdminAuthServiceInterface interface {
	CheckRechargeLimit(ctx context.Context, req *bean.RechargeCheckRequest) (*bean.RechargeCheckResponse, error)
	CheckRechargePopup(ctx context.Context, req *bean.RechargePopupRequest) (*bean.RechargePopupResponse, error)
}

// 业务逻辑结构体，依赖接口
type H5AdminAuthLogic struct {
	h5AdminAuthService H5AdminAuthServiceInterface
}

func (l *H5AdminAuthLogic) CheckRechargeLimit(ctx context.Context, req *bean.RechargeCheckRequest) (*bean.RechargeCheckResponse, error) {
	return l.h5AdminAuthService.CheckRechargeLimit(ctx, req)
}

func (l *H5AdminAuthLogic) CheckRechargePopup(ctx context.Context, req *bean.RechargePopupRequest) (*bean.RechargePopupResponse, error) {
	return l.h5AdminAuthService.CheckRechargePopup(ctx, req)
}

// mock 实现
type MockH5AdminAuthService struct {
	CheckRechargeLimitFunc func(ctx context.Context, req *bean.RechargeCheckRequest) (*bean.RechargeCheckResponse, error)
	CheckRechargePopupFunc func(ctx context.Context, req *bean.RechargePopupRequest) (*bean.RechargePopupResponse, error)
	GetUserByUserIDFunc    func(ctx context.Context, userID string) (interface{}, error)
	GetUserByUsernameFunc  func(ctx context.Context, username string) (interface{}, error)
}

func (m *MockH5AdminAuthService) CheckRechargeLimit(ctx context.Context, req *bean.RechargeCheckRequest) (*bean.RechargeCheckResponse, error) {
	return m.CheckRechargeLimitFunc(ctx, req)
}

func (m *MockH5AdminAuthService) CheckRechargePopup(ctx context.Context, req *bean.RechargePopupRequest) (*bean.RechargePopupResponse, error) {
	return m.CheckRechargePopupFunc(ctx, req)
}

func (m *MockH5AdminAuthService) GetUserByUserID(ctx context.Context, userID string) (interface{}, error) {
	return m.GetUserByUserIDFunc(ctx, userID)
}

func (m *MockH5AdminAuthService) GetUserByUsername(ctx context.Context, username string) (interface{}, error) {
	return m.GetUserByUsernameFunc(ctx, username)
}

func TestCheckRechargeLimit(t *testing.T) {
	mockSvc := &MockH5AdminAuthService{
		CheckRechargeLimitFunc: func(ctx context.Context, req *bean.RechargeCheckRequest) (*bean.RechargeCheckResponse, error) {
			if req.Username == "error" {
				return nil, errors.New("mock error")
			}
			return &bean.RechargeCheckResponse{AllowRecharge: true}, nil
		},
	}

	logic := &H5AdminAuthLogic{
		h5AdminAuthService: mockSvc,
	}

	req := &bean.RechargeCheckRequest{
		Username: "user_001",
	}

	resp, err := logic.CheckRechargeLimit(context.Background(), req)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	if !resp.AllowRecharge {
		t.Errorf("expected AllowRecharge=true, got false")
	}

	// 测试错误分支
	req.Username = "error"
	_, err = logic.CheckRechargeLimit(context.Background(), req)
	if err == nil {
		t.Errorf("expected error, got nil")
	}
}

func TestCheckRechargePopup(t *testing.T) {
	mockSvc := &MockH5AdminAuthService{
		CheckRechargePopupFunc: func(ctx context.Context, req *bean.RechargePopupRequest) (*bean.RechargePopupResponse, error) {
			if req.Username == "error" {
				return nil, errors.New("mock error")
			}
			return &bean.RechargePopupResponse{ShowPopup: true}, nil
		},
		GetUserByUsernameFunc: func(ctx context.Context, username string) (interface{}, error) {
			return nil, nil
		},
	}

	logic := &H5AdminAuthLogic{
		h5AdminAuthService: mockSvc,
	}

	popupReq := &bean.RechargePopupRequest{
		Username: "user_001",
	}

	resp, err := logic.CheckRechargePopup(context.Background(), popupReq)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	if !resp.ShowPopup {
		t.Errorf("expected ShowPopup=true, got false")
	}

	// 测试错误分支
	popupReq.Username = "error"
	_, err = logic.CheckRechargePopup(context.Background(), popupReq)
	if err == nil {
		t.Errorf("expected error, got nil")
	}
}
