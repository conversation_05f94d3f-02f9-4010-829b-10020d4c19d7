package logic

import (
	"context"
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/task"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/mysql"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
)

func Init() {
	mustInit()
	initLogger()
	initMysql()
	initCron()
	initRedis()
	initTask()
}

func mustInit() {
	config.MustInit()
}

func initLogger() {
	logger.InitLogger(&config.GlobConfig.Logger)
}

func initMysql() {
	mysql.InitMysql(&config.GlobConfig.Mysql)
	store.InitQueryDB()
}

func initTask() {
	task.InitTask()
}

func initCron() {
	// cron.InitCron()
}

func initRedis() {
	redis.InitRedis(&config.GlobConfig.Redis)
}

// 测试函数
func TestUserService_GetUserInfoByOpenID(t *testing.T) {
	Init()
	res, token, err := SingletonUserLogic().Login(context.Background(),
		&bean.LoginReq{
			DeviceID:     "1111",
			Code:         "xxx",
			Channel:      "xxx",
			PlatformType: "subscribe",
			GameID:       "",
		})
	if err != nil {
		return
	}
	logger.Logger.Debug("token :")
	logger.Logger.Debug(token)
	logger.Logger.Debug("res :")
	logger.Logger.Debug(res)
}
