package logic

import (
	"bytes"
	"context"
	"crypto/sha1"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"sync"
	"time" 

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/task"
	"github.com/hibiken/asynq"
	"github.com/tencentyun/cos-go-sdk-v5"
	"gorm.io/gorm"
)

var (
	_qiyuOnce  sync.Once
	_qiyuLogic *QiyuLogic
)

type QiyuLogic struct {
	qiyuService     *service.QiyuService
	userService     *service.UserService
	minigameService *service.MinigameService
	messageService  *service.MessageService
}

func SingletonQiyuLogic() *QiyuLogic {
	_qiyuOnce.Do(func() {
		_qiyuLogic = &QiyuLogic{
			qiyuService:     service.SingletonQiyuService(),
			userService:     service.SingletonUserService(),
			minigameService: service.SingletonMinigameService(),
			messageService:  service.SingletonMessageService(),
		}
	})
	return _qiyuLogic
}

// GetQiyuAuthCode
func (l *QiyuLogic) GetQiyuAuthCode(ctx context.Context, req *bean.GetQiyuAuthCodeReq) (*bean.GetQiyuAuthCodeResp, error) {
	return l.qiyuService.GetQiyuAuthCode(ctx, req)
}

// QiyuAuthLogin
func (l *QiyuLogic) QiyuAuthLogin(ctx context.Context, req *bean.QiyuAuthLoginReq) (*bean.QiyuAuthLoginResp, error) {
	miniprogrampage, err := l.qiyuService.GetQiyuMiniGameSession(ctx)
	if err != nil {
		return nil, fmt.Errorf("get mini game session: %w", err)
	}

	miniGameSession, err := l.minigameService.GetCode2Session(ctx, miniprogrampage.AppID, miniprogrampage.AppSecret, req.Code)
	if err != nil {
		return nil, fmt.Errorf("get code2session: %w", err)
	}

	var gameName string
	if req.GameID != "" {
		info, err := l.userService.GetGameInfo(ctx, req.GameID)
		if err != nil {
			return nil, fmt.Errorf("get game info: %w", err)
		}
		gameName = info.Name
	}

	return l.qiyuService.QiyuAuthLogin(ctx, req, miniGameSession.OpenID, gameName)
}

// CreateSupportTicket
func (l *QiyuLogic) CreateSupportTicket(ctx context.Context, req *bean.CreateSupportTicketReq) (*bean.CreateSupportTicketResp, error) {
	// gameInfo, err := l.userService.GetGameInfo(ctx, req.GameID)
	// if err != nil {
	// 	return nil, err
	// }
	return l.qiyuService.CreateSupportTicket(ctx, config.GlobConfig.Qiyu.AppKey, config.GlobConfig.Qiyu.AppSecret, req)
}

// GetTicketLogs
func (l *QiyuLogic) GetTicketLogs(ctx context.Context, req *bean.GetTicketLogsReq) (*bean.GetTicketLogsResp, error) {
	// gameInfo, err := l.userService.GetGameInfo(ctx, req.GameID)
	// if err != nil {
	// 	return nil, err
	// }
	return l.qiyuService.GetTicketLogs(ctx, config.GlobConfig.Qiyu.AppKey, config.GlobConfig.Qiyu.AppSecret, req)
}

// GetSupportTickets
func (l *QiyuLogic) GetSupportTickets(ctx context.Context, req *bean.GetSupportTicketsReq) (*bean.GetSupportTicketsResp, error) {
	// gameInfo, err := l.userService.GetGameInfo(ctx, req.GameID)
	// if err != nil {
	// 	return nil, err
	// }
	return l.qiyuService.GetSupportTickets(ctx, config.GlobConfig.Qiyu.AppKey, config.GlobConfig.Qiyu.AppSecret, req)
}

// GetTicketTemplate
func (l *QiyuLogic) GetTicketTemplate(ctx context.Context, req *bean.GetTicketTemplateReq) (*bean.GetTicketTemplateResp, error) {
	// gameInfo, err := l.userService.GetGameInfo(ctx, req.GameID)
	// if err != nil {
	// 	return nil, err
	// }
	return l.qiyuService.GetTicketTemplate(ctx, config.GlobConfig.Qiyu.AppKey, config.GlobConfig.Qiyu.AppSecret, req)
}

// GetTicketDetail
func (l *QiyuLogic) GetTicketDetail(ctx context.Context, req *bean.GetTicketDetailReq) (*bean.GetTicketDetailResp, error) {
	return l.qiyuService.GetTicketDetail(ctx, config.GlobConfig.Qiyu.AppKey, config.GlobConfig.Qiyu.AppSecret, req.TicketID)
}

// UploadFile
func (l *QiyuLogic) UploadFile(ctx context.Context, file *multipart.FileHeader, creatorID string) (*bean.UploadFileResp, error) {
	stream, err := file.Open()
	if err != nil {
		return nil, err
	}
	defer stream.Close()

	// stream to []byte
	streamByte, err := io.ReadAll(stream)
	if err != nil {
		return nil, err
	}
	md5 := util.EncodeMD5(string(streamByte))

	fileUrl, err := l.GetFileUrl(ctx, md5)
	if err != nil {
		return nil, err
	}
	if fileUrl != "" {
		return &bean.UploadFileResp{
			FileURL: fileUrl,
		}, nil
	}

	u, _ := url.Parse(config.GlobConfig.OSS.BucketURL)
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
	})
	// 使用时间戳+随机数
	filePrefix := fmt.Sprintf("%d%d", time.Now().Unix(), util.GenRandomNum())
	fileName := fmt.Sprintf("/%s/%s-%s", config.GlobConfig.OSS.Env, filePrefix, file.Filename)
	_, err = client.Object.Put(ctx, fileName, bytes.NewReader(streamByte), nil)
	if err != nil {
		return nil, err
	}
	fileURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.Domain, fileName)
	ossURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.BucketURL, fileName)
	err = l.CreateFileData(ctx, fileName, md5, fileURL, ossURL, creatorID, file.Size)
	if err != nil {
		return nil, err
	}

	return &bean.UploadFileResp{
		FileURL: fileURL,
	}, nil
}

func (l *QiyuLogic) GetFileUrl(ctx context.Context, md5 string) (string, error) {
	upload := store.QueryDB().AUpload
	uploadCtx := upload.WithContext(ctx)
	uploadInfo, err := uploadCtx.Where(upload.Md5.Eq(md5), upload.IsDeleted.Zero()).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", err
	}
	if uploadInfo == nil {
		return "", nil
	}
	return uploadInfo.URL, nil
}

// CreateFileData
func (l *QiyuLogic) CreateFileData(ctx context.Context, fileName, md5, url, ossURL, creatorID string, fileSize int64) error {
	// 获取filename ext
	fileType := util.GetFileType(fileName)

	upload := store.QueryDB().AUpload
	uploadCtx := upload.WithContext(ctx)
	err := uploadCtx.Create(&model.AUpload{
		FileName:  fileName,
		FileSize:  fileSize,
		FileType:  fileType,
		Md5:       md5,
		URL:       url,
		OssURL:    ossURL,
		OssBucket: config.GlobConfig.OSS.BucketURL,
		CreatorID: creatorID,
	})
	if err != nil {
		return err
	}
	return nil
}

// QiyuTicketCallback
func (l *QiyuLogic) QiyuTicketCallback(ctx context.Context, req *bean.QiyuTicketCallbackReq) (*bean.QiyuTicketCallbackResp, error) {
	logger.Logger.InfofCtx(ctx, "QiyuTicketCallback req: %+v", req)

	// 验证checksum
	checksum := l.genChecksum(config.GlobConfig.Qiyu.AppSecret, req.AccessToken, req.Timestamp)
	if checksum != req.Checksum {
		return nil, fmt.Errorf("QiyuTicketCallback checksum error")
	}
	logger.Logger.InfofCtx(ctx, "QiyuTicketCallback checksum success: %s", checksum)

	if req.Event != constants.QiyuCallbackEventReply {
		logger.Logger.WarnfCtx(ctx, "QiyuTicketCallback event not support, req: %+v", req)
		return nil, nil
	}

	resp, err := l.qiyuService.QiyuTicketCallback(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp.ReplyContent == "" {
		return nil, fmt.Errorf("QiyuTicketCallback reply content is empty, req: %+v", req)
	}

	data := map[string]interface{}{
		"thing6": map[string]interface{}{"value": util.TruncateString(constants.DefaultQiyuTicketTemplateReply, 17)}, // 温馨提示
		"thing7": map[string]interface{}{"value": util.TruncateString(resp.Content, 17)},                             // 咨询内容
		"thing3": map[string]interface{}{"value": util.TruncateString(resp.ReplyContent, 17)},                        // 答复内容
		"time4":  map[string]interface{}{"value": time.UnixMilli(req.Time).Format(constants.DayTimeFormatUnix)},      // 答复时间
	}

	// if req.Event == constants.QiyuCallbackEventFinish {
	// 	data["thing6"] = map[string]interface{}{"value": util.TruncateString(constants.DefaultQiyuTicketTemplateFinish, 17)}
	// }

	taskReq := bean.QiyuTicketReq{
		// AccessToken: conf.AccessToken, // 触发func时获取最新
		OpenID:     resp.OpenID,
		TemplateID: constants.DefaultQiyuTicketTemplateID,
		Page:       fmt.Sprintf("/pages/detail/detail?ticket_id=%d", req.SheetID),
		Data:       data,
	}

	taskReqByte, err := json.Marshal(taskReq)
	if err != nil {
		return nil, err
	}
	taskID, err := task.Submit(asynq.NewTask(task.TypeQiyuSubscribeMessage, taskReqByte))
	if err != nil {
		return nil, err
	}
	logger.Logger.InfofCtx(ctx, "QiyuTicketCallback submit return taskID: %s", taskID)
	return resp, nil
}

func (l *QiyuLogic) genChecksum(appSecret string, accessToken string, timestamp string) string {
	content := appSecret + accessToken + timestamp
	h := sha1.New()
	h.Write([]byte(content))
	return fmt.Sprintf("%x", h.Sum(nil))
}

// ReplyTicket
func (l *QiyuLogic) ReplyTicket(ctx context.Context, req *bean.ReplyTicketReq) error {
	err := l.qiyuService.ReplyTicket(ctx, req)
	if err != nil {
		return err
	}
	// 如果玩家上传图片了，借用Comment字段
	if len(req.FilesURL) > 0 {
		req.Comment = req.FilesURL
		err := l.qiyuService.ReplyTicket(ctx, req)
		if err != nil {
			return err
		}
	}

	return nil
}
