package logic

import (
	"context"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
)

var (
	_dateOnce  sync.Once
	_dateLogic *DateLogic
)

type DateLogic struct {
	dateService *service.DateService
}

func SingletonDateLogic() *DateLogic {
	_dateOnce.Do(func() {
		_dateLogic = &DateLogic{
			dateService: service.SingletonDateService(),
		}
	})
	return _dateLogic
}

// CheckPlayableDate 检查指定日期是否为可玩日期
func (l *DateLogic) CheckPlayableDate(ctx context.Context, req *bean.CheckPlayableDateReq) (*bean.CheckPlayableDateResp, error) {
	// 解析日期字符串
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, err
	}

	isPlayable, err := l.dateService.IsPlayableDate(ctx, date)
	if err != nil {
		return nil, err
	}

	return &bean.CheckPlayableDateResp{
		Date:       req.Date,
		IsPlayable: isPlayable,
	}, nil
}

// CheckTodayPlayable 检查今天是否为可玩日期
func (l *DateLogic) CheckTodayPlayable(ctx context.Context) (*bean.CheckTodayPlayableResp, error) {
	isPlayable, err := l.dateService.IsTodayPlayable(ctx)
	if err != nil {
		return nil, err
	}

	// 获取今天的日期字符串（北京时区）
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return nil, err
	}
	today := time.Now().In(beijingLocation).Format("2006-01-02")

	return &bean.CheckTodayPlayableResp{
		Date:       today,
		IsPlayable: isPlayable,
	}, nil
}

// AddPlayableDate 添加可玩日期
func (l *DateLogic) AddPlayableDate(ctx context.Context, req *bean.AddPlayableDateReq) (*bean.AddPlayableDateResp, error) {
	// 解析日期字符串
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, err
	}

	err = l.dateService.AddPlayableDate(ctx, date, req.Description, req.CreatorID)
	if err != nil {
		return nil, err
	}

	return &bean.AddPlayableDateResp{
		Date:        req.Date,
		Description: req.Description,
		Success:     true,
	}, nil
}

// RemovePlayableDate 移除可玩日期
func (l *DateLogic) RemovePlayableDate(ctx context.Context, req *bean.RemovePlayableDateReq) (*bean.RemovePlayableDateResp, error) {
	// 解析日期字符串
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, err
	}

	err = l.dateService.RemovePlayableDate(ctx, date)
	if err != nil {
		return nil, err
	}

	return &bean.RemovePlayableDateResp{
		Date:    req.Date,
		Success: true,
	}, nil
}
