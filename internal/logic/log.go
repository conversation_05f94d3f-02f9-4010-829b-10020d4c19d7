package logic

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
)

var (
	_logOnce  sync.Once
	_logLogic *LogLogic
)

type LogLogic struct {
	logService      *service.LogService
	minigameService *service.MinigameService
}

func SingletonLogLogic() *LogLogic {
	_logOnce.Do(func() {
		_logLogic = &LogLogic{
			logService:      service.SingletonLogService(),
			minigameService: service.SingletonMinigameService(),
		}
	})
	return _logLogic
}

func (l *LogLogic) GetWechatLog(ctx context.Context, req *bean.GetWechatLogReq) (interface{}, error) {
	config, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return nil, err
	}
	return l.logService.GetWechatLog(ctx, config.AccessToken, req)
}
