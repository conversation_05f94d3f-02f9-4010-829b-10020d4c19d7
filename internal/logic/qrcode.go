package logic

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"

	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
)

var (
	_qrCodeOnce  sync.Once
	_qrCodeLogic *QRCodeLogic
)

type QRCodeLogic struct {
	qrCodeService   *service.QRCodeService
	minigameService *service.MinigameService
}

func SingletonQRCodeLogic() *QRCodeLogic {
	_qrCodeOnce.Do(func() {
		_qrCodeLogic = &QRCodeLogic{
			qrCodeService:   service.SingletonQRCodeService(),
			minigameService: service.SingletonMinigameService(),
		}
	})
	return _qrCodeLogic
}

func (q *QRCodeLogic) CreateQRCode(ctx context.Context, req *bean.CreateQRCodeReq) ([]byte, error) {
	conf, err := q.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return nil, err
	}
	return q.qrCodeService.CreateQRCode(ctx, conf.AccessToken, req.Path, req.Width)
}

// GetQRCode
func (q *QRCodeLogic) GetQRCode(ctx context.Context, req *bean.GetQRCodeReq) ([]byte, error) {
	conf, err := q.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return nil, err
	}
	return q.qrCodeService.GetQRCode(ctx, conf.AccessToken, req)
}

// GetUnlimitedQRCode
func (q *QRCodeLogic) GetUnlimitedQRCode(ctx context.Context, req *bean.GetUnlimitedQRCodeReq) ([]byte, error) {
	conf, err := q.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return nil, err
	}
	return q.qrCodeService.GetUnlimitedQRCode(ctx, conf.AccessToken, req)
}
