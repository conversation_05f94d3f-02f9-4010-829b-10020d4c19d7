package logic

import (
	"context"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
)

var (
	_h5AdminAuthLogic *H5AdminAuthLogic
)

// H5AdminAuthLogic 认证相关的业务逻辑
type H5AdminAuthLogic struct {
	h5AdminAuthService *service.H5AdminAuthService
}

// SingletonH5AdminAuthService 获取 AuthLogic 单例
func SingletonH5AdminAuthLogic() *H5AdminAuthLogic {
	if _h5AdminAuthLogic == nil {
		_h5AdminAuthLogic = &H5AdminAuthLogic{
			h5AdminAuthService: service.SingletonH5AdminAuthService(),
		}
	}
	return _h5AdminAuthLogic
}

// Register 用户注册
// ctx: 上下文
// req: 注册请求参数
// return: 错误信息
func (l *H5AdminAuthLogic) Register(ctx context.Context, req *bean.RegisterRequest) (*bean.RegisterResponse, error) {
	return l.h5AdminAuthService.Register(ctx, req)
}

// Login 用户登录
// ctx: 上下文
// req: 登录请求参数
// return: 登录响应和错误信息
func (l *H5AdminAuthLogic) Login(ctx context.Context, req *bean.LoginRequest) (*bean.LoginResponse, error) {
	return l.h5AdminAuthService.Login(ctx, req)
}

// CheckRechargeLimit 检查用户充值额度
// ctx: 上下文
// req: 充值额度检查请求
// return: 响应结果和错误信息
func (l *H5AdminAuthLogic) CheckRechargeLimit(ctx context.Context, req *bean.RechargeCheckRequest) (*bean.RechargeCheckResponse, error) {
	return l.h5AdminAuthService.CheckRechargeLimit(ctx, req)
}

// GetTimestamp 获取时间戳
// ctx: 上下文
// isSpecifyTime: 是否指定时间
// timestampDiff: 时间戳差值
// return: 时间戳响应和错误信息
func (l *H5AdminAuthLogic) GetTimestamp(ctx context.Context, isSpecifyTime bool, timestampDiff int64) (*bean.TimestampResp, error) {
	return l.h5AdminAuthService.GetTimestamp(ctx, isSpecifyTime, timestampDiff)
}

// CheckRechargePopup 判断是否弹窗（未成年人充值提示）
func (l *H5AdminAuthLogic) CheckRechargePopup(ctx context.Context, req *bean.RechargePopupRequest) (*bean.RechargePopupResponse, error) {
	userModel, err := l.h5AdminAuthService.GetUserByUsername(ctx, req.Username)
	if err != nil {
		return nil, err
	}

	// 计算年龄
	var age int
	if userModel.BirthDateAt > 0 {
		birthTime := time.UnixMilli(userModel.BirthDateAt) // 注意：BirthDateAt 是毫秒级时间戳
		age = l.h5AdminAuthService.CalculateAge(birthTime)
	} else {
		// 如果没有生日信息，按成年人处理，不弹窗
		return &bean.RechargePopupResponse{
			ShowPopup: false,
			Message:   "",
		}, nil
	}

	resp := &bean.RechargePopupResponse{
		ShowPopup: false,
		Message:   "",
	}

	if age < 18 {
		resp.ShowPopup = true
		// 更新为需求指定的文案
		resp.Message = "该游戏不为未满8周岁的用户提供游戏付费服务。8周岁以上未满16周岁的用户，单次充值金额不得超过50元人民币，每月充值金额累计不得超过200元人民币；16周岁以上未满18周岁的用户，单次充值金额不得超过100元人民币，每月充值金额累计不得超过400元人民币。"
	}

	return resp, nil
}
