package logic

import (
	"context"
	"os"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/mysql"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/stretchr/testify/assert"
)

// 初始化测试环境
func initTestEnvForDateLogic() {
	// 设置配置文件路径到项目根目录
	originalDir, _ := os.Getwd()
	defer os.Chdir(originalDir)

	// 切换到项目根目录
	if err := os.Chdir("../../"); err != nil {
		panic("无法切换到项目根目录: " + err.Error())
	}

	config.MustInit()
	logger.InitLogger(&config.GlobConfig.Logger)
	mysql.InitMysql(&config.GlobConfig.Mysql)
	store.InitQueryDB()
	redis.InitRedis(&config.GlobConfig.Redis)
}

func TestDateLogic_CheckPlayableDate(t *testing.T) {
	initTestEnvForDateLogic()

	logic := SingletonDateLogic()
	ctx := context.Background()

	// 测试用例1: 检查周五（应该返回true）
	req := &bean.CheckPlayableDateReq{
		Date: "2024-01-05", // 2024年1月5日是周五
	}
	resp, err := logic.CheckPlayableDate(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "2024-01-05", resp.Date)
	assert.True(t, resp.IsPlayable, "周五应该是可玩日期")

	// 测试用例2: 检查周一（应该返回false）
	req = &bean.CheckPlayableDateReq{
		Date: "2024-01-08", // 2024年1月8日是周一
	}
	resp, err = logic.CheckPlayableDate(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "2024-01-08", resp.Date)
	assert.False(t, resp.IsPlayable, "周一应该不是可玩日期（除非在数据库中）")

	// 测试用例3: 无效日期格式
	req = &bean.CheckPlayableDateReq{
		Date: "invalid-date",
	}
	resp, err = logic.CheckPlayableDate(ctx, req)
	assert.Error(t, err, "无效日期格式应该返回错误")
	assert.Nil(t, resp)

	// 测试用例4: 空日期
	req = &bean.CheckPlayableDateReq{
		Date: "",
	}
	resp, err = logic.CheckPlayableDate(ctx, req)
	assert.Error(t, err, "空日期应该返回错误")
	assert.Nil(t, resp)
}

func TestDateLogic_CheckTodayPlayable(t *testing.T) {
	initTestEnvForDateLogic()

	logic := SingletonDateLogic()
	ctx := context.Background()

	// 测试检查今天是否为可玩日期
	resp, err := logic.CheckTodayPlayable(ctx)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.NotEmpty(t, resp.Date, "应该返回今天的日期")

	// 验证日期格式
	_, parseErr := time.Parse("2006-01-02", resp.Date)
	assert.NoError(t, parseErr, "返回的日期格式应该正确")

	// 获取今天是星期几
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	assert.NoError(t, err)
	today := time.Now().In(beijingLocation)
	weekday := today.Weekday()

	// 如果今天是周五、周六或周日，应该返回true
	expectedPlayable := weekday == time.Friday || weekday == time.Saturday || weekday == time.Sunday

	t.Logf("今天是 %s，预期可玩状态: %v，实际可玩状态: %v", weekday.String(), expectedPlayable, resp.IsPlayable)
}

func TestDateLogic_AddPlayableDate(t *testing.T) {
	initTestEnvForDateLogic()

	logic := SingletonDateLogic()
	ctx := context.Background()

	// 使用一个不太可能冲突的日期
	testDate := "2024-12-25" // 圣诞节

	// 先清理可能存在的测试数据
	removeReq := &bean.RemovePlayableDateReq{Date: testDate}
	logic.RemovePlayableDate(ctx, removeReq) // 忽略错误，因为可能不存在

	// 测试用例1: 添加有效日期
	req := &bean.AddPlayableDateReq{
		Date:        testDate,
		Description: "圣诞节特殊活动",
		CreatorID:   "test_creator",
	}
	resp, err := logic.AddPlayableDate(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, testDate, resp.Date)
	assert.Equal(t, "圣诞节特殊活动", resp.Description)
	assert.True(t, resp.Success)

	// 验证添加成功
	checkReq := &bean.CheckPlayableDateReq{Date: testDate}
	checkResp, err := logic.CheckPlayableDate(ctx, checkReq)
	assert.NoError(t, err)
	assert.True(t, checkResp.IsPlayable, "添加的日期应该是可玩日期")

	// 测试用例2: 尝试重复添加（应该失败）
	resp, err = logic.AddPlayableDate(ctx, req)
	assert.Error(t, err, "重复添加相同日期应该失败")

	// 测试用例3: 无效日期格式
	req = &bean.AddPlayableDateReq{
		Date:        "invalid-date",
		Description: "测试",
		CreatorID:   "test_creator",
	}
	resp, err = logic.AddPlayableDate(ctx, req)
	assert.Error(t, err, "无效日期格式应该返回错误")

	// 清理测试数据
	removeReq = &bean.RemovePlayableDateReq{Date: testDate}
	_, err = logic.RemovePlayableDate(ctx, removeReq)
	assert.NoError(t, err)
}

func TestDateLogic_RemovePlayableDate(t *testing.T) {
	initTestEnvForDateLogic()

	logic := SingletonDateLogic()
	ctx := context.Background()

	// 使用一个不太可能冲突的日期
	testDate := "2024-11-11" // 双十一

	// 先清理可能存在的测试数据
	removeReq := &bean.RemovePlayableDateReq{Date: testDate}
	logic.RemovePlayableDate(ctx, removeReq) // 忽略错误，因为可能不存在

	// 首先添加一个日期
	addReq := &bean.AddPlayableDateReq{
		Date:        testDate,
		Description: "测试移除日期",
		CreatorID:   "test_creator",
	}
	_, err := logic.AddPlayableDate(ctx, addReq)
	assert.NoError(t, err)

	// 验证日期已添加
	checkReq := &bean.CheckPlayableDateReq{Date: testDate}
	checkResp, err := logic.CheckPlayableDate(ctx, checkReq)
	assert.NoError(t, err)
	assert.True(t, checkResp.IsPlayable, "添加的日期应该是可玩日期")

	// 测试用例1: 移除存在的日期
	removeReq = &bean.RemovePlayableDateReq{Date: testDate}
	resp, err := logic.RemovePlayableDate(ctx, removeReq)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, testDate, resp.Date)
	assert.True(t, resp.Success)

	// 验证移除成功
	checkResp, err = logic.CheckPlayableDate(ctx, checkReq)
	assert.NoError(t, err)
	assert.False(t, checkResp.IsPlayable, "移除的日期应该不是可玩日期")

	// 测试用例2: 尝试移除不存在的日期（应该失败）
	resp, err = logic.RemovePlayableDate(ctx, removeReq)
	assert.Error(t, err, "移除不存在的日期应该失败")

	// 测试用例3: 无效日期格式
	removeReq = &bean.RemovePlayableDateReq{Date: "invalid-date"}
	resp, err = logic.RemovePlayableDate(ctx, removeReq)
	assert.Error(t, err, "无效日期格式应该返回错误")
}

func TestDateLogic_DateFormatValidation(t *testing.T) {
	initTestEnvForDateLogic()

	logic := SingletonDateLogic()
	ctx := context.Background()

	// 测试各种日期格式
	testCases := []struct {
		date        string
		shouldError bool
		description string
	}{
		{"2024-01-01", false, "标准格式"},
		{"2024-12-31", false, "年末日期"},
		{"2024-02-29", false, "闰年2月29日"},
		{"2023-02-29", true, "非闰年2月29日"},
		{"2024-13-01", true, "无效月份"},
		{"2024-01-32", true, "无效日期"},
		{"24-01-01", true, "年份格式错误"},
		{"2024/01/01", true, "分隔符错误"},
		{"2024-1-1", true, "缺少前导零"},
		{"", true, "空字符串"},
		{"abc", true, "非数字字符"},
	}

	for _, tc := range testCases {
		t.Run(tc.description, func(t *testing.T) {
			req := &bean.CheckPlayableDateReq{Date: tc.date}
			resp, err := logic.CheckPlayableDate(ctx, req)

			if tc.shouldError {
				assert.Error(t, err, "日期 %s 应该返回错误", tc.date)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err, "日期 %s 应该有效", tc.date)
				assert.NotNil(t, resp)
				assert.Equal(t, tc.date, resp.Date)
			}
		})
	}
}

func TestDateLogic_WeekendLogic(t *testing.T) {
	initTestEnvForDateLogic()

	logic := SingletonDateLogic()
	ctx := context.Background()

	// 测试一周中每一天的可玩状态
	testDates := []struct {
		date     string
		weekday  string
		expected bool
	}{
		{"2024-01-01", "Monday", false},    // 周一
		{"2024-01-02", "Tuesday", false},   // 周二
		{"2024-01-03", "Wednesday", false}, // 周三
		{"2024-01-04", "Thursday", false},  // 周四
		{"2024-01-05", "Friday", true},     // 周五
		{"2024-01-06", "Saturday", true},   // 周六
		{"2024-01-07", "Sunday", true},     // 周日
	}

	for _, td := range testDates {
		t.Run(td.weekday, func(t *testing.T) {
			req := &bean.CheckPlayableDateReq{Date: td.date}
			resp, err := logic.CheckPlayableDate(ctx, req)
			assert.NoError(t, err)
			assert.NotNil(t, resp)
			assert.Equal(t, td.expected, resp.IsPlayable,
				"%s (%s) 的可玩状态应该是 %v", td.date, td.weekday, td.expected)
		})
	}
}
