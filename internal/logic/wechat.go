package logic

import (
	"context"
	"encoding/json"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_wechatOnce  sync.Once
	_wechatLogic *WechatLogic
)

type WechatLogic struct {
	secretService   *service.SecretService
	minigameService *service.MinigameService
}

func SingletonWechatLogic() *WechatLogic {
	_wechatOnce.Do(func() {
		_wechatLogic = &WechatLogic{
			secretService:   service.SingletonSecretService(),
			minigameService: service.SingletonMinigameService(),
		}
	})
	return _wechatLogic
}

// DecryptData 微信数据解密
func (l *WechatLogic) DecryptData(ctx context.Context, req *bean.WechatDecryptReq) (*bean.WechatDecryptResp, error) {
	logger.Logger.InfofCtx(ctx, "[WechatLogic.DecryptData] 开始处理微信解密请求, game_id: %s", req.GameID)

	// 获取游戏配置
	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[WechatLogic.DecryptData] 获取游戏配置失败, game_id: %s, err: %v", req.GameID, err)
		return nil, err
	}

	// 这里需要根据实际业务逻辑来校验signature
	// 目前直接进行解密，后续可以根据需要添加signature校验逻辑
	logger.Logger.InfofCtx(ctx, "[WechatLogic.DecryptData] 开始解密数据, game_id: %s", req.GameID)

	// 使用配置中的session key或其他密钥进行解密
	// 注意：这里需要根据实际业务逻辑确定使用哪个session key
	// 暂时使用传入的参数直接解密
	decryptedStr, err := l.secretService.WechatDataDecryptStr(conf.AppID, req.EncryptedData, req.IV)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[WechatLogic.DecryptData] 数据解密失败, game_id: %s, err: %v", req.GameID, err)
		return nil, err
	}

	logger.Logger.InfofCtx(ctx, "[WechatLogic.DecryptData] 数据解密成功, game_id: %s", req.GameID)

	// 尝试将解密后的字符串解析为JSON
	var decryptedData interface{}
	if err := json.Unmarshal([]byte(decryptedStr), &decryptedData); err != nil {
		// 如果不是JSON格式，直接返回字符串
		logger.Logger.InfofCtx(ctx, "[WechatLogic.DecryptData] 解密数据非JSON格式，返回原始字符串, game_id: %s", req.GameID)
		decryptedData = decryptedStr
	}

	resp := &bean.WechatDecryptResp{
		Data: decryptedData,
	}

	logger.Logger.InfofCtx(ctx, "[WechatLogic.DecryptData] 微信解密处理完成, game_id: %s", req.GameID)
	return resp, nil
}
