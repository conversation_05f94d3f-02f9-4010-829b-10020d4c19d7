package logic

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_reportLogicOnce sync.Once
	_reportLogic     *ReportLogic
)

type ReportLogic struct {
	reportService *service.ReportService
}

func SingletonReportLogic() *ReportLogic {
	_reportLogicOnce.Do(func() {
		_reportLogic = &ReportLogic{
			reportService: service.SingletonReportService(),
		}
	})
	return _reportLogic
}

// SubmitReport handles the submission of a new report
func (l *ReportLogic) SubmitReport(ctx context.Context, req *bean.SubmitReportReq) (*bean.SubmitReportResp, error) {
	reportID, err := l.reportService.CreateReport(ctx, req)
	if err != nil {
		logger.Logger.Errorf("Failed to create report: %v", err)
		return nil, err
	}

	return &bean.SubmitReportResp{
		ReportID: reportID,
	}, nil
}
