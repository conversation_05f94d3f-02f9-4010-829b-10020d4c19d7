package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"github.com/google/uuid"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"

	"git.panlonggame.com/bkxplatform/admin-console/internal/https"
	"git.panlonggame.com/bkxplatform/admin-console/internal/utils"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
)

var (
	_storageOnce  sync.Once
	_storageLogic *StorageLogic
)

type StorageLogic struct {
	storageService    *service.StorageService
	minigameService   *service.MinigameService
	userService       *service.UserService
	douyinService     *service.DouyinService
	secretService     *service.SecretService
	wechatHttpService *https.WechatHttpService
}

func SingletonStorageLogic() *StorageLogic {
	_storageOnce.Do(func() {
		_storageLogic = &StorageLogic{
			storageService:    service.SingletonStorageService(),
			minigameService:   service.SingletonMinigameService(),
			userService:       service.SingletonUserService(),
			douyinService:     service.SingletonDouyinService(),
			secretService:     service.SingletonSecretService(),
			wechatHttpService: https.SingletonWechatHttpService(),
		}
	})
	return _storageLogic
}

// SetUserStorage 设置用户存储
func (l *StorageLogic) SetUserStorage(ctx context.Context, req *bean.SetUserStorageReq) error {
	if len(req.KVList) == 0 {
		return fmt.Errorf("SetUserStorage empty kv list, gameID: %s, userID: %s", req.GameID, req.UserID)
	}

	// 校验user_id长度
	if _, err := uuid.Parse(req.UserID); err != nil {
		return constants.ErrUserIDIsValid
	}

	switch req.PlatformType {
	case constants.PlatformTypeMinigame:
		conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
		if err != nil {
			return err
		}
		info, err := l.userService.GetMinigameModel(ctx, req.UserID)
		if err != nil {
			return err
		}

		// 检测info.SessionKey 是否有效，如无效则返回登录过期
		err = l.wechatHttpService.CheckSessionKey(ctx, info.SessionKey, conf.AccessToken, info.OpenID)
		if err != nil {
			logger.Logger.Warnf("SetUserStorage CheckSessionKey gameid: %s, userid: %s, openid: %s, err: %v", req.GameID, req.UserID, info.OpenID, err)
			// session验证失败时清除相关缓存，确保一致性
			cacheManager := utils.NewCacheManager()
			cacheManager.InvalidateUserCacheOnSessionFailure(ctx, info.OpenID, "SetUserStorage session验证失败")
			return constants.ErrCheckSessionKey
		}

		return l.storageService.SetUserStorage(ctx, conf.AccessToken, info.OpenID, info.SessionKey, req.KVList)
	case constants.PlatformTypeDouyin:
		conf, err := l.douyinService.GetDouyinConf(ctx, req.GameID)
		if err != nil {
			return err
		}
		info, err := l.userService.GetUserDouyinModel(ctx, req.UserID)
		if err != nil {
			return err
		}
		return l.storageService.SetDouyinUserStorage(ctx, conf.AccessToken, info.OpenID, info.SessionKey, req.KVList)
	}
	return constants.ErrStorageUnKnow
}

// RemoveUserStorage 删除用户存储
func (l *StorageLogic) RemoveUserStorage(ctx context.Context, req *bean.RemoveUserStorageReq) error {
	if len(req.Key) == 0 {
		return fmt.Errorf("RemoveUserStorage empty kv list, gameID: %s, userID: %s", req.GameID, req.UserID)
	}
	// 校验user_id长度
	if _, err := uuid.Parse(req.UserID); err != nil {
		return constants.ErrUserIDIsValid
	}

	switch req.PlatformType {
	case constants.PlatformTypeMinigame:
		conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
		if err != nil {
			return err
		}
		info, err := l.userService.GetMinigameModel(ctx, req.UserID)
		if err != nil {
			return err
		}

		// 检测info.SessionKey 是否有效，如无效则返回登录过期
		err = l.wechatHttpService.CheckSessionKey(ctx, info.SessionKey, conf.AccessToken, info.OpenID)
		if err != nil {
			logger.Logger.Warnf("RemoveUserStorage CheckSessionKey gameid: %s, userid: %s, openid: %s, err: %v", req.GameID, req.UserID, info.OpenID, err)
			// session验证失败时清除相关缓存，确保一致性
			cacheManager := utils.NewCacheManager()
			cacheManager.InvalidateUserCacheOnSessionFailure(ctx, info.OpenID, "RemoveUserStorage session验证失败")
			return constants.ErrCheckSessionKey
		}

		return l.storageService.RemoveUserStorage(ctx, conf.AccessToken, info.OpenID, info.SessionKey, req.Key)
	case constants.PlatformTypeDouyin:
		conf, err := l.douyinService.GetDouyinConf(ctx, req.GameID)
		if err != nil {
			return err
		}
		info, err := l.userService.GetUserDouyinModel(ctx, req.UserID)
		if err != nil {
			return err
		}
		return l.storageService.RemoveDouyinUserStorage(ctx, conf.AccessToken, info.OpenID, info.SessionKey, req.Key)
	}
	return constants.ErrStorageUnKnow
}

// SetWechatUserInteractiveStorage 获取微信用户互动存储
func (l *StorageLogic) SetWechatUserInteractiveStorage(ctx context.Context, req *bean.SetWechatUserInteractiveStorageReq) error {
	if len(req.KVList) == 0 {
		return fmt.Errorf("SetWechatUserInteractiveStorage empty kv list, gameID: %s, userID: %s", req.GameID, req.UserID)
	}
	// 校验user_id长度
	if _, err := uuid.Parse(req.UserID); err != nil {
		return constants.ErrUserIDIsValid
	}

	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return err
	}
	info, err := l.userService.GetMinigameModel(ctx, req.UserID)
	if err != nil {
		return err
	}

	// 检测info.SessionKey 是否有效，如无效则返回登录过期
	err = l.wechatHttpService.CheckSessionKey(ctx, info.SessionKey, conf.AccessToken, info.OpenID)
	if err != nil {
		logger.Logger.Warnf("SetWechatUserInteractiveStorage CheckSessionKey gameid: %s, userid: %s, openid: %s, err: %v", req.GameID, req.UserID, info.OpenID, err)
		// session验证失败时清除相关缓存，确保一致性
		cacheManager := utils.NewCacheManager()
		cacheManager.InvalidateUserCacheOnSessionFailure(ctx, info.OpenID, "SetWechatUserInteractiveStorage session验证失败")
		return constants.ErrCheckSessionKey
	}

	return l.storageService.SetWechatUserInteractiveStorage(ctx, conf.AccessToken, info.OpenID, info.SessionKey, req.KVList)
}

// GetWechatUserInteractiveStorageDecryption 获取微信用户互动存储
func (l *StorageLogic) GetWechatUserInteractiveStorageDecryption(ctx context.Context,
	req *bean.GetWechatUserInteractiveStorageDecryptionReq,
) (*bean.GetWechatUserInteractiveStorageDecryptionRes, error) {
	info, err := l.userService.GetMinigameModel(ctx, req.UserID)
	if err != nil {
		return nil, err
	}
	data, err := l.secretService.WechatDataDecryptStr(info.SessionKey, req.EncryptedData, req.IV)
	if err != nil {
		return nil, err
	}

	res := &bean.GetWechatUserInteractiveStorageDecryptionRes{}
	if err = json.Unmarshal([]byte(data), &res); err != nil {
		return nil, err
	}
	return res, nil
}
