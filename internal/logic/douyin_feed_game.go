package logic

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_douyinFeedGameOnce  sync.Once
	_douyinFeedGameLogic *DouyinFeedGameLogic
)

type DouyinFeedGameLogic struct {
	douyinFeedGameService *service.DouyinFeedGameService
}

func SingletonDouyinFeedGameLogic() *DouyinFeedGameLogic {
	_douyinFeedGameOnce.Do(func() {
		_douyinFeedGameLogic = &DouyinFeedGameLogic{
			douyinFeedGameService: service.SingletonDouyinFeedGameService(),
		}
	})
	return _douyinFeedGameLogic
}

func (l *DouyinFeedGameLogic) QueryUserScenes(ctx context.Context, req *bean.QueryUserScenesReq) (*bean.QueryUserScenesResp, error) {
	logger.Logger.InfofCtx(ctx, "DouyinFeedGameLogic QueryUserScenes start: openid=%s, appid=%s", req.OpenID, req.AppID)

	scenes, err := l.douyinFeedGameService.QueryUserScenes(ctx, req.OpenID, req.AppID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "DouyinFeedGameLogic QueryUserScenes service failed: %v", err)
		return nil, err
	}

	resp := &bean.QueryUserScenesResp{
		ErrNo:  constants.DouyinFeedGameErrSuccess,
		ErrMsg: "",
		Data: &bean.QueryUserScenesRespData{
			Scenes: scenes,
		},
	}

	logger.Logger.InfofCtx(ctx, "DouyinFeedGameLogic QueryUserScenes success: openid=%s, scenes_count=%d",
		req.OpenID, len(scenes))

	return resp, nil
}
