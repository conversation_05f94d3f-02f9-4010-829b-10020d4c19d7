package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_gamePlayerOnce    sync.Once
	_gamePlayerHandler *GamePlayerHandler
)

type GamePlayerHandler struct {
	middleware.BaseHandler
	gamePlayerLogic *logic.GamePlayerLogic
}

func SingletonGamePlayerHandler() *GamePlayerHandler {
	_gamePlayerOnce.Do(func() {
		_gamePlayerHandler = &GamePlayerHandler{
			gamePlayerLogic: logic.SingletonGamePlayerLogic(),
		}
	})
	return _gamePlayerHandler
}

// SaveDouyinGamePlayer 保存抖音游戏玩家数据
func (h *GamePlayerHandler) SaveDouyinGamePlayer(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.SaveDouyinGamePlayerReq{}
	if !h.Bind(c, req) {
		return
	}

	err := h.gamePlayerLogic.SaveDouyinGamePlayer(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}
