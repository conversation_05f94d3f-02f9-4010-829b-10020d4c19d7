package handler

import (
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
	"sync"
)

var (
	_shareOnce    sync.Once
	_shareHandler *ShareHandler
)

type ShareHandler struct {
	middleware.BaseHandler
	shareLogic *logic.ShareLogic
}

func SingletonShareHandler() *ShareHandler {
	_shareOnce.Do(func() {
		_shareHandler = &ShareHandler{
			shareLogic: logic.SingletonShareLogic(),
		}
	})
	return _shareHandler
}

// GetShare 获取分享
func (h *ShareHandler) GetShare(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetShareReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.shareLogic.GetShare(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// CreateUserActivityID 创建私密分享
func (h *ShareHandler) CreateUserActivityID(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.CreateUserActivityIDReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.shareLogic.CreateUserActivityID(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// DecryptUserPrivateShare 解密私密分享
func (h *ShareHandler) DecryptUserPrivateShare(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DecryptUserPrivateShareReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.shareLogic.DecryptUserPrivateShare(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
