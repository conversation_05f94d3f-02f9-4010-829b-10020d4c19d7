package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

type RTCHandler struct {
	middleware.BaseHandler
	rtcLogic *logic.RTCLogic
}

var (
	_rtcOnce    sync.Once
	_rtcHandler *RTCHandler
)

func SingletonRTCHandler() *RTCHandler {
	_rtcOnce.Do(func() {
		_rtcHandler = &RTCHandler{
			rtcLogic: logic.SingletonRTCLogic(),
		}
	})
	return _rtcHandler
}

func (h *RTCHandler) CreateRoomID(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.CreateRoomIDReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.rtcLogic.CreateRoomID(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

func (h *RTCHandler) GetRoomID(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetRoomIDReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.rtcLogic.GetRoomID(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
