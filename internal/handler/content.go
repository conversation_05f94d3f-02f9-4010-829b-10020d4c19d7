package handler

import (
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

type ContentHandler struct {
	middleware.BaseHandler
	contentLogic *logic.ContentLogic
}

func SingletonContentHandler() *ContentHandler {
	return &ContentHandler{
		contentLogic: logic.SingletonContentLogic(),
	}
}

// ReportContent 上报监控内容
func (h *ContentHandler) ReportContent(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.ReportContentReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.contentLogic.ReportContent(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
