package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_dateOnce    sync.Once
	_dateHandler *DateHandler
)

type DateHandler struct {
	middleware.BaseHandler
	dateLogic *logic.DateLogic
}

func SingletonDateHandler() *DateHandler {
	_dateOnce.Do(func() {
		_dateHandler = &DateHandler{
			dateLogic: logic.SingletonDateLogic(),
		}
	})
	return _dateHandler
}

// CheckPlayableDate 检查指定日期是否为可玩日期
func (h *DateHandler) CheckPlayableDate(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.CheckPlayableDateReq{}
	if !h.Bind(c, req, false) { // 不需要绑定header
		return
	}
	resp, err := h.dateLogic.CheckPlayableDate(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// CheckTodayPlayable 检查今天是否为可玩日期
func (h *DateHandler) CheckTodayPlayable(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.dateLogic.CheckTodayPlayable(ctx)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
