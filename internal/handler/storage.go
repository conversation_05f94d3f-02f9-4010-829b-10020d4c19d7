package handler

import (
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
	"sync"
)

var (
	_storageOnce    sync.Once
	_storageHandler *StorageHandler
)

type StorageHandler struct {
	middleware.BaseHandler
	storageLogic *logic.StorageLogic
}

func SingletonStorageHandler() *StorageHandler {
	_storageOnce.Do(func() {
		_storageHandler = &StorageHandler{
			storageLogic: logic.SingletonStorageLogic(),
		}
	})
	return _storageHandler
}

// SetUserStorage 设置用户存储
func (h *StorageHandler) SetUserStorage(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.SetUserStorageReq{}
	if !h.Bind(c, req) {
		return
	}
	err := h.storageLogic.SetUserStorage(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// RemoveUserStorage 删除用户存储
func (h *StorageHandler) RemoveUserStorage(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.RemoveUserStorageReq{}
	if !h.Bind(c, req) {
		return
	}
	err := h.storageLogic.RemoveUserStorage(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// SetWechatUserInteractiveStorage 获取微信用户互动存储
func (h *StorageHandler) SetWechatUserInteractiveStorage(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.SetWechatUserInteractiveStorageReq{}
	if !h.Bind(c, req) {
		return
	}
	err := h.storageLogic.SetWechatUserInteractiveStorage(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// GetWechatUserInteractiveStorageDecryption 获取微信用户互动存储
func (h *StorageHandler) GetWechatUserInteractiveStorageDecryption(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetWechatUserInteractiveStorageDecryptionReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.storageLogic.GetWechatUserInteractiveStorageDecryption(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
