package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_wechatOnce    sync.Once
	_wechatHandler *WechatHandler
)

type WechatHandler struct {
	middleware.BaseHandler
	wechatLogic *logic.WechatLogic
}

func SingletonWechatHandler() *WechatHandler {
	_wechatOnce.Do(func() {
		_wechatHandler = &WechatHandler{
			wechatLogic: logic.SingletonWechatLogic(),
		}
	})
	return _wechatHandler
}

// DecryptData 微信数据解密
func (h *WechatHandler) DecryptData(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WechatDecryptReq{}
	if !h.Bind(c, req) {
		return
	}

	resp, err := h.wechatLogic.DecryptData(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}
