package handler

import (
	"encoding/json"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/gin-gonic/gin"
)

var (
	_customerOnce    sync.Once
	_customerHandler *CustomerHandler
)

type CustomerHandler struct {
	middleware.BaseHandler
	customerLogic *logic.CustomerLogic
}

func SingletonCustomerHandler() *CustomerHandler {
	_customerOnce.Do(func() {
		_customerHandler = &CustomerHandler{
			customerLogic: logic.SingletonCustomerLogic(),
		}
	})
	return _customerHandler
}

// PushMinigameMessage 推送
func (h *CustomerHandler) PushMinigameMessage(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.PushMinigameMessageReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.customerLogic.PushMinigameMessage(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// DouyinCustomerServiceCallback 抖音客服推送回调
func (h *CustomerHandler) DouyinCustomerServiceCallback(c *gin.Context) {
	ctx := c.Request.Context()

	// 从URL参数获取gameID
	gameID := c.Param("game_id")
	if gameID == "" {
		errCode := int32(100001)
		h.SuccessOrigin(c, &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  "game_id is required",
		})
		return
	}

	// 获取请求头信息用于签名验证
	msgType := c.Request.Header.Get("x-msg-type")
	headerMap := map[string]string{
		"x-appid":     c.Request.Header.Get("x-appid"),
		"x-msg-type":  msgType,
		"x-nonce-str": c.Request.Header.Get("x-nonce-str"),
		"x-timestamp": c.Request.Header.Get("x-timestamp"),
	}
	requestSignature := c.Request.Header.Get("x-signature")

	// 读取请求体内容用于签名验证和解析
	bodyBytes, err := c.GetRawData()
	if err != nil {
		errCode := int32(100002)
		h.SuccessOrigin(c, &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  "failed to read request body",
		})
		return
	}

	// 调试日志：记录收到的所有参数
	logger.Logger.DebugfCtx(ctx, "[DouyinCustomerServiceCallback] gameID: %s, msgType: %s, headers: %+v, body: %s, signature: %s",
		gameID, msgType, headerMap, string(bodyBytes), requestSignature)

	// 验证签名
	verified, err := h.customerLogic.VerifyDouyinSignature(ctx, gameID, headerMap, string(bodyBytes), requestSignature)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[DouyinCustomerServiceCallback] 验证签名失败: %v", err)
		errCode := int32(100003)
		h.SuccessOrigin(c, &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  "signature verification failed",
		})
		return
	}

	if !verified {
		logger.Logger.WarnfCtx(ctx, "[DouyinCustomerServiceCallback] 签名验证不通过")
		errCode := int32(100003)
		h.SuccessOrigin(c, &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  "invalid signature",
		})
		return
	}

	// 如果是验证请求，直接返回成功
	if msgType == constants.DouyinMsgTypeVerifyRequest {
		logger.Logger.InfofCtx(ctx, "[DouyinCustomerServiceCallback] 收到验证请求，gameID: %s", gameID)
		h.SuccessOrigin(c, &bean.DouyinCustomerServiceCallbackResp{
			Success: true,
		})
		return
	}

	// 解析请求体
	req := &bean.DouyinCustomerServiceCallbackReq{}
	if err := json.Unmarshal(bodyBytes, req); err != nil {
		logger.Logger.DebugfCtx(ctx, "[DouyinCustomerServiceCallback] 解析请求体失败: %v", err)

		errCode := int32(100002)
		h.SuccessOrigin(c, &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  "invalid request body",
		})
		return
	}

	// 设置gameID
	req.GameID = gameID

	// 根据不同的消息类型处理
	switch msgType {
	case constants.DouyinMsgTypeGiftDelivery:
		// 游戏站礼包推送消息处理
		logger.Logger.InfofCtx(ctx, "[DouyinCustomerServiceCallback] 收到游戏站礼包推送消息，gameID: %s", gameID)

		// 解析礼包推送数据
		giftReq := &bean.DouyinGiftDeliveryReq{}
		if err := json.Unmarshal(bodyBytes, giftReq); err != nil {
			logger.Logger.DebugfCtx(ctx, "[DouyinCustomerServiceCallback] 解析礼包推送数据失败: %v", err)
			errCode := int32(100002)
			h.SuccessOrigin(c, &bean.DouyinCustomerServiceCallbackResp{
				Success: false,
				ErrCode: &errCode,
				Reason:  "invalid gift delivery request body",
			})
			return
		}

		// 设置gameID
		giftReq.GameID = gameID

		// 调用Logic层处理礼包推送
		resp, err := h.customerLogic.DouyinGiftDeliveryCallback(ctx, giftReq)
		if err != nil {
			// 根据抖音官方文档，即使内部处理出错，也应该返回200状态码和错误响应格式
			errCode := int32(100002)
			errorResp := &bean.DouyinCustomerServiceCallbackResp{
				Success: false,
				ErrCode: &errCode,
				Reason:  err.Error(),
			}
			h.SuccessOrigin(c, errorResp)
			return
		}
		h.SuccessOrigin(c, resp)
		return
	case constants.DouyinMsgTypeIM:
		// 抖音客服推送消息处理
		resp, err := h.customerLogic.DouyinCustomerServiceCallback(ctx, req)
		if err != nil {
			// 根据抖音官方文档，即使内部处理出错，也应该返回200状态码和错误响应格式
			errCode := int32(100002)
			errorResp := &bean.DouyinCustomerServiceCallbackResp{
				Success: false,
				ErrCode: &errCode,
				Reason:  err.Error(),
			}
			h.SuccessOrigin(c, errorResp)
			return
		}
		h.SuccessOrigin(c, resp)
		return
	default:
		logger.Logger.WarnfCtx(ctx, "[DouyinCustomerServiceCallback] 未知的消息类型: %s", msgType)
		errCode := int32(100004)
		h.SuccessOrigin(c, &bean.DouyinCustomerServiceCallbackResp{
			Success: false,
			ErrCode: &errCode,
			Reason:  "unsupported message type",
		})
		return
	}
}
