package handler

import (
	"bytes"
	"crypto/md5"
	"fmt"
	"io"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/gin-gonic/gin"
)

var (
	_qiyuOnce    sync.Once
	_qiyuHandler *QiyuHandler
)

type QiyuHandler struct {
	middleware.BaseHandler
	qiyuLogic *logic.QiyuLogic
}

func SingletonQiyuHandler() *QiyuHandler {
	_qiyuOnce.Do(func() {
		_qiyuHandler = &QiyuHandler{
			qiyuLogic: logic.SingletonQiyuLogic(),
		}
	})
	return _qiyuHandler
}

func (h *<PERSON>yu<PERSON>andler) GetQiyuAuthCode(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetQiyuAuthCodeReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.qiyuLogic.GetQiyuAuthCode(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// QiyuAuthLogin
func (h *QiyuHandler) QiyuAuthLogin(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.QiyuAuthLoginReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.qiyuLogic.QiyuAuthLogin(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	c.Header(constants.HeaderAuthKey, resp.Token)
	h.Success(c, resp)
}

// CreateSupportTicket
func (h *QiyuHandler) CreateSupportTicket(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.CreateSupportTicketReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.qiyuLogic.CreateSupportTicket(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetTicketLogs
func (h *QiyuHandler) GetTicketLogs(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetTicketLogsReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.qiyuLogic.GetTicketLogs(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetSupportTickets
func (h *QiyuHandler) GetSupportTickets(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetSupportTicketsReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.qiyuLogic.GetSupportTickets(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetTicketTemplate
func (h *QiyuHandler) GetTicketTemplate(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetTicketTemplateReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.qiyuLogic.GetTicketTemplate(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetTicketDetail
func (h *QiyuHandler) GetTicketDetail(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetTicketDetailReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.qiyuLogic.GetTicketDetail(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// UploadFile
func (h *QiyuHandler) UploadFile(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UploadFileReq{}
	if !h.Bind(c, req) {
		return
	}
	file, err := c.FormFile("name")
	if err != nil {
		h.Fail(c, err)
		return
	}
	resp, err := h.qiyuLogic.UploadFile(ctx, file, req.UserID)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// QiyuTicketCallback
func (h *QiyuHandler) QiyuTicketCallback(c *gin.Context) {
	ctx := c.Request.Context()

	// 打印所有ctx.body的数据
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		h.Fail(c, fmt.Errorf("failed to read request body: %w", err))
		return
	}
	logger.Logger.InfofCtx(ctx, "qiyu ticket callback body: %s", string(body))

	// body to md5
	md5Body := fmt.Sprintf("%x", md5.Sum(body))
	logger.Logger.InfofCtx(ctx, "qiyu ticket callback md5 body: %s", md5Body)

	// 重新赋值body
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

	req := &bean.QiyuTicketCallbackReq{}
	if !h.Bind(c, req, true) {
		return
	}
	req.AccessToken = md5Body
	resp, err := h.qiyuLogic.QiyuTicketCallback(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// ReplyTicket
func (h *QiyuHandler) ReplyTicket(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.ReplyTicketReq{}
	if !h.Bind(c, req) {
		return
	}
	err := h.qiyuLogic.ReplyTicket(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}
