package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_logOnce    sync.Once
	_logHandler *logHandler
)

type logHandler struct {
	middleware.BaseHandler
	logLogic *logic.LogLogic
}

func SingletonLogHandler() *logHandler {
	_logOnce.Do(func() {
		_logHandler = &logHandler{
			logLogic: logic.SingletonLogLogic(),
		}
	})
	return _logHandler
}

// GetWechatLog 获取微信的日志数据
func (h *logHandler) GetWechatLog(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetWechatLogReq{}
	if !h.Bind(c, &req) {
		return
	}

	resp, err := h.logLogic.GetWechatLog(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.<PERSON>(c, resp)
}
