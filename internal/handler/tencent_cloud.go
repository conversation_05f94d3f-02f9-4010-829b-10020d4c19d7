package handler

import (
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_tencentCloudHandler *TencentCloudHandler
)

type TencentCloudHandler struct {
	middleware.BaseHandler
	tencentCloudLogic *logic.TencentCloudLogic
}

func SingletonTencentCloudHandler() *TencentCloudHandler {
	if _tencentCloudHandler == nil {
		_tencentCloudHandler = &TencentCloudHandler{
			tencentCloudLogic: logic.SingletonTencentCloudLogic(),
		}
	}
	return _tencentCloudHandler
}

// VerifyRealName 获取实名核身结果
func (h *TencentCloudHandler) VerifyRealName(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.VerifyRealNameReq{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.tencentCloudLogic.VerifyRealName(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}
