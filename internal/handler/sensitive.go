package handler

import (
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
	"sync"
)

var (
	_sensitiveOnce    sync.Once
	_sensitiveHandler *SensitiveHandler
)

type SensitiveHandler struct {
	middleware.BaseHandler
	sensitiveLogic *logic.SensitiveLogic
}

func SingletonSensitiveHandler() *SensitiveHandler {
	_sensitiveOnce.Do(func() {
		_sensitiveHandler = &SensitiveHandler{
			sensitiveLogic: logic.SingletonSensitiveLogic(),
		}
	})
	return _sensitiveHandler
}

// VerifySensitiveMessage 验证敏感词
func (h *SensitiveHandler) VerifySensitiveMessage(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.VerifySensitiveMessageReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.sensitiveLogic.VerifySensitiveMessage(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
