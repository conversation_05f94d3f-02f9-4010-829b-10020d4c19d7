package handler

import (
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/gin-gonic/gin"
)

var (
	_chatMessageOnce    sync.Once
	_chatMessageHandler *ChatMessageHandler
)

// ChatMessageHandler 聊天消息处理器
type ChatMessageHandler struct {
	middleware.BaseHandler
	chatMessageLogic *logic.ChatMessageLogic
}

// SingletonChatMessageHandler 获取聊天消息处理器单例
func SingletonChatMessageHandler() *ChatMessageHandler {
	_chatMessageOnce.Do(func() {
		_chatMessageHandler = &ChatMessageHandler{
			chatMessageLogic: logic.SingletonChatMessageLogic(),
		}
	})
	return _chatMessageHandler
}

// GetMessageHistory 获取消息历史
func (h *ChatMessageHandler) GetMessageHistory(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkorderHistoryReq{}

	// 定义临时结构体获取查询参数中的game_id
	type GameIDQuery struct {
		GameID string `form:"game_id" binding:"required"`
		Limit  int    `form:"limit"` // 可选参数
	}
	var query GameIDQuery

	// 从查询参数中获取game_id和其他参数
	if err := c.ShouldBindQuery(&query); err != nil {
		logger.Logger.Errorf("获取game_id参数失败: %v", err)
		h.BadRequest(c, "game_id参数缺失或无效")
		return
	}

	// 定义临时结构体来绑定请求头信息
	type HeaderInfo struct {
		UserID       string `header:"user_id"`
		DeviceID     string `header:"device_id"`
		AppID        string `header:"app_id"`
		OpenID       string `header:"open_id"`
		RefreshToken string `header:"Refresh-Token"`
		OS           string `header:"os"`
	}

	// 绑定请求头信息
	var headerInfo HeaderInfo
	if err := c.ShouldBindHeader(&headerInfo); err != nil {
		logger.Logger.Errorf("绑定请求头信息失败: %v", err)
		h.BadRequest(c, "请求头信息无效")
		return
	}

	// 手动设置请求结构体字段
	req.UserID = headerInfo.UserID
	req.DeviceID = headerInfo.DeviceID
	req.AppID = headerInfo.AppID
	req.OpenID = headerInfo.OpenID
	req.RefreshToken = headerInfo.RefreshToken
	req.OS = headerInfo.OS

	// 使用查询参数中的game_id和limit
	req.GameID = query.GameID
	if query.Limit > 0 {
		req.Limit = query.Limit
	}

	logger.Logger.InfofCtx(ctx, "[聊天系统] 获取消息历史, GameID: %s, AppID(UnionID): %s, UserID: %s, Limit: %d", req.GameID, req.AppID, req.UserID, req.Limit)

	resp, err := h.chatMessageLogic.GetMessageHistory(ctx, req)
	if err != nil {
		logger.Logger.Errorf("获取消息历史失败: %v", err)
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// SaveFeedback 保存反馈
func (h *ChatMessageHandler) SaveFeedback(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WorkorderFeedbackReq{}
	if !h.Bind(c, req) {
		return
	}

	// DeviceID验证：检查是否为空或只包含空格
	if req.DeviceID == "" || len(strings.TrimSpace(req.DeviceID)) == 0 {
		logger.Logger.WarnfCtx(ctx, "保存反馈: DeviceID验证失败, device_id='%s'", req.DeviceID)
		h.AuthFailed(c, constants.ErrDeviceIDIsEmpty.Error())
		return
	}

	logger.Logger.InfofCtx(ctx, "保存反馈: DeviceID验证通过, device_id='%s'", req.DeviceID)

	err := h.chatMessageLogic.SaveFeedback(ctx, req)
	if err != nil {
		logger.Logger.Errorf("保存反馈失败: %v", err)
		h.Fail(c, err)
		return
	}

	h.Success(c, nil)
}

// SubmitChatMessage 提交聊天消息
func (h *ChatMessageHandler) SubmitChatMessage(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.ChatMessageSubmitReq{}
	if !h.Bind(c, req) {
		return
	}

	// DeviceID验证：检查是否为空或只包含空格
	if req.DeviceID == "" || len(strings.TrimSpace(req.DeviceID)) == 0 {
		logger.Logger.WarnfCtx(ctx, "提交聊天消息: DeviceID验证失败, device_id='%s'", req.DeviceID)
		h.AuthFailed(c, constants.ErrDeviceIDIsEmpty.Error())
		return
	}

	logger.Logger.InfofCtx(ctx, "提交聊天消息: DeviceID验证通过, device_id='%s'", req.DeviceID)

	resp, err := h.chatMessageLogic.SubmitChatMessage(ctx, req)
	if err != nil {
		logger.Logger.Errorf("提交聊天消息失败: %v", err)
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}
