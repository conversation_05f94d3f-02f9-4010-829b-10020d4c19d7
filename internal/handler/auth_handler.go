package handler

import (
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_authHandler *AuthHandler
)

type AuthHandler struct {
	middleware.BaseHandler
	h5AdminAuthLogic *logic.H5AdminAuthLogic
}

func SingletonAuthHandler() *AuthHandler {
	if _authHandler == nil {
		_authHandler = &AuthHandler{
			h5AdminAuthLogic: logic.SingletonH5AdminAuthLogic(),
		}
	}
	return _authHandler
}

// Register 用户注册
func (h *AuthHandler) Register(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.RegisterRequest{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.h5AdminAuthLogic.Register(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.LoginRequest{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.h5AdminAuthLogic.Login(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// Timestamp 获取服务器时间戳
func (h *AuthHandler) Timestamp(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.TimestampRequest{}
	if !h.Bind(c, req, false) {
		return
	}

	resp, err := h.h5AdminAuthLogic.GetTimestamp(ctx, req.IsSpecifyTime, req.TimestampDiff)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// CheckRecharge 检查用户充值限制
func (h *AuthHandler) CheckRecharge(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.RechargeCheckRequest{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.h5AdminAuthLogic.CheckRechargeLimit(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// CheckRechargePopup 判断是否弹窗（未成年人充值提示）
func (h *AuthHandler) CheckRechargePopup(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.RechargePopupRequest{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.h5AdminAuthLogic.CheckRechargePopup(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}
