package handler

import (
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
	"sync"
)

var (
	_redemptionOnce    sync.Once
	_redemptionHandler *RedemptionHandler
)

type RedemptionHandler struct {
	middleware.BaseHandler
	redemptionLogic *logic.RedemptionLogic
}

func SingletonRedemptionHandler() *RedemptionHandler {
	_redemptionOnce.Do(func() {
		_redemptionHandler = &RedemptionHandler{
			redemptionLogic: logic.SingletonRedemptionLogic(),
		}
	})
	return _redemptionHandler
}

// GetRedemptionCode 获取兑换码
func (h *RedemptionHandler) GetRedemptionCode(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetRedemptionCodeReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.redemptionLogic.GetRedemptionCode(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// RedemptionCodeCallback 获取兑换码
func (h *RedemptionHandler) RedemptionCodeCallback(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.RedemptionCodeCallbackReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.redemptionLogic.RedemptionCodeCallback(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
