package handler

import (
	"net/http"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_userOnce    sync.Once
	_userHandler *UserHandler
)

type UserHandler struct {
	middleware.BaseHandler
	userLogic *logic.UserLogic
}

func SingletonUserHandler() *UserHandler {
	_userOnce.Do(func() {
		_userHandler = &UserHandler{
			userLogic: logic.SingletonUserLogic(),
		}
	})
	return _userHandler
}

// Heartbeat 心跳检测
func (h *UserHandler) Heartbeat(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.HeartbeatReq{}
	if !h.Bind(c, req) {
		return
	}
	err := h.userLogic.Heartbeat(ctx, req)
	if err != nil {
		// logger.Logger.Errorf("heartbeat err: %s", err.Error())
		c.JSON(http.StatusOK, gin.H{
			"status": "false",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
	})
}

func (h *UserHandler) GetTimestamp(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.userLogic.GetTimestamp(ctx)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// Login 登录
func (h *UserHandler) Login(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.LoginReq{}
	if !h.Bind(c, &req, true) {
		return
	}
	resp, token, err := h.userLogic.Login(ctx, req)
	c.Header(constants.HeaderAuthKey, token)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.SuccessEncryption(c, resp, true)
}

// LoginV2
func (h *UserHandler) LoginV2(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.LoginV2Req{}
	if !h.Bind(c, &req, true) {
		return
	}
	resp, token, err := h.userLogic.LoginV2(ctx, req)
	c.Header(constants.HeaderAuthKey, token)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.SuccessEncryption(c, resp, true)
}

// LoginV2Test 测试专用登录接口，用于压力测试
func (h *UserHandler) LoginV2Test(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.LoginV2TestReq{}
	if !h.Bind(c, &req, true) {
		return
	}
	resp, token, err := h.userLogic.LoginV2Test(ctx, req)
	c.Header(constants.HeaderAuthKey, token)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.SuccessEncryption(c, resp, true)
}

func (h *UserHandler) LoginSubscribe(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.LoginSubscribeReq{}
	if !h.Bind(c, &req, true) {
		return
	}
	resp, token, err := h.userLogic.LoginSubscribe(ctx, req)
	c.Header(constants.HeaderAuthKey, token)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.SuccessEncryption(c, resp, true)
}

// LoginDouyin 抖音登录
func (h *UserHandler) LoginDouyin(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.LoginDouyinReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, token, err := h.userLogic.LoginDouyin(ctx, req)
	c.Header(constants.HeaderAuthKey, token)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.SuccessEncryption(c, resp, true)
}

func (h *UserHandler) RefreshLogin(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.RefreshLogin{}
	if !h.Bind(c, &req, true) {
		return
	}
	resp, token, err := h.userLogic.RefreshLogin(ctx, req)
	c.Header(constants.HeaderAuthKey, token)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// RefreshLoginV3 刷新登录的加密版本
func (h *UserHandler) RefreshLoginV3(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.RefreshLoginV3Req{}
	if !h.Bind(c, &req, true) {
		return
	}
	resp, token, err := h.userLogic.RefreshLoginV3(ctx, req)
	c.Header(constants.HeaderAuthKey, token)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

func (h *UserHandler) RefreshDouyinLogin(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.RefreshDouyinLoginReq{}
	if !h.Bind(c, &req, true) {
		return
	}
	resp, token, err := h.userLogic.RefreshDouyinLogin(ctx, req)
	c.Header(constants.HeaderAuthKey, token)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// RefreshQQLogin
func (h *UserHandler) RefreshQQLogin(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.RefreshQQLoginReq{}
	if !h.Bind(c, &req, true) {
		return
	}
	resp, token, err := h.userLogic.RefreshQQLogin(ctx, req)
	c.Header(constants.HeaderAuthKey, token)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// UpdateUserInfo 更新用户信息
func (h *UserHandler) UpdateUserInfo(c *gin.Context) {
	ctx := c.Request.Context()
	req := bean.UpdateUserInfoReq{}
	if !h.Bind(c, &req, true) {
		return
	}
	resp, err := h.userLogic.UpdateUserInfo(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// UpdateUserInfoV2 更新用户信息v2
func (h *UserHandler) UpdateUserInfoV2(c *gin.Context) {
	ctx := c.Request.Context()
	req := bean.UpdateUserInfoV2Req{}
	if !h.Bind(c, &req, true) {
		return
	}
	resp, err := h.userLogic.UpdateUserInfoV2(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

func (h *UserHandler) UpdateDouyinUserInfo(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateDouyinUserInfoReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.userLogic.UpdateDouyinUserInfo(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetLastUserKey 获取最后一个用户key
func (h *UserHandler) GetLastUserKey(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetLastUserKeyReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.userLogic.GetLastUserKey(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// LoginQQ 登录QQ
func (h *UserHandler) LoginQQ(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.LoginQQReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, token, err := h.userLogic.LoginQQ(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	c.Header(constants.HeaderAuthKey, token)
	h.SuccessEncryption(c, resp, true)
}

// UpdateQQUserInfo
func (h *UserHandler) UpdateQQUserInfo(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UpdateQQUserInfoReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.userLogic.UpdateQQUserInfo(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
