package handler

import (
	"net/http"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"github.com/gin-gonic/gin"

	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
)

var (
	_qrCodeOnce    sync.Once
	_qrCodeHandler *QRCodeHandler
)

type QRCodeHandler struct {
	middleware.BaseHandler
	qrCodeLogic *logic.QRCodeLogic
}

func SingletonQRCodeHandler() *QRCodeHandler {
	_qrCodeOnce.Do(func() {
		_qrCodeHandler = &QRCodeHandler{
			qrCodeLogic: logic.SingletonQRCodeLogic(),
		}
	})
	return _qrCodeHandler
}

// CreateQRCode 创建二维码
func (h *QRCodeHandler) CreateQRCode(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.CreateQRCodeReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.qrCodeLogic.CreateQRCode(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	// 设置响应头
	c.Header("Content-Type", "image/png")
	c.Header("Content-Disposition", "inline; filename=qrcode.png")

	// 直接写入二进制数据
	c.Data(http.StatusOK, "image/png", resp)
}

// GetQRCode 获取二维码
func (h *QRCodeHandler) GetQRCode(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetQRCodeReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.qrCodeLogic.GetQRCode(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	// 设置响应头
	c.Header("Content-Type", "image/png")
	c.Header("Content-Disposition", "inline; filename=qrcode.png")

	// 直接写入二进制数据
	c.Data(http.StatusOK, "image/png", resp)
}

// GetUnlimitedQRCode
func (h *QRCodeHandler) GetUnlimitedQRCode(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetUnlimitedQRCodeReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.qrCodeLogic.GetUnlimitedQRCode(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	// 设置响应头
	c.Header("Content-Type", "image/png")
	c.Header("Content-Disposition", "inline; filename=qrcode.png")

	// 直接写入二进制数据
	c.Data(http.StatusOK, "image/png", resp)
}
