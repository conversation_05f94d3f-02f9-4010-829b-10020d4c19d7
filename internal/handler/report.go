package handler

import (
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_reportHandler *ReportHandler
)

type ReportHandler struct {
	middleware.BaseHandler
	reportLogic *logic.ReportLogic
}

func SingletonReportHandler() *ReportHandler {
	if _reportHandler == nil {
		_reportHandler = &ReportHandler{
			reportLogic: logic.SingletonReportLogic(),
		}
	}
	return _reportHandler
}

// SubmitReport handles player report submission
func (h *ReportHandler) SubmitReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.SubmitReportReq{}
	if !h.Bind(c, req) {
		return
	}

	resp, err := h.reportLogic.SubmitReport(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}
