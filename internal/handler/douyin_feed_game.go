package handler

import (
	"encoding/json"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/gin-gonic/gin"
)

var (
	_douyinFeedGameOnce    sync.Once
	_douyinFeedGameHandler *DouyinFeedGameHandler
)

type DouyinFeedGameHandler struct {
	middleware.BaseHandler
	douyinFeedGameLogic *logic.DouyinFeedGameLogic
}

func SingletonDouyinFeedGameHandler() *DouyinFeedGameHandler {
	_douyinFeedGameOnce.Do(func() {
		_douyinFeedGameHandler = &DouyinFeedGameHandler{
			douyinFeedGameLogic: logic.SingletonDouyinFeedGameLogic(),
		}
	})
	return _douyinFeedGameHandler
}

// QueryUserScenes 查询用户就绪场景列表
func (h *DouyinFeedGameHandler) QueryUserScenes(c *gin.Context) {
	ctx := c.Request.Context()

	// 绑定请求参数
	req := &bean.QueryUserScenesReq{}
	if !h.Bind(c, req, false) {
		return
	}

	logger.Logger.InfofCtx(ctx, "DouyinFeedGame QueryUserScenes request: openid=%s, appid=%s, nonce=%s, timestamp=%d",
		req.OpenID, req.AppID, req.Nonce, req.Timestamp)

	// 调用业务逻辑
	resp, err := h.douyinFeedGameLogic.QueryUserScenes(ctx, req)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "DouyinFeedGame QueryUserScenes failed: %v", err)
		errorResp := &bean.QueryUserScenesResp{
			ErrNo:  constants.DouyinFeedGameErrInvalidParam,
			ErrMsg: "invalid param",
			Data:   &bean.QueryUserScenesRespData{Scenes: []*bean.DouyinFeedGameSceneInfo{}},
		}
		h.successWithDouyinFeedGameSignature(c, errorResp)
		return
	}

	logger.Logger.InfofCtx(ctx, "DouyinFeedGame QueryUserScenes success: openid=%s, scenes_count=%d",
		req.OpenID, len(resp.Data.Scenes))
	h.successWithDouyinFeedGameSignature(c, resp)
}

func (h *DouyinFeedGameHandler) successWithDouyinFeedGameSignature(c *gin.Context, data interface{}) {
	responseBytes, err := json.Marshal(data)
	if err != nil {
		logger.Logger.ErrorfCtx(c, "DouyinFeedGame failed to marshal response: %v", err)
		errorResp := &bean.QueryUserScenesResp{
			ErrNo:  constants.DouyinFeedGameErrInvalidParam,
			ErrMsg: "internal error",
			Data:   &bean.QueryUserScenesRespData{Scenes: []*bean.DouyinFeedGameSceneInfo{}},
		}
		h.SuccessOrigin(c, errorResp)
		return
	}
	middleware.SetDouyinFeedGameResponseSignature(c, string(responseBytes))
	h.SuccessOrigin(c, data)
}
