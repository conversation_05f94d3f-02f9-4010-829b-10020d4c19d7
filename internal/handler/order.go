package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_orderOnce    sync.Once
	_orderHandler *OrderHandler
)

type OrderHandler struct {
	middleware.BaseHandler
	orderLogic *logic.OrderLogic
}

func SingletonOrderHandler() *OrderHandler {
	_orderOnce.Do(func() {
		_orderHandler = &OrderHandler{
			orderLogic: logic.SingletonOrderLogic(),
		}
	})
	return _orderHandler
}

// GetOrderTotal 获取用户充值总额
func (h *OrderHandler) GetOrderTotal(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetOrderTotalReq{}

	if !h.Bind(c, req) {
		return
	}

	resp, err := h.orderLogic.GetOrderTotal(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// CreateOrder 创建订单
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.CreateOrderReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.orderLogic.CreateOrder(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetOrderDetailCustomer 获取米大师订单详情
func (h *OrderHandler) GetOrderDetailCustomer(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetOrderDetailCustomerReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.orderLogic.GetOrderDetailCustomer(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetOrderDetailSign 获取米大师订单详情
func (h *OrderHandler) GetOrderDetailSign(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetOrderDetailSignReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.orderLogic.GetOrderDetailSign(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetSubscribeConfig 获取订阅配置
func (h *OrderHandler) GetSubscribeConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetSubscribeConfigReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.orderLogic.GetSubscribeConfig(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetOrderDetailH5Pay H5支付
func (h *OrderHandler) GetOrderDetailH5Pay(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetOrderDetailH5PayReq{}
	if !h.Bind(c, req, true) {
		return
	}
	resp, err := h.orderLogic.GetOrderDetailH5Pay(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// WechatPayCallback 微信支付回调
//func (h *OrderHandler) WechatPayCallback(c *gin.Context) {
//	ctx := c.Request.Context()
//	req := &bean.WechatPayCallback{}
//	if !h.Bind(c, req) {
//		return
//	}
//	resp, err := h.orderLogic.WechatPayCallback(ctx, c.Request)
//	if err != nil {
//		h.Fail(c, err)
//		return
//	}
//	h.Success(c, resp)
//}

// WechatMidasSignCallback 微信米大师回调
func (h *OrderHandler) WechatMidasSignCallback(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WechatMidasSignCallbackReq{}
	if !h.Bind(c, req) {
		return
	}
	gameID := c.Param("game_id")
	req.GameID = gameID
	resp, err := h.orderLogic.WechatMidasSignCallback(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.SuccessData(c, resp)
}

// WechatMidasCallback 微信米大师回调
func (h *OrderHandler) WechatMidasCallback(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WechatMidasCallbackReq{}
	if !h.Bind(c, req) {
		return
	}
	gameID := c.Param("game_id")
	req.GameID = gameID
	resp, err := h.orderLogic.WechatMidasCallback(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.SuccessOrigin(c, resp)
}

// WechatH5PayCallback 微信支付回调
func (h *OrderHandler) WechatH5PayCallback(c *gin.Context) {
	ctx := c.Request.Context()
	//req := &bean.WechatPayCallback{}
	//if !h.Bind(c, req) {
	//	return
	//}
	resp, err := h.orderLogic.WechatPayCallback(ctx, c.Request)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// WechatCustomerSignCallback	会话
func (h *OrderHandler) WechatCustomerSignCallback(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WechatCustomerSignCallbackReq{}
	if !h.Bind(c, req) {
		return
	}
	gameID := c.Param("game_id")
	req.GameID = gameID
	resp, err := h.orderLogic.WechatCustomerSignCallback(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.SuccessData(c, resp)
}

// WechatCustomerCallback callback
func (h *OrderHandler) WechatCustomerCallback(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.WechatCustomerCallbackReq{}
	if !h.Bind(c, req) {
		return
	}
	gameID := c.Param("game_id")
	req.GameID = gameID
	resp, err := h.orderLogic.WechatCustomerCallback(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.SuccessData(c, resp)
}

// DouyinPaySignCallback 抖音支付认证回调
func (h *OrderHandler) DouyinPaySignCallback(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DouyinPaySignCallbackReq{}
	if !h.Bind(c, req) {
		return
	}
	gameID := c.Param("game_id")
	req.GameID = gameID
	resp, err := h.orderLogic.DouyinPaySignCallback(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.SuccessData(c, resp)
}

// DouyinPayCallback 抖音支付回调
func (h *OrderHandler) DouyinPayCallback(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DouyinPayCallbackReq{}
	if !h.Bind(c, req) {
		return
	}
	gameID := c.Param("game_id")
	req.GameID = gameID
	resp, err := h.orderLogic.DouyinPayCallback(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// DouyinPaySuccess 抖音支付客户端回调
//func (h *OrderHandler) DouyinPaySuccess(c *gin.Context) {
//	ctx := c.Request.Context()
//	req := &bean.DouyinPaySuccessReq{}
//	if !h.Bind(c, req) {
//		return
//	}
//	resp, err := h.orderLogic.DouyinPaySuccess(ctx, req)
//	if err != nil {
//		h.Fail(c, err)
//		return
//	}
//	h.Success(c, resp)
//}

// GetOrderPayURL 获取用户支付链接(ios)
func (h *OrderHandler) GetOrderPayURL(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetOrderPayURLReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.orderLogic.GetOrderPayURL(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetOrderPayQRCode 获取订单支付二维码
func (h *OrderHandler) GetOrderPayQRCode(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetOrderPayQRCodeReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.orderLogic.GetOrderPayQRCode(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// GetOrderDetailH5 获取H5订单详情
func (h *OrderHandler) GetOrderDetailH5(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetOrderDetailH5PayReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.orderLogic.GetOrderDetailH5(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// TestOrder
func (h *OrderHandler) TestOrder(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.TestOrderReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.orderLogic.TestOrder(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// TestCallbackShipment 测试产品发货回调接口，无需验证
func (h *OrderHandler) TestCallbackShipment(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.ProductShipmentOrder{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.orderLogic.TestCallbackShipment(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
