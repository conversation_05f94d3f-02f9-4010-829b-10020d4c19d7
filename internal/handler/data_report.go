package handler

import (
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
	"sync"
)

var (
	_dataReportOnce    sync.Once
	_dataReportHandler *DataReportHandler
)

type DataReportHandler struct {
	middleware.BaseHandler
	dataReportLogic *logic.DataReportLogic
}

func SingletonDataReportHandler() *DataReportHandler {
	_dataReportOnce.Do(func() {
		_dataReportHandler = &DataReportHandler{
			dataReportLogic: logic.SingletonDataReportLogic(),
		}
	})
	return _dataReportHandler
}

// InitReport
func (h *DataReportHandler) InitReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.InitReportReq{}
	if !h.Bind(c, &req, false) {
		return
	}
	req.IP = h.GetIP(c)
	err := h.dataReportLogic.InitReport(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// BatchInitReport 批量数据
func (h *DataReportHandler) BatchInitReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.BatchInitReportReq{}
	if !h.Bind(c, &req, false) {
		return
	}
	req.IP = h.GetIP(c)
	err := h.dataReportLogic.BatchInitReport(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// UploadReport 上传数据打点上报,聚合上传
func (h *DataReportHandler) UploadReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.UploadReportReq{}
	if !h.Bind(c, &req, true) {
		return
	}
	err := h.dataReportLogic.UploadReport(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

func (h *DataReportHandler) DataReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DataReportReq{}
	if !h.Bind(c, &req, true) {
		return
	}
	req.IP = h.GetIP(c)
	err := h.dataReportLogic.DataReport(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}

// BatchDataReport 批量数据上报
func (h *DataReportHandler) BatchDataReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.BatchDataReportReq{}
	if !h.Bind(c, &req, true) {
		return
	}
	req.IP = h.GetIP(c)
	err := h.dataReportLogic.BatchDataReport(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, nil)
}
