package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/gin-gonic/gin"
)

var (
	_gravityEngineOnce    sync.Once
	_gravityEngineHandler *GravityEngineHandler
)

type GravityEngineHandler struct {
	middleware.BaseHandler
	gravityEngineLogic *logic.GravityEngineLogic
}

func SingletonGravityEngineHandler() *GravityEngineHandler {
	_gravityEngineOnce.Do(func() {
		_gravityEngineHandler = &GravityEngineHandler{
			gravityEngineLogic: logic.SingletonGravityEngineLogic(),
		}
	})
	return _gravityEngineHandler
}

// GetWechatToken 处理引力引擎获取微信token的请求
// 文档: https://doc.gravity-engine.com/turbo-integrated/wechat_token_refresh.html
func (h *GravityEngineHandler) GetWechatToken(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.RefreshWechatTokenReq{}

	if !h.Bind(c, req, false) {
		return
	}

	resp, err := h.gravityEngineLogic.VerifyGravityRequestAndGetToken(ctx, req)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "VerifyGravityRequestAndGetToken failed: %v", err)
		h.Fail(c, err)
		return
	}

	h.SuccessOrigin(c, resp)
}
