package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_captchaOnce    sync.Once
	_captchaHandler *CaptchaHandler
)

type CaptchaHandler struct {
	middleware.BaseHandler
	captchaLogic *logic.CaptchaLogic
}

func SingletonCaptchaHandler() *CaptchaHandler {
	_captchaOnce.Do(func() {
		_captchaHandler = &CaptchaHandler{
			captchaLogic: logic.SingletonCaptchaLogic(),
		}
	})
	return _captchaHandler
}

// GetCaptchaConfig 获取验证码配置
func (h *CaptchaHandler) GetCaptchaConfig(c *gin.Context) {
	ctx := c.Request.Context()
	var req bean.GetCaptchaConfigReq
	if !h.Bind(c, &req) {
		return
	}

	resp, err := h.captchaLogic.GetCaptchaConfig(ctx, &req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// VerifyCaptcha 验证码校验
func (h *CaptchaHandler) VerifyCaptcha(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.VerifyCaptchaReq{}
	if !h.Bind(c, req) {
		return
	}
	req.IP = h.GetIP(c)
	resp, err := h.captchaLogic.VerifyCaptcha(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}
