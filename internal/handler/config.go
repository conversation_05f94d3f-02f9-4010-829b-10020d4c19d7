package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_configOnce    sync.Once
	_configHandler *ConfigHandler
)

type ConfigHandler struct {
	middleware.BaseHandler
	configLogic *logic.ConfigLogic
}

func SingletonConfigHandler() *ConfigHandler {
	_configOnce.Do(func() {
		_configHandler = &ConfigHandler{
			configLogic: logic.SingletonConfigLogic(),
		}
	})
	return _configHandler
}

// GetGameConfig
func (h *ConfigHandler) GetGameConfig(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetGameConfigReq{}
	if !h.Bind(c, req) {
		return
	}
	res, err := h.configLogic.GetGameConfig(ctx, req.GameID)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, res)
}

// GetSwitches
func (h *ConfigHandler) GetSwitches(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetSwitchesReq{}
	if !h.Bind(c, req) {
		return
	}
	res, err := h.configLogic.GetSwitches(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, res)
}

// GetSwitchesV2
func (h *ConfigHandler) GetSwitchesV2(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetSwitchesV2Req{}
	if !h.Bind(c, req) {
		return
	}
	req.IP = h.GetIP(c)
	res, err := h.configLogic.GetSwitchesV2(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, res)
}

// GetAd 获取广告位配置
func (h *ConfigHandler) GetAd(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GetAdReq{}
	if !h.Bind(c, req) {
		return
	}
	res, err := h.configLogic.GetAd(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, res)
}
