package handler

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestDouyinCustomerServiceCallbackResponse 测试抖音客服回调响应格式
// 注意：这个测试需要完整的依赖环境，在实际项目中运行
func TestDouyinCustomerServiceCallbackResponse(t *testing.T) {
	t.Skip("跳过集成测试，需要完整的依赖环境")

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		requestBody    bean.DouyinCustomerServiceCallbackReq
		expectedStatus int
		checkResponse  func(t *testing.T, body []byte)
	}{
		{
			name: "参数不完整的错误响应",
			requestBody: bean.DouyinCustomerServiceCallbackReq{
				AppID:   "",
				OpenID:  "",
				Content: "",
			},
			expectedStatus: 200, // 抖音要求即使错误也返回200
			checkResponse: func(t *testing.T, body []byte) {
				var resp bean.DouyinCustomerServiceCallbackResp
				err := json.Unmarshal(body, &resp)
				assert.NoError(t, err)

				// 验证错误响应格式符合抖音官方规范
				assert.False(t, resp.Success, "错误响应success字段应该为false")
				assert.NotNil(t, resp.ErrCode, "错误响应应该包含err_code")
				assert.Equal(t, int32(100002), *resp.ErrCode, "错误码应该是100002")
				assert.NotEmpty(t, resp.Reason, "错误响应应该包含reason")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试路由
			router := gin.New()
			handler := SingletonCustomerHandler()
			router.POST("/douyin/callback/:game_id", handler.DouyinCustomerServiceCallback)

			// 准备请求体
			requestBody, err := json.Marshal(tt.requestBody)
			assert.NoError(t, err)

			// 创建HTTP请求
			req, err := http.NewRequest("POST", "/douyin/callback/test_game", bytes.NewBuffer(requestBody))
			assert.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			// 创建响应记录器
			w := httptest.NewRecorder()

			// 执行请求
			router.ServeHTTP(w, req)

			// 验证状态码
			assert.Equal(t, tt.expectedStatus, w.Code)

			// 验证响应格式
			tt.checkResponse(t, w.Body.Bytes())
		})
	}
}

// TestDouyinResponseFormat 测试抖音响应格式的JSON结构
func TestDouyinResponseFormat(t *testing.T) {
	// 测试成功响应
	successResp := &bean.DouyinCustomerServiceCallbackResp{
		Success: true,
	}

	successJSON, err := json.Marshal(successResp)
	assert.NoError(t, err)

	expectedSuccessJSON := `{"success":true}`
	assert.JSONEq(t, expectedSuccessJSON, string(successJSON))

	// 测试错误响应
	errCode := int32(100002)
	errorResp := &bean.DouyinCustomerServiceCallbackResp{
		Success: false,
		ErrCode: &errCode,
		Reason:  "失败原因",
	}

	errorJSON, err := json.Marshal(errorResp)
	assert.NoError(t, err)

	expectedErrorJSON := `{"success":false,"err_code":100002,"reason":"失败原因"}`
	assert.JSONEq(t, expectedErrorJSON, string(errorJSON))
}

// TestDouyinOfficialResponseFormat 测试与抖音官方文档完全一致的响应格式
func TestDouyinOfficialResponseFormat(t *testing.T) {
	// 根据抖音官方文档的正常响应示例
	successResp := &bean.DouyinCustomerServiceCallbackResp{
		Success: true,
	}

	successJSON, err := json.Marshal(successResp)
	assert.NoError(t, err)

	// 验证JSON结构
	var parsed map[string]interface{}
	err = json.Unmarshal(successJSON, &parsed)
	assert.NoError(t, err)

	// 验证字段存在性和类型
	success, exists := parsed["success"]
	assert.True(t, exists, "响应必须包含success字段")
	assert.IsType(t, true, success, "success字段必须是布尔类型")
	assert.True(t, success.(bool), "成功响应的success字段必须为true")

	// 验证不应该存在的字段
	_, hasErrCode := parsed["err_code"]
	assert.False(t, hasErrCode, "成功响应不应该包含err_code字段")

	_, hasReason := parsed["reason"]
	assert.False(t, hasReason, "成功响应不应该包含reason字段")

	// 根据抖音官方文档的异常响应示例
	errCode := int32(100002)
	errorResp := &bean.DouyinCustomerServiceCallbackResp{
		Success: false,
		ErrCode: &errCode,
		Reason:  "失败原因",
	}

	errorJSON, err := json.Marshal(errorResp)
	assert.NoError(t, err)

	err = json.Unmarshal(errorJSON, &parsed)
	assert.NoError(t, err)

	// 验证错误响应字段
	success, exists = parsed["success"]
	assert.True(t, exists, "错误响应必须包含success字段")
	assert.False(t, success.(bool), "错误响应的success字段必须为false")

	errCodeValue, exists := parsed["err_code"]
	assert.True(t, exists, "错误响应必须包含err_code字段")
	assert.Equal(t, float64(100002), errCodeValue, "err_code必须是100002")

	reason, exists := parsed["reason"]
	assert.True(t, exists, "错误响应必须包含reason字段")
	assert.Equal(t, "失败原因", reason, "reason字段必须包含错误描述")
}
