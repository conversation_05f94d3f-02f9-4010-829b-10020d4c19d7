package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"

	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"

	"github.com/gin-gonic/gin"

	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
)

var (
	_voipOnce    sync.Once
	_voipHandler *VoipHandler
)

type VoipHandler struct {
	middleware.BaseHandler
	voipLogic *logic.VoipLogic
}

func SingletonVoipHandler() *VoipHandler {
	_voipOnce.Do(func() {
		_voipHandler = &VoipHandler{
			voipLogic: logic.SingletonVoipLogic(),
		}
	})
	return _voipHandler
}

func (h *VoipHandler) JoinRoom(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.JoinRoomReq{}
	if !h.Bind(c, &req, true) {
		return
	}
	resp, err := h.voipLogic.JoinRoom(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
