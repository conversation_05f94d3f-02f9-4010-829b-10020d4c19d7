package bean

import "git.panlonggame.com/bkxplatform/admin-console/internal/middleware"

type HeartbeatReq struct {
	Action string `form:"action"`
}

type TimeRes struct {
	DateTime  string `json:"datetime"`
	Timestamp int64  `json:"timestamp"`
}

type LoginReq struct {
	// DeviceID string `header:"device_id"`
	DeviceID string `header:"deviceId"`

	Code         string `json:"code"`
	Channel      string `json:"channel"`
	ADFrom       string `json:"ad_from"`
	PlatformType string `json:"platform_type"` // 平台类型
	OS           string `json:"os"`
	GameID       string `json:"game_id"`
}

type LoginV2Req struct {
	// DeviceID string `header:"device_id"`
	DeviceID string `header:"deviceId"`

	Code         string `json:"code"`
	Channel      string `json:"channel"`
	ADFrom       string `json:"ad_from"`
	PlatformType string `json:"platform_type"` // 平台类型
	OS           string `json:"os"`
	GameID       string `json:"game_id"`
	Version      int32  `json:"version"`

	// EncryptKey string `json:"encrypt_key"` // 解密code
	EncryptData string `json:"encrypt_data"` // 解密code
	IV          string `json:"iv"`           // 解密code
}

// LoginV2TestReq 测试专用登录请求结构体，用于压力测试
type LoginV2TestReq struct {
	DeviceID string `header:"deviceId"`

	// 简化的参数，用于压测环境
	Channel      string `json:"channel"`
	ADFrom       string `json:"ad_from"`
	PlatformType string `json:"platform_type"` // 平台类型
	OS           string `json:"os"`
	GameID       string `json:"game_id"`

	// 测试用的模拟参数
	TestOpenID  string `json:"test_open_id"`  // 测试用的openid
	TestUnionID string `json:"test_union_id"` // 测试用的unionid，可选
	TestToken   string `json:"test_token"`    // 测试用的token，必填防护
}

type LoginSubscribeReq struct {
	DeviceID string `header:"deviceId"`

	Code    string `json:"code"`
	OrderID string `json:"order_id"`
}

type RefreshLogin struct {
	middleware.Header
	// DeviceID string `json:"device_id"`
	// RefreshToken string `json:"refresh_token"`
}

type RefreshLoginV3Req struct {
	middleware.Header

	EncryptData string `json:"encrypt_data"` // 加密数据
	IV          string `json:"iv"`
	Version     int32  `json:"version"`
}

// LoginRes
type LoginRes struct {
	Sign        string `json:"sign"`
	Timestamp   int64  `json:"timestamp"`
	EncryptUser string `json:"encrypt_user"`

	// Channel string `json:"channel"`
	// ADFrom  string `json:"ad_from"`
	ChannelInfo  *ChannelInfo `json:"channel_info"`
	RefreshToken string       `json:"refresh_token"`
}

//type ChannelInfoRes struct {
//	Channel string `json:"channel"`
//	ADFrom  string `json:"ad_from"`
//}

// GetMinigameInfo
type GetMinigameInfo struct {
	UserID     string `json:"user_id"`
	SessionKey string `json:"session_key"`
}

type UpdateUserInfoReq struct {
	middleware.Header        // 自动绑定，无需传入，用户id, jwt 获取到的用户id
	PlatformType      string `json:"platform_type"`
	IV                string `json:"iv"`             // 微信小游戏向量
	EncryptedData     string `json:"encrypted_data"` // 微信小游戏加密数据
	// DeviceID          string `json:"device_id"`
}

type UpdateUserInfoV2Req struct {
	middleware.Header        // 自动绑定，无需传入，用户id, jwt 获取到的用户id
	PlatformType      string `json:"platform_type"`
	IV                string `json:"iv"`             // 微信小游戏向量
	EncryptedData     string `json:"encrypted_data"` // 微信小游戏加密数据
	// DeviceID          string `json:"device_id"`

	Signature string `json:"signature"`
	RawData   string `json:"raw_data"`
}

type UpdateMinigameInfoRes struct {
	ID int32 `json:"id"`
}

type WechatUserInfo struct {
	OpenID    string    `json:"openId"`
	UnionID   string    `json:"unionid"`
	NickName  string    `json:"nickName"`
	Gender    int32     `json:"gender"`
	City      string    `json:"city"`
	Province  string    `json:"province"`
	Country   string    `json:"country"`
	AvatarURL string    `json:"avatarUrl"`
	Language  string    `json:"language"`
	Watermark Watermark `json:"watermark"`

	SessionKey string `json:"session_key"`
}

type DouyinUserInfo struct {
	OpenID                       string    `json:"openId"`
	UnionID                      string    `json:"unionid"`
	NickName                     string    `json:"nickName"`
	Gender                       int32     `json:"gender"`
	City                         string    `json:"city"`
	Province                     string    `json:"province"`
	Country                      string    `json:"country"`
	AvatarURL                    string    `json:"avatarUrl"`
	Language                     string    `json:"language"`
	Watermark                    Watermark `json:"watermark"`
	RealNameAuthenticationStatus string    `json:"realNameAuthenticationStatus"`
	SessionKey                   string    `json:"session_key"`
}

type GetUserInfoReq struct {
	UserID string `json:"user_id"`
	GameID string `json:"game_id"`
}

type UserMinigameInfo struct {
	ID     int32  `json:"id"`
	UserID string `json:"user_id"`
}

type User struct {
	IsRegister bool `json:"is_register"`
	*UserInfo
	*ChannelInfo
}

type UserInfo struct {
	DeviceID           string `json:"device_id"` // 使用前端传入赋值，不使用数据库的值
	UserID             string `json:"user_id"`
	NickName           string `json:"nickname"`
	Gender             string `json:"gender"` // 实际类型int32
	City               string `json:"city"`
	Province           string `json:"province"`
	Country            string `json:"country"`
	AvatarURL          string `json:"avatar_url"`
	Language           string `json:"language"`
	WatermarkAppID     string `json:"watermark_app_id"`
	WatermarkTimestamp string `json:"watermark_timestamp"` //  实际类型int64
	IsExistUserInfo    string `json:"is_exist_user_info"`  // 是否存在用户信息 (微信小游戏)， 实际类型bool
	RegisterAt         int64  `json:"register_at"`         // 注册时间
}

type ChannelInfo struct {
	Channel   string `json:"channel"`
	ADFrom    string `json:"ad_from"`
	OpenID    string `json:"open_id"`
	ShareCode string `json:"share_code"`
}

type SubscribeUserInfo struct {
	// DeviceID           string `json:"device_id"` // 使用前端传入赋值，不使用数据库的值
	UserID string `json:"user_id"`
	OpenID string `json:"open_id"`

	HasUnionID bool `json:"has_union_id"`
}

type UserInfoRes struct {
	ID     int32  `json:"id"`
	UserID string `json:"user_id"`
}

type LoginDouyinReq struct {
	DeviceID string `header:"deviceId"`

	Code         string `json:"code"`
	Channel      string `json:"channel"`
	ADFrom       string `json:"ad_from"`
	PlatformType string `json:"platform_type"` // 平台类型 douyin_minigame
	OS           string `json:"os"`
	GameID       string `json:"game_id"`
	// AnonymousCode string `json:"anonymous_code"`
}

type LoginDouyinRes struct {
	Sign        string `json:"sign"`
	Timestamp   int64  `json:"timestamp"`
	EncryptUser string `json:"encrypt_user"`

	ChannelInfo *ChannelInfo `json:"channel_info"`
}

type RefreshDouyinLoginReq struct {
	middleware.Header
}

// RefreshQQLoginReq
type RefreshQQLoginReq struct {
	middleware.Header
}

type UpdateDouyinUserInfoReq struct {
	middleware.Header
	PlatformType  string `json:"platform_type"`
	RawData       string `json:"raw_data"`
	Signature     string `json:"signature"`      // 抖音Signature
	IV            string `json:"iv"`             // 抖音小游戏向量
	EncryptedData string `json:"encrypted_data"` // 抖音小游戏加密数据
}

type GetLastUserKeyReq struct {
	GameID string `json:"game_id"`
	UserID string `json:"user_id"`

	// AccessToken string `json:"access_token"`
	// OpenID      string `json:"openid"`
	// Signature   string `json:"signature"`
}

type GetLastUserKeyResp struct {
	Data []*LastUserKeyData `json:"data"`
}

type LastUserKeyData struct {
	EncryptKey string `json:"encrypt_key"`
	Version    int32  `json:"version"`
	ExpireIn   int64  `json:"expire_in"`
	IV         string `json:"iv"`
	CreateTime int64  `json:"create_time"`
}

// LoginQQReq
type LoginQQReq struct {
	DeviceID string `header:"deviceId"`

	Code    string `json:"code" binding:"required"`
	Channel string `json:"channel"`
	ADFrom  string `json:"ad_from"`
	OS      string `json:"os"`
	GameID  string `json:"game_id" binding:"required"`
	// PlatformType string `json:"platform_type"` // 已根据平台划分接口，暂不使用
}

type UpdateQQUserInfoReq struct {
	middleware.Header
	PlatformType  string `json:"platform_type"`
	RawData       string `json:"raw_data"`
	Signature     string `json:"signature"`      // QQSignature
	IV            string `json:"iv"`             // QQ小游戏向量
	EncryptedData string `json:"encrypted_data"` // QQ小游戏加密数据
}
