package bean

type Watermark struct {
	AppID     string `json:"appid"`
	Timestamp int64  `json:"timestamp"`
}

// WechatDecryptReq 微信解密请求
type WechatDecryptReq struct {
	GameID        string `json:"game_id" binding:"required"`        // 游戏ID
	Signature     string `json:"signature" binding:"required"`      // 使用 sha1( rawData + sessionkey ) 得到字符串，用于校验用户信息
	EncryptedData string `json:"encrypted_data" binding:"required"` // 包括 GameClubData 在内的加密数据
	IV            string `json:"iv" binding:"required"`             // 加密算法的初始向量
}

// WechatDecryptResp 微信解密响应
type WechatDecryptResp struct {
	Data interface{} `json:"data"` // 解密后的数据，直接返回给调用方
}
