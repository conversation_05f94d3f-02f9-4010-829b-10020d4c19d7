package bean

// CheckPlayableDateReq 检查指定日期是否为可玩日期请求
type CheckPlayableDateReq struct {
	Date string `form:"date" binding:"required"` // 日期格式：YYYY-MM-DD
}

// CheckPlayableDateResp 检查指定日期是否为可玩日期响应
type CheckPlayableDateResp struct {
	Date       string `json:"date"`        // 日期格式：YYYY-MM-DD
	IsPlayable bool   `json:"is_playable"` // 是否为可玩日期
}

// CheckTodayPlayableResp 检查今天是否为可玩日期响应
type CheckTodayPlayableResp struct {
	Date       string `json:"date"`        // 今天的日期格式：YYYY-MM-DD
	IsPlayable bool   `json:"is_playable"` // 今天是否为可玩日期
}
