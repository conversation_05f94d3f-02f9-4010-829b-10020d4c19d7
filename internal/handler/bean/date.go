package bean

// CheckPlayableDateReq 检查指定日期是否为可玩日期请求
type CheckPlayableDateReq struct {
	Date string `form:"date" binding:"required"` // 日期格式：YYYY-MM-DD
}

// CheckPlayableDateResp 检查指定日期是否为可玩日期响应
type CheckPlayableDateResp struct {
	Date       string `json:"date"`        // 日期格式：YYYY-MM-DD
	IsPlayable bool   `json:"is_playable"` // 是否为可玩日期
}

// CheckTodayPlayableResp 检查今天是否为可玩日期响应
type CheckTodayPlayableResp struct {
	Date       string `json:"date"`        // 今天的日期格式：YYYY-MM-DD
	IsPlayable bool   `json:"is_playable"` // 今天是否为可玩日期
}

// AddPlayableDateReq 添加可玩日期请求
type AddPlayableDateReq struct {
	Date        string `json:"date" binding:"required"`        // 日期格式：YYYY-MM-DD
	Description string `json:"description"`                    // 日期描述信息
	CreatorID   string `json:"creator_id" binding:"required"`  // 创建人ID
}

// AddPlayableDateResp 添加可玩日期响应
type AddPlayableDateResp struct {
	Date        string `json:"date"`        // 日期格式：YYYY-MM-DD
	Description string `json:"description"` // 日期描述信息
	Success     bool   `json:"success"`     // 是否添加成功
}

// RemovePlayableDateReq 移除可玩日期请求
type RemovePlayableDateReq struct {
	Date string `json:"date" binding:"required"` // 日期格式：YYYY-MM-DD
}

// RemovePlayableDateResp 移除可玩日期响应
type RemovePlayableDateResp struct {
	Date    string `json:"date"`    // 日期格式：YYYY-MM-DD
	Success bool   `json:"success"` // 是否移除成功
}
