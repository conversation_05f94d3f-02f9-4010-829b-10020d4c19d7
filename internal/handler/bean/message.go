package bean

// SubMessageNotifyReq 订阅消息
type SubMessageNotifyReq struct {
	GameID       string                 `json:"game_id"`
	PlatformType string                 `json:"platform_type"` // minigame
	UserIDs      []string               `json:"user_ids"`      // 用户id，豪腾: uniqueIds
	PushType     string                 `json:"push_type"`     // 取值：push(直接推)， delayed_push(延迟推送)
	TaskName     string                 `json:"task_name"`     // 名称
	Delay        int32                  `json:"delay"`         // 延迟时间 延迟多少秒, *type=delayed_push 时，必传, -1 表示删除延迟任务
	TemplateID   string                 `json:"template_id"`   // 模板id
	Page         string                 `json:"page"`
	Data         map[string]interface{} `json:"data"`
}

// SubMessageNotifyRes 订阅消息
type SubMessageNotifyRes struct {
	SuccessCount int32 `json:"success_count"`
	// SendResultMsg string `json:"send_result_msg"`
}

// DouyinSubMessageNotifyReq 抖音订阅消息
type DouyinSubMessageNotifyReq struct {
	GameID       string                 `json:"game_id"`
	PlatformType string                 `json:"platform_type"` // douyin_minigame
	UserIDs      []string               `json:"user_ids"`      // 用户id，豪腾: uniqueIds
	PushType     string                 `json:"push_type"`
	TaskName     string                 `json:"task_name"`   // 名称
	TemplateID   string                 `json:"template_id"` // 模板id
	Page         string                 `json:"page"`
	Data         map[string]interface{} `json:"data"`
}

// DouyinSubMessageNotifyRes
type DouyinSubMessageNotifyRes struct {
	SuccessCount int32 `json:"success_count"`
	// SendResultMsg string `json:"send_result_msg"`
}

type DouyinSubMessageReq struct {
	AccessToken string                 `json:"access_token"`
	AppID       string                 `json:"app_id"`
	OpenID      string                 `json:"open_id"`
	TplID       string                 `json:"tpl_id"`
	Data        map[string]interface{} `json:"data"`
	Page        string                 `json:"page"`
}

type DouyinSubMessageResp struct {
	ErrNo   int    `json:"err_no"`
	ErrTips string `json:"err_tips"`
}
