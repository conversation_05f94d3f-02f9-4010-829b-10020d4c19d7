package bean

type VerifySensitiveMessageReq struct {
	UserID       string `json:"user_id" binding:"required"`
	GameID       string `json:"game_id" binding:"required"`
	PlatformType string `json:"platform_type"` // minigame小游戏直接使用微信API检查, douyin_minigame抖音小游戏安全检查
	Msg          string `json:"msg"`
}

type VerifySensitiveMessageRes struct {
	// Result  WechatSecurityCheckResult    `json:"result"`   // 综合结果
	// Detail  []*WechatSecurityCheckDetail `json:"detail"`   // 结果明细
	// TraceID string                       `json:"trace_id"` // 微信返回的请求标识
	TraceID         string                            `json:"trace_id"`
	Source          string                            `json:"source"` // weixin or douyin
	ErrCode         int32                             `json:"err_code"`
	ErrMsg          string                            `json:"err_msg"`
	Result          WechatSecurityCheckResult         `json:"result"`
	Detail          interface{}                       `json:"detail"`
	PlatformDetail  []*SensitiveMessagePlatformDetail `json:"platform_detail"`
	ReplacedContent string                            `json:"replaced_content"`
}

type SensitiveMessagePlatformDetail struct {
	Level   int32  `json:"level"`
	Keyword string `json:"keyword"`
}

type WechatSecurityCheck struct {
	MinigameErr
	Result  WechatSecurityCheckResult    `json:"result"`   // 综合结果
	Detail  []*WechatSecurityCheckDetail `json:"detail"`   // 结果明细
	TraceID string                       `json:"trace_id"` // 微信返回的请求标识
}

type WechatSecurityCheckResult struct {
	Suggest         string `json:"suggest"`
	Label           int    `json:"label"`
	ReplacedContent string `json:"replaced_content"`
}

type WechatSecurityCheckDetail struct {
	Strategy string `json:"strategy"`
	ErrCode  int    `json:"errcode"`
	Suggest  string `json:"suggest"`
	Label    int    `json:"label"`
	Prob     int    `json:"prob"`
	Level    int    `json:"level"`
	Keyword  string `json:"keyword"`
}

// DouyinSecurityCheckDetail 抖音安全检查详情
type DouyinSecurityCheckDetail struct {
	LogID string               `json:"log_id"`
	Data  []DouyinSecurityData `json:"data"`
}

// DouyinSecurityData 抖音安全检查数据
type DouyinSecurityData struct {
	Code     int32                   `json:"code"`
	TaskID   string                  `json:"task_id"`
	DataID   interface{}             `json:"data_id"`
	Cached   bool                    `json:"cached"`
	Predicts []DouyinSecurityPredict `json:"predicts"`
	Msg      string                  `json:"msg"`
}

// DouyinSecurityPredict 抖音安全检查预测结果
type DouyinSecurityPredict struct {
	Prob      int    `json:"prob"`
	Hit       bool   `json:"hit"`
	ModelName string `json:"model_name"`
	Target    string `json:"target"`
}
