package bean

// WorkorderGetTicketTemplateReq 获取工单模板请求
type WorkorderGetTicketTemplateReq struct {
	GameID string `json:"game_id" form:"game_id"` // 游戏ID，非必传
}

// WorkorderGetTicketTemplateResp 获取工单模板响应
type WorkorderGetTicketTemplateResp struct {
	Fields []*WorkorderTemplateField `json:"fields"` // 模板字段列表
}

// WorkorderTemplateField 工单模板字段
type WorkorderTemplateField struct {
	FieldKey    string `json:"field_key"`    // 字段键名
	DisplayName string `json:"display_name"` // 显示名称
	FieldType   string `json:"field_type"`   // 字段类型: string, number, boolean
	IsVisible   bool   `json:"is_visible"`   // toC 是否可见
	Required    bool   `json:"required"`     // 是否必填: 0-否, 1-是
	SortOrder   int32  `json:"sort_order"`   // 排序顺序
}

// DouyinAuthLoginReq 抖音授权登录请求
type DouyinAuthLoginReq struct {
	Code string `json:"code" binding:"required"` // 抖音授权码
}

// DouyinAuthLoginResp 抖音授权登录响应
type DouyinAuthLoginResp struct {
	Token string `json:"token"` // JWT令牌
	// UserID   string `json:"user_id"`   // 用户ID（新用户可能为空）
	// GameName string `json:"game_name"` // 游戏名称
}

// DouyinAuthSignReq 抖音JS授权签名请求
type DouyinAuthSignReq struct {
	NonceStr  string `json:"nonce_str" binding:"required"` // 随机字符串
	Timestamp string `json:"timestamp" binding:"required"` // 时间戳（字符串类型）
	URL       string `json:"url" binding:"required"`       // 需要签名的URL（不包含#及其后面部分）
}

// DouyinAuthSignResp 抖音JS授权签名响应
type DouyinAuthSignResp struct {
	Signature string `json:"signature"` // MD5签名结果
}

// DouyinAccessTokenResp 抖音访问令牌API响应
type DouyinAccessTokenResp struct {
	Data struct {
		AccessToken      string `json:"access_token"`       // 访问令牌
		Captcha          string `json:"captcha"`            // 验证码
		DescUrl          string `json:"desc_url"`           // 描述链接
		Description      string `json:"description"`        // 错误描述
		ErrorCode        int    `json:"error_code"`         // 错误码
		ExpiresIn        int    `json:"expires_in"`         // 过期时间（秒）
		LogID            string `json:"log_id"`             // 日志ID
		OpenID           string `json:"open_id"`            // 用户OpenID
		RefreshExpiresIn int    `json:"refresh_expires_in"` // 刷新令牌过期时间（秒）
		RefreshToken     string `json:"refresh_token"`      // 刷新令牌
		Scope            string `json:"scope"`              // 授权范围
	} `json:"data"`
	Message string `json:"message"` // 响应消息
}
