package bean

type EventRes struct {
	Code int32  `json:"code"`
	Msg  string `json:"msg"`
}

// RefreshWechatTokenReq 刷新微信token的请求结构
type RefreshWechatTokenReq struct {
	GameID           string `form:"game_id" binding:"required"`           // 游戏ID
	GravityTimestamp int64  `form:"gravity_timestamp" binding:"required"` // 引力引擎时间戳
	GravitySign      string `form:"gravity_sign" binding:"required"`      // 引力引擎签名
	PlatformType     string `form:"platform_type"`                        // 平台类型，如 douyin_minigame
}
