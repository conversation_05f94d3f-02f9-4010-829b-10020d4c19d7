package bean

import "git.panlonggame.com/bkxplatform/admin-console/internal/middleware"

type GetShareReq struct {
	GameID       string `form:"game_id"`
	PlatformType string `form:"platform_type"`
}

type GetShareResp struct {
	ShareInfos []*ShareInfo `json:"share_infos"`
}

type ShareInfo struct {
	RoadblockNameCn string `json:"roadblock_name_cn"`
	RoadblockNameEn string `json:"roadblock_name_en"`
	ShareTimeout    int32  `json:"share_timeout"`

	UUID         string `json:"uuid"`
	Name         string `json:"name"`
	Title        string `json:"title"`
	ShareScenes  int32  `json:"share_scenes"`  // 分享场景
	MsgType      int32  `json:"msg_type"`      // 消息类型
	SharePic     string `json:"share_pic"`     // 手动输入图片url
	ThumbnailURL string `json:"thumbnail_url"` // 缩略图url
	ThumbnailID  string `json:"thumbnail_id"`  // 缩略图
	PreviewURL   string `json:"preview_url"`   // 预览图url
	PreviewID    string `json:"preview_id"`    // 预览图id
	//CreatorID    string `json:"creator_id"`
	CreatedAt int64 `json:"created_at"`
	UpdatedAt int64 `json:"updated_at"`
}

// CreateUserActivityIDReq 创建用户活动ID
type CreateUserActivityIDReq struct {
	middleware.Header

	GameID string `json:"game_id"`
	//UserID string `json:"user_id"`
	//OpenID string `json:"open_id"`
}

// CreateUserActivityIDResp 创建用户活动ID
type CreateUserActivityIDResp struct {
	ActivityID     string `json:"activity_id"`
	ExpirationTime int64  `json:"expiration_time"`
	ActivityKey    string `json:"activity_key"`
}

// FetchCreateActivityIDResp 创建用户活动ID
type FetchCreateActivityIDResp struct {
	MinigameErr

	ActivityID     string `json:"activity_id"`
	ExpirationTime int64  `json:"expiration_time"`
}

// DecryptUserPrivateShareReq 解密用户私密分享
type DecryptUserPrivateShareReq struct {
	GameID        string `json:"game_id"`
	IV            string `json:"iv"`
	EncryptedData string `json:"encrypted_data"`
	ActivityKey   string `json:"activity_key"`
	ShareUserID   string `json:"share_user_id"`
	UserID        string `json:"user_id" binding:"required"`
}

// DecryptUserPrivateShareResp 解密用户私密分享
type DecryptUserPrivateShareResp struct {
	MinigameErr
	ActivityID string `json:"activity_id"`
}
