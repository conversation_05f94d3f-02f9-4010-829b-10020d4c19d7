package bean

type CreateRoomIDReq struct {
	GameID       string `json:"game_id" binding:"required"`
	PlatformType string `json:"platform_type" binding:"required"`
}

type CreateRoomIDResp struct {
	GroupID string `json:"group_id"`
}

type GetRoomIDReq struct {
	GameID       string `json:"game_id" binding:"required"`
	PlatformType string `json:"platform_type" binding:"required"`
}

type GetRoomIDResp struct {
	RoomList []*Voip `json:"room_list"`
}

type Voip struct {
	ID           int32  `json:"id"`
	GameID       string `json:"game_id"`
	GroupID      string `json:"group_id"`
	PlatformType string `json:"platform_type"`
	UserID       string `json:"user_id"`
	CreatedAt    int64  `json:"created_at"`
	UpdatedAt    int64  `json:"updated_at"`
}
