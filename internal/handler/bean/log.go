package bean

// GetWechatLogReq 获取微信的日志数据
type GetWechatLogReq struct {
	GameID    string `form:"game_id"`
	Date      string `form:"date"`      // YYYYMMDD格式的日期，仅支持最近7天
	BeginTime int64  `form:"begintime"` // 开始时间，必须是 date 指定日期的时间
	EndTime   int64  `form:"endtime"`   // 结束时间，必须是 date 指定日期的时间
	Start     int    `form:"start"`     // 开始返回的数据下标，用作分页，默认为0
	Limit     int    `form:"limit"`     // 返回的数据条数，用作分页，默认为20
	TraceID   string `form:"traceId"`   // 小程序启动的唯一ID
	URL       string `form:"url"`       // 小程序页面路径，例如pages/index/index
	ID        string `form:"id"`        // 用户微信号或者OpenId
	FilterMsg string `form:"filterMsg"` // 开发者指定的 filterMsg 字段
	Level     int    `form:"level"`     // 日志等级：2(Info)、4(Warn)、8(Error)
}

type LogItem struct {
	Level          int          `json:"level"`          // 日志等级
	Platform       int          `json:"platform"`       // 平台类型
	LibraryVersion string       `json:"libraryVersion"` // 库版本
	ClientVersion  string       `json:"clientVersion"`  // 客户端版本
	ID             string       `json:"id"`             // 用户ID
	Timestamp      int64        `json:"timestamp"`      // 时间戳
	Msg            []LogMessage `json:"msg"`            // 日志消息列表
	URL            string       `json:"url"`            // 页面路径
	TraceID        string       `json:"traceid"`        // 追踪ID
	FilterMsg      string       `json:"filterMsg"`      // 过滤消息
}

// LogMessage 表示单条日志消息
type LogMessage struct {
	Time  int64    `json:"time"`  // 日志时间戳
	Msg   []string `json:"msg"`   // 日志内容
	Level int      `json:"level"` // 日志等级
}

// GetWechatLogResp 获取微信的日志数据
type GetWechatLogResp struct {
	MinigameErr

	Data struct {
		List  []LogItem `json:"list"`  // 日志列表
		Total int       `json:"total"` // 总记录数
	} `json:"data"`
}
