package bean

type SetUserStorageReq struct {
	GameID       string `json:"game_id" binding:"required"`
	UserID       string `json:"user_id" binding:"required"`
	PlatformType string `json:"platform_type"` // minigame douyin_minigame
	KVList       []*KV  `json:"kv_list"`
}

type KV struct {
	Key   string      `json:"key"`
	Value interface{} `json:"value"`
}

type RemoveUserStorageReq struct {
	GameID       string   `json:"game_id" binding:"required"`
	UserID       string   `json:"user_id" binding:"required"`
	PlatformType string   `json:"platform_type"` // minigame douyin_minigame
	Key          []string `json:"key"`
}

type SetWechatUserInteractiveStorageReq struct {
	GameID string `json:"game_id" binding:"required"`
	UserID string `json:"user_id" binding:"required"`
	KVList []*KV  `json:"kv_list"`
}

type GetWechatUserInteractiveStorageDecryptionReq struct {
	GameID        string `json:"game_id" binding:"required"`
	UserID        string `json:"user_id" binding:"required"`
	IV            string `json:"iv"`
	EncryptedData string `json:"encrypted_data"`
}

type GetWechatUserInteractiveStorageDecryptionRes struct {
	KvList []struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	} `json:"kv_list"`
}
