package bean

type CreateQRCodeReq struct {
	GameID string `json:"game_id" binding:"required"`
	Path   string `json:"path" binding:"required"`
	Width  int32  `json:"width"` // 可选
}

type GetQRCodeReq struct {
	GameID     string                 `json:"game_id" binding:"required"`
	Path       string                 `json:"path" binding:"required"`
	EnvVersion string                 `json:"env_version"`
	Width      int32                  `json:"width"`
	AutoColor  bool                   `json:"auto_color"`
	LineColor  map[string]interface{} `json:"line_color"`
	IsHyaline  bool                   `json:"is_hyaline"`
}

type GetUnlimitedQRCodeReq struct {
	GameID     string                 `json:"game_id" binding:"required"`
	Path       string                 `json:"path" binding:"required"`
	Scene      string                 `json:"scene" binding:"required"`
	CheckPath  bool                   `json:"check_path"`
	EnvVersion string                 `json:"env_version"`
	Width      int32                  `json:"width"`
	AutoColor  bool                   `json:"auto_color"`
	LineColor  map[string]interface{} `json:"line_color"`
	IsHyaline  bool                   `json:"is_hyaline"`
}
