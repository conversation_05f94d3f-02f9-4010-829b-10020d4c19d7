package bean

type QueryUserScenesReq struct {
	Nonce     string `form:"nonce" binding:"required"`
	Timestamp int64  `form:"timestamp" binding:"required"`
	OpenID    string `form:"openid" binding:"required"`
	AppID     string `form:"appid" binding:"required"`
}

type QueryUserScenesResp struct {
	ErrNo  int32                    `json:"err_no"`
	ErrMsg string                   `json:"err_msg"`
	Data   *QueryUserScenesRespData `json:"data"`
}

type QueryUserScenesRespData struct {
	Scenes []*DouyinFeedGameSceneInfo `json:"scenes"`
}

type DouyinFeedGameSceneInfo struct {
	Scene      int64    `json:"scene"`
	ContentIDs []string `json:"content_ids"`
	Extra      string   `json:"extra"`
}

const (
	SceneTypeOfflineReward = 1
	SceneTypeEnergyRecover = 2
	SceneTypeImportantDrop = 3
)
