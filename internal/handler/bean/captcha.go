package bean

import (
	"errors"

	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
)

const (
	// CaptchaProviderNetease 网易易盾
	CaptchaProviderNetease = "netease"
	// CaptchaProviderTencent 腾讯云
	CaptchaProviderTencent = "tencent"
)

var (
	// ErrInvalidCaptchaProvider 无效的验证码提供商
	ErrInvalidCaptchaProvider = errors.New("invalid captcha provider")
)

// GetCaptchaConfigReq 获取验证码配置请求
type GetCaptchaConfigReq struct {
	middleware.Header
	GameID    string `json:"game_id" binding:"required"`
	ExtraData string `json:"extra_data"`
	Sign      string `json:"sign"`
	Timestamp int64  `json:"timestamp"`
}

// GetCaptchaConfigResp 获取验证码配置响应
type GetCaptchaConfigResp struct {
	Valid        bool   `json:"valid"`          // 验证结果
	GameID       string `json:"game_id"`        // 游戏ID
	Provider     int32  `json:"provider"`       // 验证码提供商类型 1:腾讯云 2:网易易盾   0
	CaptchaID    string `json:"captcha_id"`     // 验证码ID 腾讯CaptchaAppId 和 网易易盾captchaId ""
	Sign         string `json:"sign"`           // 签名
	Timestamp    int64  `json:"timestamp"`      // 时间戳
	ExtraDataMd5 string `json:"extra_data_md5"` // 额外数据
}

// VerifyCaptchaReq 验证码校验请求
type VerifyCaptchaReq struct {
	middleware.Header
	GameID    string `json:"game_id" binding:"required"`   // 游戏ID
	Ticket    string `json:"ticket" binding:"required"`    // 验证码票据
	Timestamp int64  `json:"timestamp" binding:"required"` // 时间戳
	ExtraData string `json:"extra_data,omitempty"`         // 额外数据（网易易盾支持）
	IP        string `json:"ip"`                           // 用户IP地址
}

// VerifyCaptchaResp 验证码校验响应
type VerifyCaptchaResp struct {
	Valid        bool   `json:"valid"`          // 验证结果
	GameID       string `json:"game_id"`        // 游戏ID
	Provider     int32  `json:"provider"`       // 验证码提供商类型 1:腾讯云 2:网易易盾   0
	CaptchaID    string `json:"captcha_id"`     // 验证码ID 腾讯CaptchaAppId 和 网易易盾captchaId ""
	Sign         string `json:"sign"`           // 签名
	Timestamp    int64  `json:"timestamp"`      // 时间戳
	ExtraDataMd5 string `json:"extra_data_md5"` // 额外数据
}
