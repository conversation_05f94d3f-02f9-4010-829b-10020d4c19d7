package bean

import "git.panlonggame.com/bkxplatform/admin-console/internal/middleware"

type JoinRoomReq struct {
	middleware.Header

	// GameID       string `json:"game_id" binding:"required"`
	// UserID       string `json:"user_id" binding:"required"`
	PlatformType string `json:"platform_type" binding:"required"`
	GroupID      string `json:"group_id" binding:"required"`
}

type JoinRoomResp struct {
	GroupID         string          `json:"group_id"`                    // roomID
	UserID          string          `json:"user_id"`                     // userID
	DouyinVoipToken string          `json:"douyin_voip_token,omitempty"` // Token 的有效时长，建议设置为 24 小时。不建议设置得过长或永不过期。
	WechatVoipSign  *WechatVoipSign `json:"wechat_voip_sign,omitempty"`
}

type WechatVoipSign struct {
	Signature string `json:"signature"`
	NonceStr  string `json:"nonce_str"`
	Timestamp int64  `json:"timestamp"`
} // 微信签名
