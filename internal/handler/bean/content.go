package bean

// 监控内容上报请求
type ReportContentReq struct {
	GameID           string `json:"game_id" binding:"required"`     // 游戏ID
	UserID           string `json:"user_id" binding:"required"`     // 平台ID
	SessionFrom      string `json:"session_from"`                   // 透传参数
	SourceType       string `json:"source_type" binding:"required"` // 文本来源类型
	ServerID         string `json:"server_id" binding:"required"`   // 区服ID
	ServerName       string `json:"server_name" binding:"required"` // 区服名称
	RoleID           string `json:"role_id" binding:"required"`     // 角色ID
	RoleName         string `json:"role_name" binding:"required"`   // 角色名称
	RoleLevel        int32  `json:"role_level"`                     // 角色等级
	AllianceID       string `json:"alliance_id"`                    // 公会ID
	AllianceName     string `json:"alliance_name"`                  // 公会名称
	IsAllianceLeader bool   `json:"is_alliance_leader"`             // 是否公会长
	Content          string `json:"content" binding:"required"`     // 内容文本
}

// 监控内容上报响应
type ReportContentResp struct {
	ContentID string `json:"content_id"` // 内容ID
}

// ContentCallbackData 内容监控回调详情
type ContentCallbackData struct {
	// ID               int64    `json:"id"`                 // 处理记录主键ID
	ContentID        string   `json:"content_id"`         // 关联的内容ID
	UserID           string   `json:"user_id"`            // 平台ID
	SessionFrom      string   `json:"session_from"`       // 透传参数
	ServerID         string   `json:"server_id"`          // 区服ID
	ServerName       string   `json:"server_name"`        // 区服名称
	RoleID           string   `json:"role_id"`            // 角色ID
	RoleName         string   `json:"role_name"`          // 角色名称
	RoleLevel        int32    `json:"role_level"`         // 角色等级
	AllianceID       string   `json:"alliance_id"`        // 公会ID
	AllianceName     string   `json:"alliance_name"`      // 公会名称
	IsAllianceLeader bool     `json:"is_alliance_leader"` // 是否公会长
	SourceType       string   `json:"source_type"`        // 文本来源类型
	Content          string   `json:"content"`            // 内容文本
	Operations       []string `json:"operations"`         // 处理操作列表
	Action           int32    `json:"action"`             // 处理动作值
	ActionParam      string   `json:"action_param"`       // 处理参数(JSON格式)
	OperatorID       string   `json:"operator_id"`        // 操作人
	OperatorName     string   `json:"operator_name"`      // 操作人名称
	CreatedAt        int64    `json:"created_at"`         // 创建时间戳
}

// ContentCallbackReq 内容监控系统回调
type ContentCallbackReq struct {
	Attempt             int                  `json:"attempt"`
	GameID              string               `json:"game_id"`
	CallbackURL         string               `json:"callback_url"`
	ContentCallbackData *ContentCallbackData `json:"content_callback_data"`
}

// ContentCallbackResp 内容监控回调响应
type ContentCallbackResp struct {
	Code int32  `json:"code"`
	Msg  string `json:"msg"`
}
