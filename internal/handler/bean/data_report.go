package bean

import "git.panlonggame.com/bkxplatform/admin-console/internal/middleware"

type InitReportReq struct {
	PlatformAppID    string `json:"platform_app_id"`    // 前端传递的数数app id
	PlatformDeviceID string `json:"platform_device_id"` // 未登录情况下提供的设备id
	PlatformGameID   string `json:"platform_game_id"`   // game id

	IP         string                 `json:"ip"`
	IPRegionID string                 `json:"ip_region_id"`
	EventName  string                 `json:"event_name"`
	UUID       string                 `json:"uuid" binding:"required"`
	EventType  string                 `json:"event_type" binding:"required"`
	Properties map[string]interface{} `json:"properties" binding:"required"`
}

type BatchInitReportReq struct {
	PlatformAppID    string `json:"platform_app_id"`    // 前端传递的数数app id
	PlatformDeviceID string `json:"platform_device_id"` // 未登录情况下提供的设备id
	PlatformGameID   string `json:"platform_game_id"`   // game id

	IP          string             `json:"ip"`
	IPRegionID  string             `json:"ip_region_id"`
	InitReports []*BatchInitReport `json:"init_reports"`
}

type BatchInitReport struct {
	Time       string                 `json:"time"`
	EventName  string                 `json:"event_name"`
	UUID       string                 `json:"uuid" binding:"required"`
	EventType  string                 `json:"event_type" binding:"required"`
	Properties map[string]interface{} `json:"properties" binding:"required"`
}

type DataReportReq struct {
	middleware.Header
	// PlatformAppID    string `json:"platform_app_id"`    // 前端传递的数数app id
	// PlatformDeviceID string `json:"platform_device_id"` // 未登录情况下提供的设备id
	RoleID string `json:"role_id"`

	IP         string                 `json:"ip"`
	IPRegionID string                 `json:"ip_region_id"`
	EventName  string                 `json:"event_name"`
	UUID       string                 `json:"uuid"`
	EventType  string                 `json:"event_type"`
	Properties map[string]interface{} `json:"properties"`
}

type BatchDataReportReq struct {
	middleware.Header

	IP          string             `json:"ip"`
	IPRegionID  string             `json:"ip_region_id"`
	DataReports []*BatchDataReport `json:"data_reports"` // 上传的数据报告数组
}

type BatchDataReport struct {
	RoleID     string                 `json:"role_id"`
	Time       string                 `json:"time"`
	EventName  string                 `json:"event_name"`
	UUID       string                 `json:"uuid" binding:"required"`
	EventType  string                 `json:"event_type" binding:"required"`
	Properties map[string]interface{} `json:"properties" binding:"required"`
}

type UploadReport struct {
	UUID       string                 `json:"uuid"`
	AccountID  string                 `json:"account_id"`  // 对应数数平台的Account_ID
	DistinctID string                 `json:"distinct_id"` // 对应数数平台的Device_ID
	AppID      string                 `json:"app_id"`      // 对应数数平台的App_ID
	Timestamp  int64                  `json:"timestamp"`   // 对应数数平台的Timestamp
	GameID     string                 `json:"game_id"`
	ClientIP   string                 `json:"client_ip"`
	EventName  string                 `json:"event_name"`
	EventType  string                 `json:"event_type"`
	Properties map[string]interface{} `json:"properties"`
}

type UploadReportReq struct {
	Logs []*UploadReport `json:"logs"`
}
