package bean

import "git.panlonggame.com/bkxplatform/admin-console/internal/middleware"

// SaveDouyinGamePlayerReq 保存抖音游戏玩家数据请求
type SaveDouyinGamePlayerReq struct {
	middleware.Header

	RoleID              string `json:"role_id"`               // 角色ID
	PlayerID            string `json:"player_id"`             // 玩家ID
	PlayerName          string `json:"player_name"`           // 玩家名称
	PlayerLevel         int32  `json:"player_level"`          // 玩家等级
	RechargeTotalAmount int32  `json:"recharge_total_amount"` // 充值总金额
	CustomData          string `json:"custom_data"`           // 自定义数据
	Zone                string `json:"zone"`                  // 区服
}

// SaveDouyinGamePlayerResp 保存抖音游戏玩家数据响应
type SaveDouyinGamePlayerResp struct {
}
