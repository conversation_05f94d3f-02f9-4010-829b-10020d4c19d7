package bean

type MinigameErr struct {
	ErrCode int32  `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

type AccessTokenRes struct {
	MinigameErr
	AccessToken string `json:"access_token"`
	ExpiresIn   int32  `json:"expires_in"`
}

type Code2SessionRes struct {
	MinigameErr
	OpenID     string `json:"openid"`
	SessionKey string `json:"session_key"`
	UnionID    string `json:"unionid"`
}

type JSAPITicket struct {
	MinigameErr
	Ticket    string `json:"ticket"`
	ExpiresIn int32  `json:"expires_in"`
}

type WechatEncryptKey struct {
	MinigameErr
	KeyInfoList []*WechatKeyInfoList `json:"key_info_list"`
}

type WechatKeyInfoList struct {
	EncryptKey string `json:"encrypt_key"`
	IV         string `json:"iv"`
	Version    int32  `json:"version"`
	ExpireIn   int64  `json:"expire_in"`
	CreateTime int64  `json:"create_time"`
}
