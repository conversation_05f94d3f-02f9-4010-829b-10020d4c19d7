package bean

type PushMinigameMessageReq struct {
	GameID   string   `json:"game_id"`
	UserIDs  []string `json:"user_ids"` // 用户 uniqueID，限最多一次发送 100 个
	PushType string   `json:"push_type"`
	MsgType  string   `json:"msg_type"`  // text、image、miniprogrampage、link
	TaskName string   `json:"task_name"` // 任务名称，用于更新延迟推送消息
	Delay    int32    `json:"delay"`     // 延时秒数，如果type是 delayed_push时，必须大于0

	Content  string `json:"content"`   // 消息内容 text类型
	Title    string `json:"title"`     // 小程序卡片
	PagePath string `json:"page_path"` // 小程序卡片
	ImgUrl   string `json:"img_url"`   // image和 小程序卡片类型

	LinkTitle    string `json:"link_title"`     // 链接消息的标题
	LinkDesc     string `json:"link_desc"`      // 链接消息的描述
	LinkUrl      string `json:"link_url"`       // 链接消息的跳转地址
	LinkThumbURL string `json:"link_thumb_url"` // 链接消息的图片地址
}

type PushMinigameMessageResp struct {
	SuccessCount int32 `json:"success_count"`
}

type CallbackMsgRequest struct {
	GameID      string      `json:"game_id"`      // 游戏ID
	UserID      string      `json:"user_id"`      // 用户 uniqueID
	OpenID      string      `json:"open_id"`      // 用户 openID
	SessionFrom string      `json:"session_from"` // 用户进入客服消息来源
	MsgType     string      `json:"msg_type"`     // 消息类型
	Content     string      `json:"content"`      // 消息内容
	Event       string      `json:"event"`        // 事件类型
	Body        interface{} `json:"body"`         // 卡片数据对象，当msg_type为 miniprogrampage 时，该不为空
}

type CallbackMsgResp struct {
	Code int32  `json:"code"`
	Msg  string `json:"msg"`
}

type MediumInfo struct {
	Type      string `json:"type"`
	MediaID   string `json:"media_id"`
	CreatedAt int64  `json:"created_at"`
}
