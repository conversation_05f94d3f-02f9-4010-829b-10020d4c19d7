package bean

import "git.panlonggame.com/bkxplatform/admin-console/internal/middleware"

// CreateSessionReq represents the request parameters for creating a session
type CreateSessionReq struct {
	UID                string `json:"uid"`                          // 必填，访客在调用方系统里的标志，用于唯一确定用户身份
	ProductID          string `json:"productId,omitempty"`          // 可选，当前咨询应用唯一标识
	FromType           string `json:"fromType,omitempty"`           // 可选，当前访客咨询来源渠道
	RobotID            int64  `json:"robotId,omitempty"`            // 可选，机器人id
	WelcomeTemplateID  int64  `json:"welcomeTemplateId,omitempty"`  // 可选，七鱼机器人欢迎语模版id
	QuestionTemplateID int64  `json:"questionTemplateId,omitempty"` // 可选，七鱼机器人常见问题模版id
	// FromPage           string `json:"fromPage,omitempty"`           // 可选，当前访客咨询来源页面
}

type CreateSessionResp struct {
	Code int `json:"code"`
	// Message string `json:"message"`
	Data struct {
		SessionID    int    `json:"sessionId"`
		UserID       int    `json:"userId"`
		RobotID      int    `json:"robotId"`
		RobotName    string `json:"robotName"`
		RobotLogoURL string `json:"robotLogoUrl"`
		Welcome      struct {
			MsgType string `json:"msgType"`
			MsgBody struct {
				Content string `json:"content"`
			} `json:"msgBody"`
		} `json:"welcome"`
		CommonQuestions struct {
			QuestionGuide string `json:"questionGuide"`
			QuestionList  []struct {
				ID       int    `json:"id"`
				Question string `json:"question"`
				Type     int    `json:"type"`
			} `json:"questionList"`
		} `json:"commonQuestions"`
	} `json:"data"`
}

// SessionChatReq
type SessionChatReq struct {
	UID       string    `json:"uid"`                 // 必填，访客在调用方系统里的标志，用于唯一确定用户身份
	ProductID string    `json:"productId,omitempty"` // 可选，当前咨询应用唯一标识
	FromType  string    `json:"fromType,omitempty"`  // 可选，当前访客咨询来源渠道
	RobotID   int64     `json:"robotId,omitempty"`   // 可选，机器人id
	Msg       CommonMsg `json:"msg"`                 // 必填，访客消息，目前仅支持 text，image，audio，workflow四种类型
	SkipTrans int       `json:"skipTrans,omitempty"` // 可选，是否跳过转人工逻辑判断,0-不跳过1-跳，默认值0
}

type CommonMsg struct {
	MsgType string  `json:"msgType"` // 消息类型
	MsgBody MsgBody `json:"msgBody"` // 消息内容
}

type MsgBody struct {
	Content string `json:"content"`
}

// SessionChatResp represents the response structure for a session chat
type SessionChatResp struct {
	Code int `json:"code"`
	// Message string `json:"message"`
	Data struct {
		SessionID  int `json:"sessionId"`
		UserID     int `json:"userId"`
		RobotID    int `json:"robotId"`
		AnswerList []struct {
			MsgID      string `json:"msgId"`
			AnsType    int    `json:"ansType"`
			AnsContent struct {
				Answer struct {
					MsgType string `json:"msgType"`
					MsgBody struct {
						Content string `json:"content"`
					} `json:"msgBody"`
				} `json:"answer"`
				ConnectQuestions []struct {
					Question string `json:"question"`
					Type     int    `json:"type"`
				} `json:"connectQuestions"`
			} `json:"ansContent"`
			MsgEvaluation struct {
				Evaluation       int    `json:"evaluation"`
				EvaluationReason int    `json:"evaluationReason"`
				EvaluationGuide  string `json:"evaluationGuide"`
			} `json:"msgEvaluation"`
		} `json:"answerList"`
	} `json:"data"`
}

type GetQiyuAuthCodeReq struct {
	GameID string `json:"game_id" binding:"required"`
	OpenID string `json:"open_id" binding:"required"`
}

type GetQiyuAuthCodeResp struct {
	Code string `json:"code"`
}

type QiyuAuthLoginReq struct {
	GameID string `json:"game_id"`
	OpenID string `json:"open_id"`
	Code   string `json:"code" binding:"required"`
	// 注意：Source 和 DouyinOpenID 字段已迁移至 WorkorderCreateSupportTicketReq 中
}

type QiyuAuthLoginResp struct {
	Token string `json:"token"`
}

// CreateSupportTicketReq
type CreateSupportTicketReq struct {
	middleware.Header

	Title        string     `json:"title" binding:"required"`   // 工单标题
	Content      string     `json:"content" binding:"required"` // 工单内容
	TemplateID   int        `json:"template_id"`                // 模板ID
	CustomFields []struct { // 自定义字段列表
		ID    int         `json:"id"`    // 自定义字段ID
		Value interface{} `json:"value"` // 自定义字段值
	} `json:"custom_fields"`
	Attachments []struct {
		FileName string `json:"fileName"` //附件文件名
		Type     int    `json:"type"`     //附件类型
		Payload  string `json:"payload"`  //附件BASE64编码
	} `json:"attachments,omitempty"` //附件列表

	UID      string `json:"uid"`       //用户ID-服务器填充
	StaffID  int    `json:"staff_id"`  // 员工ID
	UserName string `json:"user_name"` // 用户名

	// TypeId        int        `json:"type_id" binding:"required"`         // 工单类型ID
	// UniqueId      string     `json:"unique_id"`       // 唯一ID
	// TargetStaffID int    `json:"target_staff_id"` // 目标员工ID

	// UserName      string   `json:"userName"`      //用户名
	// UserMobile    string   `json:"userMobile"`    //用户手机号
	// UserEmail     string   `json:"userEmail"`     //用户邮箱
	// ToEmail       string   `json:"toEmail"`       //目标邮箱
	// TargetGroupId int      `json:"targetGroupId"` //目标组ID
	// Priority    int      `json:"priority"`    //优先级
	// FollowerIds []string `json:"followerIds"` //关注者ID列表
	// Properties []struct {
	// 	Key   string `json:"key"`   //自定义字段键
	// 	Value string `json:"value"` //自定义字段值
	// } `json:"properties"` //自定义字段列表
}

type SupportTicketReq struct {
	Title        string     `json:"title"`      // 工单标题
	Content      string     `json:"content"`    // 工单内容
	TemplateID   int        `json:"templateId"` // 模板ID
	CustomFields []struct { // 自定义字段列表
		ID    int         `json:"id"`    // 自定义字段ID
		Value interface{} `json:"value"` // 自定义字段值
	} `json:"customFields"`
	Attachments []struct {
		FileName string `json:"fileName"` //附件文件名
		Type     int    `json:"type"`     //附件类型
		Payload  string `json:"payload"`  //附件BASE64编码
	} `json:"attachments,omitempty"` //附件列表

	UID      string `json:"uid"`      //用户ID-服务器填充
	StaffID  int    `json:"staffId"`  // 员工ID
	UserName string `json:"userName"` // 当做游戏ID
}

// GetTicketLogsReq
type GetTicketLogsReq struct {
	middleware.Header
	TicketID int `json:"ticket_id" binding:"required"`
}

// TicketLogsResp
type TicketLogsResp struct {
	Code int    `json:"code"`
	Data string `json:"data"`
}

type GetTicketLogsResp struct {
	Code int              `json:"code"`
	Data []TicketLogsData `json:"data"`
}

type TicketLogsData struct {
	Action         string       `json:"action"`
	ActionType     int          `json:"actionType"`
	CorpName       string       `json:"corpName"`
	ID             int64        `json:"id"`
	Info           []LogInfo    `json:"info"`
	Operator       string       `json:"operator"`
	OperatorCorpID int64        `json:"operatorCorpId"`
	OperatorID     int64        `json:"operatorId"`
	Time           int64        `json:"time"`
	Attachments    []Attachment `json:"attachments"`
}

type Attachment struct {
	Name string `json:"name"`
	Size int    `json:"size"`
	Type int    `json:"type"`
	URL  string `json:"url"`
}

type LogInfo struct {
	Content   string `json:"content"`
	Title     string `json:"title"`
	TitleLang string `json:"titleLang"`
}
type Log struct {
	ID      int    `json:"id"`
	Content string `json:"content"`
	Time    int64  `json:"time"`
}

type CreateSupportTicketResp struct {
	Code    int         `json:"code"`
	Message interface{} `json:"message"`
}

// GetSupportTicketsReq
type GetSupportTicketsReq struct {
	middleware.Header

	IsAll  bool `json:"is_all"`
	Limit  int  `json:"limit"`
	Offset int  `json:"offset"`
	// SortBy string `json:"sort_by"`
	// Order  string `json:"order"`
}
type SupportTicketsResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type GetSupportTicketsResp struct {
	Code    int                `json:"code"`
	Message SupportTicketsData `json:"message"`
}

type SupportTicketsData struct {
	Total   int                  `json:"total"`
	Tickets []SupportTicketsItem `json:"tickets"`
}

type SupportTicketsItem struct {
	ID               int           `json:"id"`
	Title            string        `json:"title"`
	ConnectionID     int           `json:"connectionId"`
	ConnectionType   int           `json:"connectionType"`
	Content          string        `json:"content"`
	CreateTime       int64         `json:"createTime"`
	CrmForeignId     string        `json:"crmForeignId"`
	CrmId            string        `json:"crmId"`
	CrmUserEmail     string        `json:"crmUserEmail"`
	CrmUserName      string        `json:"crmUserName"`
	CrmUserPhone     string        `json:"crmUserPhone"`
	Custom           []CustomField `json:"custom"`
	Follower         string        `json:"follower"` // Consider using []string if it's always a JSON array
	GroupID          int           `json:"groupId"`
	HolderID         int           `json:"holderId"`
	HolderPlatformID int           `json:"holderPlatformId"`
	ModifyLast       bool          `json:"modifyLast"`
	Priority         int           `json:"priority"` // Assuming this is what "priorit" was meant to be
	Status           int           `json:"status"`
	StaffID          int           `json:"staffId"`
	// Add other fields as needed
}

type CustomField struct {
	FieldID int    `json:"fieldId"`
	ID      int    `json:"id"`
	Name    string `json:"name"`
	Value   string `json:"value"`
}

// GetTicketTemplateReq
type GetTicketTemplateReq struct {
	middleware.Header

	TemplateID int `json:"template_id" binding:"required"`
}

type TicketTemplateResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type GetTicketTemplateResp struct {
	Code    int                     `json:"code"`
	Message []GetTicketTemplateItem `json:"message"`
}

type GetTicketTemplateItem struct {
	ID          int    `json:"id"`
	FieldID     int    `json:"fieldId"`
	Name        string `json:"name"`
	Required    int    `json:"required"`
	Type        int    `json:"type"`
	Status      int    `json:"status"`
	Description string `json:"description"`
	Prefill     string `json:"prefill"`
	Hint        string `json:"hint"`
	Customer    int    `json:"customer"`
}

// GetTicketDetailReq
type GetTicketDetailReq struct {
	middleware.Header

	TicketID int `json:"ticket_id"`
}

// UploadFileReq
type UploadFileReq struct {
	middleware.Header
}

type UploadFileResp struct {
	FileURL string `json:"file_url"`
}

type QiyuTicketCallbackQuery struct {
	AccessToken       string `form:"accessToken"`
	Timestamp         string `form:"timestamp"`
	Time              string `form:"time"`
	Checksum          string `form:"checksum"`
	ChecksumAlgorithm string `form:"checksumAlgorithm"`
}

type QiyuTicketCallbackReq struct {
	QiyuTicketCallbackQuery

	CustomerSee     bool `json:"customerSee"`
	CustomFiledList []struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	} `json:"customFiledList"`
	SheetID    int    `json:"sheetId"`
	Remark     string `json:"remark"`
	AttachList []struct {
		FileName string `json:"fileName"`
		URL      string `json:"url"`
	} `json:"attachList"`
	Time         int64 `json:"time"`
	Event        int   `json:"event"`
	NodeInfoList []struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	} `json:"nodeInfoList"`
	Operator struct {
		CorpID int    `json:"corpId"`
		Name   string `json:"name"`
		ID     int    `json:"id"`
	} `json:"operator"`
	Status int `json:"status"`
}

type QiyuTicketCallbackResp struct {
	OpenID       string `json:"open_id"`
	Content      string `json:"content"`
	ReplyContent string `json:"reply_content"`
	Title        string `json:"title"`
}

type GetTicketDetailResp struct {
	Code int              `json:"code"`
	Data TicketDetailData `json:"data"`
}

type TicketDetailData struct {
	// Attachments       []Attachment   `json:"attachments"`
	// Content           string         `json:"content"`
	// CreateTime        int64          `json:"createTime"`
	// CrmForeignId      string         `json:"crmForeignId"`
	// CrmId             string         `json:"crmId"`
	// CrmUserEmail      string         `json:"crmUserEmail"`
	// CrmUserName       string         `json:"crmUserName"`
	// CrmUserPhone      string         `json:"crmUserPhone"`
	// Custom            []CustomField  `json:"custom"`
	// Follower          map[int]string `json:"follower"`
	// FromType          int            `json:"fromType"`
	// GroupId           int            `json:"groupId"`
	// HolderCorp        int            `json:"holderCorp"`
	// HolderId          int            `json:"holderId"`
	// ID                int            `json:"id"`
	// IsCrossCorp       int            `json:"isCrossCorp"`
	// Priority          int            `json:"priority"`
	// Properties        string         `json:"properties"`
	// QuestionType      QuestionType   `json:"questionType"`
	// RelationCorp      int            `json:"relationCorp"`
	// RelationWorksheet int            `json:"relationWorksheet"`
	// StaffId           int            `json:"staffId"`
	// Status            int            `json:"status"`
	// TemplateId        int            `json:"templateId"`
	// TemplateName      string         `json:"templateName"`
	// Title             string         `json:"title"`
	// TypeId            int            `json:"typeId"`
	// UserEmail         string         `json:"userEmail"`
	// UserId            int            `json:"userId"`
	// UserMobile        string         `json:"userMobile"`
	// UserName          string         `json:"userName"`
	// Sender            string         `json:"sender"`
	// Foreword          string         `json:"foreword"`
	// LastFinishTime    int64          `json:"lastFinishTime"`
	// ConnectionType    int            `json:"connectionType"`
	// ConnectionId      int            `json:"connectionId"`
	ID               int           `json:"id"`
	Title            string        `json:"title"`
	ConnectionID     int           `json:"connectionId"`
	ConnectionType   int           `json:"connectionType"`
	Content          string        `json:"content"`
	CreateTime       int64         `json:"createTime"`
	CrmForeignId     string        `json:"crmForeignId"`
	CrmId            string        `json:"crmId"`
	CrmUserEmail     string        `json:"crmUserEmail"`
	CrmUserName      string        `json:"crmUserName"`
	CrmUserPhone     string        `json:"crmUserPhone"`
	Custom           []CustomField `json:"custom"`
	Follower         interface{}   `json:"follower"` // Consider using []string if it's always a JSON array
	GroupID          int           `json:"groupId"`
	HolderID         int           `json:"holderId"`
	HolderPlatformID int           `json:"holderPlatformId"`
	ModifyLast       bool          `json:"modifyLast"`
	Priority         int           `json:"priority"` // Assuming this is what "priorit" was meant to be
	Status           int           `json:"status"`
	StaffID          int           `json:"staffId"`
}

type QuestionType struct {
	ID     int    `json:"id"`
	Name   string `json:"name"`
	Parent int    `json:"parent"`
	Path   string `json:"path"`
}

// QiyuTicketReq
type QiyuTicketReq struct {
	AccessToken string                 `json:"access_token"`
	OpenID      string                 `json:"open_id"`
	TemplateID  string                 `json:"template_id"`
	Page        string                 `json:"page"`
	Data        map[string]interface{} `json:"data"`
}

// ReplyTicketReq
type ReplyTicketReq struct {
	middleware.Header
	TicketID int `json:"ticket_id" binding:"required"`
	// StaffID  int    `json:"staff_id" binding:"required"`
	Comment  string `json:"comment"`
	FilesURL string `json:"files_url"`
	// Attachments []struct {
	// 	FileName string `json:"file_name"`
	// 	Type     int    `json:"type"`
	// 	Payload  string `json:"payload"`
	// } `json:"attachments"`
}
