package bean

import "time"

type WechatOrder struct {
	Mode         string `json:"mode"`
	OfferId      string `json:"offerId"`
	BuyQuantity  int32  `json:"buyQuantity"`
	Env          int32  `json:"env"`
	CurrencyType string `json:"currencyType"`
	Platform     string `json:"platform"`
	ZoneId       string `json:"zoneId"`
	ProductId    string `json:"productId"`
	GoodsPrice   int32  `json:"goodsPrice"`
	OutTradeNo   string `json:"outTradeNo"`
	//Attach       string `json:"attach"`
}

type PayTransactionsJSAPI struct {
	AppID       string `json:"app_id"`
	MchID       string `json:"mch_id"`
	Description string `json:"description"`
	OutTradeNo  string `json:"out_trade_no"`
	Attach      string `json:"attach"`
	NotifyUrl   string `json:"notify_url"`
	AmountTotal int32  `json:"amount_total"`
	PayerOpenID string `json:"payer_open_id"`
}

type DecryptedResource struct {
	Amount struct {
		PayerTotal    int    `json:"payer_total"`
		Total         int    `json:"total"`
		Currency      string `json:"currency"`
		PayerCurrency string `json:"payer_currency"`
	} `json:"amount"`
	Mchid          string `json:"mchid"`
	Appid          string `json:"appid"`
	OutTradeNo     string `json:"out_trade_no"`
	TransactionId  string `json:"transaction_id"`
	TradeType      string `json:"trade_type"`
	TradeState     string `json:"trade_state"`
	TradeStateDesc string `json:"trade_state_desc"`
	BankType       string `json:"bank_type"`
	Attach         string `json:"attach"`
	SuccessTime    string `json:"success_time"`
	Payer          struct {
		Openid string `json:"openid"`
	} `json:"payer"`
}

type WechatPayCallbackInfo struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type WeChatPaymentNotification struct {
	TransactionID   string            `json:"transaction_id"`
	Amount          AmountInfo        `json:"amount"`
	MchID           string            `json:"mchid"`
	TradeState      string            `json:"trade_state"`
	BankType        string            `json:"bank_type"`
	PromotionDetail []PromotionDetail `json:"promotion_detail"`
	SuccessTime     time.Time         `json:"success_time"`
	Payer           PayerInfo         `json:"payer"`
	OutTradeNo      string            `json:"out_trade_no"`
	AppID           string            `json:"appid"`
	TradeStateDesc  string            `json:"trade_state_desc"`
	TradeType       string            `json:"trade_type"`
	Attach          string            `json:"attach"`
	SceneInfo       SceneInfo         `json:"scene_info"`
}

type AmountInfo struct {
	PayerTotal    int32  `json:"payer_total"`
	Total         int32  `json:"total"`
	Currency      string `json:"currency"`
	PayerCurrency string `json:"payer_currency"`
}

type PromotionDetail struct {
	Amount              int           `json:"amount"`
	WechatpayContribute int           `json:"wechatpay_contribute"`
	CouponID            string        `json:"coupon_id"`
	Scope               string        `json:"scope"`
	MerchantContribute  int           `json:"merchant_contribute"`
	Name                string        `json:"name"`
	OtherContribute     int           `json:"other_contribute"`
	Currency            string        `json:"currency"`
	StockID             string        `json:"stock_id"`
	GoodsDetail         []GoodsDetail `json:"goods_detail"`
}

type GoodsDetail struct {
	GoodsRemark    string `json:"goods_remark"`
	Quantity       int    `json:"quantity"`
	DiscountAmount int    `json:"discount_amount"`
	GoodsID        string `json:"goods_id"`
	UnitPrice      int    `json:"unit_price"`
}

type PayerInfo struct {
	OpenID string `json:"openid"`
}

type SceneInfo struct {
	DeviceID string `json:"device_id"`
}

type MidasResp struct {
	ErrCode int32  `json:"ErrCode"`
	ErrMsg  string `json:"ErrMsg"`
}

type PaySignatureReq struct {
}

type SignatureReq struct {
}

type PaySignatureResp struct {
}

type SignatureResp struct {
}
