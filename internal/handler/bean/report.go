package bean

// ReportType represents the type of violation being reported
type ReportType int32

const (
	ReportTypeAvatarNickname ReportType = iota + 1
	ReportTypeChatContent
	ReportTypeCheating
	ReportTypeOther
)

// ReportStatus represents the status of a report
type ReportStatus int32

const (
	ReportStatusPending ReportStatus = iota + 1
	ReportStatusProcessed
)

// SubmitReportReq represents a player report submission
type SubmitReportReq struct {
	GameID string `json:"game_id"`
	// Reported player details
	ReportedPlatformID string `json:"reported_platform_id"`
	ReportedServerID   string `json:"reported_server_id"`
	ReportedRoleID     string `json:"reported_role_id"`
	ReportedNickname   string `json:"reported_nickname"`
	ReportedAvatar     string `json:"reported_avatar"`

	// Reporter details
	ReporterServerID string `json:"reporter_server_id"`
	ReporterRoleID   string `json:"reporter_role_id"`
	ReporterNickname string `json:"reporter_nickname"`
	// ReporterAvatarURL  string `json:"reporter_avatar_url"`
	ReporterPlatformID string `json:"reporter_platform_id"`
	ReporterAvatar     string `json:"reporter_avatar"`

	// ReportItem 支持两种格式：
	// 1. 字符串数组：["举报项名称1", "举报项名称2"] - 将通过配置表转换为数字
	// 2. 数字数组：[1, 2, 3] (int32类型) - 直接使用数字值入库
	ReportItem []interface{} `json:"report_item"`

	// Report details
	ReportReason string `json:"report_reason"`
	ExtraParamA  string `json:"extra_param_a"` // Max 300 chars
	ExtraParamB  string `json:"extra_param_b"` // Max 1MB
	SessionFrom  string `json:"session_from"`  // 来源
}

// SubmitReportResp contains the response for a report submission
type SubmitReportResp struct {
	ReportID int32 `json:"report_id"`
}

// ProcessReportRequest represents a request to process a report
type ProcessReportRequest struct {
	ReportID     string `json:"report_id"`
	ProcessNotes string `json:"process_notes"`
	Action       string `json:"action"`
}

// ReportCallbackData 举报详情
type ReportCallbackData struct {
	ID                 int32  `json:"id"`                   // 举报id
	OperationID        int32  `json:"operation_id"`         // 操作id
	ReportedPlatformID string `json:"reported_platform_id"` // 被举报人平台id
	ReportedServerID   string `json:"reported_server_id"`   // 被举报人服务器id
	ReportedRoleID     string `json:"reported_role_id"`     // 被举报人角色id
	ReportedAvatar     string `json:"reported_avatar"`      // 被举报人头像url
	ReporterServerID   string `json:"reporter_server_id"`   // 举报人服务器id
	ReporterRoleID     string `json:"reporter_role_id"`     // 举报人角色id
	ReportItem         string `json:"report_item"`          // 举报事由 json数组字符串
	ReportReason       string `json:"report_reason"`        // 举报说明
	SessionFrom        string `json:"session_from"`         // 透传参数
	Action             int32  `json:"action"`               // 操作  1 禁言 2 修改头像/昵称 3 封角色 4 封账号
	ActionParam        string `json:"action_param"`         // 操作参数
}

// ReportCallbackReq 举报系统回调
type ReportCallbackReq struct {
	Attempt            int                 `json:"attempt"`
	GameID             string              `json:"game_id"`
	CallbackURL        string              `json:"callback_url"`
	ReportCallbackData *ReportCallbackData `json:"report_callback_data"`
}

type ReprtCallbackResp struct {
	Code int32  `json:"code"`
	Msg  string `json:"msg"`
}
