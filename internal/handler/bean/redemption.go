package bean

type GetRedemptionCodeReq struct {
	GameID string `form:"game_id"`
	Code   string `form:"code"`
	// UserID string `json:"user_id"`
}

type GetRedemptionCodeResp struct {
	ID          int32  `json:"id"`
	UserID      string `json:"user_id,omitempty"`
	Code        string `json:"code"`
	Title       string `json:"title"`
	Content     string `json:"content"`
	Description string `json:"description"`
}

type RedemptionCodeCallbackReq struct {
	GameID string `json:"game_id" binding:"required"`
	UserID string `json:"user_id" binding:"required"`
	Code   string `json:"code" binding:"required"`
	// CodeType int32  `json:"code_type" binding:"required"`
}
