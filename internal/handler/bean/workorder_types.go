package bean

import (
	"mime/multipart"

	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
)

// WorkorderAttachment 工单附件
type WorkorderAttachment struct {
	FileType int32  `json:"file_type"` // 文件类型: 1-图片 2-视频 3-音频 4-文件
	FileURL  string `json:"file_url"`  // 文件URL
}

// WorkorderCreateSupportTicketReq 创建工单请求
type WorkorderCreateSupportTicketReq struct {
	middleware.Header
	GameID              string                `json:"game_id" binding:"required"` // 游戏ID
	Content             string                `json:"content" binding:"required"`
	Category            string                `json:"category"`
	Priority            int32                 `json:"priority"`              // 优先级: 1-一般, 2-高, 3-紧急
	RoleID              string                `json:"role_id"`               // 角色ID
	PlayerID            string                `json:"player_id"`             // 玩家ID
	PlayerName          string                `json:"player_name"`           // 玩家名称
	PlayerLevel         int32                 `json:"player_level"`          // 玩家等级
	RechargeTotalAmount int32                 `json:"recharge_total_amount"` // 充值总额
	Zone                string                `json:"zone"`                  // 区服
	CustomData          string                `json:"custom_data"`           // 自定义数据
	SceneValue          string                `json:"scene_value"`           // 场景值
	DeviceBrand         string                `json:"device_brand"`          // 设备品牌
	DeviceModel         string                `json:"device_model"`          // 设备型号
	SystemVersion       string                `json:"system_version"`        // 系统版本
	WxVersion           string                `json:"wx_version"`            // 微信版本
	RechargeAmount      float64               `json:"recharge_amount"`       // 充值金额
	Region              string                `json:"region"`                // 地区
	IssueAt             int64                 `json:"issue_at"`              // 问题发生时间
	Attachments         []WorkorderAttachment `json:"attachments"`           // 附件列表

	// Source       string `json:"source,omitempty"`         // 新增：来源标识 wechat/douyin，默认wechat
	// DouyinOpenID string `json:"douyin_open_id,omitempty"` // 新增：抖音OpenID，抖音用户时必填
}

// WorkorderGetTicketLogsReq 获取工单日志请求
type WorkorderGetTicketLogsReq struct {
	OrderID string `json:"order_id" binding:"required"` // 工单ID
}

// WorkorderOperationLog 工单操作日志
type WorkorderOperationLog struct {
	OperationType     int32  `json:"operation_type"`     // 操作类型: 1-创建, 2-接单, 3-完结, 4-重新开单, 5-修改优先级, 6-修改标签, 7-系统自动完结, 8-回复工单
	OperationUserID   string `json:"operation_user_id"`  // 操作人ID
	OperationUsername string `json:"operation_username"` // 操作人用户名
	OperationDetail   string `json:"operation_detail"`   // 操作详情
	CreatedAt         int64  `json:"created_at"`         // 操作时间
}

// WorkorderGetTicketLogsResp 获取工单日志响应
type WorkorderGetTicketLogsResp struct {
	Data []WorkorderOperationLog `json:"data"`
}

// WorkorderGetSupportTicketsReq 获取工单列表请求
type WorkorderGetSupportTicketsReq struct {
	// UserID string `json:"user_id" binding:"required"`
	// OpenID string `json:"open_id"`
	middleware.Header
	GameID string `json:"game_id"` // 游戏ID
	Status int32  `json:"status"`  // 状态筛选：1-待接单, 2-受理中, 3-已完结
	Page   int    `json:"page"`    // 页码
	Limit  int    `json:"limit"`   // 每页条数
}

// WorkorderSupportTicketsItem 工单列表项
type WorkorderSupportTicketsItem struct {
	ID                  int32   `json:"id"`
	OrderID             string  `json:"order_id"`
	Content             string  `json:"content"`
	Status              int32   `json:"status"`
	Priority            int32   `json:"priority"`
	Category            string  `json:"category"`
	RoleID              string  `json:"role_id"`               // 角色ID
	PlayerID            string  `json:"player_id"`             // 玩家ID
	PlayerName          string  `json:"player_name"`           // 玩家名称
	PlayerLevel         int32   `json:"player_level"`          // 玩家等级
	RechargeTotalAmount int32   `json:"recharge_total_amount"` // 充值总额
	Zone                string  `json:"zone"`                  // 区服
	DeviceBrand         string  `json:"device_brand"`          // 设备品牌
	DeviceModel         string  `json:"device_model"`          // 设备型号
	SystemVersion       string  `json:"system_version"`        // 系统版本
	WxVersion           string  `json:"wx_version"`            // 微信版本
	RechargeAmount      float64 `json:"recharge_amount"`       // 充值金额
	Region              string  `json:"region"`                // 地区
	SceneValue          string  `json:"scene_value"`           // 场景值
	AcceptUserID        string  `json:"accept_user_id"`
	AcceptUsername      string  `json:"accept_username"`
	AcceptTime          int64   `json:"accept_time"`
	HasNewReply         bool    `json:"has_new_reply"`
	LastReplyTime       int64   `json:"last_reply_time"`
	CreatedAt           int64   `json:"created_at"`
	IssueAt             int64   `json:"issue_at"`
}

// WorkorderSupportTicketsData 工单列表数据
type WorkorderSupportTicketsData struct {
	Total   int64                         `json:"total"`
	Tickets []WorkorderSupportTicketsItem `json:"tickets"`
}

// WorkorderGetSupportTicketsResp 获取工单列表响应
type WorkorderGetSupportTicketsResp struct {
	Data WorkorderSupportTicketsData `json:"data"`
}

// WorkorderGetTicketDetailReq 获取工单详情请求
type WorkorderGetTicketDetailReq struct {
	OrderID string `json:"order_id" binding:"required"` // 工单ID
}

// WorkorderReplyAttachment 工单回复附件
type WorkorderReplyAttachment struct {
	FileURL  string `json:"file_url"`  // 文件URL
	FileType int32  `json:"file_type"` // 文件类型: 1-图片, 2-视频
	MimeType string `json:"mime_type"` // MIME类型
}

// WorkorderReply 工单回复
type WorkorderReply struct {
	ID          int32                      `json:"id"`          // 回复ID
	UserID      string                     `json:"user_id"`     // 回复人ID
	Username    string                     `json:"username"`    // 回复人用户名
	Content     string                     `json:"content"`     // 回复内容
	UserType    int32                      `json:"user_type"`   // 用户类型: 1-用户, 2-客服
	CreatedAt   int64                      `json:"created_at"`  // 回复时间
	Attachments []WorkorderReplyAttachment `json:"attachments"` // 附件列表
}

// WorkorderTicketDetailData 工单详情数据
type WorkorderTicketDetailData struct {
	ID                  int32                 `json:"id"`
	OrderID             string                `json:"order_id"`
	GameID              string                `json:"game_id"`
	GameName            string                `json:"game_name"`
	UserID              string                `json:"user_id"`
	PlatformID          string                `json:"platform_id"`
	OpenID              string                `json:"open_id"`
	Content             string                `json:"content"`
	Priority            int32                 `json:"priority"`
	Status              int32                 `json:"status"`
	Category            string                `json:"category"`
	AcceptUserID        string                `json:"accept_user_id"`
	AcceptUsername      string                `json:"accept_username"`
	AcceptTime          int64                 `json:"accept_time"`
	CompleteUserID      string                `json:"complete_user_id"`
	CompleteUsername    string                `json:"complete_username"`
	CompleteTime        int64                 `json:"complete_time"`
	DeviceBrand         string                `json:"device_brand"`          // 设备品牌
	DeviceModel         string                `json:"device_model"`          // 设备型号
	SystemVersion       string                `json:"system_version"`        // 系统版本
	WxVersion           string                `json:"wx_version"`            // 微信版本
	RechargeAmount      float64               `json:"recharge_amount"`       // 充值金额
	Region              string                `json:"region"`                // 地区
	RoleID              string                `json:"role_id"`               // 角色ID
	PlayerID            string                `json:"player_id"`             // 玩家ID
	PlayerName          string                `json:"player_name"`           // 玩家名称
	PlayerLevel         int32                 `json:"player_level"`          // 玩家等级
	RechargeTotalAmount int32                 `json:"recharge_total_amount"` // 充值总额
	Zone                string                `json:"zone"`                  // 区服
	CustomData          string                `json:"custom_data"`           // 自定义数据
	SceneValue          string                `json:"scene_value"`           // 场景值
	Remark              string                `json:"remark"`
	HasNewReply         bool                  `json:"has_new_reply"`
	LastReplyUserType   int32                 `json:"last_reply_user_type"`
	LastReplyTime       int64                 `json:"last_reply_time"`
	CreatedAt           int64                 `json:"created_at"`
	UpdatedAt           int64                 `json:"updated_at"`
	Attachments         []WorkorderAttachment `json:"attachments,omitempty"`
	Replies             []WorkorderReply      `json:"replies,omitempty"` // 回复列表
	IssueAt             int64                 `json:"issue_at"`          // 问题发生时间
}

// WorkorderGetTicketDetailResp 获取工单详情响应
type WorkorderGetTicketDetailResp struct {
	Data WorkorderTicketDetailData `json:"data"`
}

// WorkorderUploadFileReq 上传文件请求
type WorkorderUploadFileReq struct {
	File     *multipart.FileHeader `form:"file" binding:"required"`
	UserID   string                `form:"user_id" binding:"required"`
	FileType int32                 `form:"file_type"` // 1-图片 2-视频 3-音频 4-文件
}

// WorkorderUploadFileResp 上传文件响应
type WorkorderUploadFileResp struct {
	Code    int    `json:"code"`
	FileURL string `json:"file_url"`
}

// WorkorderReplyTicketReq 回复工单请求
type WorkorderReplyTicketReq struct {
	middleware.Header
	GameID      string                `json:"game_id" binding:"required"`  // 游戏ID
	OrderID     string                `json:"order_id" binding:"required"` // 工单ID
	Content     string                `json:"content" binding:"required"`
	Attachments []WorkorderAttachment `json:"attachments,omitempty"` // 附件列表
}

// WorkorderReplyTicketResp 回复工单响应
type WorkorderReplyTicketResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

// WorkorderGameListReq 获取客服中心游戏列表请求
type WorkorderGameListReq struct {
	middleware.Header
}

// WorkorderGameItem 客服中心游戏项
type WorkorderGameItem struct {
	GameID   string `json:"game_id"`   // 游戏ID
	GameName string `json:"game_name"` // 游戏名称
	GameIcon string `json:"game_icon"` // 游戏图标
	Weight   int32  `json:"weight"`    // 排序权重
}

// WorkorderGameListResp 获取客服中心游戏列表响应
type WorkorderGameListResp struct {
	List            []WorkorderGameItem `json:"list"`              // 游戏列表
	Version         string              `json:"version"`           // 版本号
	HasReviewSwitch bool                `json:"has_review_switch"` // 审核开关：0-关闭，1-开启
}

// WorkorderWelcomeReq 获取客服中心欢迎语请求
type WorkorderWelcomeReq struct {
	middleware.Header
	GameID string `json:"game_id" form:"game_id" binding:"required"` // 游戏ID
}

// WorkorderWelcomeResp 获取客服中心欢迎语响应
type WorkorderWelcomeResp struct {
	WelcomeContent []string `json:"welcome_content"` // 欢迎语内容，从m_question_welcome_message表获取
	Content        string   `json:"content"`         // 常见问题
	CreatedAt      int64    `json:"created_at"`      // 创建时间戳
}

// WorkorderQuestion 客服中心常见问题
type WorkorderQuestion struct {
	ID       int    `json:"id"`       // 问题ID
	Question string `json:"question"` // 问题内容
	// Type     int    `json:"type"`     // 问题类型
}

// WorkorderFeedbackReq 客服回答反馈请求
type WorkorderFeedbackReq struct {
	middleware.Header
	MessageID    int32 `json:"message_id" binding:"required"`    // 消息 ID
	FeedbackType int32 `json:"feedback_type" binding:"required"` // 反馈类型：1-有用，2-无用
}

// WorkorderFeedbackResp 客服回答反馈响应
type WorkorderFeedbackResp struct {
	ReplyContent string `json:"reply_content"` // 回复内容
	ShowTicket   bool   `json:"show_ticket"`   // 是否显示提交工单按钮
}

// WorkorderHistoryReq 获取历史消息请求
type WorkorderHistoryReq struct {
	middleware.Header
	GameID string `json:"game_id" form:"game_id" binding:"required"` // 游戏ID
	// UserID string `json:"user_id" form:"user_id" binding:"required"` // 用户ID
	// OpenID string `json:"open_id" form:"open_id" binding:"required"` // 小程序 OpenID
	Limit int `json:"limit" form:"limit"` // 限制条数，默认50条
}

// WorkorderHistoryMessage 历史消息项
type WorkorderHistoryMessage struct {
	MessageID    int32  `json:"message_id"`    // 消息 ID
	MessageType  int32  `json:"message_type"`  // 消息类型：1-用户消息，2-机器人消息，3-系统消息
	Content      string `json:"content"`       // 消息内容
	CreatedAt    int64  `json:"created_at"`    // 创建时间
	FeedbackType int32  `json:"feedback_type"` // 反馈类型：0-无反馈，1-有用，2-无用
}

// WorkorderHistoryResp 获取历史消息响应
type WorkorderHistoryResp struct {
	Messages []WorkorderHistoryMessage `json:"messages"` // 消息列表
}

// WorkorderBusySwitchReq 获取繁忙提示开关请求
type WorkorderBusySwitchReq struct {
	// 不需要任何参数
}

// WorkorderBusySwitchResp 获取繁忙提示开关响应
type WorkorderBusySwitchResp struct {
	BusySwitch bool `json:"busy_switch"` // 繁忙提示开关状态
}

// GetOngoingWorkorderStatusResp 获取用户进行中工单状态响应
type GetOngoingWorkorderStatusResp struct {
	HasOngoing  bool   `json:"has_ongoing"`   // 是否存在进行中的工单
	Content     string `json:"content"`       // 最新进行中工单的内容摘要 (如果存在)
	HasNewReply bool   `json:"has_new_reply"` // 最新进行中工单是否有新回复 (如果存在)
	OrderID     string `json:"order_id"`      // 最新进行中工单的ID (如果存在)
}

// GetOngoingWorkorderStatusReq 获取用户进行中工单状态请求
type GetOngoingWorkorderStatusReq struct {
	middleware.Header
	GameID string `json:"game_id" form:"game_id" binding:"required"` // 游戏ID
}

// ChatMessageSubmitReq 提交聊天消息请求
type ChatMessageSubmitReq struct {
	middleware.Header
	GameID  string `json:"game_id" binding:"required"` // 游戏ID
	Content string `json:"content" binding:"required"` // 消息内容
}

// ChatMessageSubmitResp 提交聊天消息响应
type ChatMessageSubmitResp struct {
	MessageID    int32  `json:"message_id"`    // 消息ID，用于反馈
	ReplyContent string `json:"reply_content"` // 回复内容
	HasMatched   bool   `json:"has_matched"`   // 是否匹配到答案
}

// ManualTriggerWorkorderStatsResp 手动触发工单统计邮件响应
type ManualTriggerWorkorderStatsResp struct {
	Success  bool   `json:"success"`  // 是否成功
	Message  string `json:"message"`  // 响应消息
	Duration string `json:"duration"` // 执行耗时
}
