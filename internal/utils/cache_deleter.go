package utils

import (
	"context"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
)

// CacheDeleter 缓存删除辅助工具
type CacheDeleter struct{}

// NewCacheDeleter 创建缓存删除工具实例
func NewCacheDeleter() *CacheDeleter {
	return &CacheDeleter{}
}

// DeleteSingle 删除单个缓存键
func (cd *CacheDeleter) DeleteSingle(ctx context.Context, key string) error {
	if key == "" {
		return nil
	}

	if err := redis.Redis().Del(ctx, key).Err(); err != nil {
		logger.Logger.WarnfCtx(ctx, "[CacheDeleter] 删除缓存失败, key: %s, error: %v", key, err)
		return err
	}

	logger.Logger.InfofCtx(ctx, "[CacheDeleter] 成功删除缓存, key: %s", key)
	return nil
}

// DeleteMultiple 批量删除缓存键
func (cd *CacheDeleter) DeleteMultiple(ctx context.Context, keys []string) error {
	if len(keys) == 0 {
		return nil
	}

	validKeys := make([]string, 0, len(keys))
	for _, key := range keys {
		if key != "" {
			validKeys = append(validKeys, key)
		}
	}

	if len(validKeys) == 0 {
		return nil
	}

	if err := redis.Redis().Del(ctx, validKeys...).Err(); err != nil {
		logger.Logger.WarnfCtx(ctx, "[CacheDeleter] 批量删除缓存失败, keys: %v, error: %v", validKeys, err)
		return err
	}

	logger.Logger.InfofCtx(ctx, "[CacheDeleter] 成功批量删除缓存, 数量: %d", len(validKeys))
	return nil
}

// DeleteByPattern 根据模式删除缓存键
// func (cd *CacheDeleter) DeleteByPattern(ctx context.Context, pattern string) (int, error) {
// 	if pattern == "" {
// 		return 0, nil
// 	}

// 	keys, err := redis.KeysE(ctx, pattern)
// 	if err != nil {
// 		logger.Logger.WarnfCtx(ctx, "[CacheDeleter] 获取缓存键失败, pattern: %s, error: %v", pattern, err)
// 		return 0, err
// 	}

// 	if len(keys) == 0 {
// 		logger.Logger.InfofCtx(ctx, "[CacheDeleter] 没有找到匹配的缓存键, pattern: %s", pattern)
// 		return 0, nil
// 	}

// 	if err := cd.DeleteMultiple(ctx, keys); err != nil {
// 		return 0, err
// 	}

// 	return len(keys), nil
// }
