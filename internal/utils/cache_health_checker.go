package utils

import (
	"context"
	"fmt"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
)

// CacheHealthChecker 缓存健康检查器
type CacheHealthChecker struct{}

// NewCacheHealthChecker 创建缓存健康检查器实例
func NewCacheHealthChecker() *CacheHealthChecker {
	return &CacheHealthChecker{}
}

// CacheHealthReport 缓存健康报告
type CacheHealthReport struct {
	TotalKeys        int64              `json:"total_keys"`
	GameExistKeys    int64              `json:"game_exist_keys"`
	GameInfoKeys     int64              `json:"game_info_keys"`
	UserInfoKeys     int64              `json:"user_info_keys"`
	UserExistsKeys   int64              `json:"user_exists_keys"`
	ExpiredKeys      int64              `json:"expired_keys"`
	CorruptedKeys    int64              `json:"corrupted_keys"`
	MemoryUsage      string             `json:"memory_usage"`
	CacheHitRates    map[string]float64 `json:"cache_hit_rates"`
	RecommendActions []string           `json:"recommend_actions"`
	CheckTime        time.Time          `json:"check_time"`
}

// CheckCacheHealth 执行缓存健康检查
// func (chc *CacheHealthChecker) CheckCacheHealth(ctx context.Context) (*CacheHealthReport, error) {
// 	report := &CacheHealthReport{
// 		CacheHitRates:    make(map[string]float64),
// 		RecommendActions: make([]string, 0),
// 		CheckTime:        time.Now(),
// 	}

// 	// 检查各类缓存键数量
// 	if err := chc.checkCacheKeysCounts(ctx, report); err != nil {
// 		logger.Logger.WarnfCtx(ctx, "[CacheHealthChecker] 检查缓存键数量失败: %v", err)
// 	}

// 	// 检查内存使用情况
// 	if err := chc.checkMemoryUsage(ctx, report); err != nil {
// 		logger.Logger.WarnfCtx(ctx, "[CacheHealthChecker] 检查内存使用失败: %v", err)
// 	}

// 	// 检查损坏的缓存数据
// 	if err := chc.checkCorruptedCache(ctx, report); err != nil {
// 		logger.Logger.WarnfCtx(ctx, "[CacheHealthChecker] 检查损坏缓存失败: %v", err)
// 	}

// 	// 生成建议
// 	chc.generateRecommendations(report)

// 	return report, nil
// }

// checkCacheKeysCounts 检查各类缓存键的数量
// func (chc *CacheHealthChecker) checkCacheKeysCounts(ctx context.Context, report *CacheHealthReport) error {
// 	// 检查游戏存在性缓存
// 	gameExistPattern := fmt.Sprintf(constants.RedisGameExistKey, "*")
// 	gameExistKeys, err := redis.KeysE(ctx, gameExistPattern)
// 	if err != nil {
// 		return fmt.Errorf("获取游戏存在性缓存键失败: %w", err)
// 	}
// 	report.GameExistKeys = int64(len(gameExistKeys))

// 	// 检查游戏信息缓存
// 	gameInfoPattern := fmt.Sprintf(constants.RedisGameInfoKey, "*")
// 	gameInfoKeys, err := redis.KeysE(ctx, gameInfoPattern)
// 	if err != nil {
// 		return fmt.Errorf("获取游戏信息缓存键失败: %w", err)
// 	}
// 	report.GameInfoKeys = int64(len(gameInfoKeys))

// 	// 检查用户信息缓存
// 	userInfoPattern := fmt.Sprintf(constants.RedisUserInfoKey, "*")
// 	userInfoKeys, err := redis.KeysE(ctx, userInfoPattern)
// 	if err != nil {
// 		return fmt.Errorf("获取用户信息缓存键失败: %w", err)
// 	}
// 	report.UserInfoKeys = int64(len(userInfoKeys))

// 	// 检查用户存在性缓存
// 	userExistsPattern := fmt.Sprintf(constants.RedisUserExistsKey, "*", "*")
// 	userExistsKeys, err := redis.KeysE(ctx, userExistsPattern)
// 	if err != nil {
// 		return fmt.Errorf("获取用户存在性缓存键失败: %w", err)
// 	}
// 	report.UserExistsKeys = int64(len(userExistsKeys))

// 	report.TotalKeys = report.GameExistKeys + report.GameInfoKeys + report.UserInfoKeys + report.UserExistsKeys

// 	return nil
// }

// checkMemoryUsage 检查Redis内存使用情况
func (chc *CacheHealthChecker) checkMemoryUsage(ctx context.Context, report *CacheHealthReport) error {
	// 获取Redis内存信息
	info, err := redis.Redis().Info(ctx, "memory").Result()
	if err != nil {
		return fmt.Errorf("获取Redis内存信息失败: %w", err)
	}

	report.MemoryUsage = info
	return nil
}

// checkCorruptedCache 检查损坏的缓存数据
// func (chc *CacheHealthChecker) checkCorruptedCache(ctx context.Context, report *CacheHealthReport) error {
// 	corruptedCount := int64(0)

// 	// 检查用户信息缓存的完整性
// 	userInfoPattern := fmt.Sprintf(constants.RedisUserInfoKey, "*")
// 	userInfoKeys, err := redis.KeysE(ctx, userInfoPattern)
// 	if err != nil {
// 		return fmt.Errorf("获取用户信息缓存键失败: %w", err)
// 	}

// 	// 抽样检查前100个键的数据完整性
// 	checkLimit := 100
// 	if len(userInfoKeys) < checkLimit {
// 		checkLimit = len(userInfoKeys)
// 	}

// 	for i := 0; i < checkLimit; i++ {
// 		key := userInfoKeys[i]
// 		value, err := redis.Get(ctx, key)
// 		if err != nil {
// 			continue
// 		}

// 		// 尝试解析JSON数据
// 		if value != "" && !chc.isValidJSON(value) {
// 			corruptedCount++
// 		}
// 	}

// 	report.CorruptedKeys = corruptedCount
// 	return nil
// }

// isValidJSON 检查字符串是否为有效的JSON
func (chc *CacheHealthChecker) isValidJSON(str string) bool {
	// 简单的JSON格式检查
	if len(str) < 2 {
		return false
	}
	return (str[0] == '{' && str[len(str)-1] == '}') || (str[0] == '[' && str[len(str)-1] == ']')
}

// isValidCacheValue 检查缓存值是否有效（使用tagged switch）
func (chc *CacheHealthChecker) isValidCacheValue(cacheType, value string) bool {
	switch cacheType {
	case "game_exist":
		// 游戏存在性缓存只能是"true"或"false"
		switch value {
		case "true", "false":
			return true
		default:
			return false
		}
	case "user_exists":
		// 用户存在性缓存只能是"0"或"1"
		switch value {
		case "0", "1":
			return true
		default:
			return false
		}
	case "user_info", "game_info":
		// JSON数据缓存
		return chc.isValidJSON(value)
	default:
		return false
	}
}

// generateRecommendations 生成优化建议
func (chc *CacheHealthChecker) generateRecommendations(report *CacheHealthReport) {
	// 基于检查结果生成建议
	if report.TotalKeys > 100000 {
		report.RecommendActions = append(report.RecommendActions, "缓存键数量过多，建议实施缓存清理策略")
	}

	if report.CorruptedKeys > 0 {
		report.RecommendActions = append(report.RecommendActions, fmt.Sprintf("发现%d个损坏的缓存键，建议清理", report.CorruptedKeys))
	}

	if report.UserExistsKeys > report.UserInfoKeys*2 {
		report.RecommendActions = append(report.RecommendActions, "用户存在性缓存数量异常，建议检查TTL设置")
	}

	if len(report.RecommendActions) == 0 {
		report.RecommendActions = append(report.RecommendActions, "缓存状态良好，无需特殊处理")
	}
}

// CleanupCorruptedCache 清理损坏的缓存数据
// func (chc *CacheHealthChecker) CleanupCorruptedCache(ctx context.Context) error {
// 	logger.Logger.InfofCtx(ctx, "[CacheHealthChecker] 开始清理损坏的缓存数据")

// 	// 清理损坏的用户信息缓存
// 	userInfoPattern := fmt.Sprintf(constants.RedisUserInfoKey, "*")
// 	userInfoKeys, err := redis.KeysE(ctx, userInfoPattern)
// 	if err != nil {
// 		return fmt.Errorf("获取用户信息缓存键失败: %w", err)
// 	}

// 	cleanedCount := 0
// 	for _, key := range userInfoKeys {
// 		value, err := redis.Get(ctx, key)
// 		if err != nil {
// 			continue
// 		}

// 		if value != "" && !chc.isValidJSON(value) {
// 			redis.UnLock(ctx, key)
// 			cleanedCount++
// 		}
// 	}

// 	logger.Logger.InfofCtx(ctx, "[CacheHealthChecker] 清理完成，共清理%d个损坏的缓存键", cleanedCount)
// 	return nil
// }

// GetCacheStatistics 获取缓存统计信息
// func (chc *CacheHealthChecker) GetCacheStatistics(ctx context.Context) (map[string]interface{}, error) {
// 	stats := make(map[string]interface{})

// 	// 获取基本统计信息
// 	report, err := chc.CheckCacheHealth(ctx)
// 	if err != nil {
// 		return nil, err
// 	}

// 	stats["total_keys"] = report.TotalKeys
// 	stats["game_exist_keys"] = report.GameExistKeys
// 	stats["game_info_keys"] = report.GameInfoKeys
// 	stats["user_info_keys"] = report.UserInfoKeys
// 	stats["user_exists_keys"] = report.UserExistsKeys
// 	stats["corrupted_keys"] = report.CorruptedKeys
// 	stats["check_time"] = report.CheckTime
// 	stats["recommendations"] = report.RecommendActions

// 	return stats, nil
// }
