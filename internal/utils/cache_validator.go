package utils

import (
	"context"
	"fmt"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

// CacheValidator 缓存值验证器
type CacheValidator struct{}

// NewCacheValidator 创建缓存验证器实例
func NewCacheValidator() *CacheValidator {
	return &CacheValidator{}
}

// CacheValidationResult 缓存验证结果
type CacheValidationResult struct {
	IsValid      bool   `json:"is_valid"`
	CacheType    string `json:"cache_type"`
	Value        string `json:"value"`
	ErrorMessage string `json:"error_message,omitempty"`
	Suggestion   string `json:"suggestion,omitempty"`
}

// ValidateGameExistenceCache 验证游戏存在性缓存值
func (cv *CacheValidator) ValidateGameExistenceCache(ctx context.Context, value string) *CacheValidationResult {
	result := &CacheValidationResult{
		CacheType: "game_existence",
		Value:     value,
	}

	// 使用tagged switch验证游戏存在性缓存值
	switch value {
	case "true":
		result.IsValid = true
		logger.Logger.DebugfCtx(ctx, "[CacheValidator] 游戏存在性缓存值有效: %s", value)
	case "false":
		result.IsValid = true
		logger.Logger.DebugfCtx(ctx, "[CacheValidator] 游戏存在性缓存值有效: %s", value)
	case "":
		result.IsValid = false
		result.ErrorMessage = "缓存值为空"
		result.Suggestion = "应设置为'true'或'false'"
	default:
		result.IsValid = false
		result.ErrorMessage = fmt.Sprintf("无效的游戏存在性缓存值: %s", value)
		result.Suggestion = "游戏存在性缓存值只能是'true'或'false'"
		logger.Logger.WarnfCtx(ctx, "[CacheValidator] 发现无效的游戏存在性缓存值: %s", value)
	}

	return result
}

// ValidateUserExistenceCache 验证用户存在性缓存值
func (cv *CacheValidator) ValidateUserExistenceCache(ctx context.Context, value string) *CacheValidationResult {
	result := &CacheValidationResult{
		CacheType: "user_existence",
		Value:     value,
	}

	// 使用tagged switch验证用户存在性缓存值
	switch value {
	case "0":
		result.IsValid = true
		logger.Logger.DebugfCtx(ctx, "[CacheValidator] 用户存在性缓存值有效: %s (新用户)", value)
	case "1":
		result.IsValid = true
		logger.Logger.DebugfCtx(ctx, "[CacheValidator] 用户存在性缓存值有效: %s (老用户)", value)
	case "":
		result.IsValid = false
		result.ErrorMessage = "缓存值为空"
		result.Suggestion = "应设置为'0'(新用户)或'1'(老用户)"
	default:
		result.IsValid = false
		result.ErrorMessage = fmt.Sprintf("无效的用户存在性缓存值: %s", value)
		result.Suggestion = "用户存在性缓存值只能是'0'(新用户)或'1'(老用户)"
		logger.Logger.WarnfCtx(ctx, "[CacheValidator] 发现无效的用户存在性缓存值: %s", value)
	}

	return result
}

// ValidateCacheValueByType 根据缓存类型验证缓存值
func (cv *CacheValidator) ValidateCacheValueByType(ctx context.Context, cacheType, value string) *CacheValidationResult {
	// 使用tagged switch根据缓存类型选择验证方法
	switch cacheType {
	case "game_existence", "game_exist":
		return cv.ValidateGameExistenceCache(ctx, value)
	case "user_existence", "user_exists":
		return cv.ValidateUserExistenceCache(ctx, value)
	case "user_info":
		return cv.validateJSONCache(ctx, "user_info", value)
	case "game_info":
		return cv.validateJSONCache(ctx, "game_info", value)
	default:
		return &CacheValidationResult{
			IsValid:      false,
			CacheType:    cacheType,
			Value:        value,
			ErrorMessage: fmt.Sprintf("不支持的缓存类型: %s", cacheType),
			Suggestion:   "支持的缓存类型: game_existence, user_existence, user_info, game_info",
		}
	}
}

// validateJSONCache 验证JSON格式的缓存值
func (cv *CacheValidator) validateJSONCache(ctx context.Context, cacheType, value string) *CacheValidationResult {
	result := &CacheValidationResult{
		CacheType: cacheType,
		Value:     value,
	}

	if value == "" {
		result.IsValid = false
		result.ErrorMessage = "JSON缓存值为空"
		result.Suggestion = "应包含有效的JSON数据"
		return result
	}

	// 简单的JSON格式验证
	if len(value) < 2 {
		result.IsValid = false
		result.ErrorMessage = "JSON缓存值长度不足"
		result.Suggestion = "应包含有效的JSON对象或数组"
		return result
	}

	// 使用tagged switch检查JSON格式
	switch {
	case value[0] == '{' && value[len(value)-1] == '}':
		// JSON对象格式
		result.IsValid = true
		logger.Logger.DebugfCtx(ctx, "[CacheValidator] JSON对象缓存值有效, 类型: %s", cacheType)
	case value[0] == '[' && value[len(value)-1] == ']':
		// JSON数组格式
		result.IsValid = true
		logger.Logger.DebugfCtx(ctx, "[CacheValidator] JSON数组缓存值有效, 类型: %s", cacheType)
	default:
		result.IsValid = false
		result.ErrorMessage = "无效的JSON格式"
		result.Suggestion = "JSON数据应以{开头}结尾或以[开头]结尾"
		logger.Logger.WarnfCtx(ctx, "[CacheValidator] 发现无效的JSON缓存值, 类型: %s", cacheType)
	}

	return result
}

// BatchValidateCacheValues 批量验证缓存值
func (cv *CacheValidator) BatchValidateCacheValues(ctx context.Context, cacheData map[string]map[string]string) map[string][]*CacheValidationResult {
	results := make(map[string][]*CacheValidationResult)

	for cacheType, values := range cacheData {
		typeResults := make([]*CacheValidationResult, 0, len(values))
		
		for key, value := range values {
			result := cv.ValidateCacheValueByType(ctx, cacheType, value)
			// 添加缓存键信息
			result.Value = fmt.Sprintf("key:%s, value:%s", key, value)
			typeResults = append(typeResults, result)
		}
		
		results[cacheType] = typeResults
	}

	return results
}

// GetValidationSummary 获取验证摘要
func (cv *CacheValidator) GetValidationSummary(results map[string][]*CacheValidationResult) map[string]interface{} {
	summary := make(map[string]interface{})
	
	totalCount := 0
	validCount := 0
	invalidCount := 0
	
	typeStats := make(map[string]map[string]int)
	
	for cacheType, typeResults := range results {
		typeValid := 0
		typeInvalid := 0
		
		for _, result := range typeResults {
			totalCount++
			if result.IsValid {
				validCount++
				typeValid++
			} else {
				invalidCount++
				typeInvalid++
			}
		}
		
		typeStats[cacheType] = map[string]int{
			"total":   len(typeResults),
			"valid":   typeValid,
			"invalid": typeInvalid,
		}
	}
	
	summary["total_count"] = totalCount
	summary["valid_count"] = validCount
	summary["invalid_count"] = invalidCount
	summary["valid_rate"] = float64(validCount) / float64(totalCount) * 100
	summary["type_statistics"] = typeStats
	
	return summary
}

// RecommendCacheCleanup 推荐缓存清理策略
func (cv *CacheValidator) RecommendCacheCleanup(ctx context.Context, results map[string][]*CacheValidationResult) []string {
	recommendations := make([]string, 0)
	
	for cacheType, typeResults := range results {
		invalidCount := 0
		for _, result := range typeResults {
			if !result.IsValid {
				invalidCount++
			}
		}
		
		if invalidCount > 0 {
			// 使用tagged switch生成针对性建议
			switch cacheType {
			case "game_existence", "game_exist":
				recommendations = append(recommendations, 
					fmt.Sprintf("发现%d个无效的游戏存在性缓存，建议清理并重新验证游戏状态", invalidCount))
			case "user_existence", "user_exists":
				recommendations = append(recommendations, 
					fmt.Sprintf("发现%d个无效的用户存在性缓存，建议清理并重新查询用户状态", invalidCount))
			case "user_info":
				recommendations = append(recommendations, 
					fmt.Sprintf("发现%d个损坏的用户信息缓存，建议清理并重新加载用户数据", invalidCount))
			case "game_info":
				recommendations = append(recommendations, 
					fmt.Sprintf("发现%d个损坏的游戏信息缓存，建议清理并重新加载游戏数据", invalidCount))
			default:
				recommendations = append(recommendations, 
					fmt.Sprintf("发现%d个无效的%s类型缓存，建议清理", invalidCount, cacheType))
			}
		}
	}
	
	if len(recommendations) == 0 {
		recommendations = append(recommendations, "所有缓存值都有效，无需清理")
	}
	
	return recommendations
}
