package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
)

// CacheManager 缓存管理器，负责缓存一致性维护
type CacheManager struct{}

// NewCacheManager 创建缓存管理器实例
func NewCacheManager() *CacheManager {
	return &CacheManager{}
}

// InvalidateUserCache 清除用户相关缓存
func (cm *CacheManager) InvalidateUserCache(ctx context.Context, openID string) error {
	if openID == "" {
		return fmt.Errorf("openID不能为空")
	}

	userCacheKey := fmt.Sprintf(constants.RedisUserInfoKey, openID)

	// 删除用户信息缓存
	redis.Redis().Del(ctx, userCacheKey)

	logger.Logger.InfofCtx(ctx, "[CacheManager] 成功清除用户缓存, openID: %s", openID)
	return nil
}

// InvalidateUserCacheOnSessionFailure session验证失败时清除用户缓存
func (cm *CacheManager) InvalidateUserCacheOnSessionFailure(ctx context.Context, openID string, reason string) {
	if openID == "" {
		return
	}

	userCacheKey := fmt.Sprintf(constants.RedisUserInfoKey, openID)

	// 异步清除缓存，不阻塞主流程
	go func() {
		// 创建带超时的context，避免主请求context取消影响异步任务
		asyncCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		redis.Redis().Del(asyncCtx, userCacheKey)
		logger.Logger.InfofCtx(asyncCtx, "[CacheManager] session验证失败，已清除用户缓存, openID: %s, reason: %s", openID, reason)
	}()
}

// InvalidateUserCacheByUserID 根据userID清除用户缓存（需要先查询openID）
func (cm *CacheManager) InvalidateUserCacheByUserID(ctx context.Context, userID string, getUserOpenID func(ctx context.Context, userID string) (string, error)) error {
	if userID == "" {
		return fmt.Errorf("userID不能为空")
	}

	// 获取用户的openID
	openID, err := getUserOpenID(ctx, userID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[CacheManager] 获取用户openID失败, userID: %s, err: %v", userID, err)
		return err
	}

	if openID == "" {
		logger.Logger.WarnfCtx(ctx, "[CacheManager] 用户openID为空, userID: %s", userID)
		return nil
	}

	return cm.InvalidateUserCache(ctx, openID)
}

// UpdateUserCache 更新用户缓存（Write-Through策略）
func (cm *CacheManager) UpdateUserCache(ctx context.Context, openID string, userData interface{}) error {
	if openID == "" {
		return fmt.Errorf("openID不能为空")
	}

	userCacheKey := fmt.Sprintf(constants.RedisUserInfoKey, openID)

	// 序列化用户数据
	userDataBytes, err := json.Marshal(userData)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[CacheManager] 序列化用户数据失败, openID: %s, err: %v", openID, err)
		// 序列化失败时删除缓存，确保一致性
		redis.Redis().Del(ctx, userCacheKey) // 使用 Del 删除数据键
		return err
	}

	// 更新缓存
	if err := redis.Set(ctx, userCacheKey, string(userDataBytes), time.Duration(constants.RedisUserInfoExpire)*time.Second); err != nil {
		logger.Logger.WarnfCtx(ctx, "[CacheManager] 更新用户缓存失败, openID: %s, err: %v", openID, err)
		// 更新失败时删除缓存，确保一致性
		redis.Redis().Del(ctx, userCacheKey) // 使用 Del 删除数据键
		return err
	}

	logger.Logger.InfofCtx(ctx, "[CacheManager] 成功更新用户缓存, openID: %s", openID)
	return nil
}

// UpdateGameCache 更新游戏缓存（Write-Through策略）
func (cm *CacheManager) UpdateGameCache(ctx context.Context, gameID string, gameData interface{}) error {
	if gameID == "" {
		return fmt.Errorf("gameID不能为空")
	}

	gameInfoKey := fmt.Sprintf(constants.RedisGameInfoKey, gameID)

	// 序列化游戏数据
	gameDataBytes, err := json.Marshal(gameData)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[CacheManager] 序列化游戏数据失败, gameID: %s, err: %v", gameID, err)
		// 序列化失败时删除缓存，确保一致性
		redis.Redis().Del(ctx, gameInfoKey)
		return err
	}

	// 更新缓存
	if err := redis.Set(ctx, gameInfoKey, string(gameDataBytes), time.Duration(constants.RedisGameInfoExpire)*time.Second); err != nil {
		logger.Logger.WarnfCtx(ctx, "[CacheManager] 更新游戏缓存失败, gameID: %s, err: %v", gameID, err)
		// 更新失败时删除缓存，确保一致性
		redis.Redis().Del(ctx, gameInfoKey)
		return err
	}

	logger.Logger.InfofCtx(ctx, "[CacheManager] 成功更新游戏缓存, gameID: %s", gameID)
	return nil
}

// BatchInvalidateCache 批量清除缓存
func (cm *CacheManager) BatchInvalidateCache(ctx context.Context, keys []string) error {
	if len(keys) == 0 {
		return nil
	}

	for _, key := range keys {
		redis.Redis().Del(ctx, key)
	}

	logger.Logger.InfofCtx(ctx, "[CacheManager] 成功批量清除缓存, 数量: %d", len(keys))
	return nil
}

// GetCacheStats 获取缓存统计信息
func (cm *CacheManager) GetCacheStats(ctx context.Context) map[string]interface{} {
	stats := make(map[string]interface{})

	// 这里可以添加缓存命中率、缓存大小等统计信息
	stats["cache_version"] = time.Now().Unix()
	stats["manager_status"] = "active"

	return stats
}
