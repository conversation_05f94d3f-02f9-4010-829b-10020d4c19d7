package utils

import (
	"context"
	"fmt"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
)

// CacheConsistencyManager 缓存一致性管理器
type CacheConsistencyManager struct{}

// NewCacheConsistencyManager 创建缓存一致性管理器实例
func NewCacheConsistencyManager() *CacheConsistencyManager {
	return &CacheConsistencyManager{}
}

// // InvalidateGameRelatedCache 清除游戏相关的所有缓存
// func (ccm *CacheConsistencyManager) InvalidateGameRelatedCache(ctx context.Context, gameID string) error {
// 	if gameID == "" {
// 		return fmt.Errorf("gameID不能为空")
// 	}

// 	logger.Logger.InfofCtx(ctx, "[CacheConsistencyManager] 开始清除游戏相关缓存, gameID: %s", gameID)

// 	// 清除游戏存在性缓存
// 	gameExistKey := fmt.Sprintf(constants.RedisGameExistKey, gameID)
// 	redis.UnLock(ctx, gameExistKey)

// 	// 清除游戏信息缓存
// 	gameInfoKey := fmt.Sprintf(constants.RedisGameInfoKey, gameID)
// 	redis.UnLock(ctx, gameInfoKey)

// 	// 清除该游戏下的用户存在性缓存
// 	userExistsPattern := fmt.Sprintf(constants.RedisUserExistsKey, gameID, "*")
// 	userExistsKeys, err := redis.KeysE(ctx, userExistsPattern)
// 	if err != nil {
// 		logger.Logger.WarnfCtx(ctx, "[CacheConsistencyManager] 获取用户存在性缓存键失败: %v", err)
// 	} else {
// 		for _, key := range userExistsKeys {
// 			redis.UnLock(ctx, key)
// 		}
// 		logger.Logger.InfofCtx(ctx, "[CacheConsistencyManager] 清除用户存在性缓存数量: %d", len(userExistsKeys))
// 	}

// 	logger.Logger.InfofCtx(ctx, "[CacheConsistencyManager] 游戏相关缓存清除完成, gameID: %s", gameID)
// 	return nil
// }

// InvalidateUserRelatedCache 清除用户相关的所有缓存
// func (ccm *CacheConsistencyManager) InvalidateUserRelatedCache(ctx context.Context, openID string) error {
// 	if openID == "" {
// 		return fmt.Errorf("openID不能为空")
// 	}

// 	logger.Logger.InfofCtx(ctx, "[CacheConsistencyManager] 开始清除用户相关缓存, openID: %s", openID)

// 	// 清除用户信息缓存
// 	userInfoKey := fmt.Sprintf(constants.RedisUserInfoKey, openID)
// 	redis.UnLock(ctx, userInfoKey)

// 	// 清除该用户在所有游戏下的存在性缓存
// 	userExistsPattern := fmt.Sprintf(constants.RedisUserExistsKey, "*", openID)
// 	userExistsKeys, err := redis.KeysE(ctx, userExistsPattern)
// 	if err != nil {
// 		logger.Logger.WarnfCtx(ctx, "[CacheConsistencyManager] 获取用户存在性缓存键失败: %v", err)
// 	} else {
// 		for _, key := range userExistsKeys {
// 			redis.UnLock(ctx, key)
// 		}
// 		logger.Logger.InfofCtx(ctx, "[CacheConsistencyManager] 清除用户存在性缓存数量: %d", len(userExistsKeys))
// 	}

// 	logger.Logger.InfofCtx(ctx, "[CacheConsistencyManager] 用户相关缓存清除完成, openID: %s", openID)
// 	return nil
// }

// RefreshGameCache 刷新游戏缓存
// func (ccm *CacheConsistencyManager) RefreshGameCache(ctx context.Context, gameID string, gameData interface{}) error {
// 	if gameID == "" {
// 		return fmt.Errorf("gameID不能为空")
// 	}

// 	// 先清除旧缓存
// 	if err := ccm.InvalidateGameRelatedCache(ctx, gameID); err != nil {
// 		return fmt.Errorf("清除游戏缓存失败: %w", err)
// 	}

// 	// 等待一小段时间确保缓存清除完成
// 	time.Sleep(100 * time.Millisecond)

// 	// 更新游戏信息缓存
// 	cacheManager := NewCacheManager()
// 	if err := cacheManager.UpdateGameCache(ctx, gameID, gameData); err != nil {
// 		return fmt.Errorf("更新游戏缓存失败: %w", err)
// 	}

// 	// 更新游戏存在性缓存
// 	gameExistKey := fmt.Sprintf(constants.RedisGameExistKey, gameID)
// 	if err := redis.Set(ctx, gameExistKey, "true", time.Duration(constants.RedisGameExistExpire)*time.Second); err != nil {
// 		logger.Logger.WarnfCtx(ctx, "[CacheConsistencyManager] 更新游戏存在性缓存失败: %v", err)
// 	}

// 	logger.Logger.InfofCtx(ctx, "[CacheConsistencyManager] 游戏缓存刷新完成, gameID: %s", gameID)
// 	return nil
// }

// RefreshUserCache 刷新用户缓存
// func (ccm *CacheConsistencyManager) RefreshUserCache(ctx context.Context, openID string, userData interface{}) error {
// 	if openID == "" {
// 		return fmt.Errorf("openID不能为空")
// 	}

// 	// 先清除旧缓存
// 	if err := ccm.InvalidateUserRelatedCache(ctx, openID); err != nil {
// 		return fmt.Errorf("清除用户缓存失败: %w", err)
// 	}

// 	// 等待一小段时间确保缓存清除完成
// 	time.Sleep(100 * time.Millisecond)

// 	// 更新用户信息缓存
// 	cacheManager := NewCacheManager()
// 	if err := cacheManager.UpdateUserCache(ctx, openID, userData); err != nil {
// 		return fmt.Errorf("更新用户缓存失败: %w", err)
// 	}

// 	logger.Logger.InfofCtx(ctx, "[CacheConsistencyManager] 用户缓存刷新完成, openID: %s", openID)
// 	return nil
// }

// ValidateCacheConsistency 验证缓存一致性
func (ccm *CacheConsistencyManager) ValidateCacheConsistency(ctx context.Context, gameID, openID string) (*CacheConsistencyReport, error) {
	report := &CacheConsistencyReport{
		GameID:      gameID,
		OpenID:      openID,
		CheckTime:   time.Now(),
		Issues:      make([]string, 0),
		Suggestions: make([]string, 0),
	}

	// 检查游戏存在性缓存一致性
	if err := ccm.checkGameExistenceConsistency(ctx, gameID, report); err != nil {
		logger.Logger.WarnfCtx(ctx, "[CacheConsistencyManager] 检查游戏存在性一致性失败: %v", err)
	}

	// 检查用户存在性缓存一致性
	if err := ccm.checkUserExistenceConsistency(ctx, gameID, openID, report); err != nil {
		logger.Logger.WarnfCtx(ctx, "[CacheConsistencyManager] 检查用户存在性一致性失败: %v", err)
	}

	// 生成修复建议
	ccm.generateConsistencyRecommendations(report)

	return report, nil
}

// CacheConsistencyReport 缓存一致性报告
type CacheConsistencyReport struct {
	GameID       string    `json:"game_id"`
	OpenID       string    `json:"open_id"`
	CheckTime    time.Time `json:"check_time"`
	Issues       []string  `json:"issues"`
	Suggestions  []string  `json:"suggestions"`
	IsConsistent bool      `json:"is_consistent"`
}

// checkGameExistenceConsistency 检查游戏存在性缓存一致性
func (ccm *CacheConsistencyManager) checkGameExistenceConsistency(ctx context.Context, gameID string, report *CacheConsistencyReport) error {
	gameExistKey := fmt.Sprintf(constants.RedisGameExistKey, gameID)
	cachedValue, err := redis.Get(ctx, gameExistKey)

	if err != nil && err.Error() != "redis: nil" {
		return fmt.Errorf("获取游戏存在性缓存失败: %w", err)
	}

	// 使用tagged switch检查缓存值的有效性
	if cachedValue != "" {
		switch cachedValue {
		case "true", "false":
			// 有效的缓存值，无需处理
		default:
			// 无效的缓存值
			report.Issues = append(report.Issues, fmt.Sprintf("游戏存在性缓存值异常: %s", cachedValue))
		}
	}

	return nil
}

// checkUserExistenceConsistency 检查用户存在性缓存一致性
func (ccm *CacheConsistencyManager) checkUserExistenceConsistency(ctx context.Context, gameID, openID string, report *CacheConsistencyReport) error {
	userExistsKey := fmt.Sprintf(constants.RedisUserExistsKey, gameID, openID)
	cachedValue, err := redis.Get(ctx, userExistsKey)

	if err != nil && err.Error() != "redis: nil" {
		return fmt.Errorf("获取用户存在性缓存失败: %w", err)
	}

	// 使用tagged switch检查缓存值的有效性
	if cachedValue != "" {
		switch cachedValue {
		case "0", "1":
			// 有效的缓存值，无需处理
		default:
			// 无效的缓存值
			report.Issues = append(report.Issues, fmt.Sprintf("用户存在性缓存值异常: %s", cachedValue))
		}
	}

	return nil
}

// generateConsistencyRecommendations 生成一致性修复建议
func (ccm *CacheConsistencyManager) generateConsistencyRecommendations(report *CacheConsistencyReport) {
	if len(report.Issues) == 0 {
		report.IsConsistent = true
		report.Suggestions = append(report.Suggestions, "缓存一致性良好，无需处理")
		return
	}

	report.IsConsistent = false

	for _, issue := range report.Issues {
		if fmt.Sprintf("%s", issue) != "" {
			if report.GameID != "" {
				report.Suggestions = append(report.Suggestions, fmt.Sprintf("建议清除游戏%s的相关缓存", report.GameID))
			}
			if report.OpenID != "" {
				report.Suggestions = append(report.Suggestions, fmt.Sprintf("建议清除用户%s的相关缓存", report.OpenID))
			}
		}
	}
}

// BatchInvalidateCache 批量清除缓存
// func (ccm *CacheConsistencyManager) BatchInvalidateCache(ctx context.Context, patterns []string) error {
// 	logger.Logger.InfofCtx(ctx, "[CacheConsistencyManager] 开始批量清除缓存, 模式数量: %d", len(patterns))

// 	totalCleaned := 0
// 	for _, pattern := range patterns {
// 		keys, err := redis.KeysE(ctx, pattern)
// 		if err != nil {
// 			logger.Logger.WarnfCtx(ctx, "[CacheConsistencyManager] 获取缓存键失败, pattern: %s, error: %v", pattern, err)
// 			continue
// 		}

// 		for _, key := range keys {
// 			redis.UnLock(ctx, key)
// 		}

// 		totalCleaned += len(keys)
// 		logger.Logger.InfofCtx(ctx, "[CacheConsistencyManager] 清除缓存, pattern: %s, 数量: %d", pattern, len(keys))
// 	}

// 	logger.Logger.InfofCtx(ctx, "[CacheConsistencyManager] 批量清除缓存完成, 总数量: %d", totalCleaned)
// 	return nil
// }
