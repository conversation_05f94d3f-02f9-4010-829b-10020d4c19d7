package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/go-resty/resty/v2"
)

var (
	_subscribeOnce    sync.Once
	_subscribeService *SubscribeService
)

var (
	oAuth2AccessTokenType = "authorization_code"
	oAuth2AccessTokenUrl  = "/sns/oauth2/access_token"
)

type SubscribeService struct {
	client *resty.Client
}

func SingletonSubscribeService() *SubscribeService {
	_subscribeOnce.Do(func() {
		_subscribeService = &SubscribeService{
			client: resty.New(),
		}
	})
	return _subscribeService
}

// GetOauth2AccessToken
func (s *SubscribeService) GetOauth2AccessToken(ctx context.Context, appID, appSecret, code string) (*bean.Oauth2UserInfo, error) {
	resp, err := s.client.R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"grant_type": oAuth2AccessTokenType,
			"code":       code,
			"appid":      appID,
			"secret":     appSecret,
		}).
		Get(config.GlobConfig.Minigame.BaseURL + oAuth2AccessTokenUrl)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("SubscribeService GetOauth2AccessToken status code: %d", resp.StatusCode())
	}
	res := &bean.Oauth2UserInfo{}
	if err := json.Unmarshal(resp.Body(), &res); err != nil {
		return nil, err
	}
	if res.ErrCode != 0 {
		if res.ErrCode == 40163 {
			return nil, constants.ErrSubscribeCodeDuplicate
		}
		return nil, fmt.Errorf("SubscribeService GetOauth2AccessToken success call, but get api error: %v", res)
	}
	return res, nil
}

func (s *SubscribeService) GetSubscribeConfig(ctx context.Context) (*model.AConfigSubscribe, error) {
	configSub := store.QueryDB().AConfigSubscribe
	subInfo, err := configSub.WithContext(ctx).First()
	if err != nil {
		return nil, err
	}
	return subInfo, nil
}

// GetSubscribeConfigs returns a list of SubscribeConfig
func (s *SubscribeService) GetSubscribeConfigs(ctx context.Context) ([]*model.AConfigSubscribe, error) {
	configSub := store.QueryDB().AConfigSubscribe
	subInfo, err := configSub.WithContext(ctx).Limit(1).Find() // 目前只有一条Configs
	if err != nil {
		return nil, err
	}
	return subInfo, nil
}

// UpdateSubscribeConfig
func (s *SubscribeService) UpdateSubscribeConfig(ctx context.Context, id int32, updateInfo map[string]interface{}) error {
	configSub := store.QueryDB().AConfigSubscribe
	_, err := configSub.WithContext(ctx).Where(configSub.ID.Eq(id)).Updates(updateInfo)
	if err != nil {
		return err
	}
	return nil
}
