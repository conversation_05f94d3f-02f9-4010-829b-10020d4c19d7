package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/go-resty/resty/v2"
)

var (
	_shareOnce    sync.Once
	_shareService *ShareService
)

type ShareService struct{}

func SingletonShareService() *ShareService {
	_shareOnce.Do(func() {
		_shareService = &ShareService{}
	})
	return _shareService
}

// GetShare 获取分享
func (s *ShareService) GetShare(ctx context.Context, req *bean.GetShareReq) (*bean.GetShareResp, error) {
	roadblock := store.QueryDB().MShareRoadblock
	roadblockCtx := roadblock.WithContext(ctx)
	roadblocks, err := roadblockCtx.
		Where(roadblock.GameID.Eq(req.GameID)).
		Where(roadblock.IsDeleted.Value(false)).Find()
	if err != nil {
		return nil, err
	}
	roadblockIDs := make([]int32, 0, len(roadblocks))
	roadblockIDInfo := make(map[int32]*model.MShareRoadblock)
	for _, v := range roadblocks {
		roadblockIDs = append(roadblockIDs, v.ID)
		roadblockIDInfo[v.ID] = v
	}

	share := store.QueryDB().MShare
	shareCtx := share.WithContext(ctx)

	shareScenes := constants.ShareScenesType[req.PlatformType]
	if len(shareScenes) > 0 {
		shareCtx = shareCtx.Where(share.ShareScenes.In(shareScenes...))
	}

	shares, err := shareCtx.
		Where(share.RoadblockID.In(roadblockIDs...)).
		Where(share.Status.Eq(1)). // 开启状态
		Where(share.IsDeleted.Value(false)).Find()
	if err != nil {
		return nil, err
	}

	// 将shares和roadblocks合并到shareInfos
	var shareInfos []*bean.ShareInfo
	for _, v := range shares {
		if v.RoadblockID == 0 {
			continue
		}
		if _, ok := roadblockIDInfo[v.RoadblockID]; !ok {
			continue
		}
		shareInfos = append(shareInfos, &bean.ShareInfo{
			RoadblockNameCn: roadblockIDInfo[v.RoadblockID].RoadblockNameCn,
			RoadblockNameEn: roadblockIDInfo[v.RoadblockID].RoadblockNameEn,
			ShareTimeout:    roadblockIDInfo[v.RoadblockID].ShareTimeout,
			UUID:            v.UUID,
			Name:            v.Name,
			Title:           v.Title,
			ShareScenes:     v.ShareScenes,
			MsgType:         v.MsgType,
			SharePic:        v.SharePic,
			ThumbnailURL:    v.ThumbnailURL,
			ThumbnailID:     v.ThumbnailID,
			PreviewURL:      v.PreviewURL,
			PreviewID:       v.PreviewID,
			CreatedAt:       v.CreatedAt,
			UpdatedAt:       v.UpdatedAt,
		})
	}

	return &bean.GetShareResp{ShareInfos: shareInfos}, nil
}

// CreateUserActivityID 创建私密分享
func (s *ShareService) CreateUserActivityID(ctx context.Context, req *bean.CreateUserActivityIDReq) (*bean.CreateUserActivityIDResp, error) {
	// s.FetchCreateActivityID(ctx, req.AccessToken, req.OpenID)
	return nil, nil
}

const fetchCreateActivityID = "/cgi-bin/message/wxopen/activityid/create"

func (s *ShareService) FetchCreateActivityID(ctx context.Context, accessToken, openID string) (*bean.FetchCreateActivityIDResp, error) {
	resp, err := resty.New().
		SetTimeout(5*time.Second).
		R().
		SetContext(ctx).
		SetQueryParam("access_token", accessToken).
		SetBody(map[string]string{"openid": openID}).
		Post(config.GlobConfig.Minigame.BaseURL + fetchCreateActivityID)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("fetchCreateActivityID err, https code: %d", resp.StatusCode())
	}

	res := &bean.FetchCreateActivityIDResp{}
	if err := json.Unmarshal(resp.Body(), res); err != nil {
		return nil, err
	}
	if res.ErrCode != 0 {
		return nil, fmt.Errorf("fetchCreateActivityID err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
	}
	return res, nil
}
