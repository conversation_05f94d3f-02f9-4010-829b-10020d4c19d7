package service

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger" // Corrected import path
	"gorm.io/gorm"
)

var (
	_chatMessageOnce    sync.Once
	_chatMessageService *ChatMessageService
)

// ChatMessageService 聊天消息服务
type ChatMessageService struct {
	db *gorm.DB
}

// SingletonChatMessageService 获取聊天消息服务单例
func SingletonChatMessageService() *ChatMessageService {
	_chatMessageOnce.Do(func() {
		_chatMessageService = &ChatMessageService{
			db: store.GOrmDB(context.Background()),
		}
	})
	return _chatMessageService
}

// GetOrCreateChatOrder 获取或创建聊天工单
// func (s *ChatMessageService) GetOrCreateChatOrder(ctx context.Context, gameID, userID, openID string) (*model.MWorkorder, error) {
// 	logger.Logger.InfofCtx(ctx, "获取或创建聊天工单, GameID: %s, UserID: %s", gameID, userID)

// 	// 查询是否已存在聊天工单
// 	q := store.QueryDB().MWorkorder
// 	workorder, err := q.WithContext(ctx).
// 		Where(
// 			q.GameID.Eq(gameID),
// 			q.UserID.Eq(userID),
// 			q.OpenID.Eq(openID),
// 			q.Category.Eq("chat"), // 使用特殊分类标识聊天工单
// 			q.IsDeleted.Is(false),
// 		).
// 		First()

// 	if err == nil {
// 		// 找到了现有的聊天工单
// 		return workorder, nil
// 	}

// 	if err != gorm.ErrRecordNotFound {
// 		// 发生了其他错误
// 		return nil, err
// 	}

// 	// 没有找到聊天工单，创建一个新的
// 	orderID := fmt.Sprintf("chat_%s", uuid.New().String())
// 	now := time.Now().UnixMilli()

// 	newWorkorder := &model.MWorkorder{
// 		OrderID:           orderID,
// 		GameID:            gameID,
// 		UserID:            userID,
// 		OpenID:            openID,
// 		MiniprogramOpenID: openID, // 使用相同的OpenID
// 		Content:           "聊天记录",
// 		Priority:          1,      // 一般优先级
// 		Status:            1,      // 待接单状态
// 		Category:          "chat", // 特殊分类
// 		HasNewReply:       false,  // 没有新回复
// 		HasRead:           true,   // 已读
// 		LastReplyUserType: 0,      // 无回复
// 		CreatedAt:         now,
// 		UpdatedAt:         now,
// 		IsDeleted:         false,
// 	}

// 	err = s.db.WithContext(ctx).Create(newWorkorder).Error
// 	if err != nil {
// 		return nil, err
// 	}

// 	return newWorkorder, nil
// }

// SaveUserQuestion 保存用户问题
func (s *ChatMessageService) SaveUserQuestion(ctx context.Context, gameID, userID, openID, question, sessionID string) (*model.MChatMessage, error) {
	logger.Logger.InfofCtx(ctx, "保存用户问题, GameID: %s, UserID: %s", gameID, userID)

	// 创建用户问题消息
	now := time.Now().UnixMilli()
	userMessage := &model.MChatMessage{
		GameID:            gameID,
		UserID:            userID,
		OpenID:            openID,
		MiniprogramOpenID: openID,
		MessageType:       1, // 用户消息
		Content:           question,
		FeedbackType:      0,
		CreatedAt:         now,
		UpdatedAt:         now,
		IsDeleted:         false,
	}

	q := store.QueryDB().MChatMessage
	err := q.WithContext(ctx).Create(userMessage)
	if err != nil {
		return nil, fmt.Errorf("保存用户问题失败: %w", err)
	}

	return userMessage, nil
}

// SaveBotAnswer 保存机器人回答
func (s *ChatMessageService) SaveBotAnswer(ctx context.Context, gameID, userID, openID, question, answer, sessionID string) (*model.MChatMessage, error) {
	logger.Logger.InfofCtx(ctx, "保存机器人回答, GameID: %s, UserID: %s", gameID, userID)

	// 创建机器人回答消息
	now := time.Now().UnixMilli()
	botMessage := &model.MChatMessage{
		GameID:            gameID,
		UserID:            userID,
		OpenID:            openID,
		MiniprogramOpenID: openID,
		MessageType:       2, // 系统消息
		Content:           answer,
		FeedbackType:      0,
		CreatedAt:         now,
		UpdatedAt:         now,
		IsDeleted:         false,
	}

	q := store.QueryDB().MChatMessage
	err := q.WithContext(ctx).Create(botMessage)
	if err != nil {
		return nil, fmt.Errorf("保存机器人回答失败: %w", err)
	}

	return botMessage, nil
}

// SaveFeedback 保存反馈
func (s *ChatMessageService) SaveFeedback(ctx context.Context, req *bean.WorkorderFeedbackReq) error {
	logger.Logger.InfofCtx(ctx, "保存反馈, MessageID: %s, FeedbackType: %d", req.MessageID, req.FeedbackType)

	// 更新反馈状态
	q := store.QueryDB().MChatMessage
	now := time.Now().UnixMilli()
	_, err := q.WithContext(ctx).
		Where(q.ID.Eq(req.MessageID)).
		UpdateSimple(
			q.FeedbackType.Value(int32(req.FeedbackType)),
			q.UpdatedAt.Value(now),
		)

	if err != nil {
		return fmt.Errorf("更新反馈状态失败: %w", err)
	}

	return nil
}

// SaveWelcomeMessage 保存欢迎消息
func (s *ChatMessageService) SaveWelcomeMessage(ctx context.Context, gameID, userID, openID, content string) error {
	logger.Logger.InfofCtx(ctx, "保存欢迎消息, GameID: %s, UserID: %s", gameID, userID)

	// 生成会话ID
	// sessionID := fmt.Sprintf("%s_%s_%d", gameID, userID, time.Now().UnixMilli())

	// 创建系统欢迎消息
	now := time.Now().UnixMilli()
	welcomeMessage := &model.MChatMessage{
		GameID:            gameID,
		UserID:            userID,
		OpenID:            openID,
		MiniprogramOpenID: openID,
		MessageType:       2, // 系统消息
		Content:           content,
		FeedbackType:      0,
		CreatedAt:         now,
		UpdatedAt:         now,
		IsDeleted:         false,
	}

	q := store.QueryDB().MChatMessage
	err := q.WithContext(ctx).Create(welcomeMessage)
	if err != nil {
		return fmt.Errorf("保存欢迎消息失败: %w", err)
	}

	return nil
}

// GetMessageHistory 获取消息历史
func (s *ChatMessageService) GetMessageHistory(ctx context.Context, req *bean.WorkorderHistoryReq) (*bean.WorkorderHistoryResp, error) {
	logger.Logger.InfofCtx(ctx, "获取消息历史, GameID: %s, UserID: %s", req.GameID, req.UserID)

	// 设置默认限制为50条
	limit := 50
	if req.Limit > 0 {
		limit = req.Limit
	}

	// 直接查询聊天消息表
	q := store.QueryDB().MChatMessage
	messages, err := q.WithContext(ctx).
		Where(
			q.GameID.Eq(req.GameID),
			q.UserID.Eq(req.UserID),
			q.OpenID.Eq(req.OpenID),
			q.IsDeleted.Is(false),
		).
		Order(q.CreatedAt.Desc()).
		Limit(limit). // 不再需要多查询一条
		Find()

	if err != nil {
		return nil, fmt.Errorf("查询聊天消息失败: %w", err)
	}
	// 构建响应
	resp := &bean.WorkorderHistoryResp{
		Messages: make([]bean.WorkorderHistoryMessage, 0, len(messages)),
	}

	// 按时间正序排列
	sort.Slice(messages, func(i, j int) bool {
		return messages[i].CreatedAt < messages[j].CreatedAt
	})

	// 处理其他消息
	for _, msg := range messages {
		resp.Messages = append(resp.Messages, bean.WorkorderHistoryMessage{
			MessageID:    msg.ID,
			MessageType:  msg.MessageType,
			Content:      msg.Content,
			CreatedAt:    msg.CreatedAt,
			FeedbackType: msg.FeedbackType,
		})
	}

	return resp, nil
}

// SubmitChatMessage 提交聊天消息并处理回复
func (s *ChatMessageService) SubmitChatMessage(ctx context.Context, req *bean.ChatMessageSubmitReq) (*bean.ChatMessageSubmitResp, error) {
	logger.Logger.InfofCtx(ctx, "提交聊天消息, GameID: %s, UserID: %s, Content: %s", req.GameID, req.UserID, req.Content)

	// 生成会话ID
	sessionID := fmt.Sprintf("%s_%s_%d", req.GameID, req.UserID, time.Now().UnixMilli())

	// 保存用户消息
	_, err := s.SaveUserQuestion(ctx, req.GameID, req.UserID, req.OpenID, req.Content, sessionID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "保存用户消息失败: %v", err)
		return nil, fmt.Errorf("保存用户消息失败: %w", err)
	}

	// 使用AI模型服务处理问题
	aiModelService := SingletonAIModelService()

	// 1. 先尝试完全匹配
	exactMatch, err := aiModelService.FindExactMatch(ctx, req.GameID, req.Content)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "查找完全匹配问题失败: %v", err)
		// 继续处理，不中断流程
	}

	var replyContent string
	if exactMatch != nil {
		// 找到完全匹配的问题，直接返回答案
		logger.Logger.InfofCtx(ctx, "找到完全匹配的问题: %s", exactMatch.Question)
		replyContent = exactMatch.Answer
	} else {
		// 2. 没有完全匹配，使用AI模型查找相似问题
		logger.Logger.InfofCtx(ctx, "未找到完全匹配的问题，使用AI模型查找相似问题")

		// 获取系统提示词
		systemPrompt, err := aiModelService.GetSystemPrompt(ctx)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "获取系统提示词失败: %v", err)
			// 使用默认提示词
			systemPrompt = "#能力：你是游戏客服，能精准分析用户提问，并从问题库中找出和用户问题最相符的一个问题。请直接返回问题条目。如果没有匹配的问题，请返回以下结果：无。#限制 ##问题条目仅保留描述，不得包含任何额外文本或解释。##返回结果中不得包含任何符号。# 问题库：{问题库}"
		}

		// 获取问题库
		questions, err := aiModelService.GetQuestionLibrary(ctx, req.GameID)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "获取问题库失败: %v", err)
			// 返回默认回复
			replyContent = constants.ChatDefaultNoMatchReply
		} else if len(questions) == 0 {
			// 问题库为空
			logger.Logger.WarnfCtx(ctx, "问题库为空")
			replyContent = constants.ChatDefaultNoMatchReply
		} else {
			// 调用AI模型
			modelAnswer, err := aiModelService.CallGLM4Flash(ctx, systemPrompt, req.Content, questions)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "调用AI模型失败: %v", err)
				// 返回默认回复
				replyContent = constants.ChatDefaultNoMatchReply
			} else {
				// 根据模型回答找到匹配的问题

				// modelAnswer可能会包含特殊符号如1-xxx, 2-xxx这种格式，FindMatchingQuestion方法会对其进行预处理
				// 清理模型回答中的空格、编号等，然后尝试在问题库中查找匹配的问题
				// 如果模型回答为"无"，表示没有找到匹配的问题，将返回nil
				matchedQuestion, err := aiModelService.FindMatchingQuestion(ctx, req.GameID, modelAnswer, questions)
				if err != nil {
					logger.Logger.WarnfCtx(ctx, "查找匹配问题失败: %v", err)
					// 继续处理，不中断流程
				}

				if matchedQuestion != nil {
					// 找到匹配的问题，返回答案
					logger.Logger.InfofCtx(ctx, "找到匹配的问题: %s", matchedQuestion.Question)
					replyContent = matchedQuestion.Answer
				} else {
					// 没有找到匹配的问题
					logger.Logger.WarnfCtx(ctx, "未找到匹配的问题")
					replyContent = constants.ChatDefaultNoMatchReply
				}
			}
		}
	}

	if replyContent == "" {
		replyContent = constants.ChatDefaultNoMatchReply
	}
	logger.Logger.InfofCtx(ctx, "最终回复: %s", replyContent)

	// 保存机器人回复
	botMessage, err := s.SaveBotAnswer(ctx, req.GameID, req.UserID, req.OpenID, req.Content, replyContent, sessionID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "保存机器人回复失败: %v", err)
		return nil, fmt.Errorf("保存机器人回复失败: %w", err)
	}

	return &bean.ChatMessageSubmitResp{
		ReplyContent: replyContent,
		MessageID:    int32(botMessage.ID),
		HasMatched:   replyContent != constants.ChatDefaultNoMatchReply,
	}, nil
}
