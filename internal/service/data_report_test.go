package service

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/mysql"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/stretchr/testify/assert"
)

func initForDataReportTest() {
	// 设置工作目录到项目根目录，确保能找到configs/目录
	// 当运行测试时，工作目录可能是internal/service，需要切换到项目根目录
	wd, err := os.Getwd()
	if err != nil {
		panic("Failed to get working directory: " + err.Error())
	}

	if filepath.Base(wd) == "service" {
		if err := os.Chdir("../.."); err != nil {
			panic("Failed to change directory to project root: " + err.Error())
		}
	}

	config.MustInit()
	logger.InitLogger(&config.GlobConfig.Logger)
	mysql.InitMysql(&config.GlobConfig.Mysql)
	store.InitQueryDB()
	redis.InitRedis(&config.GlobConfig.Redis)
}

// TestDataReportService_shouldTriggerAdditionalReport 测试触发条件判断
func TestDataReportService_shouldTriggerAdditionalReport(t *testing.T) {
	initForDataReportTest()

	service := SingletonDataReportService()

	tests := []struct {
		name      string
		eventName string
		gameID    string
		eventType string
		expected  bool
	}{
		{
			name:      "符合条件：sdk_前缀 + kof-test游戏",
			eventName: "sdk_test_event",
			gameID:    config.GlobConfig.ExtraDataReport.TargetGameID,
			eventType: constants.ThinkingdataTrack,
			expected:  true,
		},
		{
			name:      "符合条件：server_前缀 + kof-test游戏",
			eventName: "server_test_event",
			gameID:    config.GlobConfig.ExtraDataReport.TargetGameID,
			eventType: constants.ThinkingdataTrack,
			expected:  true,
		},
		{
			name:      "不符合条件：无sdk_前缀",
			eventName: "normal_event",
			gameID:    config.GlobConfig.ExtraDataReport.TargetGameID,
			eventType: constants.ThinkingdataTrack,
			expected:  false,
		},
		{
			name:      "不符合条件：游戏ID不匹配",
			eventName: "sdk_test_event",
			gameID:    "other-game",
			eventType: constants.ThinkingdataTrack,
			expected:  false,
		},
		{
			name:      "不符合条件：都不匹配",
			eventName: "normal_event",
			gameID:    "other-game",
			eventType: constants.ThinkingdataTrack,
			expected:  false,
		},
		{
			name:      "边界情况：空事件名",
			eventName: "",
			gameID:    config.GlobConfig.ExtraDataReport.TargetGameID,
			eventType: constants.ThinkingdataTrack,
			expected:  false,
		},
		{
			name:      "边界情况：空游戏ID",
			eventName: "sdk_test_event",
			gameID:    "",
			eventType: constants.ThinkingdataTrack,
			expected:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.shouldTriggerAdditionalReport(tt.eventName, tt.gameID, tt.eventType)
			assert.Equal(t, tt.expected, result, "触发条件判断结果不符合预期")
		})
	}
}

// TestDataReportService_AdditionalDataReport 测试单条额外数据上报
func TestDataReportService_AdditionalDataReport(t *testing.T) {
	initForDataReportTest()

	service := SingletonDataReportService()
	ctx := context.Background()

	t.Run("符合条件的数据上报", func(t *testing.T) {
		req := &bean.DataReportReq{
			Header: middleware.Header{
				UserID:   "test_user_123",
				DeviceID: "test_device_456",
				AppID:    "test_app_789",
				GameID:   config.GlobConfig.ExtraDataReport.TargetGameID, // "kof-test"
			},
			RoleID:     "test_role_001",
			IP:         "***********",
			IPRegionID: "CN",
			EventName:  "sdk_unit_test_event", // 包含 "sdk_" 前缀
			UUID:       "test_uuid_001",
			EventType:  constants.ThinkingdataTrack,
			Properties: map[string]interface{}{
				"test_key": "test_value",
				"level":    10,
				"source":   "unit_test",
			},
		}

		// 这个方法是异步的，不会返回错误
		service.AdditionalDataReport(ctx, req)

		// 等待异步操作完成
		time.Sleep(1 * time.Second)

		t.Log("符合条件的数据上报测试完成，请检查日志输出")
	})

	t.Run("不符合条件的数据上报", func(t *testing.T) {
		req := &bean.DataReportReq{
			Header: middleware.Header{
				UserID:   "test_user_123",
				DeviceID: "test_device_456",
				AppID:    "test_app_789",
				GameID:   "other-game", // 不是目标游戏ID
			},
			RoleID:     "test_role_002",
			IP:         "***********",
			IPRegionID: "CN",
			EventName:  "sdk_unit_test_event", // 包含 "sdk_" 前缀但游戏ID不匹配
			UUID:       "test_uuid_002",
			EventType:  constants.ThinkingdataTrack,
			Properties: map[string]interface{}{
				"test_key": "test_value2",
			},
		}

		// 这个方法是异步的，不会返回错误
		service.AdditionalDataReport(ctx, req)

		// 等待异步操作完成
		time.Sleep(1 * time.Second)

		t.Log("不符合条件的数据上报测试完成，应该没有额外上报")
	})
}

// TestDataReportService_AdditionalBatchDataReport 测试批量额外数据上报
func TestDataReportService_AdditionalBatchDataReport(t *testing.T) {
	initForDataReportTest()

	service := SingletonDataReportService()
	ctx := context.Background()

	t.Run("批量数据上报", func(t *testing.T) {
		batchReq := &bean.BatchDataReportReq{
			Header: middleware.Header{
				UserID:   "test_user_batch",
				DeviceID: "test_device_batch",
				AppID:    "test_app_batch",
				GameID:   config.GlobConfig.ExtraDataReport.TargetGameID, // "kof-test"
			},
			IP:         "*************",
			IPRegionID: "CN",
			DataReports: []*bean.BatchDataReport{
				{
					RoleID:    "role_001",
					Time:      "2024-01-01 12:00:00",
					EventName: "sdk_batch_unit_test_1", // 符合条件
					UUID:      "batch_uuid_001",
					EventType: constants.ThinkingdataTrack,
					Properties: map[string]interface{}{
						"batch_key": "batch_value_1",
						"source":    "unit_test_batch",
					},
				},
				{
					RoleID:    "role_002",
					Time:      "2024-01-01 12:01:00",
					EventName: "normal_batch_event", // 不符合条件（无sdk_前缀）
					UUID:      "batch_uuid_002",
					EventType: constants.ThinkingdataTrack,
					Properties: map[string]interface{}{
						"batch_key": "batch_value_2",
					},
				},
				{
					RoleID:    "role_003",
					Time:      "2024-01-01 12:02:00",
					EventName: "sdk_batch_unit_test_2", // 符合条件
					UUID:      "batch_uuid_003",
					EventType: constants.ThinkingdataTrack,
					Properties: map[string]interface{}{
						"batch_key": "batch_value_3",
						"source":    "unit_test_batch",
					},
				},
			},
		}

		// 这个方法是异步的，不会返回错误
		service.AdditionalBatchDataReport(ctx, batchReq)

		// 等待异步操作完成
		time.Sleep(2 * time.Second)

		t.Logf("批量数据上报测试完成，共 %d 条数据，其中 2 条应该触发额外上报", len(batchReq.DataReports))
	})
}

// TestDataReportService_performAdditionalReport 测试实际的额外上报逻辑
func TestDataReportService_performAdditionalReport(t *testing.T) {
	initForDataReportTest()

	service := SingletonDataReportService()
	ctx := context.Background()

	tests := []struct {
		name      string
		eventType string
		expectErr bool
	}{
		{
			name:      "Track事件类型",
			eventType: constants.ThinkingdataTrack,
			expectErr: false,
		},
		{
			name:      "UserSet事件类型",
			eventType: constants.ThinkingdataUserSet,
			expectErr: false,
		},
		{
			name:      "不支持的事件类型",
			eventType: "unsupported_type",
			expectErr: false, // 不支持的类型会被忽略，不返回错误
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &bean.DataReportReq{
				Header: middleware.Header{
					UserID:   "test_user_perform",
					DeviceID: "test_device_perform",
					AppID:    "test_app_perform",
					GameID:   config.GlobConfig.ExtraDataReport.TargetGameID,
				},
				RoleID:     "test_role_perform",
				IP:         "***********",
				IPRegionID: "CN",
				EventName:  "sdk_perform_test",
				UUID:       "test_uuid_perform",
				EventType:  tt.eventType,
				Properties: map[string]interface{}{
					"test_key": "perform_test_value",
					"type":     tt.eventType,
				},
			}

			err := service.performAdditionalReport(ctx, req)
			if tt.expectErr {
				assert.Error(t, err, "期望返回错误")
			} else {
				assert.NoError(t, err, "不期望返回错误")
			}
		})
	}
}

// TestDataReportService_ServerEventAdditionalReport 测试server_前缀事件的额外上报
func TestDataReportService_ServerEventAdditionalReport(t *testing.T) {
	initForDataReportTest()

	service := SingletonDataReportService()
	ctx := context.Background()

	t.Run("ReportLogin server_login事件额外上报", func(t *testing.T) {
		err := service.ReportLogin(ctx, "test_user_login", "test_device_login", config.GlobConfig.ExtraDataReport.TargetGameID, "test_app_login", &bean.ChannelInfo{
			Channel: "test_channel",
			ADFrom:  "test_ad",
		})
		assert.NoError(t, err)

		// 等待异步操作完成
		time.Sleep(1 * time.Second)

		t.Log("server_login事件额外上报测试完成，请检查日志输出")
	})

	t.Run("ReportRegister server_register事件额外上报", func(t *testing.T) {
		err := service.ReportRegister(ctx, "test_user_register", "test_device_register", config.GlobConfig.ExtraDataReport.TargetGameID, "test_app_register", "iOS", &bean.ChannelInfo{
			Channel: "test_channel",
			ADFrom:  "test_ad",
		})
		assert.NoError(t, err)

		// 等待异步操作完成
		time.Sleep(1 * time.Second)

		t.Log("server_register事件额外上报测试完成，请检查日志输出")
	})

	t.Run("ReportProductShipment server_shipment事件额外上报", func(t *testing.T) {
		shipmentInfo := &bean.ProductShipmentRes{
			Code: 200,
			Msg:  "success",
		}
		err := service.ReportProductShipment(ctx, "test_user_shipment", "test_device_shipment", config.GlobConfig.ExtraDataReport.TargetGameID, "test_app_shipment", "order_123", "goods_456", 100, 200, 1, shipmentInfo)
		assert.NoError(t, err)

		// 等待异步操作完成
		time.Sleep(1 * time.Second)

		t.Log("server_shipment事件额外上报测试完成，请检查日志输出")
	})

	t.Run("不符合条件的游戏ID", func(t *testing.T) {
		err := service.ReportLogin(ctx, "test_user_other", "test_device_other", "other-game", "test_app_other", &bean.ChannelInfo{
			Channel: "test_channel",
			ADFrom:  "test_ad",
		})
		assert.NoError(t, err)

		// 等待异步操作完成
		time.Sleep(1 * time.Second)

		t.Log("不符合条件的游戏ID测试完成，应该没有额外上报")
	})
}

// TestDataReportService_ReportUserAvatarPoint 测试用户头像埋点上报
func TestDataReportService_ReportUserAvatarPoint(t *testing.T) {
	initForDataReportTest()

	service := SingletonDataReportService()
	ctx := context.Background()

	t.Run("正常头像URL上报", func(t *testing.T) {
		err := service.ReportUserAvatarPoint(ctx,
			"test_user_avatar",
			"test_device_avatar",
			config.GlobConfig.ExtraDataReport.TargetGameID,
			"test_app_avatar",
			"https://example.com/avatar.jpg")
		assert.NoError(t, err)

		// 等待异步操作完成
		time.Sleep(1 * time.Second)

		t.Log("用户头像埋点上报测试完成，请检查日志输出")
	})

	t.Run("空头像URL上报", func(t *testing.T) {
		err := service.ReportUserAvatarPoint(ctx,
			"test_user_empty_avatar",
			"test_device_empty_avatar",
			config.GlobConfig.ExtraDataReport.TargetGameID,
			"test_app_empty_avatar",
			"")
		assert.NoError(t, err)

		// 等待异步操作完成
		time.Sleep(1 * time.Second)

		t.Log("空头像URL埋点上报测试完成，请检查日志输出")
	})

	t.Run("不符合条件的游戏ID", func(t *testing.T) {
		err := service.ReportUserAvatarPoint(ctx,
			"test_user_other_game",
			"test_device_other_game",
			"other-game",
			"test_app_other_game",
			"https://example.com/other-avatar.jpg")
		assert.NoError(t, err)

		// 等待异步操作完成
		time.Sleep(1 * time.Second)

		t.Log("不符合条件的游戏ID头像埋点测试完成，应该没有额外上报")
	})
}
