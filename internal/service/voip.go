package service

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/https"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

var (
	voipServiceOnce sync.Once
	voipService     *VoipService
)

type VoipService struct {
	douyinHttpService *https.DouyinHttpService
}

func SingletonVoipService() *VoipService {
	voipServiceOnce.Do(func() {
		voipService = &VoipService{
			douyinHttpService: https.SingletonDouyinHttpService(),
		}
	})
	return voipService
}

// CreateGroupID
func (s *VoipService) CreateGroupID(ctx context.Context, groupID string) (string, error) {
	if groupID == "" {
		groupID = util.UUIDWithoutHyphens()
	}
	return groupID, nil
}

func (s *VoipService) IsVoipRoomExist(ctx context.Context, gameID, groupID, userID string) (bool, error) {
	// 实现查询房间的逻辑
	voip := store.QueryDB().AVoip
	voipCtx := voip.WithContext(ctx)
	count, err := voipCtx.Where(voip.GameID.Eq(gameID)).Where(voip.GroupID.Eq(groupID)).Where(voip.UserID.Eq(userID)).Where(voip.IsDeleted.Zero()).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// JoinRoom
func (s *VoipService) JoinRoom(ctx context.Context, gameID, groupID, platformType, userID string) error {
	voip := store.QueryDB().AVoip
	voipCtx := voip.WithContext(ctx)
	if err := voipCtx.Create(&model.AVoip{
		GameID:       gameID,
		GroupID:      groupID,
		PlatformType: platformType,
		UserID:       userID,
	}); err != nil {
		return err
	}
	return nil
}

// GenerateNonceStrAndTimeStamp 生成随机数和时间戳
func (s *VoipService) GenerateNonceStrAndTimeStamp() (string, int64) {
	nonceStr := strconv.Itoa(util.GenRandomNum())
	timestamp := time.Now().Unix()
	return nonceStr, timestamp
}

// GenerateWechatRoomSignature 生成微信房间签名
func (s *VoipService) GenerateWechatRoomSignature(appID, groupID, nonceStr string, timeStamp int64, sessionKey string) (string, error) {
	inputs := []string{appID, groupID, nonceStr, strconv.FormatInt(timeStamp, 10)}
	sort.Strings(inputs)
	joinedString := strings.Join(inputs, "")
	h := hmac.New(sha256.New, []byte(sessionKey))
	h.Write([]byte(joinedString))

	return hex.EncodeToString(h.Sum(nil)), nil
}

// FetchDouyinChannelID
func (s *VoipService) FetchDouyinChannelID(ctx context.Context, accessToken string) (string, error) {
	resp, err := s.douyinHttpService.GenerateRoomID(ctx, accessToken)
	if err != nil {
		return "", err
	}
	if resp.Data.ChannelID == "" {
		return "", constants.ErrDouyinChannelIDIsEmpty
	}
	return resp.Data.ChannelID, nil
}
