package service

import (
	"context"
	"os"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/mysql"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
)

// 初始化测试环境
func initTestEnvForOptimized() {
	// 设置配置文件路径到项目根目录
	originalDir, _ := os.Getwd()
	defer os.Chdir(originalDir)

	// 切换到项目根目录
	if err := os.Chdir("../../"); err != nil {
		panic("无法切换到项目根目录: " + err.Error())
	}

	config.MustInit()
	logger.InitLogger(&config.GlobConfig.Logger)
	mysql.InitMysql(&config.GlobConfig.Mysql)
	store.InitQueryDB()
	redis.InitRedis(&config.GlobConfig.Redis)
}

// TestGetUserInfoByOpenIDForTestOptimized 测试优化后的函数性能
func TestGetUserInfoByOpenIDForTestOptimized(t *testing.T) {
	initTestEnvForOptimized()

	userService := SingletonUserService()
	ctx := context.Background()

	// 测试参数
	gameID := "test_game_001"
	channel := "test_channel"
	adFrom := "test_ad"
	openID := "test_openid_optimized_001"
	unionID := "test_unionid_optimized_001"
	sessionKey := "test_session_key"

	// 第一次调用 - 创建用户
	t.Run("创建新用户", func(t *testing.T) {
		start := time.Now()
		user, err := userService.GetUserInfoByOpenIDForTest(ctx, gameID, channel, adFrom, openID, unionID, sessionKey)
		duration := time.Since(start)

		if err != nil {
			t.Logf("创建用户失败: %v", err)
			return
		}

		if user == nil {
			t.Error("返回的用户信息不应该为空")
			return
		}

		if !user.IsRegister {
			t.Error("新用户应该标记为注册用户")
		}

		t.Logf("创建新用户成功，耗时: %v", duration)
		t.Logf("用户信息: UserID=%s, OpenID=%s", user.UserInfo.UserID, user.ChannelInfo.OpenID)
	})

	// 第二次调用 - 获取已存在用户（测试优化效果）
	t.Run("获取已存在用户", func(t *testing.T) {
		start := time.Now()
		user, err := userService.GetUserInfoByOpenIDForTest(ctx, gameID, channel, adFrom, openID, unionID, sessionKey)
		duration := time.Since(start)

		if err != nil {
			t.Logf("获取用户失败: %v", err)
			return
		}

		if user == nil {
			t.Error("返回的用户信息不应该为空")
			return
		}

		if user.IsRegister {
			t.Error("已存在用户不应该标记为注册用户")
		}

		t.Logf("获取已存在用户成功，耗时: %v", duration)
		t.Logf("用户信息: UserID=%s, OpenID=%s", user.UserInfo.UserID, user.ChannelInfo.OpenID)

		// 验证性能改进 - 已存在用户的查询应该很快
		if duration > 100*time.Millisecond {
			t.Logf("警告：查询已存在用户耗时较长: %v", duration)
		}
	})

	// 性能基准测试
	t.Run("性能基准测试", func(t *testing.T) {
		const iterations = 10
		var totalDuration time.Duration

		for i := 0; i < iterations; i++ {
			start := time.Now()
			_, err := userService.GetUserInfoByOpenIDForTest(ctx, gameID, channel, adFrom, openID, unionID, sessionKey)
			duration := time.Since(start)
			totalDuration += duration

			if err != nil {
				t.Logf("第%d次调用失败: %v", i+1, err)
				continue
			}
		}

		avgDuration := totalDuration / iterations
		t.Logf("性能基准测试完成:")
		t.Logf("- 总调用次数: %d", iterations)
		t.Logf("- 总耗时: %v", totalDuration)
		t.Logf("- 平均耗时: %v", avgDuration)
		t.Logf("- 预估QPS: %.2f", float64(iterations)/totalDuration.Seconds())

		// 性能目标：平均响应时间应该小于50ms
		if avgDuration > 50*time.Millisecond {
			t.Logf("性能警告：平均响应时间 %v 超过目标值 50ms", avgDuration)
		} else {
			t.Logf("性能良好：平均响应时间 %v 符合预期", avgDuration)
		}
	})
}

// BenchmarkGetUserInfoByOpenIDForTestOptimized 基准测试
func BenchmarkGetUserInfoByOpenIDForTestOptimized(b *testing.B) {
	initTestEnvForOptimized()

	userService := SingletonUserService()
	ctx := context.Background()

	// 测试参数
	gameID := "bench_game_001"
	channel := "bench_channel"
	adFrom := "bench_ad"
	unionID := "bench_unionid_001"
	sessionKey := "bench_session_key"

	// 预先创建一个用户，确保后续测试都是查询已存在用户
	openID := "bench_openid_base"
	_, err := userService.GetUserInfoByOpenIDForTest(ctx, gameID, channel, adFrom, openID, unionID, sessionKey)
	if err != nil {
		b.Fatalf("预创建用户失败: %v", err)
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			// 为每次测试生成不同的openid，避免缓存影响
			testOpenID := openID // 使用相同的openID测试已存在用户的性能
			_, err := userService.GetUserInfoByOpenIDForTest(ctx, gameID, channel, adFrom, testOpenID, unionID, sessionKey)
			if err != nil {
				b.Logf("基准测试失败: %v", err)
				continue
			}
		}
	})
}
