package service

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_reportServiceOnce sync.Once
	_reportService     *ReportService
)

type ReportService struct{}

func SingletonReportService() *ReportService {
	_reportServiceOnce.Do(func() {
		_reportService = &ReportService{}
	})
	return _reportService
}

// CreateReport creates a new report in the database
func (s *ReportService) CreateReport(ctx context.Context, req *bean.SubmitReportReq) (int32, error) {
	// 处理举报项
	reportItems, err := s.processReportItems(ctx, req.GameID, req.ReportItem)
	if err != nil {
		logger.Logger.Errorf("Failed to process report items: %v", err)
		return 0, err
	}

	report := &model.MReport{
		GameID:             req.GameID,
		ReportedPlatformID: req.ReportedPlatformID,
		ReportedRoleID:     req.ReportedRoleID,
		ReportedNickname:   req.ReportedNickname,
		ReportedAvatar:     req.ReportedAvatar,
		ReportedServerID:   req.ReportedServerID,
		ReporterServerID:   req.ReporterServerID,
		ReporterRoleID:     req.ReporterRoleID,
		ReporterNickname:   req.ReporterNickname,
		ReporterAvatar:     req.ReporterAvatar,
		ReporterPlatformID: req.ReporterPlatformID,
		ReportReason:       req.ReportReason,
		Status:             0, // 0:未处理
		ReportTimeAt:       time.Now().UnixMilli(),
		ExtraParamA:        req.ExtraParamA,
		ExtraParamB:        req.ExtraParamB,
		SessionFrom:        req.SessionFrom,
	}

	reportItemJSON, err := json.Marshal(reportItems)
	if err != nil {
		logger.Logger.Errorf("Failed to marshal report item: %v", err)
		return 0, err
	}
	report.ReportItem = string(reportItemJSON)

	if err := store.QueryDB().MReport.WithContext(ctx).Create(report); err != nil {
		logger.Logger.Errorf("Failed to create report in database: %v", err)
		return 0, err
	}

	return report.ID, nil
}

// convertReportItems 将字符串类型的举报项转换为数字类型
func (s *ReportService) convertReportItems(ctx context.Context, gameID string, reportItems []string) ([]int32, error) {
	if len(reportItems) == 0 {
		return []int32{}, nil
	}

	// 查询全局配置和特定游戏的配置
	conf := store.QueryDB().MReportItemConfig
	itemConfigs, err := conf.WithContext(ctx).
		Where(conf.GameID.Eq(gameID)).
		Where(conf.IsDeleted.Is(false)).
		Find()
	if err != nil {
		return nil, err
	}

	// 构建映射表：举报项名称 -> 举报项数值
	itemValueMap := make(map[string]int32)
	for _, config := range itemConfigs {
		itemValueMap[config.ItemName] = config.ItemValue
	}

	// 转换举报项
	var result []int32
	for _, item := range reportItems {
		if value, exists := itemValueMap[item]; exists {
			result = append(result, value)
		} else {
			logger.Logger.Warnf("Report item not found in configuration: %s", item)
			// 如果找不到配置，可以选择跳过或使用默认值
			// 这里选择跳过未找到的举报项
		}
	}

	if len(result) == 0 {
		return nil, constants.ErrReportItemsNotFound
	}

	return result, nil
}

// processReportItems 处理举报项，支持字符串数组和数字数组两种输入
func (s *ReportService) processReportItems(ctx context.Context, gameID string, reportItems []interface{}) ([]int32, error) {
	if len(reportItems) == 0 {
		return []int32{}, nil
	}

	// 检查第一个元素的类型来判断整个数组的类型
	firstItem := reportItems[0]

	switch v := firstItem.(type) {
	case string:
		// 字符串数组，使用原有的转换逻辑
		stringItems := make([]string, len(reportItems))
		for i, item := range reportItems {
			if str, ok := item.(string); ok {
				stringItems[i] = str
			} else {
				logger.Logger.Errorf("Mixed types in report items: expected all strings but found %T at index %d", item, i)
				return nil, constants.ErrInvalidReportItemType
			}
		}
		return s.convertReportItems(ctx, gameID, stringItems)

	case int32:
		// int32 类型，直接使用
		result := make([]int32, len(reportItems))
		for i, item := range reportItems {
			if num, ok := item.(int32); ok {
				result[i] = num
			} else {
				logger.Logger.Errorf("Mixed types in report items: expected all int32 but found %T at index %d", item, i)
				return nil, constants.ErrInvalidReportItemType
			}
		}
		return result, nil

	case float64:
		// JSON 中的数字默认解析为 float64，转换为 int32
		result := make([]int32, len(reportItems))
		for i, item := range reportItems {
			if num, ok := item.(float64); ok {
				result[i] = int32(num)
			} else {
				logger.Logger.Errorf("Mixed types in report items: expected all numbers but found %T at index %d", item, i)
				return nil, constants.ErrInvalidReportItemType
			}
		}
		return result, nil

	default:
		logger.Logger.Errorf("Unsupported report item type: %T, only string and int32 are supported", v)
		return nil, constants.ErrUnsupportedReportItemType
	}
}

// UpdateReport 更新举报状态
func (s *ReportService) UpdateReportOperationStatus(ctx context.Context, id int32) error {
	mOperation := store.QueryDB().MReportOperation
	_, err := mOperation.WithContext(ctx).
		Where(mOperation.ID.Eq(id)).
		UpdateColumnSimple(mOperation.CallbackStatus.Value(1))
	if err != nil {
		logger.Logger.Errorf("UpdateReport err: %s", err.Error())
		return err
	}
	return nil
}
