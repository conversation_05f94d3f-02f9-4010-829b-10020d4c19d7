package service

import (
	"context"
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/mysql"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/stretchr/testify/assert"
)

func initForTest() {
	config.MustInit()
	logger.InitLogger(&config.GlobConfig.Logger)
	mysql.InitMysql(&config.GlobConfig.Mysql)
	store.InitQueryDB()
	redis.InitRedis(&config.GlobConfig.Redis)
}

func TestStopServiceConfigService_CheckNewUserRegistrationEnabled(t *testing.T) {
	initForTest()

	service := SingletonStopServiceConfigService()
	ctx := context.Background()

	// 测试不存在配置的情况（应该允许注册）
	allowed, err := service.CheckNewUserRegistrationEnabled(ctx, "non_existent_game", "minigame")
	assert.NoError(t, err)
	assert.True(t, allowed, "不存在配置时应该允许注册")

	// 注意：实际的数据库测试需要配置测试数据库和测试数据
	t.Log("停服配置检查功能测试通过")
}

func TestStopServiceConfigService_GetStopServiceConfigByGameIDAndPlatform(t *testing.T) {
	initForTest()

	service := SingletonStopServiceConfigService()
	ctx := context.Background()

	// 测试获取不存在的游戏配置
	config, err := service.GetStopServiceConfigByGameIDAndPlatform(ctx, "non_existent_game", "minigame")
	assert.Error(t, err)
	assert.Nil(t, config)

	t.Log("获取停服配置功能测试通过")
}
