package service

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"

	"github.com/go-resty/resty/v2"
)

var (
	_qrCodeOnce    sync.Once
	_qrCodeService *QRCodeService
)

type QRCodeService struct {
	// client *resty.Client
}

func SingletonQRCodeService() *QRCodeService {
	_qrCodeOnce.Do(func() {
		_qrCodeService = &QRCodeService{}
	})
	return _qrCodeService
}

// CreateQRCode
func (q *QRCodeService) CreateQRCode(ctx context.Context, accessToken string, path string, width int32) ([]byte, error) {
	// 创建resty客户端
	client := resty.New()

	// 设置接口URL
	url := fmt.Sprintf("https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token=%s", accessToken)

	// 设置请求参数
	requestBody := map[string]interface{}{
		"path": path, // 扫码进入的小程序页面路径
	}
	if width != 0 {
		requestBody["width"] = width
	}

	// 发送POST请求
	resp, err := client.R().
		SetContext(ctx).
		SetBody(requestBody).
		SetDoNotParseResponse(true). // 不自动解析响应
		Post(url)
	if err != nil {
		return nil, err
	}

	// 检查响应状态码
	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("CreateQRCode StatusCode: %d", resp.StatusCode())
	}

	// 读取响应体
	defer resp.RawBody().Close()
	body, err := io.ReadAll(resp.RawBody())
	if err != nil {
		return nil, err
	}

	return body, nil
}

// GetQRCode
func (q *QRCodeService) GetQRCode(ctx context.Context, accessToken string, req *bean.GetQRCodeReq) ([]byte, error) {
	// 创建resty客户端
	client := resty.New()

	// 设置接口URL
	url := fmt.Sprintf("https://api.weixin.qq.com/wxa/getwxacode?access_token=%s", accessToken)

	// 设置请求参数
	requestBody := map[string]interface{}{
		"path": req.Path, // 扫码进入的小程序页面路径

	}
	if req.Width != 0 {
		requestBody["width"] = req.Width
	}
	if req.EnvVersion != "" {
		requestBody["env_version"] = req.EnvVersion
	}
	if req.AutoColor != false {
		requestBody["auto_color"] = req.AutoColor
	}
	if req.LineColor != nil {
		requestBody["line_color"] = req.LineColor
	}
	if req.IsHyaline != false {
		requestBody["is_hyaline"] = req.IsHyaline
	}

	// 发送POST请求
	resp, err := client.R().
		SetContext(ctx).
		SetBody(requestBody).
		SetDoNotParseResponse(true). // 不自动解析响应
		Post(url)
	if err != nil {
		return nil, err
	}

	// 检查响应状态码
	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("GetQRCode StatusCode: %d", resp.StatusCode())
	}

	// 读取响应体
	defer resp.RawBody().Close()
	body, err := io.ReadAll(resp.RawBody())
	if err != nil {
		return nil, err
	}

	return body, nil
}

// GetUnlimitedQRCode
func (q *QRCodeService) GetUnlimitedQRCode(ctx context.Context, accessToken string, req *bean.GetUnlimitedQRCodeReq) ([]byte, error) {
	// 创建resty客户端
	client := resty.New()

	// 设置接口URL
	url := fmt.Sprintf("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=%s", accessToken)

	// 设置请求参数
	requestBody := map[string]interface{}{
		"path":  req.Path, // 扫码进入的小程序页面路径
		"scene": req.Scene,
	}
	if req.Width != 0 {
		requestBody["width"] = req.Width
	}
	if req.EnvVersion != "" {
		requestBody["env_version"] = req.EnvVersion
	}
	if req.AutoColor != false {
		requestBody["auto_color"] = req.AutoColor
	}
	if req.LineColor != nil {
		requestBody["line_color"] = req.LineColor
	}
	if req.IsHyaline != false {
		requestBody["is_hyaline"] = req.IsHyaline
	}

	// 发送POST请求
	resp, err := client.R().
		SetContext(ctx).
		SetBody(requestBody).
		SetDoNotParseResponse(true). // 不自动解析响应
		Post(url)
	if err != nil {
		return nil, err
	}

	// 检查响应状态码
	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("GetQRCode StatusCode: %d", resp.StatusCode())
	}

	// 读取响应体
	defer resp.RawBody().Close()
	body, err := io.ReadAll(resp.RawBody())
	if err != nil {
		return nil, err
	}

	return body, nil
}
