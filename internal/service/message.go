package service

import (
	"context"
	"encoding/json"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_messageOnce    sync.Once
	_messageService *MessageService
)

type MessageService struct{}

func SingletonMessageService() *MessageService {
	_messageOnce.Do(func() {
		_messageService = &MessageService{}
	})
	return _messageService
}

// IsTaskNameExist 判断任务名称是否存在
func (s *MessageService) IsTaskNameExist(ctx context.Context, gameID string, taskName string) (bool, error) {
	msg := store.QueryDB().ASubscribeMessage
	count, err := msg.WithContext(ctx).
		Where(msg.GameID.Eq(gameID)).
		Where(msg.TaskName.Eq(taskName)).
		Where(msg.IsDeleted.Zero()).
		Count()
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "IsTaskNameExist err: %s", err.Error())
		return false, err
	}
	return count > 0, nil
}

// SubMessageNotify 订阅消息
func (s *MessageService) SubMessageNotify(ctx context.Context, userID string, req *bean.SubMessageNotifyReq) (int32, error) {
	if req.Data == nil {
		return 0, constants.ErrSubscribeMsgDataIsNil
	}
	dataStr, err := json.Marshal(req.Data)
	if err != nil {
		return 0, err
	}

	msg := store.QueryDB().ASubscribeMessage
	msgInfo := &model.ASubscribeMessage{
		GameID:       req.GameID,
		UserID:       userID,
		Status:       constants.WechatSubscribeMsgWaitStatus,
		PlatformType: req.PlatformType,
		PushType:     req.PushType,
		TaskName:     req.TaskName,
		Delay:        req.Delay,
		TemplateID:   req.TemplateID,
		Page:         req.Page,
		Data:         string(dataStr),
		ErrorMsg:     "",
	}
	err = msg.WithContext(ctx).Create(msgInfo)
	if err != nil {
		return 0, err
	}
	return msgInfo.ID, nil
}

// BatchSubMessageNotify 批量订阅消息
func (s *MessageService) BatchSubMessageNotify(ctx context.Context, userID []string, req *bean.SubMessageNotifyReq) error {
	if req.Data == nil {
		return constants.ErrSubscribeMsgDataIsNil
	}
	dataStr, err := json.Marshal(req.Data)
	if err != nil {
		return err
	}

	msg := store.QueryDB().ASubscribeMessage
	messages := make([]*model.ASubscribeMessage, 0)
	for _, u := range userID {
		msgInfo := &model.ASubscribeMessage{
			TaskID:       util.UUID(),
			GameID:       req.GameID,
			UserID:       u,
			Status:       constants.WechatSubscribeMsgWaitStatus,
			PlatformType: req.PlatformType,
			PushType:     req.PushType,
			TaskName:     req.TaskName,
			Delay:        req.Delay,
			TemplateID:   req.TemplateID,
			Page:         req.Page,
			Data:         string(dataStr),
			ErrorMsg:     "",
		}
		messages = append(messages, msgInfo)
	}

	if err = msg.WithContext(ctx).CreateInBatches(messages, len(messages)); err != nil {
		return err
	}
	return nil
}

// UpdatesSubMessageNotifyStatus 更新消息通知状态
func (s *MessageService) UpdatesSubMessageNotifyStatus(ctx context.Context, userIDs []string, status int32) error {
	msg := store.QueryDB().ASubscribeMessage
	_, err := msg.WithContext(ctx).
		Where(msg.UserID.In(userIDs...)).
		UpdateSimple(msg.Status.Value(status))
	if err != nil {
		logger.Logger.Errorf("UpdatesSubMessageNotifyStatus err: %s", err.Error())
		return err
	}
	return err
}

// GetSubscribeMessageByTaskName 根据任务名称获取消息通知
func (s *MessageService) GetSubscribeMessageByTaskName(ctx context.Context, taskName string) (*model.ASubscribeMessage, error) {
	msg := store.QueryDB().ASubscribeMessage
	msgInfo, err := msg.WithContext(ctx).Where(msg.TaskName.Eq(taskName)).Where(msg.IsDeleted.Zero()).First()
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "GetSubscribeMessageByTaskName err: %s", err.Error())
		return nil, err
	}
	return msgInfo, nil
}

// UpdateMessageNotifyTaskID 更新消息通知任务ID
func (s *MessageService) UpdateMessageNotifyTaskID(ctx context.Context, id int32, taskID string) error {
	msg := store.QueryDB().ASubscribeMessage
	_, err := msg.WithContext(ctx).
		Where(msg.ID.Eq(id)).
		UpdateSimple(msg.TaskID, msg.TaskID.Value(taskID))
	if err != nil {
		logger.Logger.Errorf("UpdateMessageNotifyTaskID err: %s", err.Error())
		return err
	}
	return err
}

// UpdateMessageNotifyStatus 更新消息通知状态
func (s *MessageService) UpdateMessageNotifyStatus(ctx context.Context, id, status int32, errMsg string) error {
	updateInfo := map[string]interface{}{
		"status": status,
	}
	if errMsg != "" {
		updateInfo["error_msg"] = errMsg
	}
	msg := store.QueryDB().ASubscribeMessage
	_, err := msg.WithContext(ctx).
		Where(msg.ID.Eq(id)).
		Updates(updateInfo)
	if err != nil {
		logger.Logger.Errorf("UpdateMessageNotifyStatus err: %s", err.Error())
		return err
	}
	return err
}
