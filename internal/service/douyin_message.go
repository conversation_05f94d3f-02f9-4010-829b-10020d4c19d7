package service

import (
	"context"
	"encoding/json"
	"fmt"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/go-resty/resty/v2"
	"net/http"
	"sync"
)

var (
	douyinMessageOnce    sync.Once
	douyinMessageService *DouyinMessageService
)

type DouyinMessageService struct {
	client *resty.Client
}

func SingletonDouyinMessageService() *DouyinMessageService {
	douyinMessageOnce.Do(func() {
		douyinMessageService = &DouyinMessageService{
			client: resty.New(),
		}
	})

	return douyinMessageService
}

const subscribeMessageURL = "/mgplatform/api/apps/subscribe_notification/developer/v1/notify"

func (s *DouyinMessageService) FetchSubscribeMessage(ctx context.Context, req *bean.DouyinSubMessageReq) (string, error) {
	body, err := json.Marshal(req)
	if err != nil {
		return "", err
	}

	resp, err := s.client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(body).
		Post(config.GlobConfig.Douyin.BaseURL + subscribeMessageURL)
	if err != nil {
		return "", err
	}

	if resp.StatusCode() != http.StatusOK {
		return "", fmt.Errorf("FetchSubscribeMessage StatusCode: %d", resp.StatusCode())
	}

	result := &bean.DouyinSubMessageResp{}
	err = json.Unmarshal(resp.Body(), &result)
	if err != nil {
		return "", err
	}
	if result.ErrNo != 0 {
		return "", fmt.Errorf("FetchSubscribeMessage err_no: %d, err_tips: %s", result.ErrNo, result.ErrTips)
	}
	return "", nil
}
