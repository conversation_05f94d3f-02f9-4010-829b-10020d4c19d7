package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"gorm.io/gorm"
)

var (
	_configOnce    sync.Once
	_configService *ConfigService
)

type ConfigService struct{}

func SingletonConfigService() *ConfigService {
	_configOnce.Do(func() {
		_configService = &ConfigService{}
	})
	return _configService
}

// GetGameConfig
func (s *ConfigService) GetGameConfig(ctx context.Context, gameID string) (*bean.GetGameConfigRes, error) {
	game := store.QueryDB().MGame
	gameCtx := game.WithContext(ctx)
	if gameID != "" {
		gameCtx = gameCtx.Where(game.GameID.Eq(gameID))
	}
	gameInfo, err := gameCtx.Where(game.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Errorf("GetGameConfig: 未找到游戏的信息, gameID :%s", gameID)
		return nil, constants.ErrGameIDNotExist
	} else if err != nil {
		return nil, err
	}
	// 获取游戏配置 minigame_config
	var appID string
	minigameConf := store.QueryDB().AConfigMinigame
	minigameInfoConf, err := minigameConf.WithContext(ctx).
		Where(minigameConf.GameID.Eq(gameID)).
		Where(minigameConf.IsDeleted.Zero()).
		First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	} else if err == nil {
		appID = minigameInfoConf.AppID
	} else {
		appID = ""
	}

	return &bean.GetGameConfigRes{
		GameName:             gameInfo.Name,
		AppID:                appID,
		GravityAccessToken:   gameInfo.GravityAccessToken,
		GravityIsEnabled:     gameInfo.GravityIsEnabled,
		PlatformAppID:        gameInfo.PlatformAppID,
		QiyuWechatAppID:      gameInfo.QiyuWechatAppID,
		PayMethod:            gameInfo.PayMethod,
		TencentDataSourceID:  gameInfo.TencentDataSourceID,
		TencentEncryptionKey: gameInfo.TencentEncryptionKey,
		TencentAdCycle:       gameInfo.TencentAdCycle,
	}, nil
}

// GetSwitches 获取自定义开关
func (s *ConfigService) GetSwitches(ctx context.Context, req *bean.GetSwitchesReq) (*bean.GetSwitchesResp, error) {
	switches := store.QueryDB().MCustomSwitch
	switchesCtx := switches.WithContext(ctx)

	if req.PlatformType != "" {
		switchesCtx = switchesCtx.Where(switches.ApplicablePlatforms.Like("%" + req.PlatformType + "%"))
	}

	switchesList, err := switchesCtx.
		Where(switches.GameID.Eq(req.GameID)).
		Where(switches.SwitchID.In(req.SwitchesIDs...)).
		Where(switches.Status.Eq(1)).
		Where(switches.EffectiveTimeStart.Eq(0)).
		Where(switches.EffectiveTimeEnd.Eq(0)).
		Where(switches.IsDeleted.Zero()).Find()
	if err != nil {
		return nil, err
	}

	// 根据switchesList匹配req.SwitchesIDs, 匹配不到按nil占位
	switchMap := make(map[string]*model.MCustomSwitch)
	for _, sw := range switchesList {
		if sw.Versions != "" && !strings.Contains(sw.Versions, req.Version) {
			continue
		}
		switchMap[sw.SwitchID] = sw
	}

	// Prepare the response slice
	respSwitches := make([]interface{}, len(req.SwitchesIDs))
	for i, id := range req.SwitchesIDs {
		if sw, found := switchMap[id]; found {
			respSwitches[i] = sw.DefaultReturn
		} else {
			respSwitches[i] = 2
		}
	}

	return &bean.GetSwitchesResp{
		Switches: respSwitches,
	}, nil
}

// GetAd 获取广告位配置
func (s *ConfigService) GetAd(ctx context.Context, req *bean.GetAdReq) (*bean.GetAdResp, error) {
	// 使用Redis缓存代替内存缓存
	cacheKey := fmt.Sprintf(constants.RedisAdConfigKey, req.GameID, req.PlatformType, req.PositionID)
	// 从Redis缓存中获取数据
	cachedData, err := redis.Get(ctx, cacheKey)
	if err == nil && cachedData != "" {
		// 缓存命中，解析JSON并返回
		var resp bean.GetAdResp
		if err := json.Unmarshal([]byte(cachedData), &resp); err == nil {
			return &resp, nil
		}
		// 解析失败时忽略错误，继续查询数据库
		logger.Logger.Warnf("GetAd: Redis缓存数据解析失败, key: %s, err: %v", cacheKey, err)
	}

	// 查询广告位
	adPosition := store.QueryDB().MAdPosition
	adPositionPlatform := store.QueryDB().MAdPositionPlatform

	// 优化: 如果指定了positionID，跳过MAdPosition表查询，直接查询平台配置
	if req.PositionID != "" {
		// 直接查询平台配置
		platform, err := adPositionPlatform.WithContext(ctx).
			Where(adPositionPlatform.PositionID.Eq(req.PositionID)).
			Where(adPositionPlatform.PlatformType.Eq(req.PlatformType)).
			Where(adPositionPlatform.Status.Eq(1)).
			Where(adPositionPlatform.IsDeleted.Zero()).
			First()

		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.Errorf("GetAd: 查询广告位平台配置失败, err: %v", err)
			return nil, err
		}

		resp := &bean.GetAdResp{PlatformCode: ""}
		if err == nil {
			resp.PlatformCode = platform.PlatformCode
		}

		// 将结果存入Redis缓存，设置5分钟过期时间
		respData, _ := json.Marshal(resp)
		if redisErr := redis.Set(ctx, cacheKey, string(respData), time.Duration(constants.RedisAdConfigExpire)*time.Second); redisErr != nil {
			logger.Logger.Warnf("GetAd: 写入Redis缓存失败, key: %s, err: %v", cacheKey, redisErr)
		}

		return resp, nil
	}

	// 构建查询条件
	adPositionQuery := adPosition.WithContext(ctx).
		Where(adPosition.GameID.Eq(req.GameID)).
		Where(adPosition.Status.Eq(1)).
		Where(adPosition.IsDeleted.Zero())

	// 查询符合条件的广告位
	adPositions, err := adPositionQuery.Find()
	if err != nil {
		logger.Logger.Errorf("GetAd: 查询广告位失败, err: %v", err)
		return nil, err
	}

	if len(adPositions) == 0 {
		// 没有找到广告位，返回空结果
		resp := &bean.GetAdResp{PlatformCode: ""}

		// 将结果存入Redis缓存，设置5分钟过期时间
		respData, _ := json.Marshal(resp)
		if redisErr := redis.Set(ctx, cacheKey, string(respData), time.Duration(constants.RedisAdConfigExpire)*time.Second); redisErr != nil {
			logger.Logger.Warnf("GetAd: 写入Redis缓存失败, key: %s, err: %v", cacheKey, redisErr)
		}

		return resp, nil
	}

	// 收集所有广告位ID
	positionIDs := make([]string, 0, len(adPositions))
	for _, pos := range adPositions {
		positionIDs = append(positionIDs, pos.PositionID)
	}

	// 优化: 当只有一个positionID时，使用First()代替Find()
	var platform *model.MAdPositionPlatform
	if len(positionIDs) == 1 {
		// 使用First()直接获取第一条记录，减少数据传输
		platform, err = adPositionPlatform.WithContext(ctx).
			Where(adPositionPlatform.PositionID.Eq(positionIDs[0])).
			Where(adPositionPlatform.PlatformType.Eq(req.PlatformType)).
			Where(adPositionPlatform.Status.Eq(1)).
			Where(adPositionPlatform.IsDeleted.Zero()).
			First()

		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.Errorf("GetAd: 查询广告位平台配置失败, err: %v", err)
			return nil, err
		}

		resp := &bean.GetAdResp{PlatformCode: ""}
		if err == nil {
			resp.PlatformCode = platform.PlatformCode
		}

		// 将结果存入Redis缓存，设置5分钟过期时间
		respData, _ := json.Marshal(resp)
		if redisErr := redis.Set(ctx, cacheKey, string(respData), time.Duration(constants.RedisAdConfigExpire)*time.Second); redisErr != nil {
			logger.Logger.Warnf("GetAd: 写入Redis缓存失败, key: %s, err: %v", cacheKey, redisErr)
		}

		return resp, nil
	}

	// 对于多个positionID的情况，查询广告位平台配置
	platforms, err := adPositionPlatform.WithContext(ctx).
		Where(adPositionPlatform.PositionID.In(positionIDs...)).
		Where(adPositionPlatform.PlatformType.Eq(req.PlatformType)).
		Where(adPositionPlatform.Status.Eq(1)).
		Where(adPositionPlatform.IsDeleted.Zero()).
		Find()
	if err != nil {
		logger.Logger.Errorf("GetAd: 查询广告位平台配置失败, err: %v", err)
		return nil, err
	}

	// 转换为响应结构
	resp := &bean.GetAdResp{PlatformCode: ""}
	if len(platforms) > 0 {
		resp.PlatformCode = platforms[0].PlatformCode
	}

	// 将结果存入Redis缓存，设置5分钟过期时间
	respData, _ := json.Marshal(resp)
	if redisErr := redis.Set(ctx, cacheKey, string(respData), time.Duration(constants.RedisAdConfigExpire)*time.Second); redisErr != nil {
		logger.Logger.Warnf("GetAd: 写入Redis缓存失败, key: %s, err: %v", cacheKey, redisErr)
	}

	return resp, nil
}
