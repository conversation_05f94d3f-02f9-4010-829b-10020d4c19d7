package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/go-resty/resty/v2"
)

var (
	_sensitiveOnce    sync.Once
	_sensitiveService *SensitiveService
)

type SensitiveService struct {
	client *resty.Client
}

func SingletonSensitiveService() *SensitiveService {
	_sensitiveOnce.Do(func() {
		_sensitiveService = &SensitiveService{
			client: resty.New(),
		}
	})
	return _sensitiveService
}

const (
	wechatSecurityUrl = "/wxa/game/content_spam/msg_sec_check"
	douyinSecurityUrl = "/api/v2/tags/text/antidirt"
)

// VerifySensitiveMessage 敏感词检测
func (s *SensitiveService) VerifySensitiveMessage(ctx context.Context, openID, token, msg string) (*bean.WechatSecurityCheck, error) {
	reqBody := map[string]interface{}{
		"openid":  openID,
		"version": constants.WechatSecurityVersion,
		"scene":   constants.WechatSecurityMaterialScene,
		"content": msg,
	}

	// retryCount := 0
	resp, err := s.client.Clone().
		SetTimeout(5*time.Second).
		SetRetryCount(3).
		// AddRetryCondition(func(response *resty.Response, err error) bool {
		// 	return (response.StatusCode() >= 500 && response.StatusCode() < 600) || err != nil
		// }).
		// SetRetryAfter(func(client *resty.Client, resp *resty.Response) (time.Duration, error) {
		// 	retryCount++
		// 	if retryCount >= 3 {
		// 		return 0, nil // 让重试机制停止，但不返回错误
		// 	}
		// 	return time.Duration(retryCount) * time.Second, nil
		// }).
		R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetQueryParam("access_token", token).
		SetBody(reqBody).
		Post(config.GlobConfig.Minigame.BaseURL + wechatSecurityUrl)
	// if retryCount >= 3 {
	// 	logger.Logger.ErrorfCtx(ctx, "SensitiveService VerifySensitiveMessage retryCount >= 3 err, https code: %d", resp.StatusCode())
	// 	return nil, constants.ErrSensitiveMessageVerificationFailed
	// }
	if err != nil {
		return nil, err
	}
	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("SensitiveService GetSensitiveMessage https :%d", resp.StatusCode())
		return nil, fmt.Errorf("SensitiveService GetSensitiveMessage https :%d", resp.StatusCode())
	}
	res := &bean.WechatSecurityCheck{}
	if err := json.Unmarshal(resp.Body(), &res); err != nil {
		logger.Logger.Errorf("SensitiveService GetSensitiveMessage Unmarshal err: %s", err.Error())
		return nil, err
	}
	if res.ErrCode != 0 {
		logger.Logger.WarnfCtx(ctx, "SensitiveService GetSensitiveMessage success call, but err: %s", res.ErrMsg)
		return nil, fmt.Errorf("SensitiveService GetSensitiveMessage success call, but err: %s", res.ErrMsg)
	}
	return res, nil
}

// VerifyDouyinSensitiveMessage 抖音敏感词检测
func (s *SensitiveService) VerifyDouyinSensitiveMessage(ctx context.Context, token, msg string) (*bean.DouyinSecurityCheckDetail, error) {
	m := map[string]interface{}{
		"content": msg,
	}
	reqBody := map[string]interface{}{
		"tasks": []map[string]interface{}{
			m,
		},
	}

	resp, err := s.client.R().SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("X-Token", token).
		SetBody(reqBody).
		Post(config.GlobConfig.Douyin.ToutiaoURL + douyinSecurityUrl)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("VerifyDouyinSensitiveMessage https :%d", resp.StatusCode())
		return nil, fmt.Errorf("VerifyDouyinSensitiveMessage :%d", resp.StatusCode())
	}
	res := &bean.DouyinSecurityCheckDetail{}
	if err := json.Unmarshal(resp.Body(), &res); err != nil {
		logger.Logger.Errorf("VerifyDouyinSensitiveMessage Unmarshal err: %s", err.Error())
		return nil, err
	}

	if len(res.Data) == 0 {
		logger.Logger.Errorf("VerifyDouyinSensitiveMessage received empty data. Response Body: %s", string(resp.Body()))
		return nil, fmt.Errorf("VerifyDouyinSensitiveMessage received empty data. Response Body: %s", string(resp.Body()))
	}

	if res.Data[0].Code != 0 {
		logger.Logger.Errorf("VerifyDouyinSensitiveMessage success call, but err: %s", string(resp.Body()))
		return nil, fmt.Errorf("VerifyDouyinSensitiveMessage success call, but err: %s", string(resp.Body()))
	}

	return res, nil
}
