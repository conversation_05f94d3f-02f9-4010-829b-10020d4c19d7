package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/mysql"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestDouyinCustomerService 测试抖音客服服务
func TestDouyinCustomerService(t *testing.T) {
	service := SingletonDouyinCustomerService()
	assert.NotNil(t, service)
	assert.NotNil(t, service.client)
}

// TestBuildTextReplyRequest 测试构建文本回复请求
func TestBuildTextReplyRequest(t *testing.T) {
	service := SingletonDouyinCustomerService()

	req := service.BuildTextReplyRequest("test_game", 111, 222, "Hello World", "test_app_id")

	assert.Equal(t, "test_game", req.MicroGameID)
	assert.Equal(t, "111", req.ConversationID)
	assert.Equal(t, "222", req.MsgID)
	assert.Equal(t, "Hello World", req.Content)
	assert.Equal(t, "text", req.MsgType)
	assert.Greater(t, req.CreateTime, int64(0))
}

// TestBuildLinkReplyRequest 测试构建链接回复请求
func TestBuildLinkReplyRequest(t *testing.T) {
	service := SingletonDouyinCustomerService()

	req := service.BuildLinkReplyRequest(
		context.Background(),
		"test_game",
		111,
		222,
		"Test Title",
		"https://example.com",
		"https://example.com/thumb.jpg",
		"test_open_id",
		"",
	)

	assert.Equal(t, "", req.MicroGameID) // appID参数为空
	assert.Equal(t, "111", req.ConversationID)
	assert.Equal(t, "222", req.MsgID)
	assert.Equal(t, "link", req.MsgType)
	assert.Contains(t, req.Content, "Test Title")
	assert.Contains(t, req.Content, "https://example.com")
	assert.Greater(t, req.CreateTime, int64(0))
}

// TestGenerateUniqueMessageID 测试生成唯一消息ID
func TestGenerateUniqueMessageID(t *testing.T) {
	service := SingletonDouyinCustomerService()

	// 生成多个ID，确保它们是唯一的
	ids := make(map[string]bool)
	for i := 0; i < 100; i++ {
		id := service.GenerateUniqueMessageID()
		assert.NotEmpty(t, id)
		assert.False(t, ids[id], "Generated duplicate ID: %s", id)
		ids[id] = true
	}
}

// TestSingletonPattern 测试单例模式
func TestSingletonPattern(t *testing.T) {
	service1 := SingletonDouyinCustomerService()
	service2 := SingletonDouyinCustomerService()

	// 确保返回的是同一个实例
	assert.Same(t, service1, service2)
}

// TestGetPlayerLinkByOpenID_ParameterValidation 测试GetPlayerLinkByOpenID函数的参数验证
func TestGetPlayerLinkByOpenID_ParameterValidation(t *testing.T) {
	service := SingletonDouyinCustomerService()
	ctx := context.Background()

	// 测试空OpenID
	req := struct {
		OpenID string `json:"open_id"`
	}{
		OpenID: "",
	}

	result, err := service.GetPlayerLinkByOpenID(ctx, req)
	assert.Error(t, err, "空OpenID应该返回错误")
	assert.Empty(t, result, "空OpenID应该返回空字符串")
	assert.Contains(t, err.Error(), "OpenID不能为空", "错误信息应该包含OpenID不能为空")
}

// TestEncodeForWechatCq 测试微信cq参数编码
func TestEncodeForWechatCq(t *testing.T) {
	service := SingletonDouyinCustomerService()

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "基本参数编码",
			input:    "gameID=tt59cd2f70c6fb7dfe02&open_id=_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6&source=douyin",
			expected: "gameID%3Dtt59cd2f70c6fb7dfe02%26open_id%3D_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6%26source%3Ddouyin",
		},
		{
			name:     "包含特殊字符的参数",
			input:    "player_name=测试名称&custom_data={\"a\":\"b\"}",
			expected: "player_name%3D测试名称%26custom_data%3D{\"a\":\"b\"}",
		},
		{
			name:     "空字符串",
			input:    "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.encodeForWechatCq(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestAddParamsToURL_WechatURL 测试微信小程序URL参数添加
func TestAddParamsToURL_WechatURL(t *testing.T) {
	service := SingletonDouyinCustomerService()

	tests := []struct {
		name     string
		url      string
		gameID   string
		openID   string
		expected string
	}{
		{
			name:     "微信小程序URL无cq参数",
			url:      "https://wxaurl.cn/cE3jUUgQM2c",
			gameID:   "tt59cd2f70c6fb7dfe02",
			openID:   "_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6",
			expected: "https://wxaurl.cn/cE3jUUgQM2c?cq=gameID%3Dtt59cd2f70c6fb7dfe02%26open_id%3D_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6%26source%3Ddouyin",
		},
		{
			name:     "微信小程序URL已有其他参数",
			url:      "https://wxaurl.cn/cE3jUUgQM2c?player_id=111&player_name=测试名称",
			gameID:   "tt59cd2f70c6fb7dfe02",
			openID:   "_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6",
			expected: "https://wxaurl.cn/cE3jUUgQM2c?player_id=111&player_name=测试名称&cq=gameID%3Dtt59cd2f70c6fb7dfe02%26open_id%3D_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6%26source%3Ddouyin",
		},
		{
			name:     "普通URL",
			url:      "https://example.com",
			gameID:   "tt59cd2f70c6fb7dfe02",
			openID:   "_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6",
			expected: "https://example.com?gameID=tt59cd2f70c6fb7dfe02&open_id=_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6&source=douyin",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.addParamsToURL(tt.url, tt.gameID, tt.openID)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestAddParamsToURLWithPlayerParams 测试包含玩家参数的URL构建
func TestAddParamsToURLWithPlayerParams(t *testing.T) {
	service := SingletonDouyinCustomerService()

	tests := []struct {
		name         string
		url          string
		gameID       string
		openID       string
		playerParams string
		expected     string
	}{
		{
			name:         "微信小程序URL包含玩家参数",
			url:          "https://wxaurl.cn/zXQDM7ZnY2l",
			gameID:       "tt59cd2f70c6fb7dfe02",
			openID:       "_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6",
			playerParams: "player_id=111&player_name=%E6%B5%8B%E8%AF%95%E5%90%8D%E7%A7%B0&player_level=1&recharge_total_amount=11&custom_data=%7B%22a%22%3A%22b%22%7D&zone=1&source=douyin",
			expected:     "https://wxaurl.cn/zXQDM7ZnY2l?cq=gameID%3Dtt59cd2f70c6fb7dfe02%26open_id%3D_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6%26player_id%3D111%26player_name%3D%E6%B5%8B%E8%AF%95%E5%90%8D%E7%A7%B0%26player_level%3D1%26recharge_total_amount%3D11%26custom_data%3D%7B%22a%22%3A%22b%22%7D%26zone%3D1%26source%3Ddouyin",
		},
		{
			name:         "微信小程序URL无玩家参数",
			url:          "https://wxaurl.cn/zXQDM7ZnY2l",
			gameID:       "tt59cd2f70c6fb7dfe02",
			openID:       "_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6",
			playerParams: "",
			expected:     "https://wxaurl.cn/zXQDM7ZnY2l?cq=gameID%3Dtt59cd2f70c6fb7dfe02%26open_id%3D_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6%26source%3Ddouyin",
		},
		{
			name:         "普通URL包含玩家参数",
			url:          "https://example.com",
			gameID:       "tt59cd2f70c6fb7dfe02",
			openID:       "_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6",
			playerParams: "player_id=111&player_name=%E6%B5%8B%E8%AF%95%E5%90%8D%E7%A7%B0&source=douyin",
			expected:     "https://example.com?gameID=tt59cd2f70c6fb7dfe02&open_id=_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6&player_id=111&player_name=%E6%B5%8B%E8%AF%95%E5%90%8D%E7%A7%B0&source=douyin",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.addParamsToURLWithPlayerParams(tt.url, tt.gameID, tt.openID, tt.playerParams)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestCompleteURLBuildingFlow 测试完整的URL构建流程
func TestCompleteURLBuildingFlow(t *testing.T) {
	service := SingletonDouyinCustomerService()

	// 模拟实际场景：微信小程序URL + 玩家参数
	originalURL := "https://wxaurl.cn/cE3jUUgQM2c"
	gameID := "tt59cd2f70c6fb7dfe02"
	openID := "_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6"

	// 模拟GetPlayerLinkByOpenID返回的参数（已URL编码）
	playerParams := "player_id=111&player_name=%E6%B5%8B%E8%AF%95%E5%90%8D%E7%A7%B0&player_level=1&recharge_total_amount=11&custom_data=%7B%22a%22%3A%22b%22%7D&zone=1&source=douyin"

	result := service.addParamsToURLWithPlayerParams(originalURL, gameID, openID, playerParams)

	// 验证结果包含所有必要的参数
	assert.Contains(t, result, "https://wxaurl.cn/cE3jUUgQM2c?cq=")
	assert.Contains(t, result, "gameID%3Dtt59cd2f70c6fb7dfe02")
	assert.Contains(t, result, "open_id%3D_0001okq9sKG55wpA5CIb-H0T8AHpkQPRCY6")
	assert.Contains(t, result, "source%3Ddouyin")
	assert.Contains(t, result, "player_id%3D111")
	assert.Contains(t, result, "player_name%3D%E6%B5%8B%E8%AF%95%E5%90%8D%E7%A7%B0")
	assert.Contains(t, result, "custom_data%3D%7B%22a%22%3A%22b%22%7D")

	// 验证参数间的连接符正确编码
	assert.Contains(t, result, "%26") // & 编码为 %26
	assert.Contains(t, result, "%3D") // = 编码为 %3D

	t.Logf("完整URL构建结果: %s", result)
}

// 初始化测试环境
func initTestEnvForDouyinCustomer() {
	// 设置工作目录到项目根目录，确保能找到configs/目录
	wd, err := os.Getwd()
	if err != nil {
		panic("Failed to get working directory: " + err.Error())
	}

	if filepath.Base(wd) == "service" {
		if err := os.Chdir("../.."); err != nil {
			panic("Failed to change directory to project root: " + err.Error())
		}
	}

	config.MustInit()
	logger.InitLogger(&config.GlobConfig.Logger)
	mysql.InitMysql(&config.GlobConfig.Mysql)
	store.InitQueryDB()
	redis.InitRedis(&config.GlobConfig.Redis)
}

// TestGetPlayerLinkByOpenID_Base64Encoding 测试base64编码功能
func TestGetPlayerLinkByOpenID_Base64Encoding(t *testing.T) {
	initTestEnvForDouyinCustomer()

	service := SingletonDouyinCustomerService()
	ctx := context.Background()

	// 测试数据准备
	testCases := []struct {
		name           string
		playerData     *model.AGamePlayerDouyin
		expectedFields map[string]string // 期望的编码字段
		rawFields      map[string]string // 期望的原始字段
	}{
		{
			name: "测试中文player_name的base64编码",
			playerData: &model.AGamePlayerDouyin{
				OpenID:              "test_openid_chinese_name",
				PlayerID:            "player_123",
				PlayerName:          "测试玩家名称",
				PlayerLevel:         10,
				RechargeTotalAmount: 100,
				CustomData:          "",
				Zone:                "",
			},
			expectedFields: map[string]string{
				"player_name": base64.StdEncoding.EncodeToString([]byte("测试玩家名称")),
			},
			rawFields: map[string]string{
				"player_id":             "player_123",
				"player_level":          "10",
				"recharge_total_amount": "100",
				"source":                constants.SourceDouyin,
			},
		},
		{
			name: "测试JSON custom_data的base64编码",
			playerData: &model.AGamePlayerDouyin{
				OpenID:              "test_openid_json_data",
				PlayerID:            "player_456",
				PlayerName:          "Player456",
				PlayerLevel:         5,
				RechargeTotalAmount: 50,
				CustomData:          `{"level":5,"score":1000,"items":["sword","shield"]}`,
				Zone:                "",
			},
			expectedFields: map[string]string{
				"custom_data": base64.StdEncoding.EncodeToString([]byte(`{"level":5,"score":1000,"items":["sword","shield"]}`)),
				"player_name": base64.StdEncoding.EncodeToString([]byte("Player456")),
			},
			rawFields: map[string]string{
				"player_id":             "player_456",
				"player_level":          "5",
				"recharge_total_amount": "50",
				"source":                constants.SourceDouyin,
			},
		},
		{
			name: "测试中文zone的base64编码",
			playerData: &model.AGamePlayerDouyin{
				OpenID:              "test_openid_chinese_zone",
				PlayerID:            "player_789",
				PlayerName:          "Player789",
				PlayerLevel:         15,
				RechargeTotalAmount: 200,
				CustomData:          "",
				Zone:                "华北区域-服务器1",
			},
			expectedFields: map[string]string{
				"zone":        base64.StdEncoding.EncodeToString([]byte("华北区域-服务器1")),
				"player_name": base64.StdEncoding.EncodeToString([]byte("Player789")),
			},
			rawFields: map[string]string{
				"player_id":             "player_789",
				"player_level":          "15",
				"recharge_total_amount": "200",
				"source":                constants.SourceDouyin,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 清理测试数据
			playerDouyin := store.QueryDB().AGamePlayerDouyin
			playerDouyin.WithContext(ctx).Where(playerDouyin.OpenID.Eq(tc.playerData.OpenID)).Delete()

			// 插入测试数据
			tc.playerData.CreatedAt = time.Now().UnixMilli()
			tc.playerData.UpdatedAt = time.Now().UnixMilli()
			err := playerDouyin.WithContext(ctx).Create(tc.playerData)
			require.NoError(t, err, "插入测试数据失败")

			// 执行测试
			req := struct {
				OpenID string `json:"open_id"`
			}{
				OpenID: tc.playerData.OpenID,
			}

			result, err := service.GetPlayerLinkByOpenID(ctx, req)
			require.NoError(t, err, "GetPlayerLinkByOpenID应该成功")
			require.NotEmpty(t, result, "结果不应该为空")

			// 解析参数字符串
			params := strings.Split(result, "&")
			paramMap := make(map[string]string)
			for _, param := range params {
				parts := strings.SplitN(param, "=", 2)
				if len(parts) == 2 {
					paramMap[parts[0]] = parts[1]
				}
			}

			// 验证base64编码的字段
			for fieldName, expectedEncoded := range tc.expectedFields {
				actualValue, exists := paramMap[fieldName]
				require.True(t, exists, "字段 %s 应该存在", fieldName)
				assert.Equal(t, expectedEncoded, actualValue, "字段 %s 的base64编码不正确", fieldName)

				// 验证可以正确解码
				decoded, err := base64.StdEncoding.DecodeString(actualValue)
				require.NoError(t, err, "字段 %s 的base64解码应该成功", fieldName)

				// 根据字段类型验证解码结果
				switch fieldName {
				case "player_name":
					assert.Equal(t, tc.playerData.PlayerName, string(decoded), "player_name解码后应该等于原始值")
				case "custom_data":
					assert.Equal(t, tc.playerData.CustomData, string(decoded), "custom_data解码后应该等于原始值")
					// 验证解码后的JSON是有效的
					var jsonData interface{}
					err := json.Unmarshal(decoded, &jsonData)
					assert.NoError(t, err, "custom_data解码后应该是有效的JSON")
				case "zone":
					assert.Equal(t, tc.playerData.Zone, string(decoded), "zone解码后应该等于原始值")
				}
			}

			// 验证不需要编码的字段
			for fieldName, expectedValue := range tc.rawFields {
				actualValue, exists := paramMap[fieldName]
				require.True(t, exists, "字段 %s 应该存在", fieldName)
				assert.Equal(t, expectedValue, actualValue, "字段 %s 应该是原始值，不需要编码", fieldName)
			}

			// 清理测试数据
			playerDouyin.WithContext(ctx).Where(playerDouyin.OpenID.Eq(tc.playerData.OpenID)).Delete()
		})
	}
}

// TestGetPlayerLinkByOpenID_EdgeCases 测试边界情况
func TestGetPlayerLinkByOpenID_EdgeCases(t *testing.T) {
	initTestEnvForDouyinCustomer()

	service := SingletonDouyinCustomerService()
	ctx := context.Background()

	t.Run("测试空OpenID", func(t *testing.T) {
		req := struct {
			OpenID string `json:"open_id"`
		}{
			OpenID: "",
		}

		result, err := service.GetPlayerLinkByOpenID(ctx, req)
		assert.Error(t, err, "空OpenID应该返回错误")
		assert.Empty(t, result, "空OpenID应该返回空字符串")
		assert.Contains(t, err.Error(), "OpenID不能为空", "错误信息应该包含OpenID不能为空")
	})

	t.Run("测试不存在的OpenID", func(t *testing.T) {
		req := struct {
			OpenID string `json:"open_id"`
		}{
			OpenID: "non_existent_openid_12345",
		}

		result, err := service.GetPlayerLinkByOpenID(ctx, req)
		assert.Error(t, err, "不存在的OpenID应该返回错误")
		assert.Empty(t, result, "不存在的OpenID应该返回空字符串")
		assert.Contains(t, err.Error(), "未找到玩家信息", "错误信息应该包含未找到玩家信息")
	})

	t.Run("测试空字符串字段处理", func(t *testing.T) {
		// 准备测试数据：所有可选字段都为空
		testPlayer := &model.AGamePlayerDouyin{
			OpenID:              "test_empty_fields",
			PlayerID:            "", // 空字符串
			PlayerName:          "", // 空字符串
			PlayerLevel:         0,  // 零值
			RechargeTotalAmount: 0,  // 零值
			CustomData:          "", // 空字符串
			Zone:                "", // 空字符串
		}

		// 清理并插入测试数据
		playerDouyin := store.QueryDB().AGamePlayerDouyin
		playerDouyin.WithContext(ctx).Where(playerDouyin.OpenID.Eq(testPlayer.OpenID)).Delete()

		testPlayer.CreatedAt = time.Now().UnixMilli()
		testPlayer.UpdatedAt = time.Now().UnixMilli()
		err := playerDouyin.WithContext(ctx).Create(testPlayer)
		require.NoError(t, err, "插入测试数据失败")

		// 执行测试
		req := struct {
			OpenID string `json:"open_id"`
		}{
			OpenID: testPlayer.OpenID,
		}

		result, err := service.GetPlayerLinkByOpenID(ctx, req)
		require.NoError(t, err, "空字段处理应该成功")
		require.NotEmpty(t, result, "结果不应该为空")

		// 验证结果只包含source参数（其他字段都为空，不应该包含）
		assert.Contains(t, result, "source="+constants.SourceDouyin, "应该包含source参数")
		assert.NotContains(t, result, "player_id=", "空的player_id不应该包含")
		assert.NotContains(t, result, "player_name=", "空的player_name不应该包含")
		assert.NotContains(t, result, "player_level=0", "零值的player_level不应该包含")
		assert.NotContains(t, result, "recharge_total_amount=0", "零值的recharge_total_amount不应该包含")
		assert.NotContains(t, result, "custom_data=", "空的custom_data不应该包含")
		assert.NotContains(t, result, "zone=", "空的zone不应该包含")

		// 清理测试数据
		playerDouyin.WithContext(ctx).Where(playerDouyin.OpenID.Eq(testPlayer.OpenID)).Delete()
	})

	t.Run("测试特殊字符处理", func(t *testing.T) {
		// 准备包含特殊字符的测试数据
		specialChars := map[string]string{
			"emoji":            "🎮玩家👾",
			"special_symbols":  "玩家@#$%^&*()_+-=[]{}|;':\",./<>?",
			"mixed_languages":  "Player玩家プレイヤー플레이어",
			"json_with_quotes": `{"name":"测试\"引号\"","value":"特殊'字符'"}`,
		}

		for testName, testValue := range specialChars {
			t.Run("特殊字符_"+testName, func(t *testing.T) {
				testPlayer := &model.AGamePlayerDouyin{
					OpenID:              "test_special_" + testName,
					PlayerID:            "player_special",
					PlayerName:          testValue,
					PlayerLevel:         1,
					RechargeTotalAmount: 1,
					CustomData:          testValue,
					Zone:                testValue,
				}

				// 清理并插入测试数据
				playerDouyin := store.QueryDB().AGamePlayerDouyin
				playerDouyin.WithContext(ctx).Where(playerDouyin.OpenID.Eq(testPlayer.OpenID)).Delete()

				testPlayer.CreatedAt = time.Now().UnixMilli()
				testPlayer.UpdatedAt = time.Now().UnixMilli()
				err := playerDouyin.WithContext(ctx).Create(testPlayer)
				require.NoError(t, err, "插入特殊字符测试数据失败")

				// 执行测试
				req := struct {
					OpenID string `json:"open_id"`
				}{
					OpenID: testPlayer.OpenID,
				}

				result, err := service.GetPlayerLinkByOpenID(ctx, req)
				require.NoError(t, err, "特殊字符处理应该成功")
				require.NotEmpty(t, result, "结果不应该为空")

				// 解析参数
				params := strings.Split(result, "&")
				paramMap := make(map[string]string)
				for _, param := range params {
					parts := strings.SplitN(param, "=", 2)
					if len(parts) == 2 {
						paramMap[parts[0]] = parts[1]
					}
				}

				// 验证base64编码的字段能正确解码
				encodedFields := []string{"player_name", "custom_data", "zone"}
				for _, fieldName := range encodedFields {
					if encodedValue, exists := paramMap[fieldName]; exists {
						decoded, err := base64.StdEncoding.DecodeString(encodedValue)
						require.NoError(t, err, "字段 %s 的base64解码应该成功", fieldName)
						assert.Equal(t, testValue, string(decoded), "字段 %s 解码后应该等于原始值", fieldName)
					}
				}

				// 清理测试数据
				playerDouyin.WithContext(ctx).Where(playerDouyin.OpenID.Eq(testPlayer.OpenID)).Delete()
			})
		}
	})
}

// TestGetPlayerLinkByOpenID_DatabaseIntegration 测试数据库集成
func TestGetPlayerLinkByOpenID_DatabaseIntegration(t *testing.T) {
	initTestEnvForDouyinCustomer()

	service := SingletonDouyinCustomerService()
	ctx := context.Background()

	t.Run("测试真实数据库数据", func(t *testing.T) {
		// 查询数据库中的真实数据进行测试
		playerDouyin := store.QueryDB().AGamePlayerDouyin
		players, err := playerDouyin.WithContext(ctx).Limit(3).Find()
		require.NoError(t, err, "查询数据库数据失败")

		if len(players) == 0 {
			t.Skip("数据库中没有测试数据，跳过真实数据测试")
			return
		}

		for i, player := range players {
			t.Run(fmt.Sprintf("真实数据_%d", i+1), func(t *testing.T) {
				req := struct {
					OpenID string `json:"open_id"`
				}{
					OpenID: player.OpenID,
				}

				result, err := service.GetPlayerLinkByOpenID(ctx, req)
				require.NoError(t, err, "处理真实数据应该成功")
				require.NotEmpty(t, result, "结果不应该为空")

				// 验证结果格式
				assert.Contains(t, result, "source="+constants.SourceDouyin, "应该包含source参数")

				// 解析参数验证编码
				params := strings.Split(result, "&")
				paramMap := make(map[string]string)
				for _, param := range params {
					parts := strings.SplitN(param, "=", 2)
					if len(parts) == 2 {
						paramMap[parts[0]] = parts[1]
					}
				}

				// 验证需要base64编码的字段
				if player.PlayerName != "" {
					encodedName, exists := paramMap["player_name"]
					require.True(t, exists, "player_name字段应该存在")
					decoded, err := base64.StdEncoding.DecodeString(encodedName)
					require.NoError(t, err, "player_name应该能正确解码")
					assert.Equal(t, player.PlayerName, string(decoded), "player_name解码后应该等于数据库值")
				}

				if player.CustomData != "" {
					encodedCustom, exists := paramMap["custom_data"]
					require.True(t, exists, "custom_data字段应该存在")
					decoded, err := base64.StdEncoding.DecodeString(encodedCustom)
					require.NoError(t, err, "custom_data应该能正确解码")
					assert.Equal(t, player.CustomData, string(decoded), "custom_data解码后应该等于数据库值")

					// 验证JSON有效性
					var jsonData interface{}
					err = json.Unmarshal(decoded, &jsonData)
					assert.NoError(t, err, "custom_data解码后应该是有效的JSON")
				}

				if player.Zone != "" {
					encodedZone, exists := paramMap["zone"]
					require.True(t, exists, "zone字段应该存在")
					decoded, err := base64.StdEncoding.DecodeString(encodedZone)
					require.NoError(t, err, "zone应该能正确解码")
					assert.Equal(t, player.Zone, string(decoded), "zone解码后应该等于数据库值")
				}

				t.Logf("真实数据测试结果: OpenID=%s, 参数=%s", player.OpenID, result)
			})
		}
	})
}

// TestGetPlayerLinkByOpenID_WechatAPIIntegration 测试与微信API的集成
func TestGetPlayerLinkByOpenID_WechatAPIIntegration(t *testing.T) {
	initTestEnvForDouyinCustomer()

	service := SingletonDouyinCustomerService()
	ctx := context.Background()

	t.Run("测试base64编码解决微信API限制", func(t *testing.T) {
		// 准备包含微信API不支持字符的测试数据
		testCases := []struct {
			name        string
			playerData  *model.AGamePlayerDouyin
			description string
		}{
			{
				name: "JSON字符串测试",
				playerData: &model.AGamePlayerDouyin{
					OpenID:              "test_wechat_json",
					PlayerID:            "player_json",
					PlayerName:          "JsonPlayer",
					PlayerLevel:         1,
					RechargeTotalAmount: 1,
					CustomData:          `{"user":"测试用户","items":["sword","shield"],"stats":{"hp":100,"mp":50}}`,
					Zone:                "zone1",
				},
				description: "JSON字符串包含双引号和花括号，微信API无法直接处理",
			},
			{
				name: "中文字符测试",
				playerData: &model.AGamePlayerDouyin{
					OpenID:              "test_wechat_chinese",
					PlayerID:            "player_chinese",
					PlayerName:          "超级玩家🎮",
					PlayerLevel:         1,
					RechargeTotalAmount: 1,
					CustomData:          `{"name":"中文名称","description":"这是一个包含中文的描述"}`,
					Zone:                "华北区域-服务器1",
				},
				description: "中文字符和emoji在URL传输中可能出现编码问题",
			},
			{
				name: "特殊符号测试",
				playerData: &model.AGamePlayerDouyin{
					OpenID:              "test_wechat_symbols",
					PlayerID:            "player_symbols",
					PlayerName:          "Player@#$%^&*()",
					PlayerLevel:         1,
					RechargeTotalAmount: 1,
					CustomData:          `{"symbols":"!@#$%^&*()_+-=[]{}|;':\",./<>?","encoded":"test"}`,
					Zone:                "Zone!@#$%",
				},
				description: "特殊符号可能在URL参数中造成解析错误",
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				// 清理并插入测试数据
				playerDouyin := store.QueryDB().AGamePlayerDouyin
				playerDouyin.WithContext(ctx).Where(playerDouyin.OpenID.Eq(tc.playerData.OpenID)).Delete()

				tc.playerData.CreatedAt = time.Now().UnixMilli()
				tc.playerData.UpdatedAt = time.Now().UnixMilli()
				err := playerDouyin.WithContext(ctx).Create(tc.playerData)
				require.NoError(t, err, "插入测试数据失败")

				// 1. 调用GetPlayerLinkByOpenID获取编码后的参数
				req := struct {
					OpenID string `json:"open_id"`
				}{
					OpenID: tc.playerData.OpenID,
				}

				playerParams, err := service.GetPlayerLinkByOpenID(ctx, req)
				require.NoError(t, err, "GetPlayerLinkByOpenID应该成功")
				require.NotEmpty(t, playerParams, "玩家参数不应该为空")

				t.Logf("测试用例: %s", tc.description)
				t.Logf("生成的玩家参数: %s", playerParams)

				// 2. 验证参数可以安全传递给微信API
				// 模拟构建完整的query参数（类似logic/customer.go中的逻辑）
				baseParams := []string{
					"game_id=test_game",
					"open_id=" + tc.playerData.OpenID,
				}
				queryParams := strings.Join(baseParams, "&") + "&" + playerParams

				// 3. 验证query参数格式正确
				assert.NotContains(t, queryParams, `"`, "query参数不应该包含未转义的双引号")
				assert.NotContains(t, queryParams, `{`, "query参数不应该包含未转义的花括号")
				assert.NotContains(t, queryParams, `}`, "query参数不应该包含未转义的花括号")

				// 4. 验证可以通过微信API的JSON转义处理
				// 手动转义JSON字符串中的特殊字符
				escapedQuery := strings.ReplaceAll(queryParams, `\`, `\\`)
				escapedQuery = strings.ReplaceAll(escapedQuery, `"`, `\"`)

				// 验证转义后的字符串是有效的JSON值
				testJSON := fmt.Sprintf(`{"query":"%s"}`, escapedQuery)
				var testData map[string]interface{}
				err = json.Unmarshal([]byte(testJSON), &testData)
				assert.NoError(t, err, "转义后的参数应该能构成有效的JSON")

				// 5. 验证base64编码的字段可以正确解码
				params := strings.Split(playerParams, "&")
				paramMap := make(map[string]string)
				for _, param := range params {
					parts := strings.SplitN(param, "=", 2)
					if len(parts) == 2 {
						paramMap[parts[0]] = parts[1]
					}
				}

				// 验证编码字段的解码
				encodedFields := map[string]string{
					"player_name": tc.playerData.PlayerName,
					"custom_data": tc.playerData.CustomData,
					"zone":        tc.playerData.Zone,
				}

				for fieldName, originalValue := range encodedFields {
					if originalValue != "" {
						encodedValue, exists := paramMap[fieldName]
						require.True(t, exists, "字段 %s 应该存在", fieldName)

						decoded, err := base64.StdEncoding.DecodeString(encodedValue)
						require.NoError(t, err, "字段 %s 应该能正确解码", fieldName)
						assert.Equal(t, originalValue, string(decoded), "字段 %s 解码后应该等于原始值", fieldName)
					}
				}

				t.Logf("完整query参数: %s", queryParams)
				t.Logf("转义后参数: %s", escapedQuery)

				// 清理测试数据
				playerDouyin.WithContext(ctx).Where(playerDouyin.OpenID.Eq(tc.playerData.OpenID)).Delete()
			})
		}
	})

	t.Run("测试编码必要性验证", func(t *testing.T) {
		// 验证为什么需要base64编码：直接传递原始数据会导致问题

		// 创建包含问题字符的测试数据
		problematicData := map[string]string{
			"json_with_quotes":   `{"name":"test","value":"data"}`,
			"chinese_characters": "测试中文字符",
			"special_symbols":    `!@#$%^&*()_+-=[]{}|;':",./<>?`,
		}

		for testName, testValue := range problematicData {
			t.Run("验证编码必要性_"+testName, func(t *testing.T) {
				// 1. 测试不编码的情况（模拟问题）
				rawParams := fmt.Sprintf("test_field=%s&other=value", testValue)

				// 尝试构建JSON（模拟微信API请求体构建）
				// 手动转义JSON字符串
				escapedRaw := strings.ReplaceAll(rawParams, `\`, `\\`)
				escapedRaw = strings.ReplaceAll(escapedRaw, `"`, `\"`)
				testJSONRaw := fmt.Sprintf(`{"query":"%s"}`, escapedRaw)

				// 2. 测试base64编码的情况
				encodedValue := base64.StdEncoding.EncodeToString([]byte(testValue))
				encodedParams := fmt.Sprintf("test_field=%s&other=value", encodedValue)
				escapedEncoded := strings.ReplaceAll(encodedParams, `\`, `\\`)
				escapedEncoded = strings.ReplaceAll(escapedEncoded, `"`, `\"`)
				testJSONEncoded := fmt.Sprintf(`{"query":"%s"}`, escapedEncoded)

				// 3. 验证编码后的JSON更安全
				var rawData, encodedData map[string]interface{}

				rawErr := json.Unmarshal([]byte(testJSONRaw), &rawData)
				encodedErr := json.Unmarshal([]byte(testJSONEncoded), &encodedData)

				// base64编码的版本应该总是能成功解析
				assert.NoError(t, encodedErr, "base64编码后的JSON应该总是有效")

				// 验证编码后的数据可以正确解码回原始值
				if encodedErr == nil {
					queryValue := encodedData["query"].(string)
					// 从query中提取编码的字段值
					if strings.Contains(queryValue, "test_field=") {
						parts := strings.Split(queryValue, "&")
						for _, part := range parts {
							if strings.HasPrefix(part, "test_field=") {
								encodedFieldValue := strings.TrimPrefix(part, "test_field=")
								decoded, err := base64.StdEncoding.DecodeString(encodedFieldValue)
								assert.NoError(t, err, "应该能正确解码")
								assert.Equal(t, testValue, string(decoded), "解码后应该等于原始值")
								break
							}
						}
					}
				}

				t.Logf("测试数据: %s", testValue)
				t.Logf("原始JSON: %s (解析结果: %v)", testJSONRaw, rawErr)
				t.Logf("编码JSON: %s (解析结果: %v)", testJSONEncoded, encodedErr)
			})
		}
	})
}
