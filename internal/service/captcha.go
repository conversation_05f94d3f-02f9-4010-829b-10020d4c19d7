package service

import (
	"context"
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"sort"
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	captcha "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/captcha/v20190722"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	yiduncaptcha "github.com/yidun/yidun-golang-sdk/yidun/service/captcha"
)

var (
	_captchaOnce    sync.Once
	_captchaService *CaptchaService
)

type CaptchaService struct{}

func SingletonCaptchaService() *CaptchaService {
	_captchaOnce.Do(func() {
		_captchaService = &CaptchaService{}
	})
	return _captchaService
}

// VerifyNeteaseCaptcha 验证网易易盾验证码
func (s *CaptchaService) VerifyNeteaseCaptcha(ctx context.Context, gameID, validate, extraData string, timestamp int64) (bool, error) {
	// 从数据库中获取验证码配置
	captchaConfig := store.QueryDB().MCaptchaConfig
	neteaseConfig, err := captchaConfig.WithContext(ctx).Where(
		captchaConfig.GameID.Eq(gameID),
		captchaConfig.IsDeleted.Is(false),
	).First()
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "get netease captcha config failed: %v", err)
		return false, err
	}

	// 使用SDK创建验证请求
	request := yiduncaptcha.NewCaptchaVerifyRequest()
	request.SetCaptchaId(neteaseConfig.CaptchaID).
		SetValidate(validate).
		SetUser("") // 可选参数，如果不需要可以设置为空字符串
	// 创建验证码客户端
	captchaClient := yiduncaptcha.NewCaptchaVerifyClientWithAccessKey(
		config.GlobConfig.NetEaseYidun.SecretID,
		config.GlobConfig.NetEaseYidun.SecretKey,
	)

	// 发送验证请求
	response, err := captchaClient.Verify(request)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "verify netease captcha failed: %v", err)
		return false, fmt.Errorf("verify netease captcha failed: %v", err)
	}

	// 检查验证结果
	if response.Error == nil {
		logger.Logger.ErrorfCtx(ctx, "verify netease captcha failed: response.Error is nil")
		return false, fmt.Errorf("verify netease captcha failed: response.Error is nil")
	}

	if *response.Error != 0 {
		logger.Logger.ErrorfCtx(ctx, "verify netease captcha failed with error code: %v, msg: %v",
			*response.Error, *response.Msg)
		return false, fmt.Errorf("verify netease captcha failed with error code: %v, msg: %v",
			*response.Error, *response.Msg)
	}

	// 如果extraData不为空，需要验证是否匹配
	if response.ExtraData != nil && *response.ExtraData != "" {
		// 获取游戏配置以获取secret
		game := store.QueryDB().MGame
		gameInfo, err := game.WithContext(ctx).Where(
			game.GameID.Eq(gameID),
			game.IsDeleted.Is(false),
		).First()
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "get game info failed: %v", err)
			return false, err
		}

		// 生成加密后的extraData
		signStr := fmt.Sprintf("%s%s%s%d", extraData, gameID, gameInfo.Secret, timestamp)
		h := md5.New()
		h.Write([]byte(signStr))
		encryptedExtraData := hex.EncodeToString(h.Sum(nil))

		if *response.ExtraData != encryptedExtraData {
			logger.Logger.ErrorfCtx(ctx, "extraData not match, expected: %s, got: %s", encryptedExtraData, *response.ExtraData)
			return false, nil
		}
	}

	return *response.Result, nil
}

// VerifyTencentCaptcha
func (s *CaptchaService) VerifyTencentCaptcha(ctx context.Context, gameID string, ticket string, userIP string) (bool, error) {
	// 从数据库中获取验证码配置
	captchaConfig := store.QueryDB().MCaptchaConfig
	capthaConfig, err := captchaConfig.WithContext(ctx).Where(
		captchaConfig.GameID.Eq(gameID),
		captchaConfig.IsDeleted.Is(false),
	).First()
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "get captcha config failed: %v", err)
		return false, err
	}

	// 实例化认证对象
	credential := common.NewCredential(
		config.GlobConfig.TencentCloud.CaptchaSecretID,
		config.GlobConfig.TencentCloud.CaptchaSecretKey,
	)

	// 实例化client选项
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "captcha.tencentcloudapi.com"

	// 实例化验证码服务客户端
	client, err := captcha.NewClient(credential, "", cpf)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "create tencent captcha client failed: %v", err)
		return false, err
	}

	// 创建验证请求
	request := captcha.NewDescribeCaptchaMiniResultRequest()
	request.CaptchaType = common.Uint64Ptr(9) // 验证码类型，9为验证码
	request.Ticket = common.StringPtr(ticket)
	request.UserIp = common.StringPtr(userIP)                                  // 使用传入的用户IP
	request.CaptchaAppId = common.Uint64Ptr(uint64(capthaConfig.CaptchaAppID)) // 从数据库配置中获取
	request.AppSecretKey = common.StringPtr(capthaConfig.AppSecretKey)         // 从数据库配置中获取

	// 发送验证请求
	response, err := client.DescribeCaptchaMiniResult(request)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "verify tencent captcha failed: %v", err)
		return false, err
	}

	logger.Logger.InfofCtx(ctx, "verify tencent captcha response: %s", response.ToJsonString())

	// 根据返回结果判断验证是否通过
	return response.Response.CaptchaCode != nil && *response.Response.CaptchaCode == 1, nil
}

/**
 * generateNeteaseSignature
 * @param params
 * @param secretKey
 * @return string
 */
func (s *CaptchaService) generateNeteaseSignature(params map[string]interface{}) string {
	// 1.
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 2.
	var builder strings.Builder
	for _, k := range keys {
		builder.WriteString(k)
		builder.WriteString(fmt.Sprintf("%v", params[k]))
	}

	// 3.
	builder.WriteString(config.GlobConfig.NetEaseYidun.SecretKey)

	// 4. SHA256
	hash := sha256.New()
	hash.Write([]byte(builder.String()))
	return hex.EncodeToString(hash.Sum(nil))
}
