package service

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strconv"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"

	"gorm.io/gorm"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/go-resty/resty/v2"
)

var (
	_customerOnce    sync.Once
	_customerService *CustomerService
)

type CustomerService struct{}

func SingletonCustomerService() *CustomerService {
	_customerOnce.Do(func() {
		_customerService = &CustomerService{}
	})
	return _customerService
}

//// PushMinigameMessage 推送小游戏消息到微信
//func (c *CustomerService) PushMinigameMessage(ctx context.Context, req *bean.PushMinigameMessageReq) (*bean.PushMinigameMessageResp, error) {
//	return nil, nil
//}

// GetCustomerServiceMsg 获取客服消息
func (c *CustomerService) GetCustomerServiceMsg(ctx context.Context, gameID, platformType string) ([]*model.MCustomerServiceMessage, error) {
	message := store.QueryDB().MCustomerServiceMessage
	messageCtx := message.WithContext(ctx)
	if platformType != "" {
		messageCtx = messageCtx.Where(message.PlatformType.Eq(platformType))
	}
	messages, err := messageCtx.Where(message.GameID.Eq(gameID), message.IsDeleted.Zero()).Find()
	if err != nil {
		return nil, err
	}
	return messages, nil
}

// CallbackMsgToServer 回调
func (c *CustomerService) CallbackMsgToServer(ctx context.Context, gameID, url string, body interface{},
) error {
	content, err := json.Marshal(body)
	if err != nil {
		return err
	}
	logger.Logger.Infof("CustomerService CallbackMsgToServer gameid: %s, content: %s", gameID, string(content))

	// timestamp to string
	timestamp := time.Now().Unix()
	timestampStr := strconv.FormatInt(timestamp, 10)

	secretKey := fmt.Sprintf(constants.SystemSecret, gameID)
	secretVal, err := redis.Get(ctx, secretKey)
	if err != nil {
		logger.Logger.Errorf("CustomerService CallbackMsgToServer: redis get secret, game id: %s, err: %s", gameID, err.Error())
		return err
	}
	sign := middleware.GenSign(gameID, secretVal, timestampStr, content)

	resp, err := resty.New().
		R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json"). // 添加sign加密信息
		SetHeader("game_id", gameID).
		SetHeader("timestamp", timestampStr).
		SetHeader("sign", sign).
		SetBody(body).
		Post(url)
	if err != nil {
		logger.Logger.Errorf("CallbackMsgToServer: 调用失败, 请检查接口 err: %s", err.Error())
		return err
	}
	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("CallbackMsgToServer: 获取状态码异常，状态码: %d", resp.StatusCode())
		return fmt.Errorf("CallbackMsgToServer: 获取状态码异常，状态码: %d", resp.StatusCode())
	}
	var result bean.CallbackMsgResp
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		logger.Logger.Errorf("CallbackMsgToServer: Unmarshal err: %s", err.Error())
		return err
	}
	if result.Code != 0 {
		logger.Logger.Errorf("CallbackMsgToServer: 成功调用，但是产品方返回Code码异常 code: %d", result.Code)
		return fmt.Errorf("CallbackMsgToServer: 调用失败 code: %+v", result)
	}
	return nil
}

// FetchMediaID 获取媒体id
func (c *CustomerService) FetchMediaID(ctx context.Context, accessToken, gameID, url string) (string, error) {
	medium := store.QueryDB().MCustomerServiceMedium
	mediumInfo, err := medium.WithContext(ctx).
		Where(medium.GameID.Eq(gameID)).
		Where(medium.URL.Eq(url)).
		Where(medium.ExpiredAt.Gte(time.Now().Unix())).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", err
	}
	if mediumInfo == nil {
		logger.Logger.Debugf("FetchMediaID: 未找到媒体信息")

		mediaInfo, err := c.UploadPicture(ctx, accessToken, url)
		if err != nil {
			return "", err
		}

		mediumInfo = &model.MCustomerServiceMedium{
			GameID:    gameID,
			MediaID:   mediaInfo.MediaID,
			URL:       url,
			FileType:  mediaInfo.Type,
			CreatedAt: mediaInfo.CreatedAt,
			ExpiredAt: mediaInfo.CreatedAt + 60*60*24*2, // 2天缓存有效期
		}
		if err := medium.WithContext(ctx).Create(mediumInfo); err != nil {
			return "", err
		}
		return mediaInfo.MediaID, nil
	}
	return mediumInfo.MediaID, nil
}

// UploadPicture 上传图片
const (
	mediaUploadUrl = "/cgi-bin/media/upload"
)

func (c *CustomerService) UploadPicture(ctx context.Context, accessToken string, url string) (*bean.MediumInfo, error) {
	picResp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("UploadPicture failed to open image file: %w", err)
	}
	defer picResp.Body.Close()

	imageData, err := io.ReadAll(picResp.Body)
	if err != nil {
		return nil, fmt.Errorf("UploadPicture failed to read image data: %w", err)
	}

	// 模拟文件上传
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	// 创建 form-data 中的 "media" 字段
	part, err := writer.CreateFormFile("media", filepath.Base(url))
	if err != nil {
		return nil, fmt.Errorf("UploadPicture failed to create form file: %w", err)
	}

	// 将图片数据写入 "media" 字段
	_, err = part.Write(imageData)
	if err != nil {
		return nil, fmt.Errorf("UploadPicture failed to write image data: %w", err)
	}

	// 添加 "type" 字段
	err = writer.WriteField("type", "image")
	if err != nil {
		return nil, fmt.Errorf("UploadPicture failed to write field: %w", err)
	}
	writer.Close()

	resp, err := resty.New().R().
		SetContext(ctx).
		SetQueryParam("access_token", accessToken).
		SetHeader("Content-Type", writer.FormDataContentType()). // Important!
		SetBody(body.Bytes()).
		Post(config.GlobConfig.Minigame.BaseURL + mediaUploadUrl)
	if err != nil {
		return nil, fmt.Errorf("UploadPicture failed to upload image: %w", err)
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("UploadPicture upload request failed: %s", resp.Status())
	}

	var mediumInfo *bean.MediumInfo
	err = json.Unmarshal(resp.Body(), &mediumInfo)
	if err != nil {
		return nil, fmt.Errorf("UploadPicture failed to parse upload response: %w", err)
	}
	return mediumInfo, nil
}

// GetCustomerServiceMessages 获取客服消息配置（支持场景筛选）
func (c *CustomerService) GetCustomerServiceMessages(ctx context.Context, gameID, platformType string, scenes int32) ([]*model.MCustomerServiceMessage, error) {
	message := store.QueryDB().MCustomerServiceMessage
	messageCtx := message.WithContext(ctx).
		Where(message.GameID.Eq(gameID)).
		Where(message.IsDeleted.Zero())
	
	if platformType != "" {
		messageCtx = messageCtx.Where(message.PlatformType.Eq(platformType))
	}
	
	if scenes > 0 {
		messageCtx = messageCtx.Where(message.Scenes.Eq(scenes))
	}
	
	messages, err := messageCtx.Find()
	if err != nil {
		return nil, err
	}
	return messages, nil
}
