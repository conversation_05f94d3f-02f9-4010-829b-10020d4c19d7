package service

import (
	"context"
	"errors"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_dateOnce    sync.Once
	_dateService *DateService
)

type DateService struct{}

func SingletonDateService() *DateService {
	_dateOnce.Do(func() {
		_dateService = &DateService{}
	})
	return _dateService
}

// IsPlayableDate 判断指定日期是否为可玩日期
// 逻辑：
// 1. 首先查询数据库表中是否存在当天日期的记录
// 2. 如果查询到匹配的日期记录，返回 true（表示当天是可玩日期）
// 3. 如果数据库中没有找到当天日期的记录，则检查当天是否为周五、周六或周日
// 4. 如果当天是周五、周六或周日，返回 true
// 5. 如果既没有在数据库中找到记录，当天也不是周五、周六、周日，则返回 false
func (s *DateService) IsPlayableDate(ctx context.Context, date time.Time) (bool, error) {
	// 将时间转换为北京时区的日期（只保留年月日）
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "DateService IsPlayableDate: 加载北京时区失败, err: %v", err)
		return false, err
	}

	// 转换为北京时区并只保留日期部分
	beijingDate := date.In(beijingLocation)
	dateOnly := time.Date(beijingDate.Year(), beijingDate.Month(), beijingDate.Day(), 0, 0, 0, 0, beijingLocation)

	logger.Logger.InfofCtx(ctx, "DateService IsPlayableDate: 检查日期 %s 是否为可玩日期", dateOnly.Format("2006-01-02"))

	// 1. 首先查询数据库表中是否存在当天日期的记录
	h5PlayableDate := store.QueryDB().H5PlayableDate
	count, err := h5PlayableDate.WithContext(ctx).
		Where(h5PlayableDate.PlayableDate.Eq(dateOnly)).
		Where(h5PlayableDate.IsDeleted.Is(false)).
		Count()

	if err != nil {
		// 区分context相关错误和真正的系统错误
		if errors.Is(err, context.Canceled) {
			logger.Logger.InfofCtx(ctx, "DateService IsPlayableDate: 查询可玩日期被客户端取消, date: %s", dateOnly.Format("2006-01-02"))
		} else if errors.Is(err, context.DeadlineExceeded) {
			logger.Logger.WarnfCtx(ctx, "DateService IsPlayableDate: 查询可玩日期超时, date: %s", dateOnly.Format("2006-01-02"))
		} else {
			logger.Logger.ErrorfCtx(ctx, "DateService IsPlayableDate: 查询数据库失败, date: %s, err: %v", dateOnly.Format("2006-01-02"), err)
		}
		return false, err
	}

	// 2. 如果查询到匹配的日期记录，返回 true
	if count > 0 {
		logger.Logger.InfofCtx(ctx, "DateService IsPlayableDate: 在数据库中找到可玩日期记录, date: %s", dateOnly.Format("2006-01-02"))
		return true, nil
	}

	// 3. 如果数据库中没有找到当天日期的记录，则检查当天是否为周五、周六或周日
	weekday := dateOnly.Weekday()
	isWeekend := weekday == time.Friday || weekday == time.Saturday || weekday == time.Sunday

	if isWeekend {
		logger.Logger.InfofCtx(ctx, "DateService IsPlayableDate: 日期 %s 是周末（%s），判定为可玩日期",
			dateOnly.Format("2006-01-02"), weekday.String())
		return true, nil
	}

	// 4. 既没有在数据库中找到记录，当天也不是周五、周六、周日，则返回 false
	logger.Logger.InfofCtx(ctx, "DateService IsPlayableDate: 日期 %s 不是可玩日期（非周末且不在数据库记录中）",
		dateOnly.Format("2006-01-02"))
	return false, nil
}

// IsTodayPlayable 判断今天是否为可玩日期
func (s *DateService) IsTodayPlayable(ctx context.Context) (bool, error) {
	now := time.Now()
	return s.IsPlayableDate(ctx, now)
}
