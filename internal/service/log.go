package service

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/https"
	"github.com/jinzhu/copier"
)

var (
	_logOnce    sync.Once
	_logService *LogService
)

type LogService struct {
	wechatHttpService *https.WechatHttpService
}

func SingletonLogService() *LogService {
	_logOnce.Do(func() {
		_logService = &LogService{
			wechatHttpService: https.SingletonWechatHttpService(),
		}
	})
	return _logService
}

func (s *LogService) GetWechatLog(ctx context.Context, token string, req *bean.GetWechatLogReq) (interface{}, error) {
	fetchReq := &https.FetchWechatLogReq{}
	err := copier.Copy(fetchReq, req)
	if err != nil {
		return nil, err
	}

	res, err := s.wechatHttpService.FetchWechatLog(ctx, token, fetchReq)
	if err != nil {
		return nil, err
	}

	// res to resp
	// resp := &bean.GetWechatLogResp{}
	// err = copier.Copy(resp, res)
	// if err != nil {
	// 	return nil, err
	// }
	return res, nil
}
