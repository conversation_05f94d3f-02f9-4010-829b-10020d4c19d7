package service

import (
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
)

// TestValidateSmallDiamondPayment 测试小额钻石支付验证逻辑
func TestValidateSmallDiamondPayment(t *testing.T) {
	service := &OrderService{}

	tests := []struct {
		name    string
		money   int32
		wantErr bool
	}{
		{
			name:    "有效金额-1分",
			money:   1,
			wantErr: false,
		},
		{
			name:    "有效金额-10分",
			money:   10,
			wantErr: false,
		},
		{
			name:    "有效金额-15分（不是10的整数倍也有效）",
			money:   15,
			wantErr: false,
		},
		{
			name:    "有效金额-100分",
			money:   100,
			wantErr: false,
		},
		{
			name:    "有效金额-1000分",
			money:   1000,
			wantErr: false,
		},
		{
			name:    "无效金额-0",
			money:   0,
			wantErr: true,
		},
		{
			name:    "无效金额-负数",
			money:   -10,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ValidateSmallDiamondPayment(tt.money)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateSmallDiamondPayment() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestSmallDiamondPayParamLogic 测试小额钻石支付参数构造逻辑（不依赖logger）
func TestSmallDiamondPayParamLogic(t *testing.T) {
	// 测试参数验证逻辑
	tests := []struct {
		name        string
		orderID     string
		orderAmount int32
		expectValid bool
	}{
		{
			name:        "有效参数",
			orderID:     "test-order-123",
			orderAmount: 100,
			expectValid: true,
		},
		{
			name:        "空订单ID",
			orderID:     "",
			orderAmount: 100,
			expectValid: false,
		},
		{
			name:        "无效金额-0",
			orderID:     "test-order-123",
			orderAmount: 0,
			expectValid: false,
		},
		{
			name:        "无效金额-负数",
			orderID:     "test-order-123",
			orderAmount: -10,
			expectValid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 验证参数有效性
			isValid := tt.orderID != "" && tt.orderAmount > 0
			if isValid != tt.expectValid {
				t.Errorf("Parameter validation failed: orderID=%s, orderAmount=%d, expected valid=%v, got valid=%v",
					tt.orderID, tt.orderAmount, tt.expectValid, isValid)
			}

			// 如果参数有效，验证构造的支付参数结构
			if tt.expectValid {
				// 模拟构造支付参数的逻辑
				douyinOrder := &bean.DouyinOrder{
					Mode:         "game",
					Env:          0,
					CurrencyType: "CNY",
					Platform:     "android",
					ZoneId:       "1",
					CustomId:     tt.orderID,
					GoodType:     2,
					OrderAmount:  tt.orderAmount,
				}

				// 验证关键字段
				if douyinOrder.GoodType != 2 {
					t.Errorf("Expected GoodType = 2, got %d", douyinOrder.GoodType)
				}
				if douyinOrder.OrderAmount != tt.orderAmount {
					t.Errorf("Expected OrderAmount = %d, got %d", tt.orderAmount, douyinOrder.OrderAmount)
				}
				if douyinOrder.CurrencyType != "CNY" {
					t.Errorf("Expected CurrencyType = CNY, got %s", douyinOrder.CurrencyType)
				}
				if douyinOrder.BuyQuantity != 0 {
					t.Errorf("Expected BuyQuantity = 0 (not set), got %d", douyinOrder.BuyQuantity)
				}
			}
		})
	}
}

// TestGetOrderDetailCustomerLogic 测试GetOrderDetailCustomer方法的小额钻石支付逻辑
func TestGetOrderDetailCustomerLogic(t *testing.T) {
	// 测试小额钻石支付和传统支付的区别
	tests := []struct {
		name               string
		isSmallDiamond     bool
		money              int32
		expectGameCurrency bool
	}{
		{
			name:               "小额钻石支付",
			isSmallDiamond:     true,
			money:              100,
			expectGameCurrency: false, // 小额钻石支付不应该有game_currency
		},
		{
			name:               "传统抖音支付",
			isSmallDiamond:     false,
			money:              100,
			expectGameCurrency: true, // 传统支付应该有game_currency
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟订单更新逻辑
			update := make(map[string]interface{})
			update["pay_type"] = constants.PayTypeIOSDouyinPay
			update["platform_type"] = constants.PlatformTypeIOS

			if tt.isSmallDiamond {
				// 小额钻石支付不应该设置game_currency
				// update中不包含game_currency字段
			} else {
				// 传统支付应该设置game_currency
				gameCurrency := tt.money / 10
				update["game_currency"] = gameCurrency
			}

			// 验证更新字段
			_, hasGameCurrency := update["game_currency"]
			if hasGameCurrency != tt.expectGameCurrency {
				t.Errorf("Expected game_currency presence: %v, got: %v", tt.expectGameCurrency, hasGameCurrency)
			}

			// 验证支付参数构造逻辑
			if tt.isSmallDiamond {
				// 小额钻石支付应该使用orderAmount
				douyinOrder := &bean.DouyinOrder{
					Mode:         "game",
					Env:          0,
					CurrencyType: "CNY",
					Platform:     "android",
					ZoneId:       "1",
					CustomId:     "test-order",
					GoodType:     2,
					OrderAmount:  tt.money,
				}

				if douyinOrder.BuyQuantity != 0 {
					t.Errorf("Small diamond payment should not have BuyQuantity, got: %d", douyinOrder.BuyQuantity)
				}
				if douyinOrder.GoodType != 2 {
					t.Errorf("Small diamond payment should have GoodType=2, got: %d", douyinOrder.GoodType)
				}
				if douyinOrder.OrderAmount != tt.money {
					t.Errorf("Small diamond payment should have OrderAmount=%d, got: %d", tt.money, douyinOrder.OrderAmount)
				}
			} else {
				// 传统支付应该使用buyQuantity
				gameCurrency := tt.money / 10
				douyinOrder := &bean.DouyinOrder{
					Mode:         "game",
					Env:          0,
					CurrencyType: "CNY",
					Platform:     "android",
					BuyQuantity:  gameCurrency,
					ZoneId:       "1",
					CustomId:     "test-order",
				}

				if douyinOrder.BuyQuantity != gameCurrency {
					t.Errorf("Traditional payment should have BuyQuantity=%d, got: %d", gameCurrency, douyinOrder.BuyQuantity)
				}
				if douyinOrder.GoodType != 0 {
					t.Errorf("Traditional payment should not have GoodType, got: %d", douyinOrder.GoodType)
				}
				if douyinOrder.OrderAmount != 0 {
					t.Errorf("Traditional payment should not have OrderAmount, got: %d", douyinOrder.OrderAmount)
				}
			}
		})
	}
}
