package service

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/mail"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

const (
	// ConfigTypeWorkorderStats 工单统计配置类型
	ConfigTypeWorkorderStats = "workorder_stats"

	// 工单状态常量
	WorkorderStatusPending    = 1 // 待接单
	WorkorderStatusProcessing = 2 // 受理中
	WorkorderStatusCompleted  = 3 // 已完结

	// 用户类型常量
	UserTypeCustomer = 1 // 用户
	UserTypeService  = 2 // 客服
)

// 单例模式相关代码
var (
	_statsOnce    sync.Once
	_statsService *StatsService
)

// StatsService 工单统计服务
type StatsService struct{}

// GameWorkorderStats 游戏工单统计
type GameWorkorderStats struct {
	GameID      string // 游戏ID
	GameName    string // 游戏名称
	Total       int64  // 总工单数
	Completed   int64  // 已完结
	InProgress  int64  // 回复中
	Unprocessed int64  // 未回复
}

// SingletonWorkorderStatsService 获取工单统计服务单例
func SingletonWorkorderStatsService() *StatsService {
	_statsOnce.Do(func() {
		_statsService = &StatsService{}
	})
	return _statsService
}

// GenerateAndSendStatsMail 生成并发送统计邮件
func (s *StatsService) GenerateAndSendStatsMail(ctx context.Context) error {
	// 检查今天是否是周日或周一（不发送）
	now := time.Now()
	weekday := now.Weekday()
	if weekday == time.Sunday || weekday == time.Monday {
		logger.Logger.Infof("[GenerateAndSendStatsMail] 今天是%s，不发送订单统计邮件", weekday)
		return nil
	}

	// 获取收件人列表
	recipients, err := s.getRecipientsEmails(ctx)
	if err != nil {
		return err
	}

	if len(recipients) == 0 {
		logger.Logger.Warnf("[GenerateAndSendStatsMail] 工单统计邮件没有配置收件人")
		return nil
	}

	// 统计时间范围: 前天20:30到昨天20:30
	yesterday := now.AddDate(0, 0, -1)
	endTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 20, 30, 0, 0, now.Location())
	startTime := endTime.AddDate(0, 0, -1) // 再往前一天的同一时间

	// 转换为Unix时间戳（毫秒）
	startTimestamp := startTime.UnixNano() / int64(time.Millisecond)
	endTimestamp := endTime.UnixNano() / int64(time.Millisecond)

	// 获取统计数据
	stats, err := s.getWorkorderStats(ctx, startTimestamp, endTimestamp)
	if err != nil {
		return err
	}

	// 生成HTML邮件内容
	subject := fmt.Sprintf("%s 工单处理统计", endTime.Format("2006-01-02"))
	htmlContent := s.GenerateHTMLReport(stats, startTime, endTime)

	// 创建邮件发送器并发送邮件
	mailSender := mail.NewSender(config.GlobConfig.Smtp)
	return mailSender.SendHTMLMail(recipients, subject, htmlContent)
}

// getWorkorderStats 获取工单统计数据
func (s *StatsService) getWorkorderStats(ctx context.Context, startTime, endTime int64) ([]GameWorkorderStats, error) {
	db := store.GOrmDB(ctx)

	// 按游戏ID分组获取该时间段内新增的工单数量 - 这种复杂分组查询保持原生方式
	rows, err := db.Model(&model.MWorkorder{}).
		Select("game_id, COUNT(*) as total").
		Where("created_at >= ? AND created_at <= ? AND is_deleted = ?", startTime, endTime, false).
		Group("game_id").
		Rows()

	if err != nil {
		logger.Logger.Errorf("[getWorkorderStats] 查询游戏工单统计失败: %v", err)
		return nil, err
	}
	defer rows.Close()

	gameStats := make(map[string]*GameWorkorderStats)
	var totalStats GameWorkorderStats
	totalStats.GameID = "total"
	totalStats.GameName = "汇总"

	// 收集所有游戏ID
	var gameIDs []string
	for rows.Next() {
		var gameID string
		var total int64
		if err := rows.Scan(&gameID, &total); err != nil {
			logger.Logger.Errorf("[getWorkorderStats] 解析游戏工单统计数据失败: %v", err)
			continue
		}
		gameIDs = append(gameIDs, gameID)

		stats := &GameWorkorderStats{
			GameID:   gameID,
			GameName: gameID, // 默认使用gameID作为名称，后面会更新
			Total:    total,
		}
		gameStats[gameID] = stats
		totalStats.Total += total
	}

	// 一次性批量查询所有游戏名称
	if len(gameIDs) > 0 {
		gameTable := store.QueryDB().MGame
		games, err := gameTable.WithContext(ctx).Where(gameTable.GameID.In(gameIDs...)).Where(gameTable.IsDeleted.Zero()).Find()
		if err != nil {
			logger.Logger.Warnf("[getWorkorderStats] 批量查询游戏名称失败: %v, 将使用gameID作为名称", err)
		} else {
			// 构建游戏ID到名称的映射
			gameNameMap := make(map[string]string)
			for _, game := range games {
				gameNameMap[game.GameID] = game.Name
			}

			// 更新统计数据中的游戏名称
			for gameID, stats := range gameStats {
				if name, exists := gameNameMap[gameID]; exists {
					stats.GameName = name
				}
			}
		}
	}

	// 使用 GORM Gen 进行 Count 查询
	mWorkorder := store.QueryDB().MWorkorder

	// 获取每个游戏的已完结工单数
	for gameID, stats := range gameStats {
		// 已完结：工单状态为"已完结"的工单
		completed, err := mWorkorder.WithContext(ctx).
			Where(mWorkorder.GameID.Eq(gameID)).
			Where(mWorkorder.CreatedAt.Gte(startTime)).
			Where(mWorkorder.CreatedAt.Lte(endTime)).
			Where(mWorkorder.Status.Eq(WorkorderStatusCompleted)).
			Where(mWorkorder.IsDeleted.Is(false)).
			Count()
		if err != nil {
			logger.Logger.Errorf("[getWorkorderStats] 查询已完结工单失败: %v", err)
			continue
		}
		stats.Completed = completed
		totalStats.Completed += completed

		// 回复中：状态是受理中，且客服回复过的工单
		// 通过子查询检查是否有客服回复
		var inProgressCount int64
		result := db.Model(&model.MWorkorder{}).
			Where("game_id = ? AND created_at >= ? AND created_at <= ? AND status = ? AND is_deleted = ?",
				gameID, startTime, endTime, WorkorderStatusProcessing, false).
			Where("EXISTS (SELECT 1 FROM m_workorder_reply WHERE m_workorder_reply.order_id = m_workorder.order_id AND m_workorder_reply.user_type = ? AND m_workorder_reply.is_deleted = ?)",
				UserTypeService, false).
			Count(&inProgressCount)
		if result.Error != nil {
			logger.Logger.Errorf("[getWorkorderStats] 查询回复中工单失败: %v", result.Error)
			continue
		}
		stats.InProgress = inProgressCount
		totalStats.InProgress += inProgressCount

		// 未回复：未受理的工单+受理中但无客服回复的工单
		// 1. 待接单的工单
		pendingCount, err := mWorkorder.WithContext(ctx).
			Where(mWorkorder.GameID.Eq(gameID)).
			Where(mWorkorder.CreatedAt.Gte(startTime)).
			Where(mWorkorder.CreatedAt.Lte(endTime)).
			Where(mWorkorder.Status.Eq(WorkorderStatusPending)).
			Where(mWorkorder.IsDeleted.Is(false)).
			Count()
		if err != nil {
			logger.Logger.Errorf("[getWorkorderStats] 查询待接单工单失败: %v", err)
			continue
		}

		// 2. 受理中但无客服回复的工单
		var processingNoReplyCount int64
		result = db.Model(&model.MWorkorder{}).
			Where("game_id = ? AND created_at >= ? AND created_at <= ? AND status = ? AND is_deleted = ?",
				gameID, startTime, endTime, WorkorderStatusProcessing, false).
			Where("NOT EXISTS (SELECT 1 FROM m_workorder_reply WHERE m_workorder_reply.order_id = m_workorder.order_id AND m_workorder_reply.user_type = ? AND m_workorder_reply.is_deleted = ?)",
				UserTypeService, false).
			Count(&processingNoReplyCount)
		if result.Error != nil {
			logger.Logger.Errorf("[getWorkorderStats] 查询受理中无回复工单失败: %v", result.Error)
			continue
		}

		stats.Unprocessed = pendingCount + processingNoReplyCount
		totalStats.Unprocessed += stats.Unprocessed
	}

	// 转换为列表并添加汇总
	result := make([]GameWorkorderStats, 0, len(gameStats)+1)
	for _, stats := range gameStats {
		result = append(result, *stats)
	}
	result = append(result, totalStats)

	return result, nil
}

// getRecipientsEmails 获取收件人邮箱列表
func (s *StatsService) getRecipientsEmails(ctx context.Context) ([]string, error) {
	emailConfig := store.QueryDB().MEmailConfig
	configs, err := emailConfig.WithContext(ctx).
		Where(emailConfig.ConfigType.Eq(ConfigTypeWorkorderStats)).
		Where(emailConfig.IsDeleted.Is(false)).
		Find()

	if err != nil {
		logger.Logger.Errorf("[getRecipientsEmails] 查询邮件收件人配置失败: %v", err)
		return nil, err
	}

	emails := make([]string, 0, len(configs))
	for _, config := range configs {
		if config.EmailAddress != "" {
			emails = append(emails, config.EmailAddress)
		}
	}

	return emails, nil
}

// GenerateHTMLReport 生成HTML格式的报表
func (s *StatsService) GenerateHTMLReport(stats []GameWorkorderStats, startTime, endTime time.Time) string {
	var sb strings.Builder

	// HTML文档结构
	sb.WriteString("<!DOCTYPE html>\n")
	sb.WriteString("<html>\n<head>\n")
	sb.WriteString("<meta charset=\"UTF-8\">\n")
	sb.WriteString("<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n")
	sb.WriteString("<title>工单处理情况</title>\n")
	sb.WriteString("</head>\n<body style=\"font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #ffffff;\">\n")

	// 标题 - 黄色背景
	sb.WriteString("<div style=\"background-color: #ffff00; padding: 10px; text-align: center;\">\n")
	sb.WriteString(fmt.Sprintf("<h2 style=\"margin: 0;\">%s 工单处理情况</h2>\n", endTime.Format("2006.01.02")))
	sb.WriteString("</div>\n")

	// 表格 - 100%宽度无边距
	sb.WriteString("<table style=\"border-collapse: collapse; width: 100%; margin: 0;\">\n")

	// 表头 - 蓝色背景
	sb.WriteString("<tr style=\"background-color: #4b8bf4;\">\n")
	sb.WriteString("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: center; color: white;\">游戏</th>\n")
	sb.WriteString("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: center; color: white;\">新增工单</th>\n")
	sb.WriteString("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: center; color: white;\">已完结</th>\n")
	sb.WriteString("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: center; color: white;\">回复中</th>\n")
	sb.WriteString("<th style=\"border: 1px solid #ddd; padding: 8px; text-align: center; color: white;\">未回复</th>\n")
	sb.WriteString("</tr>\n")

	// 表格内容 - 全都使用普通行样式
	for _, stat := range stats {
		rowStyle := "border: 1px solid #ddd; padding: 8px; text-align: center;"

		sb.WriteString("<tr>\n")
		sb.WriteString(fmt.Sprintf("<td style=\"%s\">%s</td>\n", rowStyle, stat.GameName))
		sb.WriteString(fmt.Sprintf("<td style=\"%s\">%d</td>\n", rowStyle, stat.Total))
		sb.WriteString(fmt.Sprintf("<td style=\"%s\">%d</td>\n", rowStyle, stat.Completed))
		sb.WriteString(fmt.Sprintf("<td style=\"%s\">%d</td>\n", rowStyle, stat.InProgress))
		sb.WriteString(fmt.Sprintf("<td style=\"%s\">%d</td>\n", rowStyle, stat.Unprocessed))
		sb.WriteString("</tr>\n")
	}

	sb.WriteString("</table>\n")
	sb.WriteString("</body>\n</html>")

	return sb.String()
}
