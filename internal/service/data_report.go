package service

import (
	"context"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/ThinkingDataAnalytics/go-sdk/v2/src/thinkingdata"
)

var (
	_dataReportOnce    sync.Once
	_dataReportService *DataReportService
)

type DataReportService struct {
	analytics thinkingdata.TDAnalytics
}

func SingletonDataReportService() *DataReportService {
	_dataReportOnce.Do(func() {
		config := thinkingdata.TDLogConsumerConfig{
			Directory: constants.ThinkingdataLogDirPath, // 事件采集的文件路径
		}
		consumer, _ := thinkingdata.NewLogConsumerWithConfig(config)
		te := thinkingdata.New(consumer)
		_dataReportService = &DataReportService{
			analytics: te,
		}
	})
	return _dataReportService
}

// ReportUserNamePoint 上报用户名
func (s *DataReportService) ReportUserNamePoint(ctx context.Context, userID, deviceID, gameID, platformAppID, nickname string) error {
	properties := make(map[string]interface{})
	properties["user_name"] = nickname

	uploadReq := &bean.UploadReport{ // ClientIP:   "",
		UUID:       util.UUID(),
		AccountID:  userID,
		DistinctID: deviceID,
		AppID:      platformAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     gameID,
		EventName:  "server_user_set",
		EventType:  constants.ThinkingdataUserSet,
		Properties: properties,
	}

	err := s.UploadReport(ctx, uploadReq)
	if err != nil {
		return err
	}

	// 额外上报：条件为game_id为kof-test和server_前缀，上报到 7366ccc227474c04804ba4179e42a879
	s.AdditionalUploadReport(ctx, uploadReq)

	return nil
}

// ReportUserAvatarPoint 上报用户头像
func (s *DataReportService) ReportUserAvatarPoint(ctx context.Context, userID, deviceID, gameID, platformAppID, avatarURL string) error {
	properties := make(map[string]interface{})
	properties["user_avatar"] = avatarURL

	uploadReq := &bean.UploadReport{
		UUID:       util.UUID(),
		AccountID:  userID,
		DistinctID: deviceID,
		AppID:      platformAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     gameID,
		EventName:  "server_user_set",
		EventType:  constants.ThinkingdataUserSet,
		Properties: properties,
	}

	err := s.UploadReport(ctx, uploadReq)
	if err != nil {
		return err
	}

	// 额外上报：条件为game_id为kof-test和server_前缀，上报到 7366ccc227474c04804ba4179e42a879
	s.AdditionalUploadReport(ctx, uploadReq)

	return nil
}

// ReportRegister 上报注册
func (s *DataReportService) ReportRegister(ctx context.Context, userID, deviceID, gameID, platformAppID, os string, channel *bean.ChannelInfo) error {
	properties := make(map[string]interface{})
	if channel != nil {
		properties["channel"] = channel.Channel
		properties["ad_from"] = channel.ADFrom
	}
	if os != "" {
		properties["os"] = os
	}

	uploadReq := &bean.UploadReport{ // ClientIP:   "",
		UUID:       util.UUID(),
		AccountID:  userID,
		DistinctID: deviceID,
		AppID:      platformAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     gameID,
		EventName:  "server_register",
		EventType:  constants.ThinkingdataTrack,
		Properties: properties,
	}

	err := s.UploadReport(ctx, uploadReq)
	if err != nil {
		return err
	}

	// 额外上报：条件为game_id为kof-test和server_前缀，上报到 7366ccc227474c04804ba4179e42a879
	s.AdditionalUploadReport(ctx, uploadReq)

	return nil
}

func (s *DataReportService) ReportUserRegister(ctx context.Context, userID, deviceID, gameID, platformAppID, openID string, channel *bean.ChannelInfo) error {
	properties := make(map[string]interface{})
	if channel != nil {
		properties["channel"] = channel.Channel
		properties["ad_from"] = channel.ADFrom
	}
	now := time.Now().Format(constants.DayTimeSpecificFormat)
	properties["register_time"] = now
	properties["open_id"] = openID

	uploadReq := &bean.UploadReport{
		UUID:       util.UUID(),
		AccountID:  userID,
		DistinctID: deviceID,
		AppID:      platformAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     gameID,
		EventName:  "server_user_register",
		EventType:  constants.ThinkingdataUserSet,
		Properties: properties,
	}

	err := s.UploadReport(ctx, uploadReq)
	if err != nil {
		return err
	}

	// 额外上报：条件为game_id为kof-test和server_前缀，上报到 7366ccc227474c04804ba4179e42a879
	s.AdditionalUploadReport(ctx, uploadReq)

	return nil
}

func (s *DataReportService) ReportUserLogin(ctx context.Context, userID, deviceID, gameID, platformAppID, openID string, registerAt int64, channel *bean.ChannelInfo) error {
	properties := make(map[string]interface{})
	properties["last_login_time"] = time.Now().Format(constants.DayTimeSpecificFormat)
	properties["open_id"] = openID
	properties["register_time"] = time.Unix(registerAt/1000, 0).Format(constants.DayTimeSpecificFormat)
	if channel != nil {
		properties["channel"] = channel.Channel
		properties["ad_from"] = channel.ADFrom
	}

	uploadReq := &bean.UploadReport{
		UUID:       util.UUID(),
		AccountID:  userID,
		DistinctID: deviceID,
		AppID:      platformAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     gameID,
		EventName:  "server_user_login",
		EventType:  constants.ThinkingdataUserSet,
		Properties: properties,
	}

	err := s.UploadReport(ctx, uploadReq)
	if err != nil {
		return err
	}

	// 额外上报：条件为game_id为kof-test和server_前缀，上报到 7366ccc227474c04804ba4179e42a879
	s.AdditionalUploadReport(ctx, uploadReq)

	return nil
}

// ReportLogin 上报登录
func (s *DataReportService) ReportLogin(ctx context.Context, userID, deviceID, gameID, platformAppID string, channel *bean.ChannelInfo) error {
	properties := make(map[string]interface{})
	if channel != nil {
		properties["channel"] = channel.Channel
		properties["ad_from"] = channel.ADFrom
	}

	uploadReq := &bean.UploadReport{ // ClientIP:   "",
		UUID:       util.UUID(),
		AccountID:  userID,
		DistinctID: deviceID,
		AppID:      platformAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     gameID,
		EventName:  "server_login",
		EventType:  constants.ThinkingdataTrack,
		Properties: properties,
	}

	err := s.UploadReport(ctx, uploadReq)
	if err != nil {
		return err
	}

	// 额外上报：条件为game_id为kof-test和server_前缀，上报到 7366ccc227474c04804ba4179e42a879
	s.AdditionalUploadReport(ctx, uploadReq)

	return nil
}

func (s *DataReportService) ReportCreateOrder(ctx context.Context, userID, deviceID, gameID, platformAppID, orderID, goodsId string, money int32) error {
	properties := make(map[string]interface{})
	properties["order_id"] = orderID
	properties["goods_id"] = goodsId
	properties["money"] = money

	uploadReq := &bean.UploadReport{ // ClientIP:   "",
		UUID:       util.UUID(),
		AccountID:  userID,
		DistinctID: deviceID,
		AppID:      platformAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     gameID,
		EventName:  "server_create_order",
		EventType:  constants.ThinkingdataTrack,
		Properties: properties,
	}

	err := s.UploadReport(ctx, uploadReq)
	if err != nil {
		return err
	}

	// 额外上报：条件为game_id为kof-test和server_前缀，上报到 7366ccc227474c04804ba4179e42a879
	s.AdditionalUploadReport(ctx, uploadReq)

	return nil
}

func (s *DataReportService) ReportPullOrder(ctx context.Context, userID, deviceID, gameID, platformAppID, orderID, goodsID string, money int32, deviceOS string) error {
	properties := make(map[string]interface{})
	properties["order_id"] = orderID
	properties["goods_id"] = goodsID
	properties["money"] = money
	properties["device_os"] = deviceOS

	uploadReq := &bean.UploadReport{ // ClientIP:   "",
		UUID:       util.UUID(),
		AccountID:  userID,
		DistinctID: deviceID,
		AppID:      platformAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     gameID,
		EventName:  "server_pull_order",
		EventType:  constants.ThinkingdataTrack,
		Properties: properties,
	}

	err := s.UploadReport(ctx, uploadReq)
	if err != nil {
		return err
	}

	// 额外上报：条件为game_id为kof-test和server_前缀，上报到 7366ccc227474c04804ba4179e42a879
	s.AdditionalUploadReport(ctx, uploadReq)

	return nil
}

func (s *DataReportService) ReportPaySuccess(ctx context.Context, userID, deviceID, gameID, platformAppID, orderID string, money, currencyPrice, payStatus, payType int32) error {
	properties := make(map[string]interface{})
	properties["order_id"] = orderID
	properties["money"] = money
	properties["currency_price"] = currencyPrice
	properties["pay_status"] = payStatus
	properties["pay_type"] = payType

	uploadReq := &bean.UploadReport{ // ClientIP:   "",
		UUID:       util.UUID(),
		AccountID:  userID,
		DistinctID: deviceID,
		AppID:      platformAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     gameID,
		EventName:  "server_pay",
		EventType:  constants.ThinkingdataTrack,
		Properties: properties,
	}

	err := s.UploadReport(ctx, uploadReq)
	if err != nil {
		return err
	}

	// 额外上报：条件为game_id为kof-test和server_前缀，上报到 7366ccc227474c04804ba4179e42a879
	s.AdditionalUploadReport(ctx, uploadReq)

	return nil
}

func (s *DataReportService) ReportProductShipment(ctx context.Context, userID, deviceID, gameID, platformAppID, orderID, goodsID string, money, currencyPrice, payType int32, shipmentInfo *bean.ProductShipmentRes) error {
	properties := make(map[string]interface{})
	properties["order_id"] = orderID
	properties["goods_id"] = goodsID
	properties["money"] = money
	properties["currency_price"] = currencyPrice
	properties["pay_type"] = payType
	properties["shipment_time"] = time.Now().UnixMilli()
	if shipmentInfo != nil {
		properties["server_return_code"] = shipmentInfo.Code
		properties["server_return_msg"] = shipmentInfo.Msg
	}

	uploadReq := &bean.UploadReport{ // ClientIP:   "",
		UUID:       util.UUID(),
		AccountID:  userID,
		DistinctID: deviceID,
		AppID:      platformAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     gameID,
		EventName:  "server_shipment",
		EventType:  constants.ThinkingdataTrack,
		Properties: properties,
	}

	err := s.UploadReport(ctx, uploadReq)
	if err != nil {
		return err
	}

	// 额外上报：条件为game_id为kof-test和server_前缀，上报到 7366ccc227474c04804ba4179e42a879
	s.AdditionalUploadReport(ctx, uploadReq)

	return nil
}

// ReportUserEnterTempsession 上报用户进入客服消息
func (s *DataReportService) ReportUserEnterTempsession(ctx context.Context, userID, gameID string, userSessionFrom *bean.UserSessionFrom) error {
	properties := make(map[string]interface{})
	properties["open_id"] = userID
	properties["game_id"] = gameID
	properties["role_id"] = userSessionFrom.RoleID
	properties["player_id"] = userSessionFrom.PlayerID
	properties["player_name"] = userSessionFrom.PlayerName
	properties["player_level"] = userSessionFrom.PlayerLevel
	properties["recharge_total_amount"] = userSessionFrom.RechargeTotalAmount
	properties["zone"] = userSessionFrom.Zone
	if userSessionFrom.CustomData != nil {
		properties["custom_data"] = userSessionFrom.CustomData
	}
	return s.UploadReport(ctx, &bean.UploadReport{
		UUID:      util.UUID(),
		AccountID: userID,
		// DistinctID: deviceID,
		AppID:      config.GlobConfig.Thinkdata.ThinkdataAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     gameID,
		EventName:  "user_enter_tempsession",
		EventType:  constants.ThinkingdataTrack,
		Properties: properties,
	})
}

// InitReport 未登录上报
func (s *DataReportService) InitReport(_ context.Context, req *bean.InitReportReq) error {
	if req.PlatformAppID == "" {
		return constants.ErrInitReportAppIsNil
	}

	accountID := "" // 未登录情况下为空
	distinctID := req.PlatformDeviceID
	properties := req.Properties
	if properties == nil {
		logger.Logger.Errorf("DataReportService InitReport properties is nil")
		return constants.ErrPropertiesIsNil
	}

	properties[constants.PlatformAppIDKey] = req.PlatformAppID
	properties["#uuid"] = req.UUID
	properties["#ip"] = req.IP
	if _, ok := properties["#time"]; !ok {
		properties["#time"] = time.Now().Format(constants.DayTimeSpecificFormat)
	}

	var err error
	switch req.EventType {
	case constants.ThinkingdataTrack:
		properties["game_id"] = req.PlatformGameID
		if req.IPRegionID != "" {
			properties["region_id"] = req.IPRegionID
		}
		err = s.analytics.Track(accountID, distinctID, req.EventName, properties)
	case constants.ThinkingdataUserSet:
		err = s.analytics.UserSet(accountID, distinctID, properties)
	case constants.ThinkingdataUserSetOnce:
		err = s.analytics.UserSetOnce(accountID, distinctID, properties)
	case constants.ThinkingdataUserAdd:
		err = s.analytics.UserAdd(accountID, distinctID, properties)
	case constants.ThinkingdataUserUnset:
		keys := make([]string, 0)
		for k := range properties {
			keys = append(keys, k)
		}
		err = s.analytics.UserUnset(accountID, distinctID, keys)
	case constants.ThinkingdataUserAppend:
		err = s.analytics.UserAppend(accountID, distinctID, properties)
	case constants.ThinkingdataUserDel:
		err = s.analytics.UserDelete(accountID, distinctID)
	}
	if err != nil {
		logger.Logger.Errorf("DataReportService init report error: %s", err.Error())
		return err
	}

	return nil
}

func (s *DataReportService) UploadReport(ctx context.Context, req *bean.UploadReport) error {
	if req.UUID == "" || req.EventType == "" {
		logger.Logger.WarnfCtx(ctx, "DataReportService UploadReport missing required fields: UUID=%v, EventType=%v, EventName=%v",
			req.UUID, req.EventType, req.EventName)
		return constants.ErrConfigError
	}

	distinctID := req.DistinctID
	//if distinctID == "" { // 如果使用方未传递distinct_id, 则使用account_id
	//	distinctID = req.AccountID
	//}

	accountID := req.AccountID
	properties := req.Properties
	if properties == nil {
		logger.Logger.WarnfCtx(ctx, "DataReportService properties is nil")
		return constants.ErrPropertiesIsNil
	}
	properties[constants.PlatformAppIDKey] = req.AppID
	properties["#uuid"] = req.UUID
	properties["#ip"] = req.ClientIP
	properties["#time"] = req.Timestamp

	var err error
	switch req.EventType {
	case constants.ThinkingdataTrack:
		if req.GameID != "" {
			properties["game_id"] = req.GameID
		}
		err = s.analytics.Track(accountID, distinctID, req.EventName, properties)
	case constants.ThinkingdataUserSet:
		err = s.analytics.UserSet(accountID, distinctID, properties)
	case constants.ThinkingdataUserSetOnce:
		err = s.analytics.UserSetOnce(accountID, distinctID, properties)
	case constants.ThinkingdataUserAdd:
		err = s.analytics.UserAdd(accountID, distinctID, properties)
	case constants.ThinkingdataUserUnset:
		keys := make([]string, 0)
		for k := range properties {
			keys = append(keys, k)
		}
		err = s.analytics.UserUnset(accountID, distinctID, keys)
	case constants.ThinkingdataUserAppend:
		err = s.analytics.UserAppend(accountID, distinctID, properties)
	case constants.ThinkingdataUserDel:
		err = s.analytics.UserDelete(accountID, distinctID)
	}
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "DataReportService data report error: %s", err.Error())
		return err
	}

	return nil
}

func (s *DataReportService) DataReport(ctx context.Context, req *bean.DataReportReq) error {
	accountID := req.UserID
	if req.RoleID != "" {
		accountID = req.RoleID
	}
	distinctID := req.DeviceID
	properties := req.Properties
	if properties == nil {
		logger.Logger.WarnfCtx(ctx, "DataReportService properties is nil")
		return constants.ErrPropertiesIsNil
	}
	properties[constants.PlatformAppIDKey] = req.AppID
	properties["#uuid"] = req.UUID
	// 判断properties["#time"]是否存在
	if _, ok := properties["#time"]; !ok {
		properties["#time"] = time.Now().Format(constants.DayTimeSpecificFormat)
	}
	properties["#ip"] = req.IP

	var err error
	switch req.EventType {
	case constants.ThinkingdataTrack:
		properties["game_id"] = req.GameID

		if req.IPRegionID != "" {
			properties["region_id"] = req.IPRegionID
		}
		err = s.analytics.Track(accountID, distinctID, req.EventName, properties)
	case constants.ThinkingdataUserSet:
		err = s.analytics.UserSet(accountID, distinctID, properties)
	case constants.ThinkingdataUserSetOnce:
		err = s.analytics.UserSetOnce(accountID, distinctID, properties)
	case constants.ThinkingdataUserAdd:
		err = s.analytics.UserAdd(accountID, distinctID, properties)
	case constants.ThinkingdataUserUnset:
		keys := make([]string, 0)
		for k := range properties {
			keys = append(keys, k)
		}
		err = s.analytics.UserUnset(accountID, distinctID, keys)
	case constants.ThinkingdataUserAppend:
		err = s.analytics.UserAppend(accountID, distinctID, properties)
	case constants.ThinkingdataUserDel:
		err = s.analytics.UserDelete(accountID, distinctID)
	default:
		return constants.ErrEventTypeIsNil
	}
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "DataReportService data report error: %s", err.Error())
		return err
	}

	return nil
}

// SystemDataReport 系统数据上报 req *bean.DataReportReq
func (s *DataReportService) SystemDataReport(userID, deviceID, ipCli, ipRegionID, gameID, eventName, evenType, uuid string, properties map[string]interface{}) error {
	// 如果 eventName 不包含sdk_，则返回
	if evenType != constants.ThinkingdataTrack || !strings.HasPrefix(eventName, "sdk_") {
		logger.Logger.Warnf("SystemDataReport eventName: %s, not contains sdk_", eventName)
		return nil
	}

	accountID := userID
	distinctID := deviceID
	if properties == nil {
		logger.Logger.Errorf("SystemDataReport properties is nil")
		return constants.ErrPropertiesIsNil
	}
	properties[constants.PlatformAppIDKey] = config.GlobConfig.Thinkdata.ThinkdataAppID
	properties["#uuid"] = uuid
	properties["#ip"] = ipCli
	properties["#time"] = time.Now().Format(constants.DayTimeSpecificFormat)
	properties["game_id"] = gameID
	if ipRegionID != "" {
		properties["region_id"] = ipRegionID
	}

	var err error
	switch evenType {
	case constants.ThinkingdataTrack:
		err = s.analytics.Track(accountID, distinctID, eventName, properties)
	}
	if err != nil {
		logger.Logger.Errorf("SystemDataReport data report error: %s", err.Error())
		return err
	}
	return nil
}

func (s *DataReportService) Flush() error {
	err := s.analytics.Flush()
	if err != nil {
		logger.Logger.Errorf("DataReportService flush err: %s", err.Error())
		return err
	}
	return nil
}

// ReportCaptchaConfig 上报验证码配置获取信息
func (s *DataReportService) ReportCaptchaConfig(ctx context.Context, platformAppID, gameID, accountID, distinctID, extraData string, timestamp int64, valid bool, provider int32, captchaID string) error {
	properties := make(map[string]interface{})
	properties["game_id"] = gameID
	properties["timestamp"] = timestamp
	properties["extra_data"] = extraData
	properties["valid"] = valid
	properties["provider"] = provider
	properties["captcha_id"] = captchaID

	return s.UploadReport(ctx, &bean.UploadReport{
		UUID:       util.UUID(),
		AccountID:  accountID,
		DistinctID: distinctID,
		AppID:      platformAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     gameID,
		EventName:  "captcha_config",
		EventType:  constants.ThinkingdataTrack,
		Properties: properties,
	})
}

// ReportCaptchaResult 上报验证码校验结果
func (s *DataReportService) ReportCaptchaResult(ctx context.Context, platformAppID, gameID, accountID, distinctID, ticket, extraData string, timestamp int64, valid bool) error {
	properties := make(map[string]interface{})
	properties["game_id"] = gameID
	properties["ticket"] = ticket
	properties["timestamp"] = timestamp
	properties["extra_data"] = extraData
	properties["valid"] = valid

	return s.UploadReport(ctx, &bean.UploadReport{
		UUID:       util.UUID(),
		AccountID:  accountID,
		DistinctID: distinctID,
		AppID:      platformAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     gameID,
		EventName:  "captcha_result",
		EventType:  constants.ThinkingdataTrack,
		Properties: properties,
	})
}

// ReportLibraryQuestionAnswer 上报库问题答案事件
func (s *DataReportService) ReportLibraryQuestionAnswer(ctx context.Context, userID, openID, gameID, question, answer string) error {
	properties := make(map[string]interface{})
	properties["open_id"] = openID
	properties["question"] = question
	properties["answer"] = answer

	return s.UploadReport(ctx, &bean.UploadReport{
		UUID:       util.UUID(),
		AccountID:  userID,
		AppID:      config.GlobConfig.Thinkdata.ThinkdataAppID,
		Timestamp:  time.Now().Unix(),
		GameID:     gameID,
		EventName:  "library_question_answer",
		EventType:  constants.ThinkingdataTrack,
		Properties: properties,
	})
}

// AdditionalDataReport 额外数据上报到数数科技
// 当事件名称包含"sdk_"前缀且游戏ID为"kof-test"时，将相同数据额外上报到指定的app_id
func (s *DataReportService) AdditionalDataReport(ctx context.Context, req *bean.DataReportReq) {
	// 检查触发条件
	if !s.shouldTriggerAdditionalReport(req.EventName, req.GameID, req.EventType) {
		return
	}

	// 异步执行额外上报，避免影响主流程
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Logger.ErrorfCtx(ctx, "AdditionalDataReport panic recovered: %v", r)
			}
		}()

		if err := s.performAdditionalReport(ctx, req); err != nil {
			logger.Logger.ErrorfCtx(ctx, "AdditionalDataReport failed: %v", err)
		}
	}()
}

// AdditionalBatchDataReport 批量额外数据上报到数数科技
func (s *DataReportService) AdditionalBatchDataReport(ctx context.Context, req *bean.BatchDataReportReq) {
	// 检查是否有需要额外上报的数据
	hasTargetData := false
	for _, report := range req.DataReports {
		if s.shouldTriggerAdditionalReport(report.EventName, req.GameID, report.EventType) {
			hasTargetData = true
			break
		}
	}

	if !hasTargetData {
		return
	}

	// 异步执行额外上报，避免影响主流程
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Logger.ErrorfCtx(ctx, "AdditionalBatchDataReport panic recovered: %v", r)
			}
		}()

		if err := s.performAdditionalBatchReport(ctx, req); err != nil {
			logger.Logger.ErrorfCtx(ctx, "AdditionalBatchDataReport failed: %v", err)
		}
	}()
}

// shouldTriggerAdditionalReport 检查是否应该触发额外上报
func (s *DataReportService) shouldTriggerAdditionalReport(eventName, gameID, eventType string) bool {
	// 检查游戏ID是否匹配：配置中的目标游戏ID或kof-douyin游戏
	isTargetGame := gameID == config.GlobConfig.ExtraDataReport.TargetGameID ||
		gameID == constants.DuplicateReportGameIDKofDouyin

	// 当eventType不是ThinkingdataTrack时，都可以上报
	if eventType != constants.ThinkingdataTrack {
		return isTargetGame
	}

	// 对于ThinkingdataTrack类型，保持原有的前缀检查逻辑
	hasValidPrefix := strings.HasPrefix(eventName, constants.SDKEventPrefix) ||
		strings.HasPrefix(eventName, constants.ServerEventPrefix)
	return hasValidPrefix && isTargetGame
}

// performAdditionalReport 执行单条数据的额外上报
func (s *DataReportService) performAdditionalReport(ctx context.Context, req *bean.DataReportReq) error {
	accountID := req.UserID
	if req.RoleID != "" {
		accountID = req.RoleID
	}
	distinctID := req.DeviceID

	// 复制properties，避免修改原始数据
	properties := make(map[string]interface{})
	for k, v := range req.Properties {
		properties[k] = v
	}

	// 设置目标app_id和其他必要字段
	// 为kof-douyin游戏使用特定的app_id，其他游戏使用配置中的默认app_id
	if req.GameID == constants.DuplicateReportGameIDKofDouyin {
		properties[constants.PlatformAppIDKey] = constants.ExtraDataReportKofDouyinAppID
	} else {
		properties[constants.PlatformAppIDKey] = config.GlobConfig.ExtraDataReport.TargetThinkingDataAppID
	}
	properties["#uuid"] = req.UUID
	properties["#ip"] = req.IP
	if _, ok := properties["#time"]; !ok {
		properties["#time"] = time.Now().Format(constants.DayTimeSpecificFormat)
	}

	var err error
	switch req.EventType {
	case constants.ThinkingdataTrack:
		properties["game_id"] = req.GameID
		if req.IPRegionID != "" {
			properties["region_id"] = req.IPRegionID
		}
		err = s.analytics.Track(accountID, distinctID, req.EventName, properties)
	case constants.ThinkingdataUserSet:
		err = s.analytics.UserSet(accountID, distinctID, properties)
	case constants.ThinkingdataUserSetOnce:
		err = s.analytics.UserSetOnce(accountID, distinctID, properties)
	case constants.ThinkingdataUserAdd:
		err = s.analytics.UserAdd(accountID, distinctID, properties)
	case constants.ThinkingdataUserUnset:
		keys := make([]string, 0)
		for k := range properties {
			keys = append(keys, k)
		}
		err = s.analytics.UserUnset(accountID, distinctID, keys)
	case constants.ThinkingdataUserAppend:
		err = s.analytics.UserAppend(accountID, distinctID, properties)
	case constants.ThinkingdataUserDel:
		err = s.analytics.UserDelete(accountID, distinctID)
	default:
		logger.Logger.WarnfCtx(ctx, "AdditionalDataReport unsupported event type: %s", req.EventType)
		return nil
	}

	if err != nil {
		return err
	}

	logger.Logger.InfofCtx(ctx, "AdditionalDataReport success: eventName=%s, gameID=%s, targetAppID=%s",
		req.EventName, req.GameID, config.GlobConfig.ExtraDataReport.TargetThinkingDataAppID)
	return nil
}

// performAdditionalBatchReport 执行批量数据的额外上报
func (s *DataReportService) performAdditionalBatchReport(ctx context.Context, req *bean.BatchDataReportReq) error {
	var reportCount int

	for _, report := range req.DataReports {
		// 只处理符合条件的数据
		if !s.shouldTriggerAdditionalReport(report.EventName, req.GameID, report.EventType) {
			continue
		}

		// 构造单条数据上报请求
		dataReportReq := &bean.DataReportReq{
			Header: middleware.Header{
				UserID:   req.UserID,
				DeviceID: req.DeviceID,
				AppID:    req.AppID,
				GameID:   req.GameID,
			},
			RoleID:     report.RoleID,
			IP:         req.IP,
			IPRegionID: req.IPRegionID,
			EventName:  report.EventName,
			UUID:       report.UUID,
			EventType:  report.EventType,
			Properties: report.Properties,
		}

		// 执行额外上报
		if err := s.performAdditionalReport(ctx, dataReportReq); err != nil {
			logger.Logger.ErrorfCtx(ctx, "AdditionalBatchDataReport single report failed: eventName=%s, error=%v",
				report.EventName, err)
			// 继续处理其他数据，不因单条失败而中断
			continue
		}
		reportCount++
	}

	if reportCount > 0 {
		logger.Logger.InfofCtx(ctx, "AdditionalBatchDataReport completed: processed %d reports, gameID=%s, targetAppID=%s",
			reportCount, req.GameID, config.GlobConfig.ExtraDataReport.TargetThinkingDataAppID)
	}

	return nil
}

// AdditionalUploadReport 额外数据上报到数数科技（用于UploadReport相关方法）
// 当事件名称包含"server_"前缀且游戏ID为"kof-test"时，将相同数据额外上报到指定的app_id
func (s *DataReportService) AdditionalUploadReport(ctx context.Context, req *bean.UploadReport) {
	// 检查触发条件
	if !s.shouldTriggerAdditionalReport(req.EventName, req.GameID, req.EventType) {
		return
	}

	// 异步执行额外上报，避免影响主流程
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Logger.ErrorfCtx(ctx, "AdditionalUploadReport panic recovered: %v", r)
			}
		}()

		if err := s.performAdditionalUploadReport(ctx, req); err != nil {
			logger.Logger.ErrorfCtx(ctx, "AdditionalUploadReport failed: %v", err)
		}
	}()
}

// performAdditionalUploadReport 执行UploadReport的额外上报
func (s *DataReportService) performAdditionalUploadReport(ctx context.Context, req *bean.UploadReport) error {
	// 复制properties，避免修改原始数据
	properties := make(map[string]interface{})
	for k, v := range req.Properties {
		properties[k] = v
	}

	// 设置目标app_id和其他必要字段
	// 为kof-douyin游戏使用特定的app_id，其他游戏使用配置中的默认app_id
	if req.GameID == constants.DuplicateReportGameIDKofDouyin {
		properties[constants.PlatformAppIDKey] = constants.ExtraDataReportKofDouyinAppID
	} else {
		properties[constants.PlatformAppIDKey] = config.GlobConfig.ExtraDataReport.TargetThinkingDataAppID
	}
	properties["#uuid"] = req.UUID
	properties["#ip"] = req.ClientIP
	properties["#time"] = req.Timestamp

	accountID := req.AccountID
	distinctID := req.DistinctID

	var err error
	switch req.EventType {
	case constants.ThinkingdataTrack:
		if req.GameID != "" {
			properties["game_id"] = req.GameID
		}
		err = s.analytics.Track(accountID, distinctID, req.EventName, properties)
	case constants.ThinkingdataUserSet:
		err = s.analytics.UserSet(accountID, distinctID, properties)
	case constants.ThinkingdataUserSetOnce:
		err = s.analytics.UserSetOnce(accountID, distinctID, properties)
	case constants.ThinkingdataUserAdd:
		err = s.analytics.UserAdd(accountID, distinctID, properties)
	case constants.ThinkingdataUserUnset:
		keys := make([]string, 0)
		for k := range properties {
			keys = append(keys, k)
		}
		err = s.analytics.UserUnset(accountID, distinctID, keys)
	case constants.ThinkingdataUserAppend:
		err = s.analytics.UserAppend(accountID, distinctID, properties)
	case constants.ThinkingdataUserDel:
		err = s.analytics.UserDelete(accountID, distinctID)
	default:
		logger.Logger.WarnfCtx(ctx, "AdditionalUploadReport unsupported event type: %s", req.EventType)
		return nil
	}

	if err != nil {
		return err
	}

	logger.Logger.InfofCtx(ctx, "AdditionalUploadReport success: eventName=%s, gameID=%s, targetAppID=%s",
		req.EventName, req.GameID, config.GlobConfig.ExtraDataReport.TargetThinkingDataAppID)
	return nil
}
