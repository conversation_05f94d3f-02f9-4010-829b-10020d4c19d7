package service

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/bizerrors"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

var (
	_h5AdminAuthService *H5AdminAuthService
)

// H5AdminAuthService H5管理后台用户服务
type H5AdminAuthService struct{}

// SingletonH5AdminAuthService 获取 H5AdminAuthService 单例
func SingletonH5AdminAuthService() *H5AdminAuthService {
	if _h5AdminAuthService == nil {
		_h5AdminAuthService = &H5AdminAuthService{}
	}
	return _h5AdminAuthService
}

// calculateAge 计算用户年龄
// birthTime: 出生时间
// return: 年龄
func (s *H5AdminAuthService) CalculateAge(birthTime time.Time) int {
	now := time.Now()
	age := now.Year() - birthTime.Year()

	// 如果今年还没过生日，年龄减1
	if now.Month() < birthTime.Month() || (now.Month() == birthTime.Month() && now.Day() < birthTime.Day()) {
		age--
	}

	return age
}

// GetUserByUserID 根据用户ID查询用户信息
func (s *H5AdminAuthService) GetUserByUserID(ctx context.Context, userID string) (*model.H5AdminUser, error) {
	h5AdminUser := store.QueryDB().H5AdminUser
	user, err := h5AdminUser.WithContext(ctx).Where(h5AdminUser.UserID.Eq(userID)).First()
	if err != nil {
		return nil, err
	}
	return user, nil
}

// GetUserByUsername 根据用户名查询用户信息
func (s *H5AdminAuthService) GetUserByUsername(ctx context.Context, username string) (*model.H5AdminUser, error) {
	h5AdminUser := store.QueryDB().H5AdminUser
	user, err := h5AdminUser.WithContext(ctx).Where(h5AdminUser.Username.Eq(username)).First()
	if err != nil {
		return nil, err
	}
	return user, nil
}

// Register 用户注册
// ctx: 上下文
// req: 注册请求参数
// return: 错误信息
func (s *H5AdminAuthService) Register(ctx context.Context, req *bean.RegisterRequest) (*bean.RegisterResponse, error) {
	h5AdminUser := store.QueryDB().H5AdminUser

	user := &model.H5AdminUser{}
	// 检查用户名是否已存在
	existingUser, err := h5AdminUser.WithContext(ctx).Where(h5AdminUser.Username.Eq(req.Username)).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		// 密码加密
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
		if err != nil {
			logger.Logger.Errorf("密码加密失败: %v", err)
			return nil, err
		}

		// 创建用户
		user.UserID = util.UUID()
		user.Username = req.Username
		user.Password = string(hashedPassword)
		if err := h5AdminUser.WithContext(ctx).Create(user); err != nil {
			logger.Logger.Errorf("创建用户失败: %v", err)
			return nil, err
		}
	} else if err != nil {
		return nil, err
	}
	if existingUser != nil {
		logger.Logger.Warnf("用户名已存在: %s", req.Username)
		return nil, bizerrors.NewBizError(100101, "用户名已存在")
	}

	// query user.id
	user, err = h5AdminUser.WithContext(ctx).Where(h5AdminUser.Username.Eq(req.Username)).First()
	if err != nil {
		logger.Logger.Errorf("查询用户失败: %v", err)
		return nil, err
	}
	if user == nil {
		logger.Logger.Warnf("用户不存在: %s", req.Username)
		return nil, constants.ErrInvalidUsernameOrPassword
	}

	return &bean.RegisterResponse{
		UserID:         user.UserID,
		Username:       user.Username,
		IsRealNameAuth: user.IsRealNameAuth,
		IsMinors:       user.IsMinors,
	}, nil
}

// Login 用户登录
// ctx: 上下文
// req: 登录请求参数
// return: 登录响应和错误信息
func (s *H5AdminAuthService) Login(ctx context.Context, req *bean.LoginRequest) (*bean.LoginResponse, error) {
	h5AdminUser := store.QueryDB().H5AdminUser

	// 查询用户
	user, err := h5AdminUser.WithContext(ctx).Where(h5AdminUser.Username.Eq(req.Username)).First()
	if err != nil {
		logger.Logger.Errorf("查询用户失败: %v", err)
		return nil, constants.ErrInvalidUsernameOrPassword
	}
	if user == nil {
		logger.Logger.Warnf("用户不存在: %s", req.Username)
		return nil, constants.ErrInvalidUsernameOrPassword
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		logger.Logger.Warnf("密码验证失败: %s", req.Username)
		return nil, constants.ErrInvalidUsernameOrPassword
	}

	// 清除敏感信息
	user.Password = ""

	// 计算用户年龄
	age := 0
	if user.BirthDateAt > 0 {
		birthTime := time.UnixMilli(user.BirthDateAt)
		age = s.CalculateAge(birthTime)
	}

	return &bean.LoginResponse{
		User: bean.H5AdminUser{
			UserID:         user.UserID,
			Username:       user.Username,
			IsRealNameAuth: user.IsRealNameAuth,
			IsMinors:       user.IsMinors,
			CreatedAt:      user.CreatedAt,
			UpdatedAt:      user.UpdatedAt,
			Age:            age,
		},
	}, nil
}

// ValidateUserID 校验用户ID是否存在且合法
// ctx: 上下文
// userID: 用户ID
// return: 错误信息
func (s *H5AdminAuthService) ValidateUserID(ctx context.Context, userID string) error {
	h5AdminUser := store.QueryDB().H5AdminUser

	// 检查用户ID是否存在
	_, err := h5AdminUser.WithContext(ctx).Where(h5AdminUser.UserID.Eq(userID)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户ID不存在")
		}
		return err
	}

	return nil
}

// UpdateUserRealNameAuth 更新用户实名认证状态
// ctx: 上下文
// username: 用户名
// code: 实名认证返回码，0表示认证通过
// return: 错误信息
func (s *H5AdminAuthService) UpdateUserRealNameAuth(ctx context.Context, userID string, code string) error {
	isRealNameAuth := code == "0"
	h5AdminUser := store.QueryDB().H5AdminUser
	_, err := h5AdminUser.WithContext(ctx).
		Where(h5AdminUser.UserID.Eq(userID)).
		UpdateSimple(h5AdminUser.IsRealNameAuth.Value(isRealNameAuth))
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "更新用户实名认证状态失败: %v", err)
		return err
	}
	return nil
}

// UpdateUserMinorsStatus 更新用户未成年状态
// ctx: 上下文
// username: 用户名
// idCard: 身份证号
// return: 是否未成年, 错误信息
func (s *H5AdminAuthService) UpdateUserMinorsStatus(ctx context.Context, userID string, idCard string) (bool, int, error) {
	// 解析身份证号中的出生日期
	if len(idCard) != 18 {
		logger.Logger.WarnfCtx(ctx, "身份证号格式错误: %s", idCard)
		return false, 0, nil
	}

	// 从身份证号提取出生日期(YYYYMMDD)，第7位到第15位，索引从0开始则是6到14
	birthDateStr := idCard[6:14]
	year, _ := strconv.Atoi(birthDateStr[:4])
	month, _ := strconv.Atoi(birthDateStr[4:6])
	day, _ := strconv.Atoi(birthDateStr[6:])

	// 转换为time.Time类型
	birthDate := time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.Local)

	// 计算年龄
	now := time.Now()
	age := now.Year() - birthDate.Year()

	// 如果今年还没过生日，年龄减1
	if now.Month() < birthDate.Month() || (now.Month() == birthDate.Month() && now.Day() < birthDate.Day()) {
		age--
	}

	// 判断是否未成年
	isMinors := age < 18

	// 更新数据库中的未成年状态和出生日期
	h5AdminUser := store.QueryDB().H5AdminUser
	_, err := h5AdminUser.WithContext(ctx).
		Where(h5AdminUser.UserID.Eq(userID)).
		UpdateSimple(
			h5AdminUser.IsMinors.Value(isMinors),
			h5AdminUser.BirthDateAt.Value(birthDate.UnixMilli()),
		)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "更新用户未成年状态和出生日期失败: %v", err)
		return isMinors, 0, err
	}

	return isMinors, age, nil
}

// CheckRechargeLimit 检查用户充值额度
func (s *H5AdminAuthService) CheckRechargeLimit(ctx context.Context, req *bean.RechargeCheckRequest) (*bean.RechargeCheckResponse, error) {
	h5AdminUser := store.QueryDB().H5AdminUser
	h5AdminUserRechargeLimit := store.QueryDB().H5AdminUserRechargeLimit

	// 查询用户信息
	user, err := h5AdminUser.WithContext(ctx).Where(h5AdminUser.Username.Eq(req.Username)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}

	// 计算用户年龄
	age := -1 // 默认年龄为-1，表示未获取到或未实名
	if user.BirthDateAt > 0 {
		birthTime := time.UnixMilli(user.BirthDateAt)
		age = s.CalculateAge(birthTime)
	}

	// 获取当前年月
	currentTime := time.Now()
	yearMonth := currentTime.Format("200601")

	// 查询当月累计充值金额
	rechargeLimits, err := h5AdminUserRechargeLimit.WithContext(ctx).
		Where(h5AdminUserRechargeLimit.UserID.Eq(user.UserID)).
		Where(h5AdminUserRechargeLimit.YearMonth.Eq(yearMonth)).
		Find()

	var currentMonthAmount float64
	if err != nil {
		return nil, err
	}
	// 计算所有记录的总额
	for _, limit := range rechargeLimits {
		currentMonthAmount += limit.TotalAmount
	}

	var popupMessage string

	// 根据年龄段判断充值限制
	if age != -1 && age < 8 {
		// 小于8周岁，不可购买
		popupMessage = fmt.Sprintf("系统识别到您未满8周岁，根据国家新闻出版署《关于防止未成年人沉迷网络游戏的通知》及《关于进一步严格管理 切实防止未成年人沉迷网络游戏的通知》规定，游戏不得为未满8周岁的用户提供付费服务。您当前%d周岁，不可充值。", age)
		return &bean.RechargeCheckResponse{
			AllowRecharge: false,
			ShowPopup:     true,
			PopupMessage:  popupMessage,
		}, nil
	} else if age >= 8 && age < 16 {
		// 8周岁以上未满16周岁
		if req.RechargeAmount > 50 {
			// 单次超限
			popupMessage = fmt.Sprintf("系统识别到您未满16周岁，根据国家新闻出版署《关于防止未成年人沉迷网络游戏的通知》及《关于进一步严格管理 切实防止未成年人沉迷网络游戏的通知》规定，8周岁以上未满16周岁的用户，单次充值金额不得超过50元人民币，每月充值金额累计不得超过200元人民币。您当前%d周岁，单次充值不得超过50元。", age)
			return &bean.RechargeCheckResponse{
				AllowRecharge: false,
				ShowPopup:     true,
				PopupMessage:  popupMessage,
			}, nil
		}
		remainingAmount := 200 - currentMonthAmount
		if req.RechargeAmount > remainingAmount {
			// 累计超限
			popupMessage = fmt.Sprintf("系统识别到您未满16周岁，根据国家新闻出版署《关于防止未成年人沉迷网络游戏的通知》及《关于进一步严格管理 切实防止未成年人沉迷网络游戏的通知》规定，8周岁以上未满16周岁的用户，单次充值金额不得超过50元人民币，每月充值金额累计不得超过200元人民币。您当前%d周岁，本月还可充值%.2f元，本次充值已超限，不可充值。", age, remainingAmount)
			return &bean.RechargeCheckResponse{
				AllowRecharge: false,
				ShowPopup:     true,
				PopupMessage:  popupMessage,
			}, nil
		}
	} else if age >= 16 && age < 18 {
		// 16周岁以上未满18周岁
		if req.RechargeAmount > 100 {
			// 单次超限
			popupMessage = fmt.Sprintf("系统识别到您未满18周岁，根据国家新闻出版署《关于防止未成年人沉迷网络游戏的通知》及《关于进一步严格管理 切实防止未成年人沉迷网络游戏的通知》规定，16周岁以上未满18周岁的用户，单次充值金额不得超过100元人民币，每月充值金额累计不得超过400元人民币。您当前%d周岁，单次充值不得超过100元。", age)
			return &bean.RechargeCheckResponse{
				AllowRecharge: false,
				ShowPopup:     true,
				PopupMessage:  popupMessage,
			}, nil
		}
		remainingAmount := 400 - currentMonthAmount
		if req.RechargeAmount > remainingAmount {
			// 累计超限
			popupMessage = fmt.Sprintf("系统识别到您未满18周岁，根据国家新闻出版署《关于防止未成年人沉迷网络游戏的通知》及《关于进一步严格管理 切实防止未成年人沉迷网络游戏的通知》规定，16周岁以上未满18周岁的用户，单次充值金额不得超过100元人民币，每月充值金额累计不得超过400元人民币。您当前%d周岁，本月还可充值%.2f元，本次充值已超限，不可充值。", age, remainingAmount)
			return &bean.RechargeCheckResponse{
				AllowRecharge: false,
				ShowPopup:     true,
				PopupMessage:  popupMessage,
			}, nil
		}
	}

	// 所有检查通过
	// 充值成功，将充值金额存储到recharge_limit表中
	// 当前时间戳（毫秒）
	now := time.Now().UnixMilli()
	// 记录不存在，创建新记录
	newRecord := &model.H5AdminUserRechargeLimit{
		UserID:      user.UserID,
		YearMonth:   yearMonth,
		TotalAmount: req.RechargeAmount,
		CreatedAt:   now,
		UpdatedAt:   now,
	}
	err = h5AdminUserRechargeLimit.WithContext(ctx).Create(newRecord)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "创建充值限额记录失败: %v", err)
		// 即使创建失败，仍然允许用户充值
	} else {
		logger.Logger.InfofCtx(ctx, "创建充值限额记录成功, 用户ID: %s, 金额: %.2f", user.UserID, req.RechargeAmount)
	}

	return &bean.RechargeCheckResponse{
		AllowRecharge: true,
		ShowPopup:     false,
	}, nil
}

func (s *H5AdminAuthService) GetTimestamp(ctx context.Context, isSpecifyTime bool, timestampDiff int64) (*bean.TimestampResp, error) {
	h5AdminConfig := store.QueryDB().H5AdminConfig

	// 首先查询表中是否有配置
	config, err := h5AdminConfig.WithContext(ctx).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// 查询出错但不是因为记录不存在
		logger.Logger.ErrorfCtx(ctx, "查询 H5AdminConfig 记录失败: %v", err)
		return &bean.TimestampResp{
			Timestamp: time.Now().UnixMilli(),
		}, nil
	}

	// 处理传入 isSpecifyTime = true 的情况，需要持久化 timestampDiff
	if isSpecifyTime {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 配置不存在，创建新配置
			newConfig := &model.H5AdminConfig{
				IsSpecifyTime: true,
				TimestampDiff: timestampDiff,
			}
			err = h5AdminConfig.WithContext(ctx).Create(newConfig)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "创建 H5AdminConfig 记录失败: %v", err)
			}
		} else {
			// 配置存在，更新配置
			_, err = h5AdminConfig.WithContext(ctx).
				Where(h5AdminConfig.ID.Eq(config.ID)).
				UpdateSimple(
					h5AdminConfig.IsSpecifyTime.Value(true),
					h5AdminConfig.TimestampDiff.Value(timestampDiff),
				)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "更新 H5AdminConfig 记录失败: %v", err)
			}
		}
		// 返回使用传入的 timestampDiff 计算的时间戳
		return &bean.TimestampResp{
			Timestamp: time.Now().UnixMilli() - timestampDiff,
		}, nil
	}

	// 处理传入 isSpecifyTime = false 的情况
	if config != nil && config.IsSpecifyTime {
		// 表中存在配置且 isSpecifyTime = true，使用表中的配置
		return &bean.TimestampResp{
			Timestamp: time.Now().UnixMilli() - config.TimestampDiff,
		}, nil
	}

	// 表中不存在配置或 isSpecifyTime = false，直接返回当前时间戳
	// 如果配置存在且 isSpecifyTime = true，则更新为 false
	if config != nil && config.IsSpecifyTime {
		_, err = h5AdminConfig.WithContext(ctx).
			Where(h5AdminConfig.ID.Eq(config.ID)).
			UpdateSimple(h5AdminConfig.IsSpecifyTime.Value(false))
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "更新 H5AdminConfig 记录失败: %v", err)
		}
	}

	return &bean.TimestampResp{
		Timestamp: time.Now().UnixMilli(),
	}, nil
}
