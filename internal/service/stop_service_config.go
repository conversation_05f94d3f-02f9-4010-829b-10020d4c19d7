package service

import (
	"context"
	"errors"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"gorm.io/gorm"
)

var (
	_stopServiceConfigOnce    sync.Once
	_stopServiceConfigService *StopServiceConfigService
)

type StopServiceConfigService struct{}

func SingletonStopServiceConfigService() *StopServiceConfigService {
	_stopServiceConfigOnce.Do(func() {
		_stopServiceConfigService = &StopServiceConfigService{}
	})
	return _stopServiceConfigService
}

// GetStopServiceConfigByGameIDAndPlatform 根据游戏ID和平台类型获取停服配置
func (s *StopServiceConfigService) GetStopServiceConfigByGameIDAndPlatform(ctx context.Context, gameID, platformType string) (*model.MStopServiceConfig, error) {
	stopServiceConfig := store.QueryDB().MStopServiceConfig
	return stopServiceConfig.WithContext(ctx).
		Where(stopServiceConfig.GameID.Eq(gameID)).
		Where(stopServiceConfig.PlatformType.Eq(platformType)).
		Where(stopServiceConfig.IsDeleted.Is(false)).
		First()
}

// CheckNewUserRegistrationEnabled 检查是否允许新用户注册
func (s *StopServiceConfigService) CheckNewUserRegistrationEnabled(ctx context.Context, gameID, platformType string) (bool, error) {
	config, err := s.GetStopServiceConfigByGameIDAndPlatform(ctx, gameID, platformType)
	if err != nil {
		// 如果找不到配置，默认允许注册
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return true, nil
		}
		return false, err
	}

	// 返回是否允许注册（DisableNewUserRegister为true表示禁止，所以取反）
	return !config.DisableNewUserRegister, nil
}

// CheckRechargeEnabled 检查是否允许充值
func (s *StopServiceConfigService) CheckRechargeEnabled(ctx context.Context, gameID, platformType string) (bool, error) {
	config, err := s.GetStopServiceConfigByGameIDAndPlatform(ctx, gameID, platformType)
	if err != nil {
		// 如果找不到配置，默认允许充值
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return true, nil
		}
		return false, err
	}

	// 返回是否允许充值（DisableRecharge为true表示禁止，所以取反）
	return !config.DisableRecharge, nil
}
