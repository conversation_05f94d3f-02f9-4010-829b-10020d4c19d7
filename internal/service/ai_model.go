package service

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/go-resty/resty/v2"
)

var (
	_aiModelOnce    sync.Once
	_aiModelService *AIModelService
)

// AIModelService AI模型服务
type AIModelService struct {
	client *resty.Client
}

// SingletonAIModelService 获取AI模型服务单例
func SingletonAIModelService() *AIModelService {
	_aiModelOnce.Do(func() {
		_aiModelService = &AIModelService{
			client: resty.New().SetTimeout(5 * time.Second),
		}
	})
	return _aiModelService
}

// GLM4FlashRequest 智谱GLM-4-Flash模型请求结构
type GLM4FlashRequest struct {
	Model    string                  `json:"model"`
	Messages []GLM4FlashMessageEntry `json:"messages"`
}

// GLM4FlashMessageEntry 智谱GLM-4-Flash消息条目
type GLM4FlashMessageEntry struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// GLM4FlashResponse 智谱GLM-4-Flash模型响应结构
type GLM4FlashResponse struct {
	ID      string `json:"id"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Usage   struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
	Choices []struct {
		Index        int    `json:"index"`
		FinishReason string `json:"finish_reason"`
		Message      struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
	} `json:"choices"`
}

// GetSystemPrompt 获取系统提示词
func (s *AIModelService) GetSystemPrompt(ctx context.Context) (string, error) {
	q := store.QueryDB().MQuestionSystemPrompt
	prompt, err := q.WithContext(ctx).
		Where(q.IsDeleted.Is(false)).
		Order(q.UpdatedAt.Desc()).
		First()

	if err != nil {
		return "", fmt.Errorf("获取系统提示词失败: %w", err)
	}

	return prompt.Content, nil
}

// GetQuestionLibrary 获取问题库
func (s *AIModelService) GetQuestionLibrary(ctx context.Context, gameID string) ([]*model.MQuestionLibrary, error) {
	q := store.QueryDB().MQuestionLibrary
	questions, err := q.WithContext(ctx).
		Where(
			q.GameID.Eq(gameID),
			q.IsDeleted.Is(false),
		).
		Find()

	if err != nil {
		return nil, fmt.Errorf("获取问题库失败: %w", err)
	}

	return questions, nil
}

// FindExactMatch 查找完全匹配的问题
func (s *AIModelService) FindExactMatch(ctx context.Context, gameID, userQuestion string) (*model.MQuestionLibrary, error) {
	q := store.QueryDB().MQuestionLibrary
	question, err := q.WithContext(ctx).
		Where(
			q.GameID.Eq(gameID),
			q.Question.Eq(userQuestion),
			q.IsDeleted.Is(false),
		).
		First()

	if err != nil {
		return nil, nil // 没有找到完全匹配的问题，返回nil而不是错误
	}

	return question, nil
}

// CallGLM4Flash 调用智谱GLM-4-Flash模型
func (s *AIModelService) CallGLM4Flash(ctx context.Context, systemPrompt, userQuestion string, questions []*model.MQuestionLibrary) (string, error) {
	// 构建问题库字符串，过滤掉答案中包含 <a> 标签的问题
	var questionLibrary strings.Builder
	var filteredCount int
	var totalCount int
	var validIndex int = 1 // 用于保持过滤后问题的连续编号

	for _, q := range questions {
		totalCount++
		// 检查答案是否包含 <a> 标签或 <a href 标签，如果包含则跳过
		if strings.Contains(q.Answer, "<a>") || strings.Contains(q.Answer, "<a href") {
			filteredCount++
			logger.Logger.InfofCtx(ctx, "过滤带引用答案的问题: %s", q.Question)
			continue
		}

		// 只添加答案不包含标签的问题
		questionLibrary.WriteString(fmt.Sprintf("%d-%s ", validIndex, q.Question))
		validIndex++
	}

	// 记录过滤情况的日志
	logger.Logger.InfofCtx(ctx, "问题库过滤情况: 总问题数 %d, 过滤问题数 %d, 剩余问题数 %d",
		totalCount, filteredCount, totalCount-filteredCount)

	// 替换系统提示词中的问题库占位符
	systemPromptWithQuestions := strings.Replace(systemPrompt, "{问题库}", questionLibrary.String(), 1)

	// 从配置中获取模型信息
	modelName := config.GlobConfig.AIModel.Model
	if modelName == "" {
		modelName = "GLM-4-Flash-250414" // 默认模型
	}

	// 构建请求
	req := GLM4FlashRequest{
		Model: modelName,
		Messages: []GLM4FlashMessageEntry{
			{
				Role:    "system",
				Content: systemPromptWithQuestions,
			},
			{
				Role:    "user",
				Content: userQuestion,
			},
		},
	}

	// 从配置中获取API URL
	apiURL := config.GlobConfig.AIModel.BaseURL
	if apiURL == "" {
		apiURL = "https://www.bigmodel.cn/api/paas/v4/chat/completions" // 默认URL
	}

	// 调用API
	resp, err := s.client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Authorization", "Bearer "+config.GlobConfig.AIModel.APIKey).
		SetBody(req).
		Post(apiURL)

	if err != nil {
		return "", fmt.Errorf("调用GLM-4-Flash模型失败: %w", err)
	}

	if resp.StatusCode() != 200 {
		return "", fmt.Errorf("调用GLM-4-Flash模型失败，状态码: %d, 响应: %s", resp.StatusCode(), resp.String())
	}

	// 解析响应
	var glmResp GLM4FlashResponse
	if err := json.Unmarshal(resp.Body(), &glmResp); err != nil {
		return "", fmt.Errorf("解析GLM-4-Flash响应失败: %w", err)
	}

	if len(glmResp.Choices) == 0 {
		return "", fmt.Errorf("GLM-4-Flash响应中没有选择")
	}

	// 返回模型回答
	return glmResp.Choices[0].Message.Content, nil
}

// FindMatchingQuestion 根据模型回答找到匹配的问题
func (s *AIModelService) FindMatchingQuestion(ctx context.Context, gameID, modelAnswer string, questions []*model.MQuestionLibrary) (*model.MQuestionLibrary, error) {
	// 清理模型回答，去除可能的编号和符号
	cleanedAnswer := strings.TrimSpace(modelAnswer)

	// 如果回答是"无"，表示没有匹配的问题
	if cleanedAnswer == "无" {
		return nil, nil
	}

	// 处理可能的编号格式，如 "1-问题内容" 或 "1. 问题内容"
	re := regexp.MustCompile(`^\d+[\-\.\s]+(.+)$`)
	matches := re.FindStringSubmatch(cleanedAnswer)
	if len(matches) > 1 {
		// 提取编号后的实际问题内容
		cleanedAnswer = strings.TrimSpace(matches[1])
		logger.Logger.InfofCtx(ctx, "从编号格式中提取问题: %s", cleanedAnswer)
	}

	// 尝试直接匹配问题
	for _, q := range questions {
		if strings.Contains(cleanedAnswer, q.Question) {
			return q, nil
		}
	}

	// 如果没有直接匹配，尝试查询数据库
	q := store.QueryDB().MQuestionLibrary
	question, err := q.WithContext(ctx).
		Where(
			q.GameID.Eq(gameID),
			q.Question.Eq(cleanedAnswer),
			q.IsDeleted.Is(false),
		).
		First()

	if err != nil {
		logger.Logger.WarnfCtx(ctx, "未找到匹配的问题，模型回答: %s", cleanedAnswer)
		return nil, nil
	}

	return question, nil
}

// ProcessRichText 处理富文本内
