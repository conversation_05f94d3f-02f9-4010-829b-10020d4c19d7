package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/go-resty/resty/v2"
	"gorm.io/gorm"
)

var (
	_douyinFeedGameOnce    sync.Once
	_douyinFeedGameService *DouyinFeedGameService
)

type DouyinFeedGameService struct {
	client *resty.Client
}

type GameSceneAPIRequest struct {
	GameID string `json:"game_id,omitempty"`
	UserID string `json:"user_id,omitempty"`
}

type GameSceneAPIResponse struct {
	Code int32          `json:"code"`
	Msg  string         `json:"message"`
	Data *GameSceneData `json:"data"`
}

type GameSceneData struct {
	Scenes []*GameSceneInfo `json:"scenes"`
}

type GameSceneInfo struct {
	SceneType  int64    `json:"scene_type"`
	IsReady    bool     `json:"is_ready"`
	ContentIDs []string `json:"content_ids"`
	Extra      string   `json:"extra"`
}

func SingletonDouyinFeedGameService() *DouyinFeedGameService {
	_douyinFeedGameOnce.Do(func() {
		_douyinFeedGameService = &DouyinFeedGameService{
			client: resty.New().SetTimeout(5 * time.Second),
		}
	})
	return _douyinFeedGameService
}

func (s *DouyinFeedGameService) QueryUserScenes(ctx context.Context, openID, appID string) ([]*bean.DouyinFeedGameSceneInfo, error) {
	logger.Logger.InfofCtx(ctx, "DouyinFeedGameService QueryUserScenes start: openid=%s, appid=%s", openID, appID)

	gameScenes, err := s.callGameSceneAPI(ctx, openID, appID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "DouyinFeedGameService callGameSceneAPI failed: %v", err)
		return nil, fmt.Errorf("game scene API call failed: %w", err)
	}

	scenes := make([]*bean.DouyinFeedGameSceneInfo, 0)
	for _, gameScene := range gameScenes {
		if gameScene.IsReady {
			sceneInfo := &bean.DouyinFeedGameSceneInfo{
				Scene:      gameScene.SceneType,
				ContentIDs: gameScene.ContentIDs,
				Extra:      gameScene.Extra,
			}
			scenes = append(scenes, sceneInfo)
		}
	}

	logger.Logger.InfofCtx(ctx, "DouyinFeedGameService QueryUserScenes result: openid=%s, scenes_count=%d",
		openID, len(scenes))

	return scenes, nil
}

func (s *DouyinFeedGameService) callGameSceneAPI(ctx context.Context, openID, appID string) ([]*GameSceneInfo, error) {
	// 根据appID查询游戏信息和场景API地址
	gameInfo, apiURL, err := s.getGameInfoAndSceneURL(ctx, appID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "DouyinFeedGameService getGameInfoAndSceneURL failed: appid=%s, err=%v", appID, err)
		return nil, fmt.Errorf("failed to get game info and scene URL: %w", err)
	}

	// 通过openID和appID查询user_id
	userID, err := s.getUserIDByOpenIDAndAppID(ctx, openID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "DouyinFeedGameService getUserIDByOpenIDAndAppID failed: openid=%s, appid=%s, err=%v", openID, appID, err)
		return nil, fmt.Errorf("failed to get user_id: %w", err)
	}

	reqData := &GameSceneAPIRequest{
		GameID: gameInfo.GameID,
		UserID: userID,
	}

	logger.Logger.InfofCtx(ctx, "DouyinFeedGameService calling game scene API: url=%s, gameID=%s", apiURL, gameInfo.GameID)

	// 生成签名验证相关的请求头
	headers, err := s.generateSignHeaders(ctx, gameInfo.GameID, gameInfo.Secret, reqData)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "DouyinFeedGameService generateSignHeaders failed: gameID=%s, err=%v", gameInfo.GameID, err)
		return nil, fmt.Errorf("failed to generate sign headers: %w", err)
	}

	// 构建HTTP请求 - 使用Clone确保并发安全
	request := s.client.Clone().R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(reqData)

	// 添加签名相关的请求头
	for key, value := range headers {
		request.SetHeader(key, value)
	}

	resp, err := request.Post(apiURL)
	if err != nil {
		return nil, fmt.Errorf("http request failed: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "DouyinFeedGameService game scene API response: status=%d, body=%s", resp.StatusCode(), string(resp.Body()))

	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("API returned non-200 status: %d, body: %s", resp.StatusCode(), string(resp.Body()))
	}

	var apiResp GameSceneAPIResponse
	if err := json.Unmarshal(resp.Body(), &apiResp); err != nil {
		return nil, fmt.Errorf("unmarshal response failed: %w", err)
	}

	if apiResp.Code != 0 {
		return nil, fmt.Errorf("API returned error code: %d, message: %s", apiResp.Code, apiResp.Msg)
	}

	if apiResp.Data == nil {
		return []*GameSceneInfo{}, nil
	}

	return apiResp.Data.Scenes, nil
}

// getGameInfoAndSceneURL 根据appID查询游戏信息和场景API地址
func (s *DouyinFeedGameService) getGameInfoAndSceneURL(ctx context.Context, appID string) (*model.MGame, string, error) {
	// 首先通过appID查询抖音配置获取gameID
	douyinConfig := store.QueryDB().AConfigDouyin
	douyinInfo, err := douyinConfig.WithContext(ctx).
		Where(douyinConfig.AppID.Eq(appID)).
		Where(douyinConfig.IsDeleted.Zero()).
		First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.WarnfCtx(ctx, "DouyinFeedGameService getGameInfoAndSceneURL: 未找到appID对应的抖音配置, appID: %s", appID)
		return nil, "", fmt.Errorf("douyin config not found for appID: %s", appID)
	} else if err != nil {
		logger.Logger.WarnfCtx(ctx, "DouyinFeedGameService getGameInfoAndSceneURL: 查询抖音配置失败, appID: %s, err: %v", appID, err)
		return nil, "", fmt.Errorf("failed to query douyin config: %w", err)
	}

	// 根据gameID查询游戏信息
	game := store.QueryDB().MGame
	gameInfo, err := game.WithContext(ctx).
		Where(game.GameID.Eq(douyinInfo.GameID)).
		Where(game.IsDeleted.Zero()).
		First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.WarnfCtx(ctx, "DouyinFeedGameService getGameInfoAndSceneURL: 未找到游戏信息, gameID: %s", douyinInfo.GameID)
		return nil, "", fmt.Errorf("game not found for gameID: %s", douyinInfo.GameID)
	} else if err != nil {
		logger.Logger.WarnfCtx(ctx, "DouyinFeedGameService getGameInfoAndSceneURL: 查询游戏信息失败, gameID: %s, err: %v", douyinInfo.GameID, err)
		return nil, "", fmt.Errorf("failed to query game info: %w", err)
	}

	// 获取场景API地址，优先使用数据库中的配置
	apiURL := gameInfo.GameSceneURL
	if apiURL == "" {
		logger.Logger.WarnfCtx(ctx, "DouyinFeedGameService getGameInfoAndSceneURL: 数据库中game_scene_url为空，使用配置文件默认值, gameID: %s, fallback_url: %s", gameInfo.GameID, apiURL)
	}

	logger.Logger.DebugfCtx(ctx, "DouyinFeedGameService getGameInfoAndSceneURL: 查询成功, gameID: %s, apiURL: %s", gameInfo.GameID, apiURL)
	return gameInfo, apiURL, nil
}

func (s *DouyinFeedGameService) getFallbackScenes(ctx context.Context, openID, appID string) []*bean.DouyinFeedGameSceneInfo {
	logger.Logger.WarnfCtx(ctx, "DouyinFeedGameService using fallback scenes for openid=%s, appid=%s", openID, appID)
	scenes := make([]*bean.DouyinFeedGameSceneInfo, 0)
	scenes = append(scenes, &bean.DouyinFeedGameSceneInfo{
		Scene:      bean.SceneTypeOfflineReward,
		ContentIDs: []string{"CONTENT_OFFLINE_REWARD_FALLBACK"},
		Extra:      "fallback",
	})
	return scenes
}

// generateSignHeaders 生成签名验证相关的请求头
func (s *DouyinFeedGameService) generateSignHeaders(ctx context.Context, gameID, gameSecret string, reqData *GameSceneAPIRequest) (map[string]string, error) {
	// 生成当前时间戳
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)

	// 从Redis获取游戏对应的密钥
	var redisSecret string
	var err error
	secretKey := fmt.Sprintf(constants.SystemSecret, gameID)

	// 尝试从Redis获取密钥，如果失败则使用数据库密钥作为降级
	if redis.Redis() != nil {
		redisSecret, err = redis.Get(ctx, secretKey)
		if err != nil {
			logger.Logger.DebugfCtx(ctx, "DouyinFeedGameService generateSignHeaders: 从Redis获取密钥失败, gameID: %s, err: %v", gameID, err)
			redisSecret = gameSecret
			logger.Logger.DebugfCtx(ctx, "DouyinFeedGameService generateSignHeaders: 使用数据库密钥作为降级, gameID: %s", gameID)
		}
	} else {
		// Redis未初始化，直接使用数据库密钥
		redisSecret = gameSecret
		logger.Logger.DebugfCtx(ctx, "DouyinFeedGameService generateSignHeaders: Redis未初始化，使用数据库密钥, gameID: %s", gameID)
	}

	// 序列化请求体用于签名计算
	reqBody, err := json.Marshal(reqData)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "DouyinFeedGameService generateSignHeaders: 序列化请求体失败, gameID: %s, err: %v", gameID, err)
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	// 使用middleware.GenSign生成签名
	sign := middleware.GenSign(gameID, redisSecret, timestamp, reqBody)

	headers := map[string]string{
		"game_id":   gameID,
		"timestamp": timestamp,
		"sign":      sign,
	}

	logger.Logger.DebugfCtx(ctx, "DouyinFeedGameService generateSignHeaders: 生成签名成功, gameID: %s, timestamp: %s", gameID, timestamp)
	return headers, nil
}

// getUserIDByOpenIDAndAppID 通过openID和appID查询user_id
func (s *DouyinFeedGameService) getUserIDByOpenIDAndAppID(ctx context.Context, openID string) (string, error) {
	userDouyin := store.QueryDB().AUserDouyin
	user, err := userDouyin.WithContext(ctx).
		Where(userDouyin.OpenID.Eq(openID)).
		Where(userDouyin.IsDeleted.Is(false)).
		First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.WarnfCtx(ctx, "DouyinFeedGameService getUserIDByOpenIDAndAppID: 未找到用户信息, openID: %s", openID)
		return "", fmt.Errorf("user not found for openID: %s", openID)
	} else if err != nil {
		logger.Logger.WarnfCtx(ctx, "DouyinFeedGameService getUserIDByOpenIDAndAppID: 查询用户信息失败, openID: %s,, err: %v", openID, err)
		return "", fmt.Errorf("failed to query user info: %w", err)
	}

	logger.Logger.DebugfCtx(ctx, "DouyinFeedGameService getUserIDByOpenIDAndAppID: 查询成功, openID: %s, userID: %s", openID, user.UserID)
	return user.UserID, nil
}
