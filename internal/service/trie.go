package service

import (
	"strings"
	"sync"
	"unicode"
)

var (
	_trieOnce    sync.Once
	_trieService *TrieService
)

type TrieNode struct {
	Children map[rune]*TrieNode
	IsEnd    bool
}

type TrieService struct {
	trieNode *TrieNode
}

func SingletonTrieService() *TrieService {
	_trieOnce.Do(func() {
		_trieService = &TrieService{
			trieNode: &TrieNode{Children: make(map[rune]*TrieNode)},
		}
	})
	return _trieService
}

func (s *TrieService) AddWord(word string) {
	curr := s.trieNode
	for _, char := range word {
		if _, ok := curr.Children[char]; !ok {
			curr.Children[char] = &TrieNode{Children: make(map[rune]*TrieNode)}
		}
		curr = curr.Children[char]
	}
	curr.IsEnd = true
}

// Clear 清空字典树，重置为初始状态
func (s *TrieService) Clear() {
	s.trieNode = &TrieNode{Children: make(map[rune]*TrieNode)}
}

func (s *TrieService) CheckAndReplace(sentence string) string {
	var builder strings.Builder
	runes := []rune(sentence)
	i := 0
	for i < len(runes) {
		match, length := s.matchFromIndex(runes, i)
		if match {
			builder.WriteString(strings.Repeat("*", length))
			i += length // Skip over the matched phrase
		} else {
			builder.WriteRune(runes[i])
			i++ // Proceed normally
		}
	}
	return builder.String()
}

func (s *TrieService) CheckAndReplaceByCase(sentence string, ignoreCase int32) string {
	var builder strings.Builder
	runes := []rune(sentence)
	i := 0
	for i < len(runes) {
		match, length := s.matchFromIndexByCase(runes, i, ignoreCase)
		if match {
			builder.WriteString(strings.Repeat("*", length))
			i += length // Skip over the matched phrase
		} else {
			builder.WriteRune(runes[i])
			i++ // Proceed normally
		}
	}
	return builder.String()
}

func (s *TrieService) matchFromIndex(runes []rune, index int) (bool, int) {
	curr := s.trieNode
	match := false
	length := 0
	for i := index; i < len(runes) && curr != nil; i++ {
		next, exists := curr.Children[runes[i]]
		if !exists {
			break
		}
		length++
		curr = next
		if curr.IsEnd {
			match = true
			break
		}
	}
	return match && length > 0, length
}

func (s *TrieService) matchFromIndexByCase(runes []rune, index int, ignoreCase int32) (bool, int) {
	curr := s.trieNode
	match := false
	length := 0
	for i := index; i < len(runes) && curr != nil; i++ {
		r := runes[i]
		// 如果忽略大小写，则将字符转换为小写
		if ignoreCase == 1 {
			r = unicode.ToLower(r)
		}

		next, exists := curr.Children[r]
		if !exists {
			break
		}
		length++
		curr = next
		if curr.IsEnd {
			match = true
			break
		}
	}
	return match && length > 0, length
}

func (s *TrieService) Check(sentence string) []string {
	var results []string
	runes := []rune(sentence)
	i := 0
	for i < len(runes) {
		match, word := s.matchFromWord(runes, i)
		if match {
			results = append(results, word)
			i += len([]rune(word))
		} else {
			i++
		}
	}
	return results
}

func (s *TrieService) CheckByCase(sentence string, ignoreCase int32) []string {
	var results []string
	runes := []rune(sentence)
	i := 0
	for i < len(runes) {
		match, word := s.matchFromWordByCase(runes, i, ignoreCase)
		if match {
			results = append(results, word)
			i += len([]rune(word))
		} else {
			i++
		}
	}
	return results
}

func (s *TrieService) matchFromWord(runes []rune, index int) (bool, string) {
	curr := s.trieNode
	matchWord := ""
	for i := index; i < len(runes) && curr != nil; i++ {
		next, exists := curr.Children[runes[i]]
		if !exists {
			break
		}
		curr = next
		matchWord += string(runes[i])
		if curr.IsEnd {
			break
		}
	}
	if len(matchWord) > 0 && curr.IsEnd {
		return true, matchWord
	}
	return false, ""
}

func (s *TrieService) matchFromWordByCase(runes []rune, index int, ignoreCase int32) (bool, string) {
	curr := s.trieNode
	matchWord := ""
	for i := index; i < len(runes) && curr != nil; i++ {
		r := runes[i]
		// 如果忽略大小写，则将字符转换为小写
		if ignoreCase == 1 {
			r = unicode.ToLower(r)
		}
		next, exists := curr.Children[r]
		if !exists {
			break
		}
		curr = next
		matchWord += string(runes[i]) // 保持原始大小写
		if curr.IsEnd {
			break
		}
	}
	if len(matchWord) > 0 && curr.IsEnd {
		return true, matchWord
	}
	return false, ""
}
