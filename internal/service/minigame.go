package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/bizerrors"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/https"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/go-resty/resty/v2"
	"gorm.io/gorm"
)

var (
	_minigameOnce    sync.Once
	_minigameService *MinigameService
)

type MinigameService struct {
	client            *resty.Client
	wechatHttpService *https.WechatHttpService
}

func SingletonMinigameService() *MinigameService {
	_minigameOnce.Do(func() {
		_minigameService = &MinigameService{
			client:            resty.New(),
			wechatHttpService: https.SingletonWechatHttpService(),
		}
	})
	return _minigameService
}

var (
	accessTokenUrl   = "/cgi-bin/stable_token" // 稳定版接口调用凭据
	ticketUrl        = "/cgi-bin/ticket/getticket"
	accessType       = "client_credential"
	code2SessionUrl  = "/sns/jscode2session" // 获取用户信息
	codeAuthType     = "authorization_code"
	customServiceUrl = "/cgi-bin/message/custom/send"

	// /wxa/business/getuserencryptkey
	getUserEncryptKeyUrl = "/wxa/business/getuserencryptkey"
)

// GetAccessToken 获取access_token
func (s *MinigameService) GetAccessToken(ctx context.Context, appID, secret string) (*bean.AccessTokenRes, error) {
	result := &bean.AccessTokenRes{}

	resp, err := s.client.R().
		SetContext(ctx).
		SetBody(map[string]string{
			"appid":      appID,
			"secret":     secret,
			"grant_type": accessType,
		}).
		Post(config.GlobConfig.Minigame.BaseURL + accessTokenUrl)
	if err != nil {
		logger.Logger.Errorf("Service GetAccessToken: 调用失败, 请检查接口 err: %s", err.Error())
		return nil, err
	}
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		logger.Logger.Errorf("Service GetAccessToken: Unmarshal err: %s", err.Error())
		return nil, err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("Service GetAccessToken: 获取seesion_key失败，状态码: %s", resp.Status())
		return nil, fmt.Errorf("seesion_key fail，https status code: %s", resp.Status())
	}
	if result.ErrCode != 0 {
		// logger.Logger.Errorf("Service GetAccessToken: 成功调用，但获取api结果失败，错误信息: %v", result)
		return nil, fmt.Errorf("success call, but get api session fail app_id: %s, :%v", appID, result)
	}
	return result, nil
}

// GetJSAPITicket jsapi ticket
func (s *MinigameService) GetJSAPITicket(ctx context.Context, accessToken string) (*bean.JSAPITicket, error) {
	result := &bean.JSAPITicket{}

	resp, err := s.client.R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"access_token": accessToken,
			"type":         "jsapi",
		}).
		Get(config.GlobConfig.Minigame.BaseURL + ticketUrl)
	if err != nil {
		logger.Logger.Errorf("Service GetJSAPITicket: 调用失败, 请检查接口 err: %s", err.Error())
		return nil, err
	}
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		logger.Logger.Errorf("Service GetJSAPITicket: Unmarshal err: %s", err.Error())
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("Service GetJSAPITicket: 获取seesion_key失败，状态码: %s", resp.Status())
		return nil, err
	}
	if result.ErrCode != 0 {
		logger.Logger.Errorf("Service GetJSAPITicket: 成功调用，但获取api结果失败，错误信息: %v", result)
		return nil, err
	}
	return result, nil
}

// IsGameIDExist 判断游戏ID是否存在
func (s *MinigameService) IsGameIDExist(ctx context.Context, gameID string) (bool, error) {
	game := store.QueryDB().MGame
	count, err := game.WithContext(ctx).Where(game.GameID.Eq(gameID)).Where(game.IsDeleted.Zero()).Count()
	if err != nil {
		logger.Logger.Errorf("Service MinigameService: check gameID err: %s", err.Error())
		return false, err
	}
	if count == 0 {
		logger.Logger.Errorf("Service Minigame: minigame gameID %s not found", gameID)
		return false, constants.ErrGameIDNotExist
	}
	return true, nil
}

// GetMinigameConfigs 获取所有游戏配置
func (s *MinigameService) GetMinigameConfigs(ctx context.Context) ([]*model.AConfigMinigame, error) {
	configMinigame := store.QueryDB().AConfigMinigame
	configCtx := configMinigame.WithContext(ctx)
	configInfo, err := configCtx.Where(configCtx.Where(configMinigame.IsDeleted.Zero())).Find()
	if err != nil {
		return nil, err
	}
	return configInfo, nil
}

// GetMiniprogramConfigs 获取小程序配置
func (s *MinigameService) GetMiniprogramConfig(ctx context.Context) (*model.AConfigMiniprogram, error) {
	configMiniprogram := store.QueryDB().AConfigMiniprogram
	configCtx := configMiniprogram.WithContext(ctx)
	configInfo, err := configCtx.First()
	if err != nil {
		return nil, err
	}
	return configInfo, nil
}

// GetMinigameConfig 获取小游戏配置
func (s *MinigameService) GetMinigameConfig(ctx context.Context, gameID string) (*model.AConfigMinigame, error) {
	configMinigame := store.QueryDB().AConfigMinigame
	configCtx := configMinigame.WithContext(ctx)
	if gameID != "" {
		configCtx = configCtx.Where(configMinigame.GameID.Eq(gameID))
	}
	configInfo, err := configCtx.Where(configMinigame.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return configInfo, nil
}

// UpdateMinigameConfigToken
func (s *MinigameService) UpdateMinigameConfigToken(ctx context.Context, gameID, accessToken string, expiresIn int32) error {
	configMinigame := store.QueryDB().AConfigMinigame
	configCtx := configMinigame.WithContext(ctx)
	if gameID != "" {
		configCtx = configCtx.Where(configMinigame.GameID.Eq(gameID))
	}
	// 更新token、过期时间和刷新时间
	_, err := configCtx.Where(configMinigame.IsDeleted.Zero()).UpdateSimple(
		configMinigame.AccessToken.Value(accessToken),
		configMinigame.ExpiresIn.Value(expiresIn),
		configMinigame.TokenRefreshedAt.Value(time.Now().UnixMilli()))
	if err != nil {
		return err
	}
	return nil
}

// UpdateMiniprogramConfigToken
func (s *MinigameService) UpdateMiniprogramConfigToken(ctx context.Context, accessToken string, expiresIn int32) error {
	configMiniprogram := store.QueryDB().AConfigMiniprogram
	configCtx := configMiniprogram.WithContext(ctx)
	_, err := configCtx.Where(configMiniprogram.ID.Eq(1)).UpdateSimple(configMiniprogram.AccessToken.Value(accessToken),
		configMiniprogram.ExpiresIn.Value(expiresIn))
	if err != nil {
		return err
	}
	return nil
}

// UpdateMinigameConfigTicket
//func (s *MinigameService) UpdateMinigameConfigTicket(ctx context.Context, gameID, ticket string, expiresIn int32) error {
//	configMinigame := store.QueryDB().ConfigMinigame
//	configCtx := configMinigame.WithContext(ctx)
//	if gameID != "" {
//		configCtx = configCtx.Where(configMinigame.GameID.Eq(gameID))
//	}
//	_, err := configCtx.Where(configMinigame.IsDeleted.Zero()).UpdateSimple(configMinigame.JsapiTicket.Value(ticket),
//		configMinigame.JsapiTokenExpiresIn.Value(expiresIn))
//	if err != nil {
//		return err
//	}
//	return nil
//}

// GetCode2Session 获取session_key
func (s *MinigameService) GetCode2Session(ctx context.Context, appID, secret, code string) (*bean.Code2SessionRes, error) {
	// 统计此函数的时长
	start := time.Now()

	resp, err := s.client.
		SetTimeout(10 * time.Second).
		R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"appid":      appID,
			"secret":     secret,
			"js_code":    code,
			"grant_type": codeAuthType,
		}).
		Get(config.GlobConfig.Minigame.BaseURL + code2SessionUrl)
	if err != nil {
		logger.Logger.Errorf("Service GetCode2Session: 调用失败, 请检查接口 err: %s", err.Error())
		return nil, err
	}
	result := &bean.Code2SessionRes{}
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		logger.Logger.Errorf("Service GetCode2Session: Unmarshal err: %s", err.Error())
		return nil, err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("Service GetCode2Session: 获取seesion_key失败，状态码: %d", resp.StatusCode())
		return nil, fmt.Errorf("service GetCode2Session: 获取seesion_key失败，状态码: %d", resp.StatusCode())
	}
	if result.ErrCode != 0 {
		logger.Logger.Errorf("Service GetCode2Session: 成功调用，但获取api结果失败，app_id: %s", appID)
		logger.Logger.Errorf("Service GetCode2Session: 成功调用，但获取api结果失败，错误信息: %v", result)
		return nil, fmt.Errorf("service GetCode2Session: 成功调用，但获取api结果失败")
	}

	latency := time.Since(start)
	logger.Logger.Debugf("Service GetCode2Session: 耗时: %sms", fmt.Sprintf("%.3f", float64(latency)/float64(time.Millisecond)))
	return result, nil
}

// SendCustomerService
func (s *MinigameService) SendCustomerService(ctx context.Context, accessToken string, toUser, content, thumbUrl string) error {
	data := map[string]interface{}{
		"touser":  toUser,
		"msgtype": "link",
		"link": map[string]string{
			"title":       constants.CustomerServiceTitle,
			"description": constants.CustomerServiceDesc,
			"thumb_url":   thumbUrl,
			"url":         content,
		},
	}
	reqUrl := fmt.Sprintf("%s%s?access_token=%s", config.GlobConfig.Minigame.BaseURL, customServiceUrl, accessToken)
	client := resty.New()
	resp, err := client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		Post(reqUrl)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "error while sending custom message: %v", err)
		return fmt.Errorf("error while sending custom message: %v", err)
	}

	result := &bean.MinigameErr{}
	err = json.Unmarshal(resp.Body(), &result)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "MinigameService SendCustomerService unmarshal err: %s", err.Error())
		return err
	}
	if result.ErrCode != 0 {
		logger.Logger.ErrorfCtx(ctx, "error response from WeChat API err: code is:%d, msg is:%s", result.ErrCode, result.ErrMsg)
		return fmt.Errorf("error response from WeChat API err: code is:%d, msg is:%s", result.ErrCode, result.ErrMsg)
	}
	return nil
}

func (s *MinigameService) SendWechatCustomerService(ctx context.Context, accessToken, openID, msgType string, data map[string]interface{}) error {
	body := map[string]interface{}{
		"touser":  openID,
		"msgtype": msgType,
	}

	if msgData, ok := data[msgType]; ok {
		body[msgType] = msgData
	} else {
		logger.Logger.ErrorfCtx(ctx, "SendWechatCustomerService unknown msgType: %s", msgType)
		return fmt.Errorf("SendWechatCustomerService unknown msgType: %s", msgType)
	}
	reqUrl := fmt.Sprintf("%s%s?access_token=%s", config.GlobConfig.Minigame.BaseURL, customServiceUrl, accessToken)
	client := resty.New()
	resp, err := client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(body).
		Post(reqUrl)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "error while sending custom message: %v", err)
		return fmt.Errorf("error while sending custom message: %v", err)
	}

	result := &bean.MinigameErr{}
	err = json.Unmarshal(resp.Body(), &result)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "MinigameService SendCustomerService unmarshal err: %s", err.Error())
		return err
	}
	if result.ErrCode != 0 {
		// 如果是45015和45047错误，表示正常业务限制，不报错处理，使用warn
		if result.ErrCode == constants.ErrCodeNotSubscribe || result.ErrCode == constants.ErrCodeExceedSendLimit {
			logger.Logger.WarnfCtx(ctx, "error response from WeChat API err: code is:%d, msg is:%s", result.ErrCode, result.ErrMsg)
			return nil
		}
		return fmt.Errorf("error response from WeChat API err: code is:%d, msg is:%s", result.ErrCode, result.ErrMsg)
	}
	return nil
}

// GetUserEncryptKey 获取用户getUserEncryptKey
func (s *MinigameService) GetUserEncryptKey(ctx context.Context, token, openID, signature, sigMethod string) (*bean.WechatEncryptKey, error) {
	resp, err := resty.New().
		SetTimeout(10 * time.Second).
		R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"access_token": token,
			"openid":       openID,
			"signature":    signature,
			"sig_method":   sigMethod,
		}).
		Post(config.GlobConfig.Minigame.BaseURL + getUserEncryptKeyUrl)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.WarnfCtx(ctx, "GetUserEncryptKey err, https code: %d", resp.StatusCode())
		return nil, fmt.Errorf("GetUserEncryptKey err, https code: %d", resp.StatusCode())
	}

	result := &bean.WechatEncryptKey{}
	if err = json.Unmarshal(resp.Body(), result); err != nil {
		return nil, bizerrors.Wrap(err, "MinigameService GetUserEncryptKey")
	}
	if result.ErrCode != 0 {
		logger.Logger.WarnfCtx(ctx, "getUserEncryptKey err, code: %d, msg: %s", result.ErrCode, result.ErrMsg)
		return nil, fmt.Errorf("getUserEncryptKey err, code: %d, msg: %s", result.ErrCode, result.ErrMsg)
	}
	return result, nil
}

// GetCompanyConfig returns the company config
func (s *MinigameService) GetCompanyConfig(ctx context.Context, gameID string) (*model.ACompany, error) {
	company := store.QueryDB().ACompany
	companyCtx := company.WithContext(ctx)
	if gameID != "" {
		companyCtx = companyCtx.Where(company.GameID.Eq(gameID))
	}
	companyInfo, err := companyCtx.First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Errorf("[MinigameService] GetCompanyConfig getting company is empty")
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return companyInfo, nil
}

// RefreshMiniprogramUrlLink 刷新小程序 url link
func (s *MinigameService) RefreshMiniprogramUrlLink(ctx context.Context, token string) (string, error) {
	url, err := s.wechatHttpService.RefreshMiniprogramUrlLink(ctx, token, config.GlobConfig.Miniprogram.EnvVersion)
	if err != nil {
		return "", err
	}
	logger.Logger.InfofCtx(ctx, "RefreshMiniprogramUrlLink success, url: %s", url)
	// 更新 url
	miniprogram := store.QueryDB().AConfigMiniprogram
	_, err = miniprogram.WithContext(ctx).Where(miniprogram.ID.Eq(1)).
		UpdateSimple(miniprogram.URLLink.Value(url),
			miniprogram.IsUpdatedSys.Value(0),
			miniprogram.URLLinkCreatedAt.Value(time.Now().UnixMilli()))
	if err != nil {
		return "", err
	}
	logger.Logger.InfofCtx(ctx, "RefreshMiniprogramUrlLink success, url: %s", url)
	return url, nil
}
