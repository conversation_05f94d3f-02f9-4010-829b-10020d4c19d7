package service

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/bizerrors"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"gorm.io/gorm"
)

var (
	_redemptionOnce    sync.Once
	_redemptionService *RedemptionService
)

type RedemptionService struct{}

func SingletonRedemptionService() *RedemptionService {
	_redemptionOnce.Do(func() {
		_redemptionService = &RedemptionService{}
	})
	return _redemptionService
}

// GetRedemptionCode 获取兑换码
func (r *RedemptionService) GetRedemptionCode(ctx context.Context, req *bean.GetRedemptionCodeReq) (*bean.GetRedemptionCodeResp, error) {
	entity := store.QueryDB().MRedemptionCodeEntity
	entityInfo, err := entity.WithContext(ctx).
		Where(entity.Code.Eq(req.Code)).First()
	if err != nil {
		return nil, err
	}

	code := store.QueryDB().MRedemptionCode
	codeInfo, err := code.WithContext(ctx).
		Where(code.ID.Eq(entityInfo.CodeID)).
		Where(code.IsDeleted.Zero()).
		First()
	if err != nil {
		return nil, constants.ErrRedemptionCodeNotFound
	}

	return &bean.GetRedemptionCodeResp{
		ID:          entityInfo.ID,
		Code:        entityInfo.Code,
		Title:       codeInfo.Title,
		Content:     codeInfo.Content,
		Description: codeInfo.Description,
	}, nil
}

//func (r *RedemptionService) RedemptionCodeCallback(ctx context.Context, req *bean.RedemptionCodeCallbackReq) (*bean.GetRedemptionCodeResp, error) {
//	// 使用redis分布式锁
//	lockKey := fmt.Sprintf("%s:%s", constants.SystemRedemptionLockKey, req.UserID)
//	if isLock := redis.Lock(ctx, lockKey, constants.SystemRedemptionLockExpire*time.Second); !isLock {
//		return nil, constants.ErrSystemServiceIsBusy
//	}
//	defer redis.UnLock(ctx, lockKey)
//
//	if req.CodeType == constants.RedemptionCodeSloganType {
//		code := store.QueryDB().MRedemptionCode
//		codeInfo, err := code.WithContext(ctx).
//			Where(code.Slogan.Eq(req.Code)).
//			Where(code.IsDeleted.Zero()).First()
//		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
//			return nil, constants.ErrRedemptionConfigNotOpen
//		} else if err != nil {
//			return nil, bizerrors.Wrap(err, "not found code config or status is close")
//		}
//
//		record := store.QueryDB().MRedemptionCodeRecord
//		count, err := record.WithContext(ctx).Select(record.ID).Where(record.UserID.Eq(req.UserID)).Count()
//		if err != nil {
//			return nil, err
//		}
//		if count == int64(codeInfo.Frequency) {
//			return nil, constants.ErrRedemptionFrequency
//		}
//
//		if err := record.WithContext(ctx).Create(&model.MRedemptionCodeRecord{
//			UserID: req.UserID, // 目前仅记录通兑码 CodeType:  2,
//			GameID: req.GameID,
//			Code:   codeInfo.Slogan,
//		}); err != nil {
//			return nil, err
//		}
//		return &bean.GetRedemptionCodeResp{
//			ID:          codeInfo.ID,
//			UserID:      req.UserID,
//			Code:        codeInfo.Slogan,
//			Title:       codeInfo.Title,
//			Content:     codeInfo.Content,
//			Description: codeInfo.Description,
//		}, nil
//	} else if req.CodeType == constants.RedemptionCodeNormalType {
//		entity := store.QueryDB().MRedemptionCodeEntity
//		entityInfo, err := entity.WithContext(ctx).
//			Where(entity.Code.Eq(req.Code)).
//			Where(entity.Status.Eq(constants.RedemptionCodeStatusUnused)).First()
//		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
//			return nil, constants.ErrRedemptionCodeNotExist
//		} else if err != nil {
//			return nil, err
//		}
//
//		code := store.QueryDB().MRedemptionCode
//		codeInfo, err := code.WithContext(ctx).
//			Where(code.ID.Eq(entityInfo.CodeID)).
//			Where(code.Status.Eq(constants.RedemptionCodeStatusOpen)).
//			Where(code.IsDeleted.Zero()).First()
//		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
//			return nil, constants.ErrRedemptionConfigNotOpen
//		} else if err != nil {
//			return nil, err
//		}
//
//		// 判断codeInfo的生效时间和过期时间
//		if codeInfo.CommencementDate > time.Now().UnixMilli() || codeInfo.ExpiredDate < time.Now().UnixMilli() {
//			return nil, constants.ErrRedemptionCodeExpire
//		}
//
//		updateResp, err := entity.WithContext(ctx).
//			Where(entity.Code.Eq(req.Code)).
//			Where(entity.Status.Eq(constants.RedemptionCodeStatusUnused)).
//			UpdateSimple(
//				entity.SpendUserID.Value(req.UserID),
//				entity.Status.Value(constants.RedemptionCodeStatusUsed))
//		if err != nil {
//			return nil, err
//		}
//		if updateResp.RowsAffected == 0 {
//			return nil, constants.ErrRedemptionCodeNotExist
//		}
//		return &bean.GetRedemptionCodeResp{
//			ID:          codeInfo.ID,
//			UserID:      req.UserID,
//			Code:        entityInfo.Code,
//			Title:       codeInfo.Title,
//			Content:     codeInfo.Content,
//			Description: codeInfo.Description,
//		}, nil
//	}
//	return nil, nil
//}

// IsSloganCode 检测是否是通兑码
func (r *RedemptionService) IsSloganCode(ctx context.Context, req *bean.RedemptionCodeCallbackReq) bool {
	code := store.QueryDB().MRedemptionCode
	count, err := code.WithContext(ctx).Where(code.Slogan.Eq(req.Code)).Where(code.IsDeleted.Zero()).Count()
	if err != nil {
		logger.Logger.Errorf("IsSloganCode gameid: %s, userid: %s, err: %v", req.GameID, req.UserID, err)
		return false
	}
	return count > 0
}

func (r *RedemptionService) RedemptionCodeCallback(ctx context.Context, req *bean.RedemptionCodeCallbackReq, codeType int) (*bean.GetRedemptionCodeResp, error) {
	// 使用redis分布式锁
	lockKey := fmt.Sprintf("%s:%s", constants.SystemRedemptionLockKey, req.UserID)
	if isLock := redis.Lock(ctx, lockKey, constants.SystemRedemptionLockExpire*time.Second); !isLock {
		return nil, constants.ErrSystemServiceIsBusy
	}
	defer redis.UnLock(ctx, lockKey)

	switch codeType {
	case constants.RedemptionCodeSloganType:
		return r.handleSloganCode(ctx, req)
	case constants.RedemptionCodeNormalType:
		return r.handleNormalCode(ctx, req)
	default:
		return nil, nil
	}
}

// 处理标语兑换码
func (r *RedemptionService) handleSloganCode(ctx context.Context, req *bean.RedemptionCodeCallbackReq) (*bean.GetRedemptionCodeResp, error) {
	code := store.QueryDB().MRedemptionCode
	codeInfo, err := code.WithContext(ctx).
		Where(code.Slogan.Eq(req.Code)).
		Where(code.IsDeleted.Zero()).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, constants.ErrRedemptionConfigNotOpen
		}
		return nil, bizerrors.Wrap(err, "not found code config or status is close")
	}

	// 检查通兑码是否属于请求的游戏
	if codeInfo.GameID != req.GameID {
		return nil, constants.ErrRedemptionCodeNotFound
	}

	if err := r.checkSloganCodeFrequency(ctx, req.UserID, codeInfo.Slogan, codeInfo.Frequency); err != nil {
		return nil, err
	}

	if err := r.createRedemptionRecord(ctx, req.UserID, req.GameID, codeInfo.Slogan); err != nil {
		return nil, err
	}

	return r.buildRedemptionResp(codeInfo, req.UserID), nil
}

// 处理普通兑换码
func (r *RedemptionService) handleNormalCode(ctx context.Context, req *bean.RedemptionCodeCallbackReq) (*bean.GetRedemptionCodeResp, error) {
	entity := store.QueryDB().MRedemptionCodeEntity
	entityInfo, err := entity.WithContext(ctx).
		Where(entity.Code.Eq(req.Code)).
		Where(entity.Status.Eq(constants.RedemptionCodeStatusUnused)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, constants.ErrRedemptionCodeNotExist
		}
		return nil, err
	}

	code := store.QueryDB().MRedemptionCode
	codeInfo, err := code.WithContext(ctx).
		Where(code.ID.Eq(entityInfo.CodeID)).
		Where(code.Status.Eq(constants.RedemptionCodeStatusOpen)).
		Where(code.IsDeleted.Zero()).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, constants.ErrRedemptionConfigNotOpen
		}
		return nil, err
	}

	// 校验普通码的游戏ID
	if req.GameID != codeInfo.GameID {
		return nil, constants.ErrRedemptionConfigNotOpen
	}

	// 判断codeInfo的生效时间和过期时间
	if codeInfo.CommencementDate > time.Now().UnixMilli() || codeInfo.ExpiredDate < time.Now().UnixMilli() {
		return nil, constants.ErrRedemptionCodeExpire
	}

	if err := r.updateCodeEntityStatus(ctx, req.Code, req.UserID); err != nil {
		return nil, err
	}

	return r.buildRedemptionResp(codeInfo, req.UserID), nil
}

// 检查标语兑换码使用频率
func (r *RedemptionService) checkSloganCodeFrequency(ctx context.Context, userID string, slogan string, frequency int32) error {
	record := store.QueryDB().MRedemptionCodeRecord
	count, err := record.WithContext(ctx).Select(record.ID).
		Where(record.UserID.Eq(userID)).
		Where(record.Code.Eq(slogan)).Count()
	if err != nil {
		return err
	}
	if count >= int64(frequency) {
		return constants.ErrRedemptionFrequency
	}
	return nil
}

// 创建兑换记录
func (r *RedemptionService) createRedemptionRecord(ctx context.Context, userID string, gameID string, code string) error {
	record := store.QueryDB().MRedemptionCodeRecord
	return record.WithContext(ctx).Create(&model.MRedemptionCodeRecord{
		UserID: userID, // 目前仅记录通兑码 CodeType:  2,
		GameID: gameID,
		Code:   code,
	})
}

// 更新兑换码实体状态
func (r *RedemptionService) updateCodeEntityStatus(ctx context.Context, code string, userID string) error {
	entity := store.QueryDB().MRedemptionCodeEntity
	updateResp, err := entity.WithContext(ctx).
		Where(entity.Code.Eq(code)).
		Where(entity.Status.Eq(constants.RedemptionCodeStatusUnused)).
		UpdateSimple(
			entity.SpendUserID.Value(userID),
			entity.Status.Value(constants.RedemptionCodeStatusUsed))
	if err != nil {
		return err
	}
	if updateResp.RowsAffected == 0 {
		return constants.ErrRedemptionCodeNotExist
	}
	return nil
}

// 构建兑换码响应
func (r *RedemptionService) buildRedemptionResp(codeInfo *model.MRedemptionCode, userID string) *bean.GetRedemptionCodeResp {
	return &bean.GetRedemptionCodeResp{
		ID:          codeInfo.ID,
		UserID:      userID,
		Code:        codeInfo.Slogan,
		Title:       codeInfo.Title,
		Content:     codeInfo.Content,
		Description: codeInfo.Description,
	}
}
