package service

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/go-resty/resty/v2"
)

var (
	_StorageOnce    sync.Once
	_StorageService *StorageService
)

type StorageService struct{}

func SingletonStorageService() *StorageService {
	_StorageOnce.Do(func() {
		_StorageService = &StorageService{}
	})
	return _StorageService
}

// Signature 签名
func (s *StorageService) Signature(sessionKey string, params []byte) (string, error) {
	h := hmac.New(sha256.New, []byte(sessionKey))
	h.Write(params)
	return hex.EncodeToString(h.Sum(nil)), nil
}

// Douyin Signature https://developer.open-douyin.com/docs/resource/zh-CN/mini-game/develop/server/other/user-login-sign/
func (s *StorageService) DouyinSignature(sessionKey string, body interface{}) (string, error) {
	// Convert body to JSON string
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return "", fmt.Errorf("marshal body failed: %w", err)
	}

	// Create HMAC-SHA256 hash
	h := hmac.New(sha256.New, []byte(sessionKey))
	h.Write(jsonBody)

	// Convert hash to hex string
	return hex.EncodeToString(h.Sum(nil)), nil
}

// SetUserStorage 设置用户存储
func (s *StorageService) SetUserStorage(ctx context.Context, accessToken, openID, sessionKey string, kvList []*bean.KV) error {
	KeyValue := map[string]interface{}{
		"kv_list": kvList,
	}

	marshal, err := json.Marshal(KeyValue)
	if err != nil {
		return err
	}
	sign, err := s.Signature(sessionKey, marshal)
	if err != nil {
		return err
	}

	if err := s.WechatUserStorage(ctx, accessToken, openID, sign, constants.SecretSha256Type, KeyValue); err != nil {
		logger.Logger.Errorf("SetUserStorage err: %s", err.Error())
		return err
	}
	return nil
}

// RemoveUserStorage 删除用户存储
func (s *StorageService) RemoveUserStorage(ctx context.Context, accessToken, openID, sessionKey string, kStr []string) error {
	KeyValue := map[string]interface{}{
		"key": kStr,
	}

	marshal, err := json.Marshal(KeyValue)
	if err != nil {
		return err
	}
	sign, err := s.Signature(sessionKey, marshal)
	if err != nil {
		return err
	}
	if err := s.WechatRemoveUserStorage(ctx, accessToken, openID, sign, constants.SecretSha256Type, kStr); err != nil {
		logger.Logger.Errorf("RemoveUserStorage err: %s", err.Error())
		return err
	}
	return nil
}

// SetWechatUserInteractiveStorage 设置微信用户互动存储
func (s *StorageService) SetWechatUserInteractiveStorage(ctx context.Context, accessToken, openID, sessionKey string, kvList []*bean.KV) error {
	KeyValue := map[string]interface{}{
		"kv_list": kvList,
	}

	marshal, err := json.Marshal(KeyValue)
	if err != nil {
		return err
	}
	sign, err := s.Signature(sessionKey, marshal)
	if err != nil {
		return err
	}

	if err := s.WechatUserInteractiveData(ctx, accessToken, openID, sign, constants.SecretSha256Type, KeyValue); err != nil {
		logger.Logger.Errorf("WechatUserInteractiveData err: %s", err.Error())
		return err
	}
	return nil
}

// SetDouyinUserStorage 设置抖音用户存储
func (s *StorageService) SetDouyinUserStorage(ctx context.Context, accessToken, openID, sessionKey string, kvList []*bean.KV) error {
	// 构造正确的请求格式
	requestBody := map[string]interface{}{
		"kv_list": kvList,
	}

	// 使用完整请求体计算签名
	sign, err := s.DouyinSignature(sessionKey, requestBody)
	if err != nil {
		return err
	}

	// 发送请求时也使用相同的结构
	if err := s.DouyinUserStorage(ctx, accessToken, openID, sign, constants.SecretSha256Type, requestBody); err != nil {
		logger.Logger.Errorf("SetUserStorage err: %s", err.Error())
		return err
	}
	return nil
}

// RemoveDouyinUserStorage 删除抖音用户存储
func (s *StorageService) RemoveDouyinUserStorage(ctx context.Context, accessToken, openID, sessionKey string, kStr []string) error {
	// 构造正确的请求格式
	requestBody := map[string]interface{}{
		"key": kStr,
	}

	// 使用完整请求体计算签名
	sign, err := s.DouyinSignature(sessionKey, requestBody)
	if err != nil {
		return err
	}

	// 发送请求时也使用相同的结构
	if err := s.DouyinRemoveUserStorage(ctx, accessToken, openID, sign, constants.SecretSha256Type, requestBody); err != nil {
		logger.Logger.Errorf("RemoveUserStorage err: %s", err.Error())
		return err
	}
	return nil
}

const (
	setUserStorage          = "/wxa/set_user_storage"
	removeUserStorage       = "/wxa/remove_user_storage"
	setUserInteractiveData  = "/wxa/setuserinteractivedata"
	douyinSetUserStorage    = "/mgplatform/api/apps/set_user_storage"
	douyinRemoveUserStorage = "/mgplatform/api/apps/remove_user_storage"
)

func (s *StorageService) WechatUserStorage(ctx context.Context,
	accessToken, openID, signature, sigMethod string, kvList map[string]interface{},
) error {
	resp, err := resty.New().
		R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"access_token": accessToken,
			"openid":       openID,
			"signature":    signature,
			"sig_method":   sigMethod,
		}).
		SetBody(kvList).
		Post(config.GlobConfig.Minigame.BaseURL + setUserStorage)
	if err != nil {
		return err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("WechatUserStorage err, https code: %d", resp.StatusCode())
		return fmt.Errorf("WechatUserStorage err, https code: %d", resp.StatusCode())
	}

	res := &bean.MinigameErr{}
	if err := json.Unmarshal(resp.Body(), res); err != nil {
		return err
	}
	if res.ErrCode != 0 {
		logger.Logger.Errorf("WechatUserStorage err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
		return fmt.Errorf("WechatUserStorage err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
	}
	return nil
}

func (s *StorageService) WechatRemoveUserStorage(ctx context.Context,
	accessToken, openID, signature, sigMethod string, kStr []string,
) error {
	resp, err := resty.New().
		R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"access_token": accessToken,
			"openid":       openID,
			"signature":    signature,
			"sig_method":   sigMethod,
		}).
		SetBody(kStr).
		Post(config.GlobConfig.Minigame.BaseURL + removeUserStorage)
	if err != nil {
		return err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("WechatRemoveUserStorage err, https code: %d", resp.StatusCode())
		return fmt.Errorf("WechatRemoveUserStorage err, https code: %d", resp.StatusCode())
	}

	res := &bean.MinigameErr{}
	if err := json.Unmarshal(resp.Body(), res); err != nil {
		return err
	}
	if res.ErrCode != 0 {
		logger.Logger.Errorf("WechatRemoveUserStorage err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
		return fmt.Errorf("WechatRemoveUserStorage err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
	}
	return nil
}

func (s *StorageService) WechatUserInteractiveData(ctx context.Context,
	accessToken, openID, signature, sigMethod string, kvList map[string]interface{},
) error {
	resp, err := resty.New().
		R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"access_token": accessToken,
			"openid":       openID,
			"signature":    signature,
			"sig_method":   sigMethod,
		}).
		SetBody(kvList).
		Post(config.GlobConfig.Minigame.BaseURL + setUserInteractiveData)
	if err != nil {
		return err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("WechatUserInteractiveData err, https code: %d", resp.StatusCode())
		return fmt.Errorf("WechatUserInteractiveData err, https code: %d", resp.StatusCode())
	}

	res := &bean.MinigameErr{}
	if err := json.Unmarshal(resp.Body(), res); err != nil {
		return err
	}
	if res.ErrCode != 0 {
		logger.Logger.Errorf("WechatUserInteractiveData err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
		return fmt.Errorf("WechatUserInteractiveData err, code: %d, msg: %s", res.ErrCode, res.ErrMsg)
	}
	return nil
}

func (s *StorageService) DouyinUserStorage(ctx context.Context,
	accessToken, openID, signature, sigMethod string,
	body map[string]interface{},
) error {
	resp, err := resty.New().
		R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"access_token": accessToken,
			"openid":       openID,
			"signature":    signature,
			"sig_method":   sigMethod,
		}).
		SetBody(body).
		Post(config.GlobConfig.Douyin.BaseURL + douyinSetUserStorage)
	if err != nil {
		return err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("DouyinUserStorage err, https code: %d", resp.StatusCode())
		return fmt.Errorf("DouyinUserStorage err, https code: %d", resp.StatusCode())
	}

	res := &bean.DouyinError{}
	if err := json.Unmarshal(resp.Body(), res); err != nil {
		return err
	}
	if res.Error != 0 || res.ErrCode != 0 {
		logger.Logger.Errorf("DouyinUserStorage err, code: %d, msg: %s, error: %d, message: %s", res.ErrCode, res.ErrMsg, res.Error, res.Message)
		return fmt.Errorf("DouyinUserStorage err, code: %d, msg: %s, error: %d, message: %s", res.ErrCode, res.ErrMsg, res.Error, res.Message)
	}
	return nil
}

func (s *StorageService) DouyinRemoveUserStorage(ctx context.Context,
	accessToken, openID, signature, sigMethod string,
	body map[string]interface{},
) error {
	resp, err := resty.New().
		R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"access_token": accessToken,
			"openid":       openID,
			"signature":    signature,
			"sig_method":   sigMethod,
		}).
		SetBody(body).
		Post(config.GlobConfig.Douyin.BaseURL + douyinRemoveUserStorage)
	if err != nil {
		return err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("DouyinRemoveUserStorage err, https code: %d", resp.StatusCode())
		return fmt.Errorf("DouyinRemoveUserStorage err, https code: %d", resp.StatusCode())
	}

	res := &bean.DouyinError{}
	if err := json.Unmarshal(resp.Body(), res); err != nil {
		return err
	}
	if res.Error != 0 || res.ErrCode != 0 {
		logger.Logger.Errorf("DouyinRemoveUserStorage err, code: %d, msg: %s, error: %d, message: %s",
			res.ErrCode, res.ErrMsg, res.Error, res.Message)
		return fmt.Errorf("DouyinRemoveUserStorage err, code: %d, msg: %s, error: %d, message: %s",
			res.ErrCode, res.ErrMsg, res.Error, res.Message)
	}
	return nil
}
