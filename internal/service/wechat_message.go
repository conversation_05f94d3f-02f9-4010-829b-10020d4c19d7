package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/go-resty/resty/v2"
)

var (
	wechatMessageOnce    sync.Once
	wechatMessageService *WechatMessageService
)

type WechatMessageService struct {
	client *resty.Client
}

func SingletonWechatMessageService() *WechatMessageService {
	wechatMessageOnce.Do(func() {
		wechatMessageService = &WechatMessageService{
			client: resty.New(),
		}
	})

	return wechatMessageService
}

const (
	subMessageUrl = "/cgi-bin/message/subscribe/send" // 次数限制：开通支付能力的是3kw/日，没开通的是1kw/日。
)

// SubMessageNotify 发送微信消息订阅
func (s *WechatMessageService) SubMessageNotify(ctx context.Context, accessToken string, openID string, req *bean.SubMessageNotifyReq) (string, error) {
	logger.Logger.InfofCtx(ctx, "SubMessageNotify openID: %s", openID)
	logger.Logger.InfofCtx(ctx, "SubMessageNotify accessToken: %s", accessToken)
	logger.Logger.InfofCtx(ctx, "SubMessageNotify req: %+v", req)

	body := map[string]interface{}{
		"touser":      openID,         // OpenID
		"template_id": req.TemplateID, // 订阅消息模板ID
		"page":        req.Page,       // 点击订阅消息卡片后会打开的小程序页面
		"data":        req.Data,
	}

	resp, err := s.client.
		R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(body).
		Post(config.GlobConfig.Minigame.BaseURL + subMessageUrl + fmt.Sprintf("?access_token=%s", accessToken))
	if err != nil {
		return "", err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("SubMessageNotify https status code: %d", resp.StatusCode())
		return "", fmt.Errorf("SubMessageNotify https status code: %d", resp.StatusCode())
	}

	var result bean.MinigameErr
	err = json.Unmarshal(resp.Body(), &result)
	if err != nil {
		logger.Logger.Errorf("SubMessageNotify unmarshal err: %s", err.Error())
		return "", err
	}

	if result.ErrCode != 0 {
		if result.ErrCode == 43101 || result.ErrCode == 43108 { // user refuse to accept the msg
			logger.Logger.Warnf("SubMessageNotify call success, but err, code: %d, msg: %s", result.ErrCode, result.ErrMsg)
		} else {
			logger.Logger.Errorf("SubMessageNotify call success, but err, code: %d, msg: %s", result.ErrCode, result.ErrMsg)
		}
		return fmt.Sprintf("code: %d, msg: %s", result.ErrCode, result.ErrMsg), nil
	}
	return "", nil
}

func (s *WechatMessageService) SubMessageNotifySetReturnEnv(ctx context.Context, accessToken, openID, returnEnv string, req *bean.SubMessageNotifyReq) (string, error) {
	body := map[string]interface{}{
		"touser":            openID,         // OpenID
		"template_id":       req.TemplateID, // 订阅消息模板ID
		"miniprogram_state": returnEnv,
		"page":              req.Page, // 点击订阅消息卡片后会打开的小程序页面
		"data":              req.Data,
	}

	logger.Logger.Infof("SubMessageNotifySetReturnEnv body: %v", body)

	resp, err := s.client.
		R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(body).
		Post(config.GlobConfig.Minigame.BaseURL + subMessageUrl + fmt.Sprintf("?access_token=%s", accessToken))
	if err != nil {
		return "", err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("SubMessageNotify https status code: %d", resp.StatusCode())
		return "", fmt.Errorf("SubMessageNotify https status code: %d", resp.StatusCode())
	}

	var result bean.MinigameErr
	err = json.Unmarshal(resp.Body(), &result)
	if err != nil {
		logger.Logger.Errorf("SubMessageNotify unmarshal err: %s", err.Error())
		return "", err
	}

	if result.ErrCode != 0 {
		if result.ErrCode == 43101 || result.ErrCode == 43108 { // user refuse to accept the msg
			logger.Logger.Warnf("SubMessageNotify call success, but err, code: %d, msg: %s", result.ErrCode, result.ErrMsg)
		} else {
			logger.Logger.Errorf("SubMessageNotify call success, but err, code: %d, msg: %s", result.ErrCode, result.ErrMsg)
		}
		return fmt.Sprintf("code: %d, msg: %s", result.ErrCode, result.ErrMsg), nil
	}
	return "", nil
}
