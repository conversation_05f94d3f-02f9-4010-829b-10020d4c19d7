package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/jinzhu/copier"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/regions"
	faceid "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/faceid/v20180301"
	tms "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tms/v20201229"
)

var (
	_tencentCloudOnce    sync.Once
	_tencentCloudService *TencentCloudService
)

type TencentCloudService struct {
	client       *tms.Client
	clientFaceID *faceid.Client
}

func SingletonTencentCloudService() *TencentCloudService {
	_tencentCloudOnce.Do(func() {
		credential := common.NewCredential(
			config.GlobConfig.TencentCloud.SecretID,
			config.GlobConfig.TencentCloud.SecretKey,
		)
		client, err := tms.NewClient(credential, regions.Beijing, profile.NewClientProfile())
		if err != nil {
			// panic(err) 不可以使用panic影响正常流程
			logger.Logger.Errorf("!!! SingletonTencentCloudService init error, tencent cloud client error: %v", err)
			return
		}

		cpf := profile.NewClientProfile()
		cpf.HttpProfile.Endpoint = "faceid.tencentcloudapi.com"
		clientFaceID, err := faceid.NewClient(credential, "ap-guangzhou", cpf)
		if err != nil {
			// panic(err) 不可以使用panic影响正常流程
			logger.Logger.Errorf("!!! SingletonTencentCloudService init error, tencent cloud face id client error: %v", err)
			return
		}

		_tencentCloudService = &TencentCloudService{
			client:       client,
			clientFaceID: clientFaceID,
		}
	})
	return _tencentCloudService
}

// FatchTencentCloudCheckText
func (s *TencentCloudService) FatchTencentCloudCheckText(ctx context.Context, content, platformType string) (*bean.VerifySensitiveMessageRes, error) {
	// content to base64
	base64Content := base64.StdEncoding.EncodeToString([]byte(content))
	resp, err := s.reqSensitiveCheckText(ctx, base64Content)
	if err != nil {
		return nil, err
	}

	// 将resp转换成VerifySensitiveMessageRes
	platformDetails := make([]*bean.SensitiveMessagePlatformDetail, 0)
	for _, keyword := range resp.Response.Keywords {
		platformDetails = append(platformDetails, &bean.SensitiveMessagePlatformDetail{
			Level:   1, // 可以根据实际需求调整级别
			Keyword: keyword,
		})
	}

	// 转换建议结果
	result := bean.WechatSecurityCheckResult{
		Suggest: resp.Response.Suggestion,
		Label:   int(resp.Response.Score), // 使用Score作为label，可以根据实际需求调整
		// ReplacedContent: content,
		// 将此词语替换成*
	}

	// Keywords.
	replacedContent := content
	for _, keyword := range resp.Response.Keywords {
		stars := strings.Repeat("*", len([]rune(keyword))) // 使用 []rune 来正确处理中文字符
		replacedContent = strings.Replace(replacedContent, keyword, stars, -1)
	}
	result.ReplacedContent = replacedContent

	// 将detailResults转换成TencentCloudCheckTextDetailResp
	detailResults := make([]*bean.TencentCloudCheckTextDetailResp, 0)
	for _, detail := range resp.Response.DetailResults {
		detailResult := &bean.TencentCloudCheckTextDetailResp{}
		err := copier.Copy(detailResult, detail)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "FatchTencentCloudCheckText tencent cloud copy detail error: %v", err)
			return nil, err
		}
		detailResults = append(detailResults, detailResult)
	}

	verifySensitiveMessageRes := &bean.VerifySensitiveMessageRes{
		TraceID:         resp.Response.RequestId,
		Source:          "tencent_cloud",
		ErrCode:         int32(resp.Retcode),
		ErrMsg:          s.getErrMsg(resp.Retcode, resp.Retmsg),
		Result:          result,
		Detail:          detailResults,
		PlatformDetail:  platformDetails,
		ReplacedContent: replacedContent,
	}

	return verifySensitiveMessageRes, nil
}

func (s *TencentCloudService) getErrMsg(retcode int, retmsg string) string {
	if retcode == 0 {
		return "ok"
	}
	return retmsg
}

func (s *TencentCloudService) reqSensitiveCheckText(ctx context.Context, content string) (*bean.TencentCloudCheckTextResp, error) {
	request := tms.NewTextModerationRequest()
	request.Content = common.StringPtr(content)
	request.BizType = common.StringPtr(config.GlobConfig.TencentCloud.BizType)

	response, err := s.client.TextModeration(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Logger.ErrorfCtx(ctx, "RequestSensitiveCheckText tencent cloud text moderation error: %v", err)
		return nil, err
	}
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "RequestSensitiveCheckText tencent cloud error: %v", err)
		return nil, err
	}

	resp := &bean.TencentCloudCheckTextResp{}
	err = json.Unmarshal([]byte(response.ToJsonString()), resp)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "RequestSensitiveCheckText tencent cloud unmarshal error: %v", err)
		return nil, err
	}
	return resp, nil
}

// VerifyRealName 获取实名核身结果
func (s *TencentCloudService) VerifyRealName(ctx context.Context, req *bean.VerifyRealNameReq) (string, string, error) {
	// 创建请求
	request := faceid.NewIdCardVerificationRequest()
	request.IdCard = common.StringPtr(req.IDCard)
	request.Name = common.StringPtr(req.Name)

	// 发送请求
	response, err := s.clientFaceID.IdCardVerification(request)
	if err != nil {
		return "", "", err
	}
	if response.Response == nil {
		logger.Logger.ErrorfCtx(ctx, "VerifyRealName tencent cloud error: %v", err)
		return "", "", fmt.Errorf("response is nil, err: %v", err)
	}
	if response.Response.Result != nil && *response.Response.Result != "0" {
		logger.Logger.WarnfCtx(ctx, "VerifyRealName tencent cloud request id: %v", response.Response.RequestId)
	}

	return *response.Response.Result, *response.Response.Description, nil
}
