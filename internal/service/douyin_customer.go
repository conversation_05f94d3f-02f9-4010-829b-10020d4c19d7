package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	neturl "net/url"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"encoding/base64"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/go-resty/resty/v2"
	"gorm.io/gorm"
)

var (
	douyinCustomerOnce    sync.Once
	douyinCustomerService *DouyinCustomerService
	messageIDCounter      int64
)

type DouyinCustomerService struct {
	client *resty.Client
}

func SingletonDouyinCustomerService() *DouyinCustomerService {
	douyinCustomerOnce.Do(func() {
		douyinCustomerService = &DouyinCustomerService{
			client: resty.New().SetTimeout(10 * time.Second),
		}
	})
	return douyinCustomerService
}

// 抖音客服消息API端点
const (
	douyinReplyTextURL  = "/mgplatform/api/apps/reply/reply_user_text"
	douyinReplyImageURL = "/mgplatform/api/apps/reply/reply_user_image"
)

// DouyinReplyTextRequest 抖音回复文本消息请求
type DouyinReplyTextRequest struct {
	MicroGameID    string `json:"micro_game_id"`
	ConversationID string `json:"conversation_id"`
	MsgID          string `json:"msg_id"`
	CreateTime     int64  `json:"create_time"`
	MsgType        string `json:"msg_type"`
	Content        string `json:"content"`
}

// DouyinReplyResponse 抖音回复消息响应
type DouyinReplyResponse struct {
	BaseResp struct {
		StatusCode    int    `json:"StatusCode"`
		StatusMessage string `json:"StatusMessage"`
	} `json:"BaseResp"`
	Data struct {
		ImMsgID      string `json:"im_msg_id"`
		FilterReason int    `json:"filter_reason,omitempty"`
	} `json:"data"`
	ErrMsg string `json:"err_msg"`
	ErrNo  int32  `json:"err_no"`
	LogID  string `json:"log_id"`
}

// SendTextMessage 发送文本消息
func (s *DouyinCustomerService) SendTextMessage(ctx context.Context, accessToken string, req *DouyinReplyTextRequest) error {
	logger.Logger.InfofCtx(ctx, "[DouyinCustomerService.SendTextMessage] 发送抖音文本消息，GameID: %s, ConversationID: %s, Content: %s",
		req.MicroGameID, req.ConversationID, req.Content)

	reqBody, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("marshal request failed: %v", err)
	}

	resp, err := s.client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("access-token", accessToken).
		SetBody(reqBody).
		Post(config.GlobConfig.Douyin.BaseURL + douyinReplyTextURL)
	if err != nil {
		return fmt.Errorf("send request failed: %v", err)
	}

	if resp.StatusCode() != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode(), string(resp.Body()))
	}

	var result DouyinReplyResponse
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return fmt.Errorf("unmarshal response failed: %v", err)
	}

	// 检查响应是否成功
	if result.ErrNo != 0 {
		logger.Logger.InfofCtx(ctx, "[DouyinCustomerService.SendTextMessage] 发送消息失败，所有参数如下 - reqBody: %s, accessToken: %s, response: %+v",
			string(reqBody), accessToken, result)
		return fmt.Errorf("send message failed, err_no: %d, err_msg: %s, log_id: %s", result.ErrNo, result.ErrMsg, result.LogID)
	}

	// 检查消息是否被过滤
	if result.Data.FilterReason != 0 {
		return fmt.Errorf("message filtered, filter_reason: %d", result.Data.FilterReason)
	}

	// 检查是否有消息ID返回
	if result.Data.ImMsgID == "" {
		return fmt.Errorf("no message id returned")
	}

	logger.Logger.InfofCtx(ctx, "[DouyinCustomerService.SendTextMessage] 发送抖音文本消息成功，message_id: %s, log_id: %s",
		result.Data.ImMsgID, result.LogID)
	return nil
}

// SendLinkMessage 发送链接消息（通过文本消息实现）
func (s *DouyinCustomerService) SendLinkMessage(ctx context.Context, accessToken string, req *DouyinReplyTextRequest) error {
	// 打印accessToken
	logger.Logger.DebugfCtx(ctx, "[DouyinCustomerService.SendLinkMessage] accessToken: %s", accessToken)

	// 链接消息通过文本消息发送，设置消息类型为link
	req.MsgType = "link"
	return s.SendTextMessage(ctx, accessToken, req)
}

// buildReplyRequest 构建回复请求的基础方法
func (s *DouyinCustomerService) buildReplyRequest(appID string, conversationID int64, msgID int64, msgType, content string) *DouyinReplyTextRequest {
	return &DouyinReplyTextRequest{
		MicroGameID:    appID,
		ConversationID: fmt.Sprintf("%d", conversationID),
		MsgID:          fmt.Sprintf("%d", msgID),
		CreateTime:     time.Now().Unix(),
		MsgType:        msgType,
		Content:        content,
	}
}

// BuildTextReplyRequest 构建文本回复请求
func (s *DouyinCustomerService) BuildTextReplyRequest(gameID string, conversationID int64, msgID int64, content, appID string) *DouyinReplyTextRequest {
	return s.buildReplyRequest(gameID, conversationID, msgID, "text", content)
}

// BuildLinkReplyRequest 构建链接回复请求
func (s *DouyinCustomerService) BuildLinkReplyRequest(ctx context.Context, targetGameID string, conversationID int64, msgID int64, title, url, thumbURL, openID, appID string) *DouyinReplyTextRequest {
	logger.Logger.InfofCtx(ctx, "[DouyinCustomerService.BuildLinkReplyRequest] 开始构建链接回复请求，OpenID: %s, URL: %s", openID, url)

	// 直接使用原始URL，参数已通过微信API的query机制处理
	var linkContent string
	if url == "" {
		linkContent = fmt.Sprintf("%s\n", title)
	} else {
		linkContent = fmt.Sprintf("<a rel=\"noopener noreferrer\" target=\"_blank\" href=\"%s\">%s</a>", url, title)
	}

	logger.Logger.InfofCtx(ctx, "[DouyinCustomerService.BuildLinkReplyRequest] 链接回复请求构建完成，OpenID: %s", openID)
	return s.buildReplyRequest(appID, conversationID, msgID, "link", linkContent)
}

// GenerateUniqueMessageID 生成唯一的消息ID
func (s *DouyinCustomerService) GenerateUniqueMessageID() string {
	// 使用时间戳 + 原子计数器确保唯一性
	counter := atomic.AddInt64(&messageIDCounter, 1)
	return fmt.Sprintf("%d_%d", time.Now().UnixNano(), counter)
}

// GetPlayerLinkByOpenID 根据OpenID获取玩家信息并生成链接参数字符串
func (s *DouyinCustomerService) GetPlayerLinkByOpenID(ctx context.Context, req struct {
	OpenID string `json:"open_id"`
}) (string, error) {
	logger.Logger.InfofCtx(ctx, "[DouyinCustomerService.GetPlayerLinkByOpenID] 开始查询玩家信息，OpenID: %s", req.OpenID)

	// 验证输入参数
	if req.OpenID == "" {
		logger.Logger.WarnfCtx(ctx, "[DouyinCustomerService.GetPlayerLinkByOpenID] OpenID不能为空")
		return "", fmt.Errorf("OpenID不能为空")
	}

	// 从数据库查询玩家信息
	playerDouyin := store.QueryDB().AGamePlayerDouyin
	player, err := playerDouyin.WithContext(ctx).
		Where(playerDouyin.OpenID.Eq(req.OpenID)).
		First()

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Logger.WarnfCtx(ctx, "[DouyinCustomerService.GetPlayerLinkByOpenID] 未找到玩家信息，OpenID: %s", req.OpenID)
			return "", fmt.Errorf("未找到玩家信息")
		}
		logger.Logger.ErrorfCtx(ctx, "[DouyinCustomerService.GetPlayerLinkByOpenID] 查询玩家信息失败，OpenID: %s, 错误: %v", req.OpenID, err)
		return "", fmt.Errorf("查询玩家信息失败: %w", err)
	}

	// 构建链接参数，排除 user_id 和 role_id 字段
	params := []string{}

	// 添加玩家信息，微信API不需要URL编码，但指定字段需base64编码
	if player.PlayerID != "" {
		encodedID := base64.StdEncoding.EncodeToString([]byte(player.PlayerID))
		params = append(params, "player_id="+encodedID)
	}
	if player.PlayerName != "" {
		encodedName := base64.StdEncoding.EncodeToString([]byte(player.PlayerName))
		params = append(params, "player_name="+encodedName)
	}
	if player.PlayerLevel > 0 {
		encodedLevel := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%d", player.PlayerLevel)))
		params = append(params, "player_level="+encodedLevel)
	}
	if player.RechargeTotalAmount > 0 {
		encodedAmount := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%d", player.RechargeTotalAmount)))
		params = append(params, "recharge_total_amount="+encodedAmount)
	}
	if player.CustomData != "" {
		encodedCustom := base64.StdEncoding.EncodeToString([]byte(player.CustomData))
		params = append(params, "custom_data="+encodedCustom)
	}
	if player.Zone != "" {
		encodedZone := base64.StdEncoding.EncodeToString([]byte(player.Zone))
		params = append(params, "zone="+encodedZone)
	}

	// 添加固定的source参数（注意：避免与玩家参数中的source重复）
	params = append(params, "source="+constants.SourceDouyin)

	// 拼接参数字符串
	paramString := strings.Join(params, "&")

	logger.Logger.InfofCtx(ctx, "[DouyinCustomerService.GetPlayerLinkByOpenID] 生成玩家参数成功，OpenID: %s, 参数: %s", req.OpenID, paramString)
	return paramString, nil
}

// addParamsToURL 将gameID和openID添加到URL中
func (s *DouyinCustomerService) addParamsToURL(url, gameID, openID string) string {
	params := []string{}
	if gameID != "" {
		params = append(params, "gameID="+neturl.QueryEscape(gameID))
	}
	if openID != "" {
		params = append(params, "open_id="+neturl.QueryEscape(openID))
	}
	// 添加固定的source参数
	params = append(params, "source="+constants.SourceDouyin)

	paramsStr := strings.Join(params, "&")

	// 检查是否是微信小程序URL格式 (https://wxaurl.cn/TICKET?cq=CUSTOM_PARAMETER 或 https://wxmpurl.cn/TICKET?cq=CUSTOM_PARAMETER)
	if strings.Contains(url, "://wxaurl.cn/") || strings.Contains(url, "://wxmpurl.cn/") {
		// 处理微信小程序URL的cq参数
		if cqIndex := strings.Index(url, "cq="); cqIndex != -1 {
			// 已存在cq参数，需要在其值中追加参数
			parsedURL, err := neturl.Parse(url)
			if err != nil {
				// 如果解析失败，回退到原始逻辑
				if strings.Contains(url, "?") {
					return url + "&" + paramsStr
				}
				return url + "?" + paramsStr
			}

			query := parsedURL.Query()
			existingCqValue := query.Get("cq")

			// 如果cq参数已有值，用&连接新参数；否则直接设置
			// 注意：这里的paramsStr需要按照微信规则进行编码
			var newCqValue string
			if existingCqValue != "" {
				// 对现有cq值和新参数进行微信规则编码
				newCqValue = s.encodeForWechatCq(existingCqValue + "&" + paramsStr)
			} else {
				newCqValue = s.encodeForWechatCq(paramsStr)
			}

			// 直接设置编码后的cq值，不使用query.Set()避免二次编码
			parsedURL.RawQuery = s.updateCqParameter(parsedURL.RawQuery, newCqValue)
			return parsedURL.String()
		} else {
			// 没有cq参数，需要添加cq参数
			encodedCqValue := s.encodeForWechatCq(paramsStr)
			if strings.Contains(url, "?") {
				return url + "&cq=" + encodedCqValue
			} else {
				return url + "?cq=" + encodedCqValue
			}
		}
	}

	// 普通URL参数处理
	if strings.Contains(url, "?") {
		return url + "&" + paramsStr
	}
	return url + "?" + paramsStr
}

// encodeForWechatCq 按照微信规则对cq参数值进行编码
// 微信规则：= 编码为 %3D，& 编码为 %26
func (s *DouyinCustomerService) encodeForWechatCq(value string) string {
	// 按照微信规则，对cq参数值中的特殊字符进行编码
	// = 编码为 %3D，& 编码为 %26
	encoded := strings.ReplaceAll(value, "=", "%3D")
	encoded = strings.ReplaceAll(encoded, "&", "%26")
	return encoded
}

// updateCqParameter 更新URL查询字符串中的cq参数值
func (s *DouyinCustomerService) updateCqParameter(rawQuery, newCqValue string) string {
	if rawQuery == "" {
		return "cq=" + newCqValue
	}

	// 解析现有的查询参数
	params := strings.Split(rawQuery, "&")
	var result []string
	cqUpdated := false

	for _, param := range params {
		if strings.HasPrefix(param, "cq=") {
			result = append(result, "cq="+newCqValue)
			cqUpdated = true
		} else {
			result = append(result, param)
		}
	}

	// 如果没有找到cq参数，添加它
	if !cqUpdated {
		result = append(result, "cq="+newCqValue)
	}

	return strings.Join(result, "&")
}

// buildWechatLinkContentWithPlayerParams 构建包含玩家参数的微信链接内容
func (s *DouyinCustomerService) buildWechatLinkContentWithPlayerParams(ctx context.Context, url, gameID, openID, title, desc, playerParams string) string {
	if url == "" {
		linkContent := fmt.Sprintf("%s\n%s", title, desc)
		logger.Logger.DebugfCtx(ctx, "[buildWechatLinkContentWithPlayerParams] 空URL情况，linkContent: %s", linkContent)
		return linkContent
	}

	// 添加gameID、openID和玩家参数到链接中
	linkWithParams := s.addParamsToURLWithPlayerParams(url, gameID, openID, playerParams)
	logger.Logger.DebugfCtx(ctx, "[buildWechatLinkContentWithPlayerParams] 原始URL: %s, GameID: %s, OpenID: %s, 玩家参数: %s, 处理后URL: %s", url, gameID, openID, playerParams, linkWithParams)
	linkContent := fmt.Sprintf("%s\n<a rel=\"noopener noreferrer\" target=\"_blank\" href=\"%s\">%s</a>", desc, linkWithParams, title)
	logger.Logger.DebugfCtx(ctx, "[buildWechatLinkContentWithPlayerParams] 最终linkContent: %s", linkContent)
	return linkContent
}

// addParamsToURLWithPlayerParams 将gameID、openID和玩家参数添加到URL中
func (s *DouyinCustomerService) addParamsToURLWithPlayerParams(url, gameID, openID, playerParams string) string {
	// 构建基础参数
	params := []string{}
	if gameID != "" {
		params = append(params, "gameID="+neturl.QueryEscape(gameID))
	}
	if openID != "" {
		params = append(params, "open_id="+neturl.QueryEscape(openID))
	}
	// 合并玩家参数，避免重复的source参数
	var allParams string
	if playerParams != "" {
		// 玩家参数已经在GetPlayerLinkByOpenID中进行了URL编码，且已包含source=douyin
		// 因此这里不再添加source参数，避免重复
		allParams = strings.Join(params, "&") + "&" + playerParams
	} else {
		// 没有玩家参数时，添加固定的source参数
		params = append(params, "source="+constants.SourceDouyin)
		allParams = strings.Join(params, "&")
	}

	// 检查是否是微信小程序URL格式
	if strings.Contains(url, "://wxaurl.cn/") || strings.Contains(url, "://wxmpurl.cn/") {
		// 处理微信小程序URL的cq参数
		if cqIndex := strings.Index(url, "cq="); cqIndex != -1 {
			// 已存在cq参数，需要在其值中追加参数
			parsedURL, err := neturl.Parse(url)
			if err != nil {
				// 如果解析失败，回退到原始逻辑
				if strings.Contains(url, "?") {
					return url + "&" + allParams
				}
				return url + "?" + allParams
			}

			query := parsedURL.Query()
			existingCqValue := query.Get("cq")

			// 如果cq参数已有值，用&连接新参数；否则直接设置
			var newCqValue string
			if existingCqValue != "" {
				// 对现有cq值和新参数进行微信规则编码
				newCqValue = s.encodeForWechatCq(existingCqValue + "&" + allParams)
			} else {
				newCqValue = s.encodeForWechatCq(allParams)
			}

			// 直接设置编码后的cq值
			parsedURL.RawQuery = s.updateCqParameter(parsedURL.RawQuery, newCqValue)
			return parsedURL.String()
		} else {
			// 没有cq参数，需要添加cq参数
			encodedCqValue := s.encodeForWechatCq(allParams)
			if strings.Contains(url, "?") {
				return url + "&cq=" + encodedCqValue
			} else {
				return url + "?cq=" + encodedCqValue
			}
		}
	}

	// 普通URL参数处理
	if strings.Contains(url, "?") {
		return url + "&" + allParams
	}
	return url + "?" + allParams
}
