package service

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"sync"
	"time"

	"net/http"
	"net/url"

	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/tencentyun/cos-go-sdk-v5"
	"gorm.io/gorm"
)

var (
	_uploadOnce    sync.Once
	_uploadService *UploadService
)

type UploadService struct{}

func SingletonUploadService() *UploadService {
	_uploadOnce.Do(func() {
		_uploadService = &UploadService{}
	})
	return _uploadService
}

// UploadFile 上传文件
func (s *UploadService) UploadFile(ctx context.Context, file *multipart.FileHeader) (string, error) {
	stream, err := file.Open()
	if err != nil {
		return "", err
	}
	defer stream.Close()

	// stream to []byte
	streamByte, err := io.ReadAll(stream)
	if err != nil {
		return "", err
	}
	md5 := util.EncodeMD5(string(streamByte))

	// 检查文件是否已存在
	fileUrl, err := s.GetFileUrl(ctx, md5)
	if err != nil {
		return "", err
	}
	if fileUrl != "" {
		return fileUrl, nil
	}

	// 上传到腾讯云COS
	u, _ := url.Parse(config.GlobConfig.OSS.BucketURL)
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
	})

	// 使用时间戳+随机数作为文件名前缀
	filePrefix := fmt.Sprintf("%d%d", time.Now().Unix(), util.GenRandomNum())
	fileName := fmt.Sprintf("/%s/%s-%s", config.GlobConfig.OSS.Env, filePrefix, file.Filename)
	_, err = client.Object.Put(ctx, fileName, bytes.NewReader(streamByte), nil)
	if err != nil {
		return "", err
	}

	// 构建文件URL
	fileURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.Domain, fileName)
	ossURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.BucketURL, fileName)

	// 保存文件信息到数据库
	err = s.CreateFileData(ctx, fileName, md5, fileURL, ossURL, file.Size)
	if err != nil {
		return "", err
	}

	return fileURL, nil
}

// GetFileUrl 根据MD5获取文件URL
func (s *UploadService) GetFileUrl(ctx context.Context, md5 string) (string, error) {
	upload := store.QueryDB().MUpload
	uploadCtx := upload.WithContext(ctx)
	uploadInfo, err := uploadCtx.Where(upload.Md5.Eq(md5), upload.IsDeleted.Zero()).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", err
	}
	if uploadInfo == nil {
		return "", nil
	}
	return uploadInfo.URL, nil
}

// CreateFileData 创建文件记录
func (s *UploadService) CreateFileData(ctx context.Context, fileName, md5, url, ossURL string, fileSize int64) error {
	// 获取文件扩展名
	fileType := util.GetFileType(fileName)

	upload := store.QueryDB().MUpload
	uploadCtx := upload.WithContext(ctx)
	err := uploadCtx.Create(&model.MUpload{
		FileName:    fileName,
		FileSize:    fileSize,
		FileType:    fileType,
		Md5:         md5,
		URL:         url,
		OssURL:      ossURL,
		OssBucket:   config.GlobConfig.OSS.BucketURL,
		Description: "工单附件",
		CreatedAt:   time.Now().UnixMilli(),
		UpdatedAt:   time.Now().UnixMilli(),
	})
	if err != nil {
		return err
	}
	return nil
}
