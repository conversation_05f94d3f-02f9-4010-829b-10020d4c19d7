package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"golang.org/x/sync/singleflight"
)

// DouyinConfig 抖音配置缓存项
type DouyinConfig struct {
	AppSecret    string    `json:"app_secret"`
	PaySecret    string    `json:"pay_secret"`
	AccessToken  string    `json:"access_token"`
	PayToken     string    `json:"pay_token"`
	RtcAppID     string    `json:"rtc_app_id"`
	RtcAppKey    string    `json:"rtc_app_key"`
	CachedAt     time.Time `json:"cached_at"`
}

// DouyinConfigCache 抖音配置缓存管理器
type DouyinConfigCache struct {
	cache             sync.Map
	ttl               time.Duration
	group             singleflight.Group
	mu                sync.RWMutex
	douyinConfigSvc   *DouyinConfigService
}

var (
	douyinConfigCacheOnce sync.Once
	douyinConfigCache     *DouyinConfigCache
)

// GetDouyinConfigCache 获取抖音配置缓存实例
func GetDouyinConfigCache() *DouyinConfigCache {
	douyinConfigCacheOnce.Do(func() {
		douyinConfigCache = &DouyinConfigCache{
			ttl:             30 * time.Minute, // 30分钟TTL
			douyinConfigSvc: SingletonDouyinConfigService(),
		}
	})
	return douyinConfigCache
}

// GetConfig 获取抖音配置，支持延迟加载和缓存
func (c *DouyinConfigCache) GetConfig(ctx context.Context, appID string) (*DouyinConfig, error) {
	// 首先尝试从缓存获取
	if config := c.getFromCache(appID); config != nil {
		return config, nil
	}

	// 缓存未命中，使用singleflight防止缓存击穿
	result, err, _ := c.group.Do(appID, func() (interface{}, error) {
		return c.loadFromDatabase(ctx, appID)
	})

	if err != nil {
		return nil, err
	}

	config := result.(*DouyinConfig)
	c.setToCache(appID, config)
	return config, nil
}

// getFromCache 从缓存获取配置
func (c *DouyinConfigCache) getFromCache(appID string) *DouyinConfig {
	value, ok := c.cache.Load(appID)
	if !ok {
		return nil
	}

	config := value.(*DouyinConfig)

	// 检查TTL
	if time.Since(config.CachedAt) > c.ttl {
		c.cache.Delete(appID)
		return nil
	}

	return config
}

// setToCache 设置缓存
func (c *DouyinConfigCache) setToCache(appID string, config *DouyinConfig) {
	config.CachedAt = time.Now()
	c.cache.Store(appID, config)
}

// loadFromDatabase 从数据库加载配置
func (c *DouyinConfigCache) loadFromDatabase(ctx context.Context, appID string) (*DouyinConfig, error) {
	logger.Logger.DebugfCtx(ctx, "Loading douyin config from database for appID: %s", appID)

	// 使用Service层获取配置
	douyinInfo, err := c.douyinConfigSvc.GetDouyinConfigByAppID(ctx, appID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "Failed to load douyin config for appID %s: %v", appID, err)
		return nil, fmt.Errorf("failed to load douyin config: %w", err)
	}

	config := &DouyinConfig{
		AppSecret:   douyinInfo.AppSecret,
		PaySecret:   douyinInfo.PaySecret,
		AccessToken: douyinInfo.AccessToken,
		PayToken:    douyinInfo.PayToken,
		RtcAppID:    douyinInfo.RtcAppID,
		RtcAppKey:   douyinInfo.RtcAppKey,
	}

	logger.Logger.InfofCtx(ctx, "Successfully loaded douyin config for appID: %s", appID)
	return config, nil
}

// RefreshConfig 手动刷新指定appID的配置
func (c *DouyinConfigCache) RefreshConfig(ctx context.Context, appID string) error {
	c.cache.Delete(appID)
	_, err := c.GetConfig(ctx, appID)
	return err
}

// RefreshAllConfigs 刷新所有缓存配置
func (c *DouyinConfigCache) RefreshAllConfigs(ctx context.Context) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 收集所有的appID
	var appIDs []string
	c.cache.Range(func(key, value interface{}) bool {
		appIDs = append(appIDs, key.(string))
		return true
	})

	// 清空缓存
	c.cache = sync.Map{}

	// 重新加载所有配置
	for _, appID := range appIDs {
		go func(id string) {
			if _, err := c.GetConfig(ctx, id); err != nil {
				logger.Logger.WarnfCtx(ctx, "Failed to refresh config for appID %s: %v", id, err)
			}
		}(appID)
	}
}

// GetCacheStats 获取缓存统计信息
func (c *DouyinConfigCache) GetCacheStats() map[string]interface{} {
	stats := make(map[string]interface{})
	count := 0

	c.cache.Range(func(key, value interface{}) bool {
		count++
		return true
	})

	stats["cached_items"] = count
	stats["ttl_minutes"] = int(c.ttl.Minutes())

	return stats
}
