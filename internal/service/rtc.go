package service

import (
	"context"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/https"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"github.com/jinzhu/copier"
)

type RTCService struct {
	douyinHttpService *https.DouyinHttpService
}

var (
	_rtcOnce    sync.Once
	_rtcService *RTCService
)

func SingletonRTCService() *RTCService {
	_rtcOnce.Do(func() {
		_rtcService = &RTCService{
			douyinHttpService: https.SingletonDouyinHttpService(),
		}
	})
	return _rtcService
}

// CreateRoomID
func (s *RTCService) CreateRoomID(ctx context.Context, gameID, accessToken, platformType string) (*bean.CreateRoomIDResp, error) {
	switch platformType {
	case constants.PlatformTypeMinigame:
		// For minigames, we generate a unique ID
		return &bean.CreateRoomIDResp{
			GroupID: util.UUIDWithoutHyphens(),
		}, nil
	case constants.PlatformTypeDouyin:
		// For Douyin, we need to fetch a channel ID using the access token
		channelID, err := s.fetchDouyinChannelID(ctx, accessToken)
		if err != nil {
			return nil, err
		}
		return &bean.CreateRoomIDResp{
			GroupID: channelID,
		}, nil
	default:
		return nil, constants.ErrPlatformTypeNotSupport
	}
}

func (s *RTCService) fetchDouyinChannelID(ctx context.Context, accessToken string) (string, error) {
	resp, err := s.douyinHttpService.GenerateRoomID(ctx, accessToken)
	if err != nil {
		return "", err
	}
	if resp.Data.ChannelID == "" {
		return "", constants.ErrDouyinChannelIDIsEmpty
	}
	return resp.Data.ChannelID, nil
}

// GetRoomID
func (s *RTCService) GetRoomID(ctx context.Context, gameID, platformType string) (*bean.GetRoomIDResp, error) {
	voip := store.QueryDB().AVoip
	voipList, err := voip.WithContext(ctx).Where(voip.GameID.Eq(gameID)).Where(voip.PlatformType.Eq(platformType)).Where(voip.IsDeleted.Zero()).Find()
	if err != nil {
		return nil, err
	}

	roomList := make([]*bean.Voip, len(voipList))
	for i := range voipList {
		roomList[i] = &bean.Voip{}
		err = copier.Copy(roomList[i], voipList[i])
		if err != nil {
			return nil, err
		}
	}
	return &bean.GetRoomIDResp{
		RoomList: roomList,
	}, nil
}
