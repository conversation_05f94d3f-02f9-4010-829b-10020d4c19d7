package service

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/https"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

var (
	_qqOnce    sync.Once
	_qqService *QQService
)

type QQService struct {
	qqHttpService *https.QQHttpService
}

func SingletonQQService() *QQService {
	_qqOnce.Do(func() {
		_qqService = &QQService{
			qqHttpService: https.SingletonQQHttpService(),
		}
	})
	return _qqService
}

// GetAccessToken get QQ access token
func (s *QQService) GetAccessToken(ctx context.Context, appID, appSecret string) (*bean.QQAccessToken, error) {
	return s.qqHttpService.GetAccessToken(ctx, appID, appSecret)
}

// refreshTokenForQQConfig
func (s *QQService) RefreshTokenForQQConfig(ctx context.Context, config *model.AConfigQq) error {
	accessTokenResp, err := s.GetAccessToken(ctx, config.AppID, config.AppSecret)
	if err != nil {
		return err
	}
	err = s.UpdateQQConfigToken(ctx, config.GameID, accessTokenResp.AccessToken, accessTokenResp.ExpiresIn)
	if err != nil {
		return err
	}
	return nil
}

// UpdateQQConfigToken update QQ config token
func (s *QQService) UpdateQQConfigToken(ctx context.Context, gameID, accessToken string, expiresIn int32) error {
	qq := store.QueryDB().AConfigQq
	_, err := qq.WithContext(ctx).Where(qq.GameID.Eq(gameID)).
		UpdateSimple(qq.AccessToken.Value(accessToken), qq.ExpiresIn.Value(expiresIn))
	if err != nil {
		return err
	}
	return nil
}

func (s *QQService) LoginQQ(ctx context.Context, code, accessToken, appID, secret string) (*bean.QQLoginResp, error) {
	// 使用 code 和 secret 获取用户信息
	return s.qqHttpService.LoginQQ(ctx, code, accessToken, appID, secret)
}

// GetQQConfs 获取QQ配置
func (s *QQService) GetQQConfs(ctx context.Context) ([]*model.AConfigQq, error) {
	qq := store.QueryDB().AConfigQq
	qqCtx := qq.WithContext(ctx)
	return qqCtx.Where(qq.IsDeleted.Zero()).Find()
}

func (s *QQService) GetQQConf(ctx context.Context, gameID string) (*model.AConfigQq, error) {
	qq := store.QueryDB().AConfigQq
	qqCtx := qq.WithContext(ctx)
	return qqCtx.Where(qq.GameID.Eq(gameID)).Where(qq.IsDeleted.Zero()).First()
}

// GetUserInfo 获取用户信息
func (s *QQService) GetUserInfo(ctx context.Context, gameID string, code string, loginResp *bean.QQLoginResp, channel *bean.ChannelInfo) (*bean.User, error) {
	// 使用分布式锁防止并发问题
	openIDLockKey := fmt.Sprintf("%s:%s", constants.SystemLoginQQLockKey, code)
	if !redis.Lock(ctx, openIDLockKey, constants.SystemLoginQQLockExpire*time.Second) {
		logger.Logger.Warnf("GetUserInfo qq server is busy, game_id :%s, openID :%s", gameID, loginResp.OpenID)
		return nil, constants.ErrSystemServiceIsBusy
	}
	defer redis.UnLock(ctx, openIDLockKey)

	// 查询用户信息
	qqUser := store.QueryDB().AUserQq
	userInfo, err := qqUser.WithContext(ctx).Where(qqUser.OpenID.Eq(loginResp.OpenID), qqUser.IsDeleted.Zero()).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 未找到记录，创建新用户
			return s.createQQUser(ctx, gameID, loginResp, channel)
		}
		return nil, err
	}

	// 更新SessionKey
	if err := s.updateSessionKey(ctx, userInfo.UserID, loginResp.SessionKey); err != nil {
		return nil, err
	}

	// 转换为用户信息
	userRes, err := s.mapUserInfoToUser(userInfo)
	if err != nil {
		return nil, err
	}
	userRes.IsRegister = false
	userRes.ChannelInfo = channel
	return userRes, nil
}

// createQQUser 新建QQ用户和AUser
func (s *QQService) createQQUser(ctx context.Context, gameID string, loginResp *bean.QQLoginResp, channel *bean.ChannelInfo) (*bean.User, error) {
	qqUser := store.QueryDB().AUserQq
	user := store.QueryDB().AUser

	newUserID := util.UUID()
	qqUserModel := &model.AUserQq{
		UserID:     newUserID,
		OpenID:     loginResp.OpenID,
		UnionID:    loginResp.UnionID,
		SessionKey: loginResp.SessionKey,
	}
	userModel := &model.AUser{
		GameID:  gameID,
		UserID:  newUserID,
		Channel: channel.Channel,
		AdFrom:  channel.ADFrom,
	}
	if err := qqUser.WithContext(ctx).Create(qqUserModel); err != nil {
		return nil, err
	}
	if err := user.WithContext(ctx).Create(userModel); err != nil {
		return nil, err
	}
	return &bean.User{
		IsRegister: true,
		UserInfo: &bean.UserInfo{
			UserID: newUserID,
		},
		ChannelInfo: &bean.ChannelInfo{
			Channel: channel.Channel,
			ADFrom:  channel.ADFrom,
			OpenID:  channel.OpenID,
		},
	}, nil
}

func (s *QQService) updateSessionKey(ctx context.Context, userID string, sessionKey string) error {
	qqUser := store.QueryDB().AUserQq
	_, err := qqUser.WithContext(ctx).Where(qqUser.UserID.Eq(userID)).Where(qqUser.IsDeleted.Zero()).
		UpdateSimple(qqUser.SessionKey.Value(sessionKey))
	if err != nil {
		return err
	}
	return nil
}

func (s *QQService) mapUserInfoToUser(userInfo *model.AUserQq) (*bean.User, error) {
	// 直接将 userInfo 转换为 bean.User
	user := &bean.User{
		UserInfo: &bean.UserInfo{
			UserID:    userInfo.UserID,
			NickName:  userInfo.NickName,
			AvatarURL: userInfo.AvatarURL,
		},
	}

	return user, nil
}

// GetUserQQModel 获取QQ用户信息
func (s *QQService) GetUserQQModel(ctx context.Context, userID string) (*model.AUserQq, error) {
	qqUser := store.QueryDB().AUserQq
	userInfo, err := qqUser.WithContext(ctx).Where(qqUser.UserID.Eq(userID), qqUser.IsDeleted.Zero()).First()
	if err != nil {
		return nil, err
	}
	return userInfo, nil
}

// UpdateQQInfo 更新QQ用户信息
func (s *QQService) UpdateQQInfo(ctx context.Context, userID string, qqUserInfo *bean.WechatUserInfo) error { // 微信和QQ的结构通用，统一使用微信结构
	qqUser := store.QueryDB().AUserQq
	_, err := qqUser.WithContext(ctx).Where(qqUser.UserID.Eq(userID), qqUser.IsDeleted.Zero()).
		UpdateSimple(qqUser.NickName.Value(qqUserInfo.NickName), qqUser.AvatarURL.Value(qqUserInfo.AvatarURL))
	if err != nil {
		return err
	}
	return nil
}

// GetDouyinUserInfo gets the
func (s *QQService) GetQQUserInfo(ctx context.Context, userID string) (*bean.UserInfo, error) {
	qq := store.QueryDB().AUserQq
	qqUserInfo, err := qq.WithContext(ctx).Where(qq.UserID.Eq(userID)).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Errorf(constants.LogUserIDNotFound, userID)
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	userInfo := &bean.UserInfo{}
	err = copier.Copy(&userInfo, qqUserInfo)
	if err != nil {
		return nil, err
	}
	userInfo.Gender = strconv.Itoa(int(qqUserInfo.Gender))
	userInfo.WatermarkTimestamp = strconv.FormatInt(qqUserInfo.WatermarkTimestamp, 10)
	if qqUserInfo.NickName != "" {
		userInfo.IsExistUserInfo = constants.TrueStr // 用于加密，所以使用字符串类型
	}
	return userInfo, nil
}
