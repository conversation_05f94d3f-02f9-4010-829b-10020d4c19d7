package service

import (
	"context"
	"fmt"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/google/uuid"
)

type ContentService struct{}

func SingletonContentService() *ContentService {
	return &ContentService{}
}

// ReportContent 上报监控内容
func (s *ContentService) ReportContent(ctx context.Context, req *bean.ReportContentReq) (*bean.ReportContentResp, error) {
	logger.Logger.InfofCtx(ctx, "ContentService ReportContent 开始处理内容上报, GameID: %s, PlatformID: %s, SourceType: %s, ServerID: %s, RoleID: %s",
		req.GameID, req.UserID, req.SourceType, req.ServerID, req.RoleID)

	// 验证内容不为空
	if req.Content == "" {
		logger.Logger.WarnfCtx(ctx, "ContentService ReportContent 内容为空")
		return nil, constants.ErrInvalidContentFormat
	}

	contentID := uuid.New().String()
	now := time.Now().UnixMilli()
	expireAt := now + constants.ThirtyOneDaysInMillis // 30天后过期

	// 将bool类型转换为int32类型
	var isAllianceLeader int32
	if req.IsAllianceLeader {
		isAllianceLeader = 1
	} else {
		isAllianceLeader = 0
	}

	content := &model.MMonitorGameContent{
		ContentID:        contentID,
		GameID:           req.GameID,
		UserID:           req.UserID,
		SessionFrom:      req.SessionFrom,
		SourceType:       req.SourceType,
		ServerID:         req.ServerID,
		ServerName:       req.ServerName,
		RoleID:           req.RoleID,
		RoleName:         req.RoleName,
		RoleLevel:        req.RoleLevel,
		AllianceID:       req.AllianceID,
		AllianceName:     req.AllianceName,
		IsAllianceLeader: isAllianceLeader,
		Content:          req.Content,
		CreatedAt:        now,
		ExpireAt:         expireAt,
	}

	// 保存内容
	contents := store.QueryDB().MMonitorGameContent
	logger.Logger.DebugfCtx(ctx, "ContentService ReportContent 开始保存内容到数据库, ContentID: %s, GameID: %s", contentID, req.GameID)
	err := contents.WithContext(ctx).Create(content)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService ReportContent 保存内容到数据库失败: %v", err)
		return nil, fmt.Errorf("create content failed: %w", err)
	}
	logger.Logger.DebugfCtx(ctx, "ContentService ReportContent 内容保存成功, ContentID: %s", contentID)

	// 更新区服信息
	logger.Logger.DebugfCtx(ctx, "ContentService ReportContent 开始更新区服信息, GameID: %s, ServerID: %s, ServerName: %s", req.GameID, req.ServerID, req.ServerName)
	err = s.upsertServerInfo(ctx, req.GameID, req.ServerID, req.ServerName, req.UserID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService ReportContent 更新区服信息失败: %v", err)
		// 不影响主流程，只记录日志
	} else {
		logger.Logger.DebugfCtx(ctx, "ContentService ReportContent 区服信息更新成功")
	}

	logger.Logger.DebugfCtx(ctx, "ContentService ReportContent 处理完成, ContentID: %s, GameID: %s", contentID, req.GameID)
	return &bean.ReportContentResp{
		ContentID: contentID,
	}, nil
}

// isValidSourceType 验证文本来源类型
func (s *ContentService) isValidSourceType(sourceType string) bool {
	validTypes := []string{
		constants.SourceTypePublicChat,
		constants.SourceTypeAllianceChat,
		constants.SourceTypePrivateChat,
		constants.SourceTypeRoleName,
		constants.SourceTypeAllianceName,
		constants.SourceTypeAllianceAnnouncement,
	}

	for _, validType := range validTypes {
		if sourceType == validType {
			return true
		}
	}
	return false
}

// upsertServerInfo 更新区服信息
func (s *ContentService) upsertServerInfo(ctx context.Context, gameID, serverID, serverName, platformID string) error {
	servers := store.QueryDB().MMonitorGameServer
	now := time.Now().UnixMilli()

	// 查找是否已存在
	existingServer, err := servers.WithContext(ctx).
		Where(servers.GameID.Eq(gameID)).
		Where(servers.ServerID.Eq(serverID)).
		Where(servers.PlatformID.Eq(platformID)).
		First()

	if err != nil {
		// 不存在，创建新记录
		newServer := &model.MMonitorGameServer{
			GameID:     gameID,
			ServerID:   serverID,
			ServerName: serverName,
			PlatformID: platformID,
			CreatedAt:  now,
			UpdatedAt:  now,
		}

		err = servers.WithContext(ctx).Create(newServer)
		if err != nil {
			return fmt.Errorf("create server info failed: %w", err)
		}
	} else {
		// 存在，更新服务器名称（如果有变化）
		if existingServer.ServerName != serverName {
			_, err = servers.WithContext(ctx).
				Where(servers.ID.Eq(existingServer.ID)).
				Update(servers.ServerName, serverName)
			if err != nil {
				return fmt.Errorf("update server name failed: %w", err)
			}
		}
	}

	return nil
}

// UpdateContentProcessingStatus 更新内容处理状态
func (s *ContentService) UpdateContentProcessingStatus(ctx context.Context, processingID string) error {
	logger.Logger.InfofCtx(ctx, "ContentService UpdateContentProcessingStatus 开始更新内容处理状态, ProcessingID: %s", processingID)

	// 这里可以根据业务需求更新内容处理记录的状态
	// 例如：更新回调状态、处理完成状态等
	// 目前先记录日志，具体的状态更新逻辑可以根据实际需求添加

	logger.Logger.InfofCtx(ctx, "ContentService UpdateContentProcessingStatus 内容处理状态更新完成, ProcessingID: %s", processingID)
	return nil
}

// UpdateContentStatusByContentID 根据ContentID更新内容状态
func (s *ContentService) UpdateContentStatusByContentID(ctx context.Context, contentID string) error {
	logger.Logger.InfofCtx(ctx, "ContentService UpdateContentStatusByContentID 开始更新内容状态, ContentID: %s", contentID)

	contents := store.QueryDB().MMonitorGameContent

	// 更新状态为已处理 (1)
	_, err := contents.WithContext(ctx).
		Where(contents.ContentID.Eq(contentID)).
		Where(contents.IsDeleted.Is(false)).
		Update(contents.Status, int32(1))

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "ContentService UpdateContentStatusByContentID 更新内容状态失败, ContentID: %s, error: %v", contentID, err)
		return fmt.Errorf("update content status failed: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "ContentService UpdateContentStatusByContentID 内容状态更新完成, ContentID: %s", contentID)
	return nil
}
