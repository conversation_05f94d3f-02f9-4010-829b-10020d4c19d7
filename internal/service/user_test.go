package service

import (
	"context"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/mysql"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"testing"
)

func Init() {
	mustInit()
	initLogger()
	initMysql()
	initCron()
	initRedis()
}

func mustInit() {
	config.MustInit()
}

func initLogger() {
	logger.InitLogger(&config.GlobConfig.Logger)
}

func initMysql() {
	mysql.InitMysql(&config.GlobConfig.Mysql)
	store.InitQueryDB()
}

func initCron() {
	//cron.InitCron()
}

func initRedis() {
	redis.InitRedis(&config.GlobConfig.Redis)
}

// 测试函数
func TestUserService_GetUserInfoByOpenID(t *testing.T) {
	Init()
	userMinigame, err := SingletonUserService().GetUserInfoByOpenID(context.Background(),
		"", "mygame", "", "1111", "22221111", "33311111", "44411111")
	if err != nil {
		return
	}
	logger.Logger.Debug("userMinigame :")
	logger.Logger.Debug(userMinigame)
}

func TestUserService_GetCode2Session(t *testing.T) {
	Init()
	res, err := SingletonMinigameService().GetCode2Session(context.Background(), "", "", "0b1rtjGa18NDhH0KQvJa1Q4V5N3rtjG9")
	if err != nil {
		return
	}
	logger.Logger.Debug("res :")
	logger.Logger.Debug(res)
}
