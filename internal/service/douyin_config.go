package service

import (
	"context"
	"fmt"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

// DouyinConfigService 抖音配置服务
type DouyinConfigService struct{}

var douyinConfigServiceInstance *DouyinConfigService

// SingletonDouyinConfigService 获取抖音配置服务单例
func SingletonDouyinConfigService() *DouyinConfigService {
	if douyinConfigServiceInstance == nil {
		douyinConfigServiceInstance = &DouyinConfigService{}
	}
	return douyinConfigServiceInstance
}

// GetDouyinConfigByAppID 根据AppID获取抖音配置
func (s *DouyinConfigService) GetDouyinConfigByAppID(ctx context.Context, appID string) (*model.AConfigDouyin, error) {
	logger.Logger.DebugfCtx(ctx, "Getting douyin config for appID: %s", appID)

	douyinConfig := store.QueryDB().AConfigDouyin
	config, err := douyinConfig.WithContext(ctx).
		Where(douyinConfig.AppID.Eq(appID)).
		Where(douyinConfig.IsDeleted.Is(false)).
		First()

	if err != nil {
		logger.Logger.WarnfCtx(ctx, "Failed to get douyin config for appID %s: %v", appID, err)
		return nil, fmt.Errorf("failed to get douyin config for appID %s: %w", appID, err)
	}

	logger.Logger.DebugfCtx(ctx, "Successfully got douyin config for appID: %s", appID)
	return config, nil
}

// GetDouyinConfigByGameID 根据GameID获取抖音配置
func (s *DouyinConfigService) GetDouyinConfigByGameID(ctx context.Context, gameID string) (*model.AConfigDouyin, error) {
	logger.Logger.DebugfCtx(ctx, "Getting douyin config for gameID: %s", gameID)

	douyinConfig := store.QueryDB().AConfigDouyin
	config, err := douyinConfig.WithContext(ctx).
		Where(douyinConfig.GameID.Eq(gameID)).
		Where(douyinConfig.IsDeleted.Is(false)).
		First()

	if err != nil {
		logger.Logger.WarnfCtx(ctx, "Failed to get douyin config for gameID %s: %v", gameID, err)
		return nil, fmt.Errorf("failed to get douyin config for gameID %s: %w", gameID, err)
	}

	logger.Logger.DebugfCtx(ctx, "Successfully got douyin config for gameID: %s", gameID)
	return config, nil
}

// ListDouyinConfigs 获取所有抖音配置列表
func (s *DouyinConfigService) ListDouyinConfigs(ctx context.Context) ([]*model.AConfigDouyin, error) {
	logger.Logger.DebugfCtx(ctx, "Getting all douyin configs")

	douyinConfig := store.QueryDB().AConfigDouyin
	configs, err := douyinConfig.WithContext(ctx).
		Where(douyinConfig.IsDeleted.Is(false)).
		Find()

	if err != nil {
		logger.Logger.WarnfCtx(ctx, "Failed to get douyin configs: %v", err)
		return nil, fmt.Errorf("failed to get douyin configs: %w", err)
	}

	logger.Logger.DebugfCtx(ctx, "Successfully got %d douyin configs", len(configs))
	return configs, nil
}

// UpdateDouyinConfig 更新抖音配置
func (s *DouyinConfigService) UpdateDouyinConfig(ctx context.Context, appID string, updates map[string]interface{}) error {
	logger.Logger.DebugfCtx(ctx, "Updating douyin config for appID: %s", appID)

	douyinConfig := store.QueryDB().AConfigDouyin
	_, err := douyinConfig.WithContext(ctx).
		Where(douyinConfig.AppID.Eq(appID)).
		Where(douyinConfig.IsDeleted.Is(false)).
		Updates(updates)

	if err != nil {
		logger.Logger.WarnfCtx(ctx, "Failed to update douyin config for appID %s: %v", appID, err)
		return fmt.Errorf("failed to update douyin config for appID %s: %w", appID, err)
	}

	logger.Logger.InfofCtx(ctx, "Successfully updated douyin config for appID: %s", appID)
	return nil
}

// CreateDouyinConfig 创建抖音配置
func (s *DouyinConfigService) CreateDouyinConfig(ctx context.Context, config *model.AConfigDouyin) error {
	logger.Logger.DebugfCtx(ctx, "Creating douyin config for appID: %s", config.AppID)

	douyinConfig := store.QueryDB().AConfigDouyin
	err := douyinConfig.WithContext(ctx).Create(config)

	if err != nil {
		logger.Logger.WarnfCtx(ctx, "Failed to create douyin config for appID %s: %v", config.AppID, err)
		return fmt.Errorf("failed to create douyin config for appID %s: %w", config.AppID, err)
	}

	logger.Logger.InfofCtx(ctx, "Successfully created douyin config for appID: %s", config.AppID)
	return nil
}

// DeleteDouyinConfig 删除抖音配置（软删除）
func (s *DouyinConfigService) DeleteDouyinConfig(ctx context.Context, appID string) error {
	logger.Logger.DebugfCtx(ctx, "Deleting douyin config for appID: %s", appID)

	douyinConfig := store.QueryDB().AConfigDouyin
	_, err := douyinConfig.WithContext(ctx).
		Where(douyinConfig.AppID.Eq(appID)).
		Where(douyinConfig.IsDeleted.Is(false)).
		Update(douyinConfig.IsDeleted, true)

	if err != nil {
		logger.Logger.WarnfCtx(ctx, "Failed to delete douyin config for appID %s: %v", appID, err)
		return fmt.Errorf("failed to delete douyin config for appID %s: %w", appID, err)
	}

	logger.Logger.InfofCtx(ctx, "Successfully deleted douyin config for appID: %s", appID)
	return nil
}