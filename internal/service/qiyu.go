package service

import (
	"context"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/https"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/go-resty/resty/v2"
	"github.com/jinzhu/copier"
)

// qiyu service
var (
	_qiyuOnce    sync.Once
	_qiyuService *QiyuService
)

type QiyuService struct {
	https *https.DouyinHttpService
}

func SingletonQiyuService() *QiyuService {
	_qiyuOnce.Do(func() {
		_qiyuService = &QiyuService{
			https: https.SingletonDouyinHttpService(),
		}
	})
	return _qiyuService
}

var (
	createSessionUrl = "/openapi/robot/session/create"
	sessionChatUrl   = "/openapi/robot/session/chat"
	// https://qiyukf.com//openapi/v2/ticket/template/fields?appKey=[APP_KEY]&time=[TIME]&checksum=[CHECKSUM]
	getTicketTemplateUrl = "/openapi/v2/ticket/template/fields"
	// https://qiyukf.com/openapi/v2/ticket/create?appKey=[APP_KEY]&time=[TIME]&checksum=[CHECKSUM]
	createTicketUrl = "/openapi/v2/ticket/create"
	// https://qiyukf.com/openapi/v2/ticket/search?appKey=[APP_KEY]&time=[TIME]&checksum=[CHECKSUM]
	getSupportTicketsUrl = "/openapi/v2/ticket/search"
	// https://qiyukf.com/openapi/v2/ticket/log?appKey=[APP_KEY]&time=[TIME]&checksum=[CHECKSUM]
	getTicketLogsUrl = "/openapi/v2/ticket/log"

	// https://qiyukf.com/openapi/v2/ticket/new/detail
	getTicketDetailUrl = "/openapi/v2/ticket/new/detail"

	// /openapi/v2/ticket/reply
	replyTicketUrl = "/openapi/v2/ticket/reply"
)

// GetQiyuAuthCode
func (s *QiyuService) GetQiyuAuthCode(ctx context.Context, req *bean.GetQiyuAuthCodeReq) (*bean.GetQiyuAuthCodeResp, error) {
	authCode := util.EncodeMD5Salt(req.GameID+req.OpenID+fmt.Sprintf("%d", time.Now().UnixMilli()), constants.Md5Salt)
	redisKey := fmt.Sprintf(constants.RedisQiyuAuthCode, req.GameID, req.OpenID)

	// 写入redis， 有效时间为5分钟
	if err := redis.Set(ctx, redisKey+authCode, authCode, 5*time.Minute); err != nil {
		if errors.Is(err, redis.Nil) {
			return nil, constants.ErrQiyuAuthCodeIsEmpty
		}
		logger.Logger.ErrorfCtx(ctx, "failed to set redis: %v", err)
		return nil, err
	}

	return &bean.GetQiyuAuthCodeResp{Code: authCode}, nil
}

// QiyuAuthLogin
func (s *QiyuService) QiyuAuthLogin(ctx context.Context, req *bean.QiyuAuthLoginReq, openID, gameName string) (*bean.QiyuAuthLoginResp, error) {
	// 注意：这里使用req.OpenID 为小游戏的openID, 临时作为用户ID，而openID是客服小程序的openID
	token, err := util.GenerateToken(req.OpenID, "", "", req.GameID, openID, gameName)
	if err != nil {
		return nil, err
	}
	return &bean.QiyuAuthLoginResp{Token: token}, nil
}

func (s *QiyuService) GenQiyuPushCheckSum(appSecret, md5, timestamp string) string {
	content := appSecret + md5 + timestamp
	hash := sha1.New()
	hash.Write([]byte(content))
	return hex.EncodeToString(hash.Sum(nil))
}

// /openapi/robot/session/create
func (s *QiyuService) CreateSession(ctx context.Context, appKey, appSecret string, req *bean.CreateSessionReq) (*bean.CreateSessionResp, error) {
	logger.Logger.InfofCtx(ctx, "create session %+v", req)

	// req to byte
	body, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	md5 := util.EncodeMD5(string(body))

	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	resp, err := resty.New().R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"appKey":   appKey,
			"time":     timestamp,
			"checksum": s.GenQiyuPushCheckSum(appSecret, md5, timestamp),
		}).
		SetBody(body).
		Post(config.GlobConfig.Qiyu.BaseURL + createSessionUrl)

	if err != nil {
		return nil, err
	}

	// CreateSessionResp
	createSessionResp := &bean.CreateSessionResp{}
	err = json.Unmarshal(resp.Body(), createSessionResp)
	if err != nil {
		return nil, err
	}

	if createSessionResp.Code != http.StatusOK { // 七鱼返回状态码为200
		logger.Logger.ErrorfCtx(ctx, "failed to create session: %+v", createSessionResp)
		return nil, fmt.Errorf("failed to create session: %+v", createSessionResp)
	}

	return createSessionResp, nil
}

// /openapi/robot/session/chat

// SessionChat
func (s *QiyuService) SessionChat(ctx context.Context, appKey, appSecret string, req *bean.SessionChatReq) (*bean.SessionChatResp, error) {
	logger.Logger.InfofCtx(ctx, "session chat %+v", req)

	body, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	md5 := util.EncodeMD5(string(body))

	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	resp, err := resty.New().R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"appKey":   appKey,
			"time":     timestamp,
			"checksum": s.GenQiyuPushCheckSum(appSecret, md5, timestamp),
		}).
		SetBody(body).
		Post(config.GlobConfig.Qiyu.BaseURL + sessionChatUrl)
	if err != nil {
		return nil, err
	}

	// SessionChatResp
	sessionChatResp := &bean.SessionChatResp{}
	err = json.Unmarshal(resp.Body(), sessionChatResp)
	if err != nil {
		return nil, err
	}

	return sessionChatResp, nil
}

// CreateSupportTicket
func (s *QiyuService) CreateSupportTicket(ctx context.Context, appKey, appSecret string, req *bean.CreateSupportTicketReq) (*bean.CreateSupportTicketResp, error) {
	logger.Logger.InfofCtx(ctx, "create support ticket")
	req.StaffID = constants.DefaultQuestionTicketStaffID // 指定客服ID为管理员
	req.UID = req.OpenID
	req.UserName = req.UserID

	supportTicketReq := &bean.SupportTicketReq{}
	err := copier.Copy(supportTicketReq, req)
	if err != nil {
		return nil, err
	}

	body, err := json.Marshal(supportTicketReq)
	if err != nil {
		return nil, err
	}
	md5 := util.EncodeMD5(string(body))

	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	resp, err := resty.New().R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"appKey":   appKey,
			"time":     timestamp,
			"checksum": s.GenQiyuPushCheckSum(appSecret, md5, timestamp),
		}).
		SetBody(body).
		Post(config.GlobConfig.Qiyu.BaseURL + createTicketUrl)
	if err != nil {
		return nil, err
	}

	// CreateSupportTicketResp
	createSupportTicketResp := &bean.CreateSupportTicketResp{}
	err = json.Unmarshal(resp.Body(), createSupportTicketResp)
	if err != nil {
		return nil, err
	}

	return createSupportTicketResp, nil
}

// GetTicketLogs
func (s *QiyuService) GetTicketLogs(ctx context.Context, appKey, appSecret string, req *bean.GetTicketLogsReq) (*bean.GetTicketLogsResp, error) {
	logger.Logger.InfofCtx(ctx, "get ticket logs")

	timestamp := fmt.Sprintf("%d", time.Now().Unix())

	logsReq := map[string]interface{}{
		"ticketId": req.TicketID,
	}

	body, err := json.Marshal(logsReq)
	if err != nil {
		return nil, err
	}
	md5 := util.EncodeMD5(string(body))

	resp, err := resty.New().R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"appKey":   appKey,
			"time":     timestamp,
			"checksum": s.GenQiyuPushCheckSum(appSecret, md5, timestamp),
		}).
		SetBody(body).
		Post(config.GlobConfig.Qiyu.BaseURL + getTicketLogsUrl)
	if err != nil {
		return nil, err
	}

	// TicketLogsResp
	ticketLogsResp := &bean.GetTicketLogsResp{}
	err = json.Unmarshal(resp.Body(), ticketLogsResp)
	if err != nil {
		return nil, err
	}

	// respData := make([]bean.TicketLogsData, 0)
	// err = json.Unmarshal([]byte(ticketLogsResp.Data), &respData)
	// if err != nil {
	// 	return nil, err
	// }

	return ticketLogsResp, nil
}

// GetSupportTickets
func (s *QiyuService) GetSupportTickets(ctx context.Context, appKey, appSecret string, req *bean.GetSupportTicketsReq) (*bean.GetSupportTicketsResp, error) {
	logger.Logger.InfofCtx(ctx, "get support tickets")
	if req.Offset == 0 {
		req.Offset = 1
	}
	offset := (req.Offset - 1) * req.Limit

	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	ticketsReq := map[string]interface{}{
		"limit":           req.Limit,
		"offset":          offset,
		"uid":             req.OpenID,
		"sortBy":          "ct",
		"order":           "desc",
		"withCustomField": true,
	}

	if !req.IsAll {
		ticketsReq["mobile"] = req.UserID
	}

	body, err := json.Marshal(ticketsReq)
	if err != nil {
		return nil, err
	}
	md5 := util.EncodeMD5(string(body))

	resp, err := resty.New().R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"appKey":   appKey,
			"time":     timestamp,
			"checksum": s.GenQiyuPushCheckSum(appSecret, md5, timestamp),
		}).
		SetBody(body).
		Post(config.GlobConfig.Qiyu.BaseURL + getSupportTicketsUrl)
	if err != nil {
		return nil, err
	}

	// GetSupportTicketsResp
	ticketsResp := &bean.SupportTicketsResp{}
	err = json.Unmarshal(resp.Body(), ticketsResp)
	if err != nil {
		return nil, err
	}

	supportTicketsData := bean.SupportTicketsData{}
	err = json.Unmarshal([]byte(ticketsResp.Message), &supportTicketsData)
	if err != nil {
		return nil, err
	}

	return &bean.GetSupportTicketsResp{
		Code:    ticketsResp.Code,
		Message: supportTicketsData,
	}, nil
}

// GetTicketTemplate
func (s *QiyuService) GetTicketTemplate(ctx context.Context, appKey, appSecret string, req *bean.GetTicketTemplateReq) (*bean.GetTicketTemplateResp, error) {
	logger.Logger.InfofCtx(ctx, "get ticket template")

	timestamp := fmt.Sprintf("%d", time.Now().Unix())

	templateMap := map[string]int{
		"templateId": req.TemplateID,
	}

	body, err := json.Marshal(templateMap)
	if err != nil {
		return nil, err
	}
	md5 := util.EncodeMD5(string(body))

	resp, err := resty.New().R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"appKey":   appKey,
			"time":     timestamp,
			"checksum": s.GenQiyuPushCheckSum(appSecret, md5, timestamp),
		}).
		SetBody(body).
		Post(config.GlobConfig.Qiyu.BaseURL + getTicketTemplateUrl)
	if err != nil {
		return nil, err
	}

	// GetTicketTemplateResp
	temp := &bean.TicketTemplateResp{}
	err = json.Unmarshal(resp.Body(), temp)
	if err != nil {
		return nil, err
	}
	var items []bean.GetTicketTemplateItem
	err = json.Unmarshal([]byte(temp.Message), &items)
	if err != nil {
		return nil, err
	}
	return &bean.GetTicketTemplateResp{
		Code:    temp.Code,
		Message: items,
	}, nil
}

// GetTicketDetail
func (s *QiyuService) GetTicketDetail(ctx context.Context, appKey, appSecret string, ticketID int) (*bean.GetTicketDetailResp, error) {
	logger.Logger.InfofCtx(ctx, "get ticket detail")

	timestamp := fmt.Sprintf("%d", time.Now().Unix())

	templateMap := map[string]int{
		"ticketId": ticketID,
	}

	body, err := json.Marshal(templateMap)
	if err != nil {
		return nil, err
	}
	md5 := util.EncodeMD5(string(body))

	resp, err := resty.New().R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"appKey":   appKey,
			"time":     timestamp,
			"checksum": s.GenQiyuPushCheckSum(appSecret, md5, timestamp),
		}).
		SetBody(body).
		Post(config.GlobConfig.Qiyu.BaseURL + getTicketDetailUrl)
	if err != nil {
		return nil, err
	}

	// 打印resp.Body()
	logger.Logger.InfofCtx(ctx, "get ticket detail resp: %s", string(resp.Body()))

	// GetTicketTemplateResp
	respData := &bean.GetTicketDetailResp{}
	err = json.Unmarshal(resp.Body(), respData)
	if err != nil {
		return nil, err
	}

	return respData, nil
}

// 读取七鱼客服小程序
func (s *QiyuService) GetQiyuMiniGameSession(ctx context.Context) (*model.AConfigMiniprogram, error) {
	mini := store.QueryDB().AConfigMiniprogram
	return mini.First()
}

// GetRobotFilter 获取机器人过滤
func (s *QiyuService) GetRobotFilter(ctx context.Context, gameID string) ([]string, error) {
	filter := store.QueryDB().ARobotFilter
	filterList, err := filter.Where(filter.GameID.Eq(gameID)).Find()
	if err != nil {
		return nil, err
	}
	contentList := make([]string, 0)
	for _, f := range filterList {
		contentList = append(contentList, f.Content)
	}
	return contentList, nil
}

// GetRobotReply 获取机器人回复
func (s *QiyuService) GetRobotReply(ctx context.Context, appKey, appSecret, content, userID string) (string, error) {
	logger.Logger.InfofCtx(ctx, "获取机器人回复, Content: %s, UserID: %s", content, userID)

	// 创建会话
	_, err := s.CreateSession(ctx, appKey, appSecret, &bean.CreateSessionReq{
		UID: userID,
	})
	if err != nil {
		return "", fmt.Errorf("创建会话失败: %w", err)
	}

	// 发送消息
	sessionChatReq := &bean.SessionChatReq{
		UID: userID,
		Msg: bean.CommonMsg{
			MsgType: "text",
			MsgBody: bean.MsgBody{
				Content: content,
			},
		},
	}

	sessionChatResp, err := s.SessionChat(ctx, appKey, appSecret, sessionChatReq)
	if err != nil {
		return "", fmt.Errorf("发送消息失败: %w", err)
	}

	// 获取回复
	if sessionChatResp.Code != http.StatusOK {
		return "", fmt.Errorf("发送消息失败: %d", sessionChatResp.Code)
	}

	// 如果没有回复，返回空字符串
	if len(sessionChatResp.Data.AnswerList) == 0 {
		return "", nil
	}

	// 获取机器人回复
	for _, answer := range sessionChatResp.Data.AnswerList {
		// 只处理第一个回复
		return answer.AnsContent.Answer.MsgBody.Content, nil
	}

	return "", nil
}

// QiyuTicketCallback
func (s *QiyuService) QiyuTicketCallback(ctx context.Context, req *bean.QiyuTicketCallbackReq) (*bean.QiyuTicketCallbackResp, error) {
	logger.Logger.InfofCtx(ctx, "qiyu ticket callback: %+v", req)
	if req.SheetID == 0 {
		return nil, errors.New("ticket id is empty")
	}

	// 查找工单明细
	detail, err := s.GetTicketDetail(ctx, config.GlobConfig.Qiyu.AppKey, config.GlobConfig.Qiyu.AppSecret, req.SheetID)
	if err != nil {
		return nil, err
	}
	if detail == nil {
		return nil, errors.New("open id is empty")
	}
	openID := detail.Data.CrmForeignId
	title := detail.Data.Title
	content := detail.Data.Content

	return &bean.QiyuTicketCallbackResp{OpenID: openID, Title: title, Content: content, ReplyContent: req.Remark}, nil
}

// ReplyTicket
func (s *QiyuService) ReplyTicket(ctx context.Context, req *bean.ReplyTicketReq) error {
	logger.Logger.InfofCtx(ctx, "reply ticket: %+v", req)

	timestamp := fmt.Sprintf("%d", time.Now().Unix())

	replyReq := map[string]interface{}{
		"ticketId": req.TicketID,
		"staffId":  constants.PlayerQuestionTicketStaffID,
		"comment":  req.Comment,
	}

	body, err := json.Marshal(replyReq)
	if err != nil {
		return err
	}
	md5 := util.EncodeMD5(string(body))

	resp, err := resty.New().R().
		SetContext(ctx).
		SetQueryParams(map[string]string{
			"appKey":   config.GlobConfig.Qiyu.AppKey,
			"time":     timestamp,
			"checksum": s.GenQiyuPushCheckSum(config.GlobConfig.Qiyu.AppSecret, md5, timestamp),
		}).
		SetBody(body).
		Post(config.GlobConfig.Qiyu.BaseURL + replyTicketUrl)
	if err != nil {
		return err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.ErrorfCtx(ctx, "reply ticket failed with status code: %d, response: %s",
			resp.StatusCode(), string(resp.Body()))
		return fmt.Errorf("reply ticket failed with status code: %d", resp.StatusCode())
	}

	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	}
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return fmt.Errorf("failed to unmarshal response: %v", err)
	}

	if result.Code != http.StatusOK {
		return fmt.Errorf("reply ticket failed with code: %d, message: %s", result.Code, result.Message)
	}

	return nil
}
