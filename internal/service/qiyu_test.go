package service

import (
	"context"
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

// 测试函数
func TestQiyuService_GetTicketDetail(t *testing.T) {
	Init()
	userMinigame, err := SingletonQiyuService().GetTicketDetail(context.Background(), "697f4043e84daf5672b527dcefaf3042", "D1DE4D037B944458932182D99E6D0A39", 112875803)
	if err != nil {
		return
	}
	logger.Logger.Debug("userMinigame :")
	logger.Logger.Debug(userMinigame)
}
