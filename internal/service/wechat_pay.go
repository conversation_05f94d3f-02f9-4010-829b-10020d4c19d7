package service

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/downloader"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
)

var (
	wechatPayOnce    sync.Once
	wechatPayService *WechatPayService
)

type WechatPayService struct {
	client *core.Client
}

var (
	mchID       = "" // 商户号
	mchNum      = "" // 商户证书序列号
	mchAPIv3Key = "" // 商户APIv3密钥
)

func SingletonWechatPayService() *WechatPayService {
	wechatPayOnce.Do(func() {
		ctx := context.Background()
		// 加载 redis 读取商户号
		company := store.QueryDB().ACompany
		companyInfo, err := company.WithContext(ctx).First()
		if err != nil {
			logger.Logger.Errorf("SERIOUS!!! SingletonWechatPayService find config error: %s", err.Error())
			return
		}
		mchID = companyInfo.WechatPayMchID
		mchNum = companyInfo.WechatPayMchNum
		mchAPIv3Key = companyInfo.WechatPayAPIKey
		mchPrivateKey, err := utils.LoadPrivateKey(companyInfo.WechatPayPrivateKey)
		if err != nil {
			logger.Logger.Errorf("SERIOUS!!! SingletonWechatPayService load merchant private key")
		}

		// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
		opts := []core.ClientOption{
			option.WithWechatPayAutoAuthCipher(mchID, mchNum, mchPrivateKey, mchAPIv3Key),
		}
		client, err := core.NewClient(ctx, opts...)
		if err != nil {
			logger.Logger.Errorf("SERIOUS!!! SingletonWechatPayService Error creating client err: %s", err.Error())
		}
		wechatPayService = &WechatPayService{
			client: client,
		}
	})
	return wechatPayService
}

// GetOrderSignData 获取微信支付订单签名数据
func (s *WechatPayService) GetOrderSignData(_ context.Context, order *bean.Order, offerID string) (*bean.WechatOrder, error) {
	return &bean.WechatOrder{
		Mode:         "goods",
		OfferId:      offerID, // offerID
		BuyQuantity:  1,
		Env:          config.GlobConfig.WechatPay.MidasEnv, // 沙箱环境 0: 米大师正式环境 1:米大师沙箱环境
		CurrencyType: "CNY",
		Platform:     "android",
		ZoneId:       "1",
		ProductId:    order.WechatProductID,
		GoodsPrice:   order.Money,
		OutTradeNo:   order.OrderID,
	}, nil
}

// PaySignature method
func (s *WechatPayService) PaySignature(appKey string, signData []byte) (string, error) {
	needEncodeBody := fmt.Sprintf("%s&%s", "requestMidasPaymentGameItem", string(signData))

	h := hmac.New(sha256.New, []byte(appKey))
	h.Write([]byte(needEncodeBody))

	paySig := hex.EncodeToString(h.Sum(nil))
	return paySig, nil
}

// Signature 签名
func (s *WechatPayService) Signature(sessionKey string, params []byte) (string, error) {
	h := hmac.New(sha256.New, []byte(sessionKey))
	h.Write(params)
	return hex.EncodeToString(h.Sum(nil)), nil
}

func (s *WechatPayService) WechatPayCallback(ctx context.Context, r *http.Request) (*model.AOrder, bool, error) { //*bean.WechatPayCallbackRes
	company := store.QueryDB().ACompany
	companyInfo, err := company.WithContext(ctx).First()
	if err != nil {
		logger.Logger.Errorf("SERIOUS!!! SingletonWechatPayService find config error: %s", err.Error())
		return nil, false, err
	}
	logger.Logger.Warnf("SERIOUS!!! WechatPayCallback err: %s", companyInfo.WechatPayMchID)
	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(companyInfo.WechatPayMchID)
	handler := notify.NewNotifyHandler(mchAPIv3Key, verifiers.NewSHA256WithRSAVerifier(certificateVisitor))
	transaction := new(payments.Transaction)
	notifyReq, err := handler.ParseNotifyRequest(context.Background(), r, transaction)
	// 如果验签未通过，或者解密失败
	if err != nil {
		logger.Logger.Errorf("WechatPayService WechatPayCallback callback notify err: %s", err.Error())
		return nil, false, err
	}

	info := &bean.WeChatPaymentNotification{}
	err = json.Unmarshal([]byte(notifyReq.Resource.Plaintext), info)
	if err != nil {
		logger.Logger.Errorf("WechatPayService WechatPayCallback callback unmarshal err: %s", err.Error())
		return nil, false, err
	}
	if info.OutTradeNo == "" {
		return nil, false, errors.New("WechatPayService WechatPayCallback callback outTradeNo is empty")
	}
	// 订单金额对比
	order := store.QueryDB().AOrder
	orderInfo, err := order.WithContext(ctx).Where(order.OrderID.Eq(info.OutTradeNo)).First()
	if err != nil {
		logger.Logger.Errorf("WechatPayService WechatPayCallback callback find order err: %s", err.Error())
		return nil, false, err
	}

	// 如果订单状态已经是支付成功，说明是重复回调，直接返回订单信息和重复回调标志
	if orderInfo.Status == constants.PaymentWechatPaySuccess || orderInfo.Status == constants.PaymentProductShipmentSuccess {
		logger.Logger.Infof("WechatPayService WechatPayCallback 订单 %s 已处于支付成功状态，可能是重复回调", info.OutTradeNo)
		return orderInfo, true, nil // true表示是重复回调
	}
	if orderInfo.Money != info.Amount.Total {
		logger.Logger.Errorf("[WechatPayService] WechatPayCallback 用户订单金额和实际价格不相等")
		return nil, false, fmt.Errorf("WechatPayService WechatPayCallback callback money is not equal, order money is %d, currency moeny is %d",
			orderInfo.Money, info.Amount.Total)
	}

	// 处理通知内容
	_, err = order.WithContext(ctx).Where(order.OrderID.Eq(info.OutTradeNo)).UpdateSimple(
		order.Status.Value(constants.PaymentWechatPaySuccess),
		order.CurrencyPrice.Value(info.Amount.PayerTotal),
		order.CallbackOriginData.Value(notifyReq.Resource.Plaintext),
	)
	if err != nil {
		logger.Logger.Errorf("WechatPayService WechatPayCallback update order err: %s", err.Error())
		return nil, false, err
	}

	finalOrderInfo, err := order.WithContext(ctx).Where(order.OrderID.Eq(info.OutTradeNo)).First()
	if err != nil {
		logger.Logger.Errorf("WechatPayService WechatPayCallback callback find order err: %s", err.Error())
		return nil, false, err
	}
	return finalOrderInfo, false, nil // 返回false表示不是重复回调
}

func (s *WechatPayService) PayTransactionsJSAPI(ctx context.Context, req *bean.PayTransactionsJSAPI) (*bean.JSAPIWechatPayData, error) {
	if s.client == nil {
		logger.Logger.Errorf("PayTransactionsJSAPI pay client need reset system, %s", req.OutTradeNo)
		return nil, fmt.Errorf("PayTransactionsJSAPI pay client need reset system, %s", req.OutTradeNo)
	}
	svc := jsapi.JsapiApiService{Client: s.client}
	resp, _, err := svc.PrepayWithRequestPayment(ctx,
		jsapi.PrepayRequest{
			Appid:       core.String(req.AppID),
			Mchid:       core.String(req.MchID),
			Description: core.String(req.Description),
			OutTradeNo:  core.String(req.OutTradeNo),
			Attach:      core.String(req.Attach),
			NotifyUrl:   core.String(req.NotifyUrl),
			Amount: &jsapi.Amount{
				Total: core.Int64(int64(req.AmountTotal)),
			},
			Payer: &jsapi.Payer{
				Openid: core.String(req.PayerOpenID),
			},
		},
	)
	if err != nil {
		return nil, err
	}
	if resp == nil {
		return nil, errors.New("WechatPayService PayTransactionsJSAPI resp is nil")
	}
	return &bean.JSAPIWechatPayData{
		AppID:     *resp.Appid,
		PrepayID:  *resp.PrepayId,
		NonceStr:  *resp.NonceStr,
		TimeStamp: *resp.TimeStamp,
		Package:   *resp.Package,
		SignType:  *resp.SignType,
		PaySign:   *resp.PaySign,
	}, nil
}
