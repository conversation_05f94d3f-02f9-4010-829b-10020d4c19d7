package service

import (
	"context"
	"os"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/mysql"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/stretchr/testify/assert"
)

// 初始化测试环境
func initTestEnvForDate() {
	// 设置配置文件路径到项目根目录
	originalDir, _ := os.Getwd()
	defer os.Chdir(originalDir)

	// 切换到项目根目录
	if err := os.Chdir("../../"); err != nil {
		panic("无法切换到项目根目录: " + err.Error())
	}

	config.MustInit()
	logger.InitLogger(&config.GlobConfig.Logger)
	mysql.InitMysql(&config.GlobConfig.Mysql)
	store.InitQueryDB()
	redis.InitRedis(&config.GlobConfig.Redis)
}

func TestDateService_IsPlayableDate(t *testing.T) {
	initTestEnvForDate()

	service := SingletonDateService()
	ctx := context.Background()

	// 获取北京时区
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	assert.NoError(t, err)

	// 测试用例1: 测试周五（应该返回true）
	friday := time.Date(2024, 1, 5, 10, 0, 0, 0, beijingLocation) // 2024年1月5日是周五
	isPlayable, err := service.IsPlayableDate(ctx, friday)
	assert.NoError(t, err)
	assert.True(t, isPlayable, "周五应该是可玩日期")

	// 测试用例2: 测试周六（应该返回true）
	saturday := time.Date(2024, 1, 6, 10, 0, 0, 0, beijingLocation) // 2024年1月6日是周六
	isPlayable, err = service.IsPlayableDate(ctx, saturday)
	assert.NoError(t, err)
	assert.True(t, isPlayable, "周六应该是可玩日期")

	// 测试用例3: 测试周日（应该返回true）
	sunday := time.Date(2024, 1, 7, 10, 0, 0, 0, beijingLocation) // 2024年1月7日是周日
	isPlayable, err = service.IsPlayableDate(ctx, sunday)
	assert.NoError(t, err)
	assert.True(t, isPlayable, "周日应该是可玩日期")

	// 测试用例4: 测试周一（应该返回false，除非在数据库中有记录）
	monday := time.Date(2024, 1, 8, 10, 0, 0, 0, beijingLocation) // 2024年1月8日是周一
	isPlayable, err = service.IsPlayableDate(ctx, monday)
	assert.NoError(t, err)
	assert.False(t, isPlayable, "周一应该不是可玩日期（除非在数据库中有记录）")

	// 注意：由于我们移除了AddPlayableDate和RemovePlayableDate方法，
	// 这里只测试基本的周末逻辑，不测试数据库添加功能
}

func TestDateService_IsTodayPlayable(t *testing.T) {
	initTestEnvForDate()

	service := SingletonDateService()
	ctx := context.Background()

	// 测试今天是否为可玩日期
	isPlayable, err := service.IsTodayPlayable(ctx)
	assert.NoError(t, err)

	// 获取今天是星期几
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	assert.NoError(t, err)
	today := time.Now().In(beijingLocation)
	weekday := today.Weekday()

	// 如果今天是周五、周六或周日，应该返回true
	expectedPlayable := weekday == time.Friday || weekday == time.Saturday || weekday == time.Sunday

	// 注意：如果数据库中有今天的记录，结果可能不同
	// 这里我们只测试基本逻辑
	t.Logf("今天是 %s，预期可玩状态: %v，实际可玩状态: %v", weekday.String(), expectedPlayable, isPlayable)
}

func TestDateService_DatabaseQuery(t *testing.T) {
	initTestEnvForDate()

	service := SingletonDateService()
	ctx := context.Background()

	// 获取北京时区
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	assert.NoError(t, err)

	// 测试日期：2024年2月14日（情人节，周三）
	testDate := time.Date(2024, 2, 14, 10, 0, 0, 0, beijingLocation)

	// 测试数据库查询功能 - 这个日期应该不在数据库中（除非之前有数据）
	isPlayable, err := service.IsPlayableDate(ctx, testDate)
	assert.NoError(t, err)
	// 由于是周三且数据库中没有记录，应该返回false
	assert.False(t, isPlayable, "测试日期（周三）应该不是可玩日期")
}

func TestDateService_TimeZoneHandling(t *testing.T) {
	initTestEnvForDate()

	service := SingletonDateService()
	ctx := context.Background()

	// 测试不同时区的时间是否正确转换为北京时区
	utcTime := time.Date(2024, 1, 5, 16, 0, 0, 0, time.UTC) // UTC时间
	tokyoLocation, _ := time.LoadLocation("Asia/Tokyo")
	tokyoTime := time.Date(2024, 1, 6, 1, 0, 0, 0, tokyoLocation) // 东京时间

	// 这两个时间都应该对应北京时间的同一天
	isPlayableUTC, err := service.IsPlayableDate(ctx, utcTime)
	assert.NoError(t, err)

	isPlayableTokyo, err := service.IsPlayableDate(ctx, tokyoTime)
	assert.NoError(t, err)

	// 由于都是周五，应该都返回true
	assert.True(t, isPlayableUTC, "UTC时间转换后应该是可玩日期")
	assert.True(t, isPlayableTokyo, "东京时间转换后应该是可玩日期")
}

func TestDateService_EdgeCases(t *testing.T) {
	initTestEnvForDate()

	service := SingletonDateService()
	ctx := context.Background()

	// 测试边界情况：年末和年初
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	assert.NoError(t, err)

	// 2023年12月31日（周日）
	yearEnd := time.Date(2023, 12, 31, 23, 59, 59, 0, beijingLocation)
	isPlayable, err := service.IsPlayableDate(ctx, yearEnd)
	assert.NoError(t, err)
	assert.True(t, isPlayable, "年末的周日应该是可玩日期")

	// 2024年1月1日（周一）
	yearStart := time.Date(2024, 1, 1, 0, 0, 1, 0, beijingLocation)
	isPlayable, err = service.IsPlayableDate(ctx, yearStart)
	assert.NoError(t, err)
	assert.False(t, isPlayable, "年初的周一应该不是可玩日期（除非在数据库中）")
}
