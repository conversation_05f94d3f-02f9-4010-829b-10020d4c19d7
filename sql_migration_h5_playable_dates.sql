-- H5 可玩日期表创建 SQL
-- 用于存储特定的可玩日期，支持日期查询功能

-- 创建 h5_playable_dates 表
CREATE TABLE IF NOT EXISTS `h5_playable_dates` (
  `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `playable_date` DATE NOT NULL COMMENT '可玩日期（北京时区）',
  `description` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '日期描述信息',
  `creator_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建人ID',
  `created_at` BIGINT NOT NULL DEFAULT 0 COMMENT '创建时间戳（毫秒）',
  `updated_at` BIGINT NOT NULL DEFAULT 0 COMMENT '更新时间戳（毫秒）',
  `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除，0=未删，1=已删',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_playable_date` (`playable_date`),
  KEY `idx_playable_date` (`playable_date`),
  <PERSON>EY `idx_created_at` (`created_at`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='H5可玩日期表';

-- 插入一些示例数据（可选）
-- INSERT INTO `h5_playable_dates` (`playable_date`, `description`, `creator_id`, `created_at`, `updated_at`) VALUES
-- ('2024-12-25', '圣诞节特殊活动日', 'system', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
-- ('2024-01-01', '元旦特殊活动日', 'system', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 查看表结构验证创建
-- DESCRIBE `h5_playable_dates`;
