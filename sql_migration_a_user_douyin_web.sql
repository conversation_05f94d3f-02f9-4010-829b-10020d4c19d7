-- AUserDouyinWeb 表结构升级 SQL
-- 基于抖音开放平台 get-account-open-info API 字段要求

-- 添加新字段以支持完整的抖音用户信息和令牌管理
ALTER TABLE `a_user_douyin_web` 
ADD COLUMN `description` VARCHAR(512) NOT NULL DEFAULT '' COMMENT '用户描述信息' AFTER `nick_name`,
ADD COLUMN `e_account_role` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '企业账号角色' AFTER `description`,
ADD COLUMN `client_key` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '客户端密钥' AFTER `e_account_role`,
ADD COLUMN `access_token` VARCHAR(512) NOT NULL DEFAULT '' COMMENT '访问令牌' AFTER `client_key`,
ADD COLUMN `refresh_token` VARCHAR(512) NOT NULL DEFAULT '' COMMENT '刷新令牌' AFTER `access_token`,
ADD COLUMN `token_expires_at` BIGINT NOT NULL DEFAULT 0 COMMENT '令牌过期时间（毫秒时间戳）' AFTER `refresh_token`;

-- 为新增字段添加索引以提高查询性能
ALTER TABLE `a_user_douyin_web` 
ADD INDEX `idx_client_key` (`client_key`),
ADD INDEX `idx_access_token` (`access_token`),
ADD INDEX `idx_token_expires_at` (`token_expires_at`);

-- 为已有的重要字段添加索引（如果还没有的话）
ALTER TABLE `a_user_douyin_web` 
ADD INDEX `idx_open_id` (`open_id`) IF NOT EXISTS,
ADD INDEX `idx_union_id` (`union_id`) IF NOT EXISTS,
ADD INDEX `idx_user_id` (`user_id`) IF NOT EXISTS;

-- 查看表结构验证变更
-- DESCRIBE `a_user_douyin_web`;
