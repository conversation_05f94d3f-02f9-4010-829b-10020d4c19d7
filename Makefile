# Makefile for the 'admin-console' Golang project with 'Env' environment variable set to 'local'

.PHONY: build run clean

# Build the project
build:
	@echo "Building the admin-console project..."
	@GO111MODULE=on go build -o admin-console

# Run the project
run: build
	@echo "Running the admin-console project..."
	@Env=local ./admin-console

# Clean up the build
clean:
	@echo "Cleaning up..."
	@rm -f admin-console
